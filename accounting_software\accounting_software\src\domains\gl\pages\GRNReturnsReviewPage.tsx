import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Card,
  CardContent,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  AssignmentReturn as ReturnIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';
import { grnReturnService, GRNReturn, GRNReturnItem, GRNReturnStats } from '../../inventory/services/grn-return.service';

// Extended GRN Return interface for review page with additional financial fields
interface GRNReturnReview extends GRNReturn {
  grn_number?: string;
  po_number?: string;
  vendor_name?: string;
  tax_amount?: number;
  grand_total?: number;
  currency?: string;
}



const GRNReturnsReviewPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [grnReturns, setGrnReturns] = useState<GRNReturnReview[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedReturn, setSelectedReturn] = useState<GRNReturnReview | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [stats, setStats] = useState<GRNReturnStats | null>(null);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadGRNReturns();
    loadStats();
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadGRNReturns = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem('token');
      const queryParams = new URLSearchParams();
      
      queryParams.append('page', (page + 1).toString());
      if (searchTerm) queryParams.append('search', searchTerm);
      if (statusFilter) queryParams.append('status', statusFilter);

      const response = await fetch(`http://localhost:8000/api/inventory/grn-returns/?${queryParams}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load GRN returns');
      }

      const data = await response.json();
      setGrnReturns(data.results || []);
      setTotalCount(data.count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRN returns');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/inventory/grn-returns/stats/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load GRN return statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Failed to load GRN return stats:', err);
    }
  };

  const handleViewReturn = async (grnReturn: GRNReturnReview) => {
    try {
      console.log('🔍 Loading GRN Return details for:', grnReturn);
      
      // Use the grn_return_id field instead of id, and use the grnReturnService
      if (!grnReturn.grn_return_id) {
        throw new Error('GRN Return ID is missing');
      }
      
      const data = await grnReturnService.getGRNReturn(grnReturn.grn_return_id);
      console.log('✅ GRN Return details loaded:', data);
      
      // Map the service data to the review format
      const reviewData: GRNReturnReview = {
        ...data,
        grn_number: data.original_grn_number || '',
        po_number: '', // Not available in return data
        vendor_name: data.vendor_name || '',
        tax_amount: 0, // Not available in return data
        grand_total: data.total_value || 0,
        currency: 'USD', // Default currency
      };
      
      setSelectedReturn(reviewData);
      setViewDialogOpen(true);
    } catch (err) {
      console.error('❌ Failed to load GRN Return details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load GRN return details');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      draft: { color: 'default' as const, label: 'Draft' },
      posted: { color: 'success' as const, label: 'Posted' },
      cancelled: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const columns = [
    {
      field: 'grn_return_number',
      headerName: 'Return Number',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2" fontWeight="medium">
          {row.grn_return_number}
        </Typography>
      ),
    },
    {
      field: 'original_grn_number',
      headerName: 'GRN Number',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2">
          {row.original_grn_number || row.grn_number}
        </Typography>
      ),
    },
    {
      field: 'po_number',
      headerName: 'PO Number',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2">
          {row.po_number || '-'}
        </Typography>
      ),
    },
    {
      field: 'vendor_name',
      headerName: 'Vendor',
      flex: 1.5,
    },
    {
      field: 'return_date',
      headerName: 'Return Date',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => formatDate(row.return_date),
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => getStatusChip(row.status),
    },
    {
      field: 'total_quantity',
      headerName: 'Total Qty',
      flex: 0.8,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right' }}>
          {Number(row.total_quantity || 0).toFixed(2)}
        </Typography>
      ),
    },
    {
      field: 'total_value',
      headerName: 'Subtotal',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'medium', color: 'error.main' }}>
          -{formatCurrency(Number(row.total_value || 0))}
        </Typography>
      ),
    },
    {
      field: 'tax_amount',
      headerName: 'Tax Amount',
      flex: 1,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', color: 'error.main' }}>
          -{formatCurrency(Number(row.tax_amount || 0))}
        </Typography>
      ),
    },
    {
      field: 'grand_total',
      headerName: 'Grand Total',
      flex: 1.2,
      renderCell: ({ row }: { row: GRNReturnReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'bold', color: 'error.main' }}>
          -{formatCurrency(Number(row.grand_total || row.total_value || 0))}
        </Typography>
      ),
    },
  ];

  const rowActions = [
    {
      icon: <ViewIcon />,
      label: 'View Details',
      onClick: (row: GRNReturnReview) => handleViewReturn(row),
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold" display="flex" alignItems="center" gap={1}>
            <ReturnIcon />
            GRN Returns Review
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Financial review of goods return notes for accounting purposes
          </Typography>
        </Box>
      </PageHeader>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <BusinessIcon color="primary" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Returns</Typography>
                    <Typography variant="h6" fontWeight="bold">{stats.total_returns}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <CalendarIcon color="success" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Posted</Typography>
                    <Typography variant="h6" fontWeight="bold">{stats.posted_returns}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <AccountBalanceIcon color="error" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Value</Typography>
                    <Typography variant="h6" fontWeight="bold" color="error.main">
                      -{formatCurrency(stats.total_value)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrendingDownIcon color="warning" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Tax</Typography>
                    <Typography variant="h6" fontWeight="bold" color="error.main">
                      -{formatCurrency(0)} {/* Tax calculation not available in return stats */}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <DataTable
        columns={columns}
        rows={grnReturns}
        loading={loading}
        totalCount={totalCount}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        rowActions={rowActions}
        serverSide={true}
        showToolbar={true}
        title="GRN Returns"
      />

      {/* View GRN Return Dialog */}
      <Dialog 
        open={viewDialogOpen} 
        onClose={() => setViewDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" display="flex" alignItems="center" gap={1}>
            <ReturnIcon />
            GRN Return Details - {selectedReturn?.grn_return_number}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedReturn && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>Basic Information</Typography>
                    <Typography variant="body2" color="text.secondary">GRN Number</Typography>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      {selectedReturn.grn_number}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">PO Number</Typography>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      {selectedReturn.po_number}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Vendor</Typography>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      {selectedReturn.vendor_name}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Return Date</Typography>
                    <Typography variant="body1" gutterBottom>
                      {formatDate(selectedReturn.return_date)}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Status</Typography>
                    <Box sx={{ mb: 2 }}>
                      {getStatusChip(selectedReturn.status)}
                    </Box>
                    
                    {selectedReturn.warehouse_name && (
                      <>
                        <Typography variant="body2" color="text.secondary">Warehouse</Typography>
                        <Typography variant="body1" gutterBottom>
                          {selectedReturn.warehouse_name}
                        </Typography>
                      </>
                    )}
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>Financial Summary</Typography>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Total Quantity:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {Number(selectedReturn.total_quantity || 0).toFixed(2)}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Subtotal:</Typography>
                      <Typography variant="body2" fontWeight="medium" color="error.main">
                        -{formatCurrency(Number(selectedReturn.total_value || 0))}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Tax Amount:</Typography>
                      <Typography variant="body2" color="error.main">
                        -{formatCurrency(Number(selectedReturn.tax_amount || 0))}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" sx={{ pt: 1, borderTop: 1, borderColor: 'divider' }}>
                      <Typography variant="h6">Grand Total:</Typography>
                      <Typography variant="h6" fontWeight="bold" color="error.main">
                        -{formatCurrency(Number(selectedReturn.grand_total || 0))}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">Currency:</Typography>
                      <Typography variant="body1">{selectedReturn.currency}</Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                {selectedReturn.return_reason && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Return Reason</Typography>
                      <Typography variant="body1">{selectedReturn.return_reason}</Typography>
                    </Paper>
                  </Grid>
                )}
                
                {selectedReturn.items && selectedReturn.items.length > 0 && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Return Items</Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Product</TableCell>
                              <TableCell align="right">Return Quantity</TableCell>
                              <TableCell align="right">Unit Price</TableCell>
                              <TableCell align="right">Tax Amount</TableCell>
                              <TableCell align="right">Total Amount</TableCell>
                              <TableCell>Reason</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {selectedReturn.items.map((item, index) => (
                              <TableRow key={item.grn_return_item_id || index}>
                                <TableCell>{item.product_name}</TableCell>
                                <TableCell align="right">
                                  {Number(item.quantity_returned || 0).toFixed(2)}
                                </TableCell>
                                <TableCell align="right">
                                  {formatCurrency(Number(item.unit_cost || 0))}
                                </TableCell>
                                <TableCell align="right" sx={{ color: 'error.main' }}>
                                  -{formatCurrency(0)} {/* Tax amount not available in return items */}
                                </TableCell>
                                <TableCell align="right" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                                  -{formatCurrency(Number(item.return_value || 0))}
                                </TableCell>
                                <TableCell>{item.return_reason || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                  </Grid>
                )}
                
                {selectedReturn.notes && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Notes</Typography>
                      <Typography variant="body1">{selectedReturn.notes}</Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default GRNReturnsReviewPage; 