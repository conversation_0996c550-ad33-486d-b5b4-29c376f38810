import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Avatar,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  AlertTitle,
  Stack,
  Tooltip,
  Fab,
  CircularProgress,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Pagination,
  TablePagination,
  Snackbar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {

  Edit as EditIcon,
  Visibility as ViewIcon,
  AccountBalance as AccountBalanceIcon,
  CreditCard as CreditCardIcon,
  Savings as SavingsIcon,
  AccountBalanceWallet as WalletIcon,
  TrendingUp as TrendingUpIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  GetApp as ExportIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  AccountTree as AccountTreeIcon,
  Timeline as TimelineIcon,
  Receipt as ReceiptIcon,
  CompareArrows as TransferIcon,
  Schedule as ScheduleIcon,
  Security as SecurityIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import GLAccountSelector from '../components/GLAccountSelector';

// Cash Account Interfaces
interface CashAccountType {
  id: number;
  code: string;
  name: string;
  description: string;
  requires_bank_details: boolean;
  allows_overdraft: boolean;
  requires_reconciliation: boolean;
  is_investment_account: boolean;
  sort_order: number;
  is_active: boolean;
}

interface CashAccount {
  id: number;
  account_name: string;
  account_number: string;
  account_type: CashAccountType;
  gl_account?: number | null;
  bank_name?: string;
  bank_branch?: string;
  bank_routing_number?: string;
  bank_swift_code?: string;
  bank_iban?: string;
  currency: string;
  opening_balance: number;
  opening_balance_date: string;
  current_balance: number;
  credit_limit?: number;
  interest_rate?: number;
  account_manager?: string;
  description?: string;
  is_active: boolean;
  is_default: boolean;
  requires_approval: boolean;
  last_reconciled_date?: string;
  last_reconciled_balance?: number;
  created_at: string;
  updated_at: string;
  detail_type?: CashAccountType;
}

// AccountFormData interface removed - no longer needed for view-only mode

const CashAccountsPage: React.FC = () => {
  // State variables for UI
  const [accounts, setAccounts] = useState<CashAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<CashAccount | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  // Removed unused variables for form handling since accounts are now read-only

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadAccounts();
  }, [searchTerm, filterActive]);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        search: searchTerm,
        ordering: '-created_at',
      });

      if (filterActive !== null) {
        params.append('is_active', filterActive.toString());
      }

      const response = await fetch(`http://localhost:8000/api/cms/accounts/?${params}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load cash accounts');
      }

      const data = await response.json();
      setAccounts(data.results || []);
      setTotalCount(data.count || 0);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load accounts');
      console.error('Load accounts error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleView = (account: CashAccount) => {
    setSelectedAccount(account);
    setOpenDialog(true);
  };

  const formatCurrency = (amount: number, currency?: string) => {
    if (!currencyInfo) return '0.00';
    
    const currencyCode = currency || currencyInfo.functional_currency;
    const locale = currencyCode === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getAccountTypeIcon = (accountType: string) => {
    const type = accountType.toLowerCase();
    if (type.includes('checking')) return <AccountBalanceIcon />;
    if (type.includes('savings')) return <SavingsIcon />;
    if (type.includes('credit')) return <CreditCardIcon />;
    if (type.includes('petty') || type.includes('cash')) return <WalletIcon />;
    if (type.includes('investment')) return <TrendingUpIcon />;
    return <AccountBalanceIcon />;
  };

  const getAccountTypeColor = (accountType: string | undefined) => {
    if (!accountType) return 'default';
    const type = accountType.toLowerCase();
    if (type.includes('checking')) return 'primary';
    if (type.includes('savings')) return 'success';
    if (type.includes('credit')) return 'warning';
    if (type.includes('petty') || type.includes('cash')) return 'info';
    if (type.includes('investment')) return 'secondary';
    return 'default';
  };

  if (loading && accounts.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Cash Accounts...</Typography>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              💳 Cash Accounts
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Manage bank accounts, cash funds, and payment methods
            </Typography>
            {currencyInfo && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                📊 {currencyInfo.company_name} • Default Currency: {currencyInfo.functional_currency} ({currencyInfo.functional_currency_symbol})
              </Typography>
            )}
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic', textAlign: 'right' }}>
            Accounts are managed from GL → Chart of Accounts
          </Typography>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search accounts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filterActive === null ? 'all' : filterActive.toString()}
                    label="Status"
                    onChange={(e) => {
                      const value = e.target.value;
                      setFilterActive(value === 'all' ? null : value === 'true');
                    }}
                  >
                    <MenuItem value="all">All Status</MenuItem>
                    <MenuItem value="true">Active</MenuItem>
                    <MenuItem value="false">Inactive</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={loadAccounts}
                  size="small"
                >
                  Refresh
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Accounts Table */}
        <Card>
          <CardContent>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <AlertTitle>Error</AlertTitle>
                {error}
              </Alert>
            )}

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Account</TableCell>
                    <TableCell>GL Type</TableCell>
                    <TableCell align="right">Balance</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {accounts.map((account) => (
                    <TableRow key={account.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar 
                            sx={{ 
                              width: 40, 
                              height: 40, 
                              mr: 2, 
                              bgcolor: `${getAccountTypeColor(account.account_type?.name)}.light` 
                            }}
                          >
                            {getAccountTypeIcon(account.account_type?.name || 'default')}
                          </Avatar>
                          <Box>
                            <Typography variant="body1" fontWeight="medium">
                              {account.account_name}
                              {account.is_default && (
                                <StarIcon 
                                  sx={{ ml: 1, fontSize: 16, color: 'warning.main' }} 
                                />
                              )}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {account.account_number}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Chip
                            size="small"
                            label={account.account_type?.name || 'Assets'}
                            color="primary"
                            variant="outlined"
                          />
                          <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                            {account.detail_type?.name || 'Cash and Bank'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Typography
                          variant="body1"
                          fontWeight="medium"
                          color={account.current_balance < 0 ? 'error.main' : 'text.primary'}
                        >
                          {formatCurrency(account.current_balance, account.currency)}
                        </Typography>
                        {account.credit_limit && (
                          <Typography variant="caption" color="text.secondary">
                            Credit: {formatCurrency(account.credit_limit, account.currency)}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1} justifyContent="center">
                          <Chip
                            size="small"
                            label={account.is_active ? 'Active' : 'Inactive'}
                            color={account.is_active ? 'success' : 'default'}
                            icon={account.is_active ? <CheckCircleIcon /> : <CancelIcon />}
                          />
                          {account.requires_approval && (
                            <Chip
                              size="small"
                              label="Approval"
                              color="warning"
                              icon={<ScheduleIcon />}
                            />
                          )}
                        </Stack>
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1} justifyContent="center">
                          <Tooltip title="View Details">
                            <IconButton size="small" onClick={() => handleView(account)}>
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Manage in Chart of Accounts">
                            <IconButton 
                              size="small" 
                              color="primary"
                              component="a"
                              href="/gl/chart-of-accounts"
                              target="_blank"
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Account Dialog - Simplified for GL accounts */}
        <Dialog 
          open={openDialog} 
          onClose={() => setOpenDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" alignItems="center">
              <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                <AccountBalanceIcon />
              </Avatar>
              GL Cash Account Details
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {selectedAccount ? (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Account Information
                      </Typography>
                      <List dense>
                        <ListItem>
                          <ListItemText 
                            primary="Account Name" 
                            secondary={selectedAccount.account_name} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Account Number" 
                            secondary={selectedAccount.account_number} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="GL Account Type" 
                            secondary={selectedAccount.account_type?.name || 'Assets'} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Detail Type" 
                            secondary={selectedAccount.detail_type?.name || 'Cash and Bank'} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Currency" 
                            secondary={selectedAccount.currency} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Current Balance" 
                            secondary={formatCurrency(selectedAccount.current_balance, selectedAccount.currency)} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Status" 
                            secondary={selectedAccount.is_active ? 'Active' : 'Inactive'} 
                          />
                        </ListItem>
                        {selectedAccount.description && (
                          <ListItem>
                            <ListItemText 
                              primary="Description" 
                              secondary={selectedAccount.description} 
                            />
                          </ListItem>
                        )}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            ) : (
              <Alert severity="info">
                No account selected for viewing.
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>
              Close
            </Button>
            <Button 
              variant="contained" 
              color="primary"
              component="a"
              href="/gl/chart-of-accounts"
              target="_blank"
            >
              Manage in Chart of Accounts
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default CashAccountsPage; 