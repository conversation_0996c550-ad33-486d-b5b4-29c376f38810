"""
Django Admin Configuration for General Ledger Module

This creates a comprehensive administrative interface for managing
all General Ledger data. Organized for accounting professionals
with proper fieldsets, filters, and search capabilities.
"""

from django.contrib import admin
from django.utils.html import format_html
from decimal import Decimal
from .models import AccountType, DetailType, Account, JournalEntry, JournalEntryLine

@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    """
    Admin interface for Account Types
    
    Account types are the fundamental categories (Assets, Liabilities, etc.)
    that determine how accounts behave in the accounting system.
    """
    
    list_display = [
        'code', 'name', 'type', 'normal_balance', 
        'financial_statement', 'accounts_count', 'is_active'
    ]
    list_filter = ['type', 'normal_balance', 'financial_statement', 'is_active']
    search_fields = ['code', 'name']
    ordering = ['sort_order', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'type'),
            'description': 'Fundamental account type identification and classification'
        }),
        ('Accounting Behavior', {
            'fields': ('normal_balance', 'financial_statement'),
            'description': 'How this account type behaves in accounting transactions and reporting'
        }),
        ('Display Settings', {
            'fields': ('sort_order', 'is_active'),
            'description': 'Control how account types are displayed in lists and forms'
        }),
    )
    
    def accounts_count(self, obj):
        """Show count of accounts using this type."""
        count = obj.accounts.filter(is_active=True).count()
        return format_html(
            '<span style="background-color: #e1f5fe; padding: 2px 6px; border-radius: 3px;">{}</span>',
            count
        )
    accounts_count.short_description = 'Active Accounts'

@admin.register(DetailType)
class DetailTypeAdmin(admin.ModelAdmin):
    """
    Admin interface for Detail Types
    
    Detail types provide QuickBooks-style granular classification
    within each account type for better reporting and automation.
    """
    
    list_display = [
        'code', 'name', 'account_type', 'accounts_count',
        'requires_customer', 'requires_vendor', 'is_tax_account', 'is_active'
    ]
    list_filter = [
        'account_type', 'requires_customer', 'requires_vendor', 
        'requires_item', 'is_tax_account', 'is_active'
    ]
    search_fields = ['code', 'name', 'description']
    ordering = ['account_type__sort_order', 'sort_order', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'account_type', 'description'),
            'description': 'Detail type identification and classification'
        }),
        ('Tracking Requirements', {
            'fields': ('requires_customer', 'requires_vendor', 'requires_item'),
            'description': 'Additional data tracking requirements for accounts of this type'
        }),
        ('Tax and Compliance', {
            'fields': ('is_tax_account', 'default_tax_code'),
            'description': 'Tax-related settings and default configurations'
        }),
        ('Display Settings', {
            'fields': ('sort_order', 'is_active'),
            'description': 'Control display order and availability'
        }),
    )
    
    def accounts_count(self, obj):
        """Show count of accounts using this detail type."""
        count = obj.accounts.filter(is_active=True).count()
        return format_html(
            '<span style="background-color: #f3e5f5; padding: 2px 6px; border-radius: 3px;">{}</span>',
            count
        )
    accounts_count.short_description = 'Active Accounts'

class JournalEntryLineInline(admin.TabularInline):
    """
    Inline editor for Journal Entry Lines
    
    This allows editing journal entry lines directly within
    the journal entry admin interface for easier data entry.
    """
    
    model = JournalEntryLine
    extra = 2
    fields = [
        'line_number', 'account', 'description', 
        'debit_amount', 'credit_amount', 'customer', 'vendor',
        'sales_tax', 'taxable_amount', 'sales_tax_amount'
    ]
    ordering = ['line_number']

@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    """
    Admin interface for Chart of Accounts
    
    Comprehensive interface for managing individual accounts
    with all the features needed for professional accounting.
    """
    
    list_display = [
        'account_number', 'account_name', 'account_type', 'detail_type',
        'parent_account', 'current_balance_display', 'currency', 'is_active'
    ]
    list_filter = [
        'account_type', 'detail_type', 'currency', 
        'is_active', 'is_header_account', 'created_by'
    ]
    search_fields = ['account_number', 'account_name', 'description']
    ordering = ['account_number']
    date_hierarchy = 'created_at'
    
    fieldsets = (
        ('Account Identification', {
            'fields': ('account_number', 'account_name', 'description'),
            'description': 'Basic account identification and description'
        }),
        ('Account Classification', {
            'fields': ('account_type', 'detail_type', 'parent_account'),
            'description': 'How this account is classified in the Chart of Accounts'
        }),
        ('Account Settings', {
            'fields': ('currency', 'is_active', 'is_header_account'),
            'description': 'Account behavior and availability settings'
        }),
        ('Opening Balance', {
            'fields': ('opening_balance', 'opening_balance_date'),
            'description': 'Initial balance when account was created'
        }),
        ('Banking Integration', {
            'fields': ('bank_account_number', 'bank_routing_number'),
            'description': 'Bank account details for cash/bank accounts',
            'classes': ['collapse']
        }),
        ('Tax and Compliance', {
            'fields': ('tax_line',),
            'description': 'Tax reporting and compliance settings',
            'classes': ['collapse']
        }),
        ('System Information', {
            'fields': ('created_by', 'version'),
            'description': 'System tracking and audit information',
            'classes': ['collapse']
        }),
    )
    
    readonly_fields = ['created_by', 'created_at', 'updated_at']
    
    def current_balance_display(self, obj):
        """Display current balance with currency formatting."""
        balance = obj.get_current_balance()
        if balance >= 0:
            color = '#2e7d32'  # Green for positive
        else:
            color = '#d32f2f'  # Red for negative
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{} {}</span>',
            color,
            obj.currency,
            f'{balance:,.2f}'
        )
    current_balance_display.short_description = 'Current Balance'
    
    def save_model(self, request, obj, form, change):
        """Set created_by when creating new accounts."""
        if not change:  # Creating new account
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    """
    Admin interface for Journal Entries
    
    Complete interface for managing journal entries with
    inline editing of journal entry lines and proper workflow.
    """
    
    list_display = [
        'entry_number', 'transaction_date', 'description',
        'total_amount_display', 'status', 'is_balanced_display', 'created_by'
    ]
    list_filter = [
        'status', 'entry_type', 'currency', 'transaction_date',
        'created_by', 'posted_by'
    ]
    search_fields = ['entry_number', 'description', 'reference_number', 'memo']
    ordering = ['-transaction_date', '-entry_number']
    date_hierarchy = 'transaction_date'
    
    fieldsets = (
        ('Entry Identification', {
            'fields': ('entry_number', 'transaction_date', 'entry_type'),
            'description': 'Basic journal entry identification'
        }),
        ('Transaction Details', {
            'fields': ('description', 'reference_number', 'memo'),
            'description': 'Description and documentation of the business transaction'
        }),
        ('Currency and Exchange', {
            'fields': ('currency', 'exchange_rate'),
            'description': 'Currency settings for multi-currency transactions'
        }),
        ('Status and Workflow', {
            'fields': ('status', 'posting_date', 'posted_by'),
            'description': 'Entry status and posting information'
        }),
        ('Reversal Information', {
            'fields': ('reversed_entry', 'reversal_reason'),
            'description': 'Information about entry reversals',
            'classes': ['collapse']
        }),
        ('Audit Trail', {
            'fields': ('created_by', 'created_at', 'updated_at', 'posted_at'),
            'description': 'System audit and tracking information',
            'classes': ['collapse']
        }),
    )
    
    readonly_fields = [
        'created_by', 'posted_by', 'created_at', 'updated_at', 'posted_at', 'posting_date'
    ]
    
    inlines = [JournalEntryLineInline]
    
    def total_amount_display(self, obj):
        """Display total transaction amount."""
        total = obj.get_total_debits()
        return format_html(
            '<span style="font-weight: bold;">{} {}</span>',
            obj.currency,
            f'{total:,.2f}'
        )
    total_amount_display.short_description = 'Total Amount'
    
    def is_balanced_display(self, obj):
        """Show if entry is balanced with visual indicator."""
        if obj.is_balanced():
            return format_html(
                '<span style="color: #2e7d32; font-weight: bold;">✓ Balanced</span>'
            )
        else:
            return format_html(
                '<span style="color: #d32f2f; font-weight: bold;">✗ Unbalanced</span>'
            )
    is_balanced_display.short_description = 'Balance Status'
    
    def save_model(self, request, obj, form, change):
        """Set created_by when creating new entries."""
        if not change:  # Creating new entry
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
    
    actions = ['post_selected_entries']
    
    def post_selected_entries(self, request, queryset):
        """Admin action to post multiple journal entries."""
        posted_count = 0
        error_count = 0
        
        for entry in queryset:
            if entry.can_be_posted():
                try:
                    entry.post_entry(request.user)
                    posted_count += 1
                except Exception:
                    error_count += 1
            else:
                error_count += 1
        
        if posted_count > 0:
            self.message_user(
                request,
                f'Successfully posted {posted_count} journal entries.'
            )
        if error_count > 0:
            self.message_user(
                request,
                f'{error_count} entries could not be posted (check balance and status).',
                level='WARNING'
            )
    
    post_selected_entries.short_description = "Post selected journal entries"

@admin.register(JournalEntryLine)
class JournalEntryLineAdmin(admin.ModelAdmin):
    """
    Admin interface for Journal Entry Lines
    
    Detailed view of individual debits and credits for
    advanced accounting analysis and troubleshooting.
    """
    
    list_display = [
        'journal_entry', 'line_number', 'account', 'description',
        'debit_amount', 'credit_amount', 'customer', 'vendor',
        'sales_tax', 'sales_tax_amount'
    ]
    list_filter = [
        'journal_entry__status', 'account__account_type', 
        'journal_entry__transaction_date'
    ]
    search_fields = [
        'journal_entry__entry_number', 'account__account_number',
        'account__account_name', 'description', 'customer', 'vendor'
    ]
    ordering = ['journal_entry__transaction_date', 'journal_entry', 'line_number']
    date_hierarchy = 'journal_entry__transaction_date'
    
    fieldsets = (
        ('Journal Entry Information', {
            'fields': ('journal_entry', 'line_number'),
            'description': 'Parent journal entry and line sequence'
        }),
        ('Account and Amounts', {
            'fields': ('account', 'debit_amount', 'credit_amount'),
            'description': 'Account being affected and transaction amounts'
        }),
        ('Description and Documentation', {
            'fields': ('description', 'memo'),
            'description': 'Line-specific description and notes'
        }),
        ('Tracking Dimensions', {
            'fields': ('customer', 'vendor', 'project', 'department', 'location'),
            'description': 'Additional tracking for detailed reporting',
            'classes': ['collapse']
        }),
        ('Sales Tax Information', {
            'fields': ('sales_tax', 'taxable_amount', 'sales_tax_amount'),
            'description': 'Sales tax applied to this line item',
            'classes': ['collapse']
        }),
        ('Multi-Currency', {
            'fields': ('original_amount', 'original_currency'),
            'description': 'Original currency information if applicable',
            'classes': ['collapse']
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        """Make fields readonly if journal entry is posted."""
        readonly = ['created_at', 'updated_at']
        
        if obj and obj.journal_entry.status == 'POSTED':
            readonly.extend([
                'journal_entry', 'account', 'debit_amount', 'credit_amount',
                'description', 'memo', 'customer', 'vendor', 'project',
                'department', 'location'
            ])
        
        return readonly 