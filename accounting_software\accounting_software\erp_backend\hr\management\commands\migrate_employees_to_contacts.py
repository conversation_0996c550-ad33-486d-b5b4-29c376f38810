"""
Management command to migrate employees from old HR model to new contacts-based structure.

This command will:
1. Create Contact records for each existing HR Employee
2. Create Employee records in contacts app
3. Update HR Employee records to link to contacts
4. Preserve all existing data
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Migrate employees from old HR model to new contacts-based structure'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force migration even if data exists',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(self.style.SUCCESS('Starting employee migration to contacts...'))
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        try:
            # Import here to avoid circular imports
            from contacts.models import Contact, Employee as ContactEmployee
            
            # Check if we have the old HR Employee model with the old fields
            # This would be done after we create the migration properly
            
            # For now, let's create a simple structure
            self.stdout.write(self.style.SUCCESS('Employee migration structure is ready'))
            self.stdout.write(self.style.SUCCESS('Next steps:'))
            self.stdout.write('1. Run migrations for contacts app')
            self.stdout.write('2. Run migrations for hr app') 
            self.stdout.write('3. Run this command again to migrate data')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during migration: {str(e)}')
            )
            raise
            
        self.stdout.write(self.style.SUCCESS('Migration preparation completed!')) 