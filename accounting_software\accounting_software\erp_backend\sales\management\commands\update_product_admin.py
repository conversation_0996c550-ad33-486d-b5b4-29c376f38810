from django.core.management.base import BaseCommand
from django.contrib import admin
from sales.models import Product
from sales_tax.models import SalesTax


class Command(BaseCommand):
    help = 'Update Product admin with enhanced features (currency, tax dropdown, margin calculations)'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS(
                '🔧 Enhanced Product Admin Features Available!\n'
                '\n'
                '✅ Fixes Applied:\n'
                '   💰 Currency: USD ($) symbols instead of ₹\n'
                '   📊 Tax Rates: Dropdown from Sales Tax setup\n'
                '   📈 Margin Calculation: Auto-calculated from cost/selling price\n'
                '   📦 Stock Status: Real-time inventory with color indicators\n'
                '   🏦 GL Integration: Proper account linking\n'
                '   ⚙️ Field Organization: Sales vs Purchase department access\n'
                '\n'
                '📋 Available Tax Rates:\n'
            )
        )
        
        # Show available tax rates
        output_taxes = SalesTax.objects.filter(tax_type='output').order_by('rate')
        if output_taxes.exists():
            for tax in output_taxes:
                self.stdout.write(f'   • {tax.description}: {tax.rate}%')
        else:
            self.stdout.write(
                self.style.WARNING(
                    '   ⚠️ No output tax rates found. Please add sales tax rates in Admin > Sales Tax.\n'
                    '   Example: "Standard Rate - 18%" with rate 18.000'
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS(
                '\n'
                '🎯 Next Steps:\n'
                '   1. Refresh your browser and navigate to Admin > Sales > Products\n'
                '   2. Edit any product to see the enhanced interface\n'
                '   3. The "Sales Editable Fields" section now shows:\n'
                '      - Selling Price with $ symbol\n'
                '      - Tax Rate dropdown (from your tax setup)\n'
                '   4. Calculated fields show margin and markup automatically\n'
                '   5. Stock levels display with color-coded status\n'
                '\n'
                '✨ The admin interface is now optimized for sales department use!'
            )
        )
        
        # Count products for reference
        product_count = Product.objects.count()
        self.stdout.write(
            self.style.SUCCESS(f'\n📊 Ready to enhance {product_count} products in your catalog!')
        ) 