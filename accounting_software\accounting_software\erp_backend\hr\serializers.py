from rest_framework import serializers
from .models import Employee, Attendance, Department
from contacts.models import Contact, Employee as ContactEmployee
from contacts.serializers import EmployeeSerializer as ContactEmployeeSerializer

class EmployeeSerializer(serializers.ModelSerializer):
    # Include the full nested contact employee data
    contact_employee = ContactEmployeeSerializer(read_only=True)
    
    # Keep these for backward compatibility and convenience
    full_name = serializers.CharField(read_only=True)
    email = serializers.EmailField(read_only=True)
    phone = serializers.CharField(read_only=True)
    
    class Meta:
        model = Employee
        fields = [
            'contact_employee',
            'join_date',
            'employment_type', 
            'salary',
            'status',
            'created_at',
            'updated_at',
            # Convenience fields
            'full_name',
            'email', 
            'phone'
        ]
        read_only_fields = ('created_at', 'updated_at')

    def validate_salary(self, value):
        """
        Validate that salary is not negative
        """
        if value < 0:
            raise serializers.ValidationError("Salary cannot be negative")
        return value



class AttendanceSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.full_name', read_only=True)
    
    class Meta:
        model = Attendance
        fields = '__all__'
        read_only_fields = ('id',)

class DepartmentSerializer(serializers.ModelSerializer):
    employee_count = serializers.SerializerMethodField()
    active_employee_count = serializers.SerializerMethodField()
    manager_name = serializers.SerializerMethodField()
    parent_department_name = serializers.SerializerMethodField()

    class Meta:
        model = Department
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'employee_count', 'active_employee_count')

    def get_employee_count(self, obj):
        # Count employees in this department through contact_employee relationship
        return Employee.objects.filter(contact_employee__department=obj.name).count()

    def get_active_employee_count(self, obj):
        # Count active employees in this department
        return Employee.objects.filter(
            contact_employee__department=obj.name,
            status='Active'
        ).count()

    def get_manager_name(self, obj):
        try:
            if obj.manager:
                return obj.manager.full_name
        except (AttributeError, Employee.DoesNotExist, Exception):
            pass
        return None

    def get_parent_department_name(self, obj):
        if obj.parent_department:
            return obj.parent_department.name
        return None

    def validate_code(self, value):
        """
        Validate that department code is unique, excluding the current instance during updates.
        """
        qs = Department.objects.filter(code=value)
        if self.instance:
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise serializers.ValidationError("This department code is already in use")
        return value.upper()  # Convert code to uppercase 