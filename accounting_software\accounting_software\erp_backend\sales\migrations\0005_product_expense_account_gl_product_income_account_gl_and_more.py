# Generated by Django 4.2.21 on 2025-06-14 09:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('gl', '0006_add_sales_tax_to_journal_entry_line'),
        ('sales', '0004_update_productcategory_model'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='expense_account_gl',
            field=models.ForeignKey(blank=True, help_text='COGS/Expense account for purchasing this product', limit_choices_to={'account_type__type': 'EXPENSE'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='expense_products', to='gl.account'),
        ),
        migrations.AddField(
            model_name='product',
            name='income_account_gl',
            field=models.ForeignKey(blank=True, help_text='Revenue account for sales of this product/service', limit_choices_to={'account_type__type': 'REVENUE'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='sales_products', to='gl.account'),
        ),
        migrations.AddField(
            model_name='product',
            name='inventory_asset_account_gl',
            field=models.ForeignKey(blank=True, help_text='Asset account for inventory tracking', limit_choices_to={'account_type__type': 'ASSET', 'detail_type__code': 'INVENTORY'}, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='inventory_products', to='gl.account'),
        ),
        migrations.AlterField(
            model_name='productcategory',
            name='code',
            field=models.CharField(default='CAT', help_text='Short code for the category (e.g., FRT, VEG)', max_length=20, unique=True),
        ),
    ]
