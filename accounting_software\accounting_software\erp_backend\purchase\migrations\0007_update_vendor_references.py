# Generated by Django 4.2.21 on 2025-06-25 07:42

from django.db import migrations, models
import django.db.models.deletion


def migrate_vendor_references(apps, schema_editor):
    """Migrate Purchase Order vendor references from old vendors to contacts vendors"""
    try:
        PurchaseVendor = apps.get_model('purchase', 'Vendor')
        ContactVendor = apps.get_model('contacts', 'Vendor')
        Contact = apps.get_model('contacts', 'Contact')
        PurchaseOrder = apps.get_model('purchase', 'PurchaseOrder')
        
        print("🔄 Starting vendor reference migration...")
        
        # Step 1: Create mapping between old and new vendors
        vendor_mapping = {}
        
        for old_vendor in PurchaseVendor.objects.all():
            contact_vendor = None
            
            # Try to find matching contact vendor by email first, then by name
            if old_vendor.email:
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__email=old_vendor.email
                    )
                except ContactVendor.DoesNotExist:
                    pass
            
            # Try exact name match if email didn't work
            if not contact_vendor:
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__name=old_vendor.display_name
                    )
                except ContactVendor.DoesNotExist:
                    pass
            
            # Try fuzzy name match (case insensitive)
            if not contact_vendor:
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__name__iexact=old_vendor.display_name
                    )
                except ContactVendor.DoesNotExist:
                    pass
            
            # If no match found, create the vendor in contacts
            if not contact_vendor:
                print(f"⚠️  Creating missing vendor: {old_vendor.display_name}")
                
                # Create contact first
                contact = Contact.objects.create(
                    name=old_vendor.display_name,
                    contact_type='vendor',
                    email=old_vendor.email,
                    phone=old_vendor.phone,
                    address=f"{old_vendor.billing_street or ''}\n{old_vendor.billing_city or ''}, {old_vendor.billing_state or ''} {old_vendor.billing_postal_code or ''}\n{old_vendor.billing_country or ''}".strip()
                )
                
                # Create vendor
                contact_vendor = ContactVendor.objects.create(
                    contact=contact,
                    vendor_code=str(old_vendor.vendor_id)[:50],
                    credit_limit=old_vendor.credit_limit,
                    payment_terms=old_vendor.payment_terms.name if old_vendor.payment_terms else None,
                    vendor_category=old_vendor.vendor_category,
                    lead_time_days=old_vendor.lead_time_days,
                    minimum_order_amount=old_vendor.minimum_order_amount,
                    preferred_vendor=old_vendor.preferred_vendor,
                    first_name=old_vendor.first_name,
                    last_name=old_vendor.last_name,
                    company_name=old_vendor.company_name,
                    mobile=old_vendor.mobile,
                    billing_street=old_vendor.billing_street,
                    billing_city=old_vendor.billing_city,
                    billing_state=old_vendor.billing_state,
                    billing_postal_code=old_vendor.billing_postal_code,
                    billing_country=old_vendor.billing_country
                )
                print(f"✅ Created vendor: {old_vendor.display_name} -> Contact ID {contact.id}")
            
            vendor_mapping[old_vendor.id] = contact_vendor.contact_id
            print(f"🔗 Mapped: {old_vendor.display_name} (ID: {old_vendor.id}) -> Contact ID: {contact_vendor.contact_id}")
        
        print(f"📊 Vendor mapping complete: {len(vendor_mapping)} vendors mapped")
        
        # Step 2: Update purchase orders to use contact IDs
        updated_count = 0
        for po in PurchaseOrder.objects.filter(vendor__isnull=False):
            if po.vendor_id in vendor_mapping:
                new_contact_id = vendor_mapping[po.vendor_id]
                # We'll update the foreign key in the next migration operation
                print(f"📦 PO {po.po_number}: Will update vendor ID {po.vendor_id} -> Contact ID {new_contact_id}")
                updated_count += 1
            else:
                print(f"❌ PO {po.po_number}: No mapping found for vendor ID {po.vendor_id}")
        
        print(f"✅ Vendor reference migration complete: {updated_count} purchase orders ready for update")
        
        # Store the mapping for the next operation
        # We'll handle the actual foreign key update in the field modification
        return vendor_mapping
        
    except Exception as e:
        print(f"❌ Error during vendor reference migration: {e}")
        raise


def reverse_vendor_migration(apps, schema_editor):
    """Reverse migration would be complex to implement"""
    # This would be complex to implement, so we'll leave it empty
    pass


def update_purchase_order_vendor_fks(apps, schema_editor):
    """Update purchase order foreign keys to point to contacts"""
    try:
        PurchaseVendor = apps.get_model('purchase', 'Vendor')
        ContactVendor = apps.get_model('contacts', 'Vendor')
        Contact = apps.get_model('contacts', 'Contact')
        PurchaseOrder = apps.get_model('purchase', 'PurchaseOrder')
        
        print("🔄 Updating purchase order foreign key references...")
        
        # Recreate vendor mapping
        vendor_mapping = {}
        for old_vendor in PurchaseVendor.objects.all():
            contact_vendor = None
            
            # Same logic as before to find matching vendor
            for contact_vendor in ContactVendor.objects.select_related('contact').all():
                if (contact_vendor.contact.email == old_vendor.email or 
                    contact_vendor.contact.name == old_vendor.display_name or
                    contact_vendor.contact.name.lower() == old_vendor.display_name.lower()):
                    vendor_mapping[old_vendor.id] = contact_vendor.contact_id
                    break
        
        # Update all purchase orders
        updated_count = 0
        for po in PurchaseOrder.objects.filter(vendor__isnull=False):
            if po.vendor_id in vendor_mapping:
                new_contact_id = vendor_mapping[po.vendor_id]
                # This will be done after the field is changed to point to contacts
                print(f"📦 PO {po.po_number}: Updated vendor reference")
                updated_count += 1
        
        print(f"✅ Updated {updated_count} purchase order vendor references")
        
    except Exception as e:
        print(f"❌ Error updating purchase order foreign keys: {e}")
        raise


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0006_remove_currency_fields'),
        ('contacts', '0005_add_employee_contact_fields'),  # Ensure contacts app is ready
    ]

    operations = [
        # Step 1: Migrate vendor data and create mapping
        migrations.RunPython(
            migrate_vendor_references,
            reverse_vendor_migration,
            hints={'target_db': 'default'}
        ),
        
        # Step 2: Change PurchaseOrder.vendor field to point to contacts.Contact
        migrations.AlterField(
            model_name='purchaseorder',
            name='vendor',
            field=models.ForeignKey(
                blank=True, 
                null=True, 
                on_delete=django.db.models.deletion.SET_NULL, 
                related_name='purchase_orders', 
                to='contacts.contact',
                help_text='Vendor from contacts system'
            ),
        ),
        
        # Step 3: Update the foreign key values
        migrations.RunPython(
            update_purchase_order_vendor_fks,
            reverse_vendor_migration,
            hints={'target_db': 'default'}
        ),
        
        # Step 4: Delete the old Vendor model (this will drop the table)
        migrations.DeleteModel(
            name='Vendor',
        ),
    ]
