# Generated by Django 4.2.21 on 2025-06-06 12:46

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text="Unique code for the account type (e.g., 'ASSET', 'LIAB')", max_length=10, unique=True)),
                ('name', models.CharField(help_text='Display name of the account type', max_length=100)),
                ('type', models.CharField(choices=[('ASSET', 'Asset'), ('LIABILITY', 'Liability'), ('EQUITY', 'Equity'), ('REVENUE', 'Revenue'), ('EXPENSE', 'Expense')], help_text='The fundamental accounting category this type belongs to', max_length=20)),
                ('normal_balance', models.CharField(choices=[('DEBIT', 'Debit'), ('CREDIT', 'Credit')], help_text='Normal balance side for this account type (Debit or Credit)', max_length=6)),
                ('financial_statement', models.CharField(choices=[('BALANCE_SHEET', 'Balance Sheet'), ('INCOME_STATEMENT', 'Income Statement'), ('CASH_FLOW', 'Cash Flow Statement')], help_text='Which financial statement this account type appears on', max_length=20)),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order for displaying account types in reports and lists')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this account type is available for new accounts')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Account Type',
                'verbose_name_plural': 'Account Types',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(help_text='Unique journal entry number for tracking and reference', max_length=50, unique=True)),
                ('transaction_date', models.DateField(help_text='The actual date when the business transaction occurred')),
                ('posting_date', models.DateField(blank=True, help_text='Date when the entry was posted to the general ledger', null=True)),
                ('entry_type', models.CharField(choices=[('GENERAL', 'General Journal Entry'), ('ADJUSTING', 'Adjusting Entry'), ('CLOSING', 'Closing Entry'), ('REVERSING', 'Reversing Entry'), ('RECURRING', 'Recurring Entry')], default='GENERAL', help_text='Type of journal entry for reporting and processing', max_length=50)),
                ('description', models.CharField(help_text='Clear description of the business transaction', max_length=500)),
                ('reference_number', models.CharField(blank=True, help_text='External reference number (invoice, receipt, etc.)', max_length=100)),
                ('memo', models.TextField(blank=True, help_text='Additional notes or explanation for the entry')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending'), ('POSTED', 'Posted'), ('REVERSED', 'Reversed')], default='DRAFT', help_text='Current status of the journal entry', max_length=20)),
                ('currency', models.CharField(default='USD', help_text='Currency of the transaction (ISO 4217 code)', max_length=3)),
                ('exchange_rate', models.DecimalField(decimal_places=6, default=Decimal('1.000000'), help_text='Exchange rate to functional currency if applicable', max_digits=10)),
                ('reversal_reason', models.TextField(blank=True, help_text='Reason for reversing the original entry')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('posted_at', models.DateTimeField(blank=True, help_text='Timestamp when the entry was posted', null=True)),
                ('created_by', models.ForeignKey(help_text='User who created this journal entry', on_delete=django.db.models.deletion.PROTECT, related_name='created_journal_entries', to=settings.AUTH_USER_MODEL)),
                ('posted_by', models.ForeignKey(blank=True, help_text='User who posted this journal entry', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='posted_journal_entries', to=settings.AUTH_USER_MODEL)),
                ('reversed_entry', models.ForeignKey(blank=True, help_text='Original entry that this entry reverses', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reversing_entries', to='gl.journalentry')),
            ],
            options={
                'verbose_name': 'Journal Entry',
                'verbose_name_plural': 'Journal Entries',
                'ordering': ['-transaction_date', '-entry_number'],
            },
        ),
        migrations.CreateModel(
            name='DetailType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text="Unique code for the detail type (e.g., 'CASH_BANK', 'AR_TRADE')", max_length=20)),
                ('name', models.CharField(help_text='Display name of the detail type', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Detailed description of when to use this detail type')),
                ('requires_customer', models.BooleanField(default=False, help_text='Whether accounts of this type require customer tracking')),
                ('requires_vendor', models.BooleanField(default=False, help_text='Whether accounts of this type require vendor tracking')),
                ('requires_item', models.BooleanField(default=False, help_text='Whether accounts of this type require inventory item tracking')),
                ('is_tax_account', models.BooleanField(default=False, help_text='Whether this is used for tax-related transactions')),
                ('default_tax_code', models.CharField(blank=True, help_text='Default tax code for transactions using this detail type', max_length=20)),
                ('sort_order', models.PositiveIntegerField(default=0, help_text='Order for displaying detail types within account type')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this detail type is available for new accounts')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account_type', models.ForeignKey(help_text='The main account type this detail type belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='detail_types', to='gl.accounttype')),
            ],
            options={
                'verbose_name': 'Detail Type',
                'verbose_name_plural': 'Detail Types',
                'ordering': ['account_type', 'sort_order', 'name'],
                'unique_together': {('account_type', 'code')},
            },
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_number', models.CharField(help_text='Unique account number following company numbering scheme', max_length=20, unique=True)),
                ('account_name', models.CharField(help_text='Descriptive name of the account', max_length=200)),
                ('description', models.TextField(blank=True, help_text="Detailed description of the account's purpose and usage")),
                ('currency', models.CharField(default='USD', help_text='Default currency for this account (ISO 4217 code)', max_length=3)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this account is available for new transactions')),
                ('is_header_account', models.BooleanField(default=False, help_text='Whether this is a header account (summary only, no transactions)')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Opening balance when account was created', max_digits=15)),
                ('opening_balance_date', models.DateField(blank=True, help_text='Date of the opening balance', null=True)),
                ('tax_line', models.CharField(blank=True, help_text='Tax form line this account maps to for tax reporting', max_length=100)),
                ('bank_account_number', models.CharField(blank=True, help_text='Associated bank account number for cash/bank accounts', max_length=50)),
                ('bank_routing_number', models.CharField(blank=True, help_text='Bank routing number for cash/bank accounts', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('version', models.PositiveIntegerField(default=1, help_text='Version number for audit trail purposes')),
                ('account_type', models.ForeignKey(help_text='The main account type (Asset, Liability, etc.)', on_delete=django.db.models.deletion.PROTECT, related_name='accounts', to='gl.accounttype')),
                ('created_by', models.ForeignKey(help_text='User who created this account', on_delete=django.db.models.deletion.PROTECT, related_name='created_accounts', to=settings.AUTH_USER_MODEL)),
                ('detail_type', models.ForeignKey(help_text='Specific detail type for more granular classification', on_delete=django.db.models.deletion.PROTECT, related_name='accounts', to='gl.detailtype')),
                ('parent_account', models.ForeignKey(blank=True, help_text='Parent account for hierarchical Chart of Accounts structure', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_accounts', to='gl.account')),
            ],
            options={
                'verbose_name': 'Account',
                'verbose_name_plural': 'Chart of Accounts',
                'ordering': ['account_number'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntryLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Debit amount (leave 0.00 if this is a credit line)', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Credit amount (leave 0.00 if this is a debit line)', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('description', models.CharField(help_text='Description specific to this account posting', max_length=500)),
                ('memo', models.TextField(blank=True, help_text='Additional notes for this specific line')),
                ('customer', models.CharField(blank=True, help_text='Customer associated with this transaction line', max_length=100)),
                ('vendor', models.CharField(blank=True, help_text='Vendor associated with this transaction line', max_length=100)),
                ('project', models.CharField(blank=True, help_text='Project or job associated with this transaction line', max_length=100)),
                ('department', models.CharField(blank=True, help_text='Department associated with this transaction line', max_length=100)),
                ('location', models.CharField(blank=True, help_text='Location or branch associated with this transaction line', max_length=100)),
                ('line_number', models.PositiveIntegerField(help_text='Sequence number of this line within the journal entry')),
                ('original_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Original amount in transaction currency before conversion', max_digits=15, null=True)),
                ('original_currency', models.CharField(blank=True, help_text='Original currency code if different from functional currency', max_length=3)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(help_text='The Chart of Accounts account being debited or credited', on_delete=django.db.models.deletion.PROTECT, related_name='journal_lines', to='gl.account')),
                ('journal_entry', models.ForeignKey(help_text='The journal entry this line belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='journal_lines', to='gl.journalentry')),
            ],
            options={
                'verbose_name': 'Journal Entry Line',
                'verbose_name_plural': 'Journal Entry Lines',
                'ordering': ['journal_entry', 'line_number'],
                'unique_together': {('journal_entry', 'line_number')},
            },
        ),
    ]
