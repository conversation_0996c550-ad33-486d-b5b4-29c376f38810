# Generated by Django 4.2.21 on 2025-06-25 07:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contacts', '0005_add_employee_contact_fields'),
        ('sales', '0010_remove_old_customer_model'),
    ]

    operations = [
        # Update SalesOrder customer field
        migrations.AlterField(
            model_name='salesorder',
            name='customer',
            field=models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_orders', to='contacts.contact'),
        ),
        # Update DeliveryNote customer field
        migrations.AlterField(
            model_name='deliverynote',
            name='customer',
            field=models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivery_notes', to='contacts.contact'),
        ),
        # Update Invoice customer field
        migrations.AlterField(
            model_name='invoice',
            name='customer',
            field=models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='contacts.contact'),
        ),
        # Update Payment customer field
        migrations.AlterField(
            model_name='payment',
            name='customer',
            field=models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='contacts.contact'),
        ),
        # Update Estimate customer field
        migrations.AlterField(
            model_name='estimate',
            name='customer',
            field=models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='estimates', to='contacts.contact'),
        ),
    ] 