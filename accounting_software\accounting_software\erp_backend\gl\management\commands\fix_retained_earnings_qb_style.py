"""
Django management command to fix retained earnings accounts to match QuickBooks style:
1. <PERSON><PERSON> "Opening Retained Earnings" to "Retained Earnings" 
2. Remove "For the Period Retained Earnings" account
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from gl.models import Account


class Command(BaseCommand):
    help = 'Fix retained earnings accounts to match QuickBooks style exactly'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm the changes to retained earnings accounts',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will:\n'
                    '1. Rename "Opening Retained Earnings" to "Retained Earnings"\n'
                    '2. Remove "For the Period Retained Earnings" account\n'
                    'Add --confirm to proceed.'
                )
            )
            return

        try:
            with transaction.atomic():
                changes_made = []
                
                # 1. Rename "Opening Retained Earnings" to "Retained Earnings"
                opening_re = Account.objects.filter(account_number='36100').first()
                if opening_re:
                    old_name = opening_re.account_name
                    opening_re.account_name = 'Retained Earnings'
                    opening_re.save()
                    changes_made.append(f'✅ Renamed "{old_name}" to "Retained Earnings"')
                else:
                    changes_made.append('⚠️ Account 36100 (Opening Retained Earnings) not found')

                # 2. Remove "For the Period Retained Earnings" account
                period_re = Account.objects.filter(account_number='36200').first()
                if period_re:
                    # Check if account has any transactions
                    journal_lines_count = period_re.journal_lines.count()
                    
                    if journal_lines_count > 0:
                        changes_made.append(
                            f'❌ Cannot delete account 36200 "{period_re.account_name}". '
                            f'It has {journal_lines_count} journal line(s). Please move transactions first.'
                        )
                    else:
                        account_name = period_re.account_name
                        period_re.delete()
                        changes_made.append(f'✅ Removed account 36200 "{account_name}"')
                else:
                    changes_made.append('✅ Account 36200 (For the Period Retained Earnings) already removed')

                # 3. Show current retained earnings accounts
                self.stdout.write(
                    self.style.SUCCESS('\n📊 QuickBooks-Style Retained Earnings Setup Complete!\n')
                )
                
                for change in changes_made:
                    if change.startswith('✅'):
                        self.stdout.write(self.style.SUCCESS(change))
                    elif change.startswith('⚠️'):
                        self.stdout.write(self.style.WARNING(change))
                    elif change.startswith('❌'):
                        self.stdout.write(self.style.ERROR(change))

                # Show final account structure
                self.stdout.write('\n📋 Final Retained Earnings Structure (QuickBooks Style):')
                retained_accounts = Account.objects.filter(
                    account_name__icontains='retained'
                ).order_by('account_number')
                
                for account in retained_accounts:
                    self.stdout.write(f'   {account.account_number} - {account.account_name}')
                
                self.stdout.write('\n🎯 Plus virtual "Profit for the year" line item in Balance Sheet')
                self.stdout.write('   This matches QuickBooks presentation exactly!')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error fixing retained earnings accounts: {str(e)}')
            ) 