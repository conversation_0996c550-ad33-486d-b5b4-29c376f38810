from rest_framework import serializers
from .models import SalesTax

class SalesTaxSerializer(serializers.ModelSerializer):
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    class Meta:
        model = SalesTax
        fields = [
            'id', 'tax_type', 'description', 'rate', 'remarks',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_by_username', 'created_at', 'updated_at'] 