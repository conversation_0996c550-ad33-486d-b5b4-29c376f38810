import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Receipt as ReceiptIcon,
  ShoppingCart as ShoppingCartIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { inventoryService, StockTransaction } from '../services/inventory.service';

// Extended interface for transactions with running balance
interface StockTransactionWithBalance extends StockTransaction {
  running_balance: number;
}

const StockTransactionsPage: React.FC = () => {
  const [transactions, setTransactions] = useState<StockTransactionWithBalance[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('📊 Loading all stock transactions...');
      const allTransactions = await inventoryService.getAllStockTransactions();
      console.log(`📊 Loaded ${allTransactions.length} total stock transactions`);
      
      // Calculate running balance for each product
      const transactionsWithBalance = calculateRunningBalances(allTransactions);
      setTransactions(transactionsWithBalance);
    } catch (err) {
      console.error('Error loading transactions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load stock transactions');
      setTransactions([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  const calculateRunningBalances = (transactions: StockTransaction[]): StockTransactionWithBalance[] => {
    if (!transactions || transactions.length === 0) {
      return [];
    }

    try {
      // Group by product and calculate running balance
      const productBalances: { [key: number]: number } = {};
      
      // Sort transactions by date (oldest first) for proper balance calculation
      const sortedTransactions = [...transactions].sort((a, b) => {
        const dateA = new Date(a.txn_date).getTime();
        const dateB = new Date(b.txn_date).getTime();
        return dateA - dateB;
      });
      
      const result = sortedTransactions.map(txn => {
        const productId = Number(txn.product) || 0;
        
        if (!productBalances[productId]) {
          productBalances[productId] = 0;
        }
        
        // Add quantity to running balance (positive for receipts, negative for issues)
        const quantity = Number(txn.quantity) || 0;
        productBalances[productId] += quantity;
        
        return {
          ...txn,
          running_balance: productBalances[productId]
        };
      });

      // Reverse to show newest first in display
      return result.reverse();
    } catch (error) {
      console.error('Error calculating running balances:', error);
      return transactions.map(txn => ({
        ...txn,
        running_balance: 0
      }));
    }
  };

  const columns = [
    {
      field: 'txn_date',
      headerName: 'Date',
      flex: 1,
      renderCell: ({ row }: { row: any }) => {
        try {
          return formatDate(row.txn_date);
        } catch {
          return row.txn_date || '-';
        }
      },
    },
    {
      field: 'transaction_type',
      headerName: 'Type',
      flex: 1,
      renderCell: ({ row }: { row: any }) => (
        <Chip 
          label={row.transaction_type || 'Unknown'} 
          color={row.transaction_type === 'RECEIPT' ? 'success' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'reference_type',
      headerName: 'Reference',
      flex: 1.2,
      renderCell: ({ row }: { row: any }) => (
        <Box>
          <Typography variant="body2" fontWeight="medium">
            {row.reference_type === 'GRN' 
              ? `GRN-${String(row.reference_id || 0).padStart(6, '0')}` 
              : `${row.reference_type || 'REF'}-${row.reference_id || 0}`
            }
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {row.reference_type || 'Unknown'} #{row.reference_id || 0}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'product_name',
      headerName: 'Product',
      flex: 2,
      renderCell: ({ row }: { row: any }) => (
        <Typography variant="body2">
          {row.product_name || 'Unknown Product'}
        </Typography>
      ),
    },
    {
      field: 'quantity',
      headerName: 'Quantity',
      flex: 1,
      renderCell: ({ row }: { row: any }) => {
        const quantity = Number(row.quantity) || 0;
        return (
          <Typography color={quantity > 0 ? 'success.main' : 'error.main'}>
            {quantity > 0 ? '+' : ''}{quantity.toFixed(2)}
          </Typography>
        );
      },
    },
    {
      field: 'running_balance',
      headerName: 'Running Balance',
      flex: 1.2,
      renderCell: ({ row }: { row: any }) => {
        const balance = Number(row.running_balance) || 0;
        return (
          <Typography 
            variant="body2" 
            fontWeight="bold"
            color={balance > 0 ? 'success.main' : balance === 0 ? 'text.secondary' : 'error.main'}
          >
            {balance.toFixed(2)}
          </Typography>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            📊 Stock Transactions
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Complete audit trail of all inventory movements
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={loadTransactions}
        >
          Refresh
        </Button>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>🔍 Your GRN Impact:</strong> Look for "RECEIPT" transactions - 
          these show where your posted GRN items went into inventory!
        </Typography>
      </Alert>

      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>📊 Accounting Integration:</strong> Each RECEIPT transaction automatically creates 
          GL journal entries debiting the product's Inventory Asset Account and crediting Accounts Payable.
        </Typography>
      </Alert>

      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <DataTable
              rows={transactions || []}
              columns={columns}
              loading={loading}
              pageSize={25}
              pageSizeOptions={[10, 25, 50, 100]}
              getRowId={(row) => row.stock_txn_id || Math.random()}
            />
          )}
        </CardContent>
      </Card>
    </PageContainer>
  );
};

export default StockTransactionsPage; 