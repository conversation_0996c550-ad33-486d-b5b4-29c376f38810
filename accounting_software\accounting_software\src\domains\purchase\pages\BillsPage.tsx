import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Add as AddIcon,
  Receipt as ReceiptIcon,
  Visibility as ViewIcon,
  AccountBalance as JournalIcon,
  Search as SearchIcon,
} from '@mui/icons-material';

interface BillableItem {
  type: 'GRN' | 'GRN_RETURN';
  id: number;
  document_number: string;
  document_date: string;
  vendor_name: string;
  po_number: string;
  total_value: number;
  warehouse: string;
  items_count: number;
  items?: Array<{
    product_name: string;
    product_sku: string;
    quantity_received?: number;
    quantity_returned?: number;
    unit_cost: number;
    total_cost?: number;
    return_value?: number;
    tax_rate: number;
    tax_amount: number;
    condition?: string;
    return_reason?: string;
  }>;
}

const BillsPage: React.FC = () => {
  const navigate = useNavigate();
  const [billableItems, setBillableItems] = useState<BillableItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadBillableItems();
  }, []);

  const loadBillableItems = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/purchase/purchase-orders/billable_receipts/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to load billable items: ${response.statusText}`);
      }

      const data = await response.json();
      
      // The API returns an object with billable_items array
      const items = data.billable_items || [];
      setBillableItems(Array.isArray(items) ? items : []);
    } catch (err) {
      console.error('Error loading billable items:', err);
      setError(err instanceof Error ? err.message : 'Failed to load billable items');
      // Reset to empty array on error
      setBillableItems([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateJournalVoucher = (item: BillableItem) => {
    // Navigate to journal voucher creation with detailed item information
    const params = new URLSearchParams({
      type: item.type,
      id: item.id.toString(),
      document_number: item.document_number,
      po_number: item.po_number,
      vendor_name: item.vendor_name,
      total_value: item.total_value.toString(),
      // Pass detailed items data as JSON string to create comprehensive description
      items_data: JSON.stringify(item),
    });
    
    navigate(`/dashboard/purchases/create-journal-voucher?${params.toString()}`);
  };

  // Ensure billableItems is always an array before filtering
  const filteredItems = (Array.isArray(billableItems) ? billableItems : []).filter(item =>
    item.document_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vendor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.po_number.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    return type === 'GRN' ? 'success' : 'warning';
  };

  const getTypeIcon = (type: string) => {
    return type === 'GRN' ? <ReceiptIcon /> : <ReceiptIcon />;
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Bills & Journal Vouchers
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/dashboard/purchases/create-journal-voucher')}
          sx={{ 
            background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
            }
          }}
        >
          Create Journal Voucher
        </Button>
      </Box>

      {/* Search */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            placeholder="Search by document number, vendor, or PO number..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
          />
        </CardContent>
      </Card>

      {/* Content */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
          <CircularProgress />
          <Typography variant="h6" sx={{ ml: 2 }}>Loading billable items...</Typography>
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
          <Button onClick={loadBillableItems} sx={{ ml: 2 }}>
            Retry
          </Button>
        </Alert>
      ) : (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Billable Items ({filteredItems.length})
            </Typography>
            
            {filteredItems.length === 0 ? (
              <Alert severity="info">
                No billable items found. Items will appear here when GRNs are ready to be billed.
              </Alert>
            ) : (
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Type</TableCell>
                      <TableCell>Document #</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Vendor</TableCell>
                      <TableCell>PO Number</TableCell>
                      <TableCell>Value</TableCell>
                      <TableCell>Items</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredItems.map((item) => (
                      <TableRow key={`${item.type}-${item.id}`} hover>
                        <TableCell>
                          <Chip
                            icon={getTypeIcon(item.type)}
                            label={item.type}
                            color={getTypeColor(item.type)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>
                          {item.document_number}
                        </TableCell>
                        <TableCell>
                          {new Date(item.document_date).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{item.vendor_name}</TableCell>
                        <TableCell>{item.po_number}</TableCell>
                        <TableCell sx={{ fontWeight: 600 }}>
                          ${item.total_value.toFixed(2)}
                        </TableCell>
                        <TableCell>{item.items_count}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Tooltip title="Create Journal Voucher">
                              <IconButton
                                size="small"
                                onClick={() => handleCreateJournalVoucher(item)}
                                sx={{ color: 'primary.main' }}
                              >
                                <JournalIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default BillsPage; 