import React from 'react';
import { Box, Card, CardContent, Typography, Avatar, Chip } from '@mui/material';
import { TrendingUp, TrendingDown } from '@mui/icons-material';

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  trend?: number;
  trendLabel?: string;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  trend,
  trendLabel,
  color = 'primary',
}) => {
  const isTrendPositive = trend !== undefined && trend >= 0;
  const trendIcon = isTrendPositive ? <TrendingUp fontSize="small" /> : <TrendingDown fontSize="small" />;
  const trendColor = isTrendPositive ? 'success' : 'error';
  const trendText = trend !== undefined ? `${isTrendPositive ? '+' : ''}${trend}%` : '';
  const trendDisplayLabel = trendLabel || (isTrendPositive ? 'Increase' : 'Decrease');

  return (
    <Card 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
        borderRadius: 2,
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.12)',
        }
      }}
    >
      <CardContent sx={{ flexGrow: 1, p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="subtitle2" color="text.secondary" fontWeight="medium">
            {title}
          </Typography>
          {icon && (
            <Avatar 
              sx={{ 
                bgcolor: `${color}.lighter`, 
                color: `${color}.main`,
                width: 40, 
                height: 40 
              }}
            >
              {icon}
            </Avatar>
          )}
        </Box>
        
        <Typography variant="h4" component="div" fontWeight="bold" sx={{ mb: 1 }}>
          {value}
        </Typography>
        
        {trend !== undefined && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip
              icon={trendIcon}
              label={trendText}
              size="small"
              color={trendColor}
              variant="outlined"
              sx={{ mr: 1, height: 24 }}
            />
            <Typography variant="caption" color="text.secondary">
              {trendDisplayLabel} from last month
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default StatCard;
