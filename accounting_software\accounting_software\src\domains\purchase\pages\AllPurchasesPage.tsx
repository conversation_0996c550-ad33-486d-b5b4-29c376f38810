import React from 'react';
import { 
  Container, Typography, Paper, Box, Grid, 
  Card, CardContent, IconButton, Tooltip 
} from '@mui/material';
import { 
  TrendingUp, AttachMoney, Receipt, 
  ShowChart, Download as DownloadIcon 
} from '@mui/icons-material';
import StatCard from '../../../shared/components/StatCard';
import RecentPurchases from '../components/RecentPurchases';
import PurchasesChart from '../components/PurchasesChart';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';

const AllPurchasesPage: React.FC = () => {
  // Mock data for analytics
  const purchaseStats = {
    totalPurchases: 187000,
    monthlyGrowth: 12.5,
    averageOrderValue: 1350,
    pendingOrders: 8,
  };

  const monthlyData = [
    { month: 'Jan', purchases: 15200 },
    { month: 'Feb', purchases: 16800 },
    { month: 'Mar', purchases: 16200 },
    { month: 'Apr', purchases: 18500 },
    { month: 'May', purchases: 19700 },
    { month: 'Jun', purchases: 21300 },
  ];

  return (
    <PageContainer title="Purchases Overview">
      <Grid container spacing={3}>
        {/* Stats Cards */}
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Total Purchases"
            value={`$${purchaseStats.totalPurchases.toLocaleString()}`}
            icon={<AttachMoney color="primary" />}
            change={purchaseStats.monthlyGrowth}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Average Order Value"
            value={`$${purchaseStats.averageOrderValue.toLocaleString()}`}
            icon={<TrendingUp color="primary" />}
            change={3.2}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Pending Orders"
            value={purchaseStats.pendingOrders.toString()}
            icon={<Receipt color="primary" />}
            change={-2}
            changeText="vs last month"
            changeDirection="down"
          />
        </Grid>
        
        <Grid item xs={12} md={6} lg={3}>
          <StatCard
            title="Vendor Satisfaction"
            value="92%"
            icon={<ShowChart color="primary" />}
            change={1.5}
            changeText="vs last month"
            changeDirection="up"
          />
        </Grid>
        
        {/* Charts */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Purchase Trends</Typography>
              <Tooltip title="Download Report">
                <IconButton size="small">
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
            <PurchasesChart data={monthlyData} />
          </Paper>
        </Grid>
        
        {/* Recent Purchases */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6">Recent Purchases</Typography>
              <Tooltip title="View All">
                <IconButton size="small" onClick={() => window.location.href = '/dashboard/purchases/bills'}>
                  <ShowChart />
                </IconButton>
              </Tooltip>
            </Box>
            <RecentPurchases />
          </Paper>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default AllPurchasesPage; 