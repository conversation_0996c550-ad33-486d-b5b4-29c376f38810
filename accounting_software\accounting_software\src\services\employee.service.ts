import api from './api';
import {
  Employee,
  EmployeeFormData,
  Department,
  DepartmentFormData,
  EmployeesApiResponse,
  DepartmentsApiResponse,
} from '../shared/types/employee.types';

const EMPLOYEES_BASE_URL = '/hr/employees';
const DEPARTMENTS_BASE_URL = '/hr/departments';
const CONTACTS_BASE_URL = '/contacts';

export class EmployeeService {
  // Employees CRUD
  static async getEmployees(params?: Record<string, any>): Promise<EmployeesApiResponse> {
    const response = await api.get(`${EMPLOYEES_BASE_URL}/`, { params });
    return response.data;
  }

  static async getEmployee(id: number): Promise<Employee> {
    const response = await api.get(`${EMPLOYEES_BASE_URL}/${id}/`);
    return response.data;
  }

  static async createEmployee(data: EmployeeFormData): Promise<Employee> {
    // Create the contact employee record (this will create both contact and employee)
    const contactEmployeeData = {
      contact: {
        contact_type: 'employee',
        name: data.name,
        email: data.email,
        phone: data.phone,
        address: data.address,
        website: data.website,
        tax_id: data.tax_id,
        notes: data.notes,
        is_active: data.is_active
      },
      first_name: data.first_name,
      last_name: data.last_name,
      date_of_birth: data.date_of_birth || null,
      gender: data.gender,
      mobile: data.mobile,
      employee_id: data.employee_id,
      position: data.position,
      department: data.department,
      street_address: data.street_address,
      city: data.city,
      state: data.state,
      postal_code: data.postal_code,
      country: data.country,
      emergency_contact_name: data.emergency_contact_name,
      emergency_contact_phone: data.emergency_contact_phone,
      emergency_contact_relationship: data.emergency_contact_relationship
    };
    
    const contactEmployeeResponse = await api.post(`${CONTACTS_BASE_URL}/employees/`, contactEmployeeData);
    const contactEmployee = contactEmployeeResponse.data;
    
    // Create the HR employee record
    const hrEmployeeData = {
      contact_employee: contactEmployee.contact.id, // Use the contact ID from the contact employee response
      join_date: data.join_date,
      employment_type: data.employment_type,
      salary: data.salary,
      status: data.status
    };
    
    const response = await api.post(`${EMPLOYEES_BASE_URL}/`, hrEmployeeData);
    return response.data;
  }

  static async updateEmployee(id: number, data: Partial<EmployeeFormData>): Promise<Employee> {
    const employee = await this.getEmployee(id);
    
    // Update contact if contact fields are provided
    if (data.name || data.email || data.phone || data.address || data.website || data.tax_id || data.notes || data.is_active !== undefined) {
      const contactData = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        address: data.address,
        website: data.website,
        tax_id: data.tax_id,
        notes: data.notes,
        is_active: data.is_active
      };
      
      await api.patch(`${CONTACTS_BASE_URL}/contacts/${employee.contact_employee.contact.id}/`, contactData);
    }
    
    // Update contact employee if relevant fields are provided
    if (data.first_name || data.last_name || data.date_of_birth || data.gender || data.mobile || 
        data.employee_id || data.position || data.department || data.street_address || 
        data.city || data.state || data.postal_code || data.country || 
        data.emergency_contact_name || data.emergency_contact_phone || data.emergency_contact_relationship) {
      
      const contactEmployeeData = {
        first_name: data.first_name,
        last_name: data.last_name,
        date_of_birth: data.date_of_birth || null,
        gender: data.gender,
        mobile: data.mobile,
        employee_id: data.employee_id,
        position: data.position,
        department: data.department,
        street_address: data.street_address,
        city: data.city,
        state: data.state,
        postal_code: data.postal_code,
        country: data.country,
        emergency_contact_name: data.emergency_contact_name,
        emergency_contact_phone: data.emergency_contact_phone,
        emergency_contact_relationship: data.emergency_contact_relationship
      };
      
      await api.patch(`${CONTACTS_BASE_URL}/employees/${employee.contact_employee.contact.id}/`, contactEmployeeData);
    }
    
    // Update HR employee fields
    const hrEmployeeData = {
      join_date: data.join_date,
      employment_type: data.employment_type,
      salary: data.salary,
      status: data.status
    };
    
    const response = await api.patch(`${EMPLOYEES_BASE_URL}/${id}/`, hrEmployeeData);
    return response.data;
  }

  static async deleteEmployee(id: number): Promise<void> {
    await api.delete(`${EMPLOYEES_BASE_URL}/${id}/`);
  }

  // Departments CRUD
  static async getDepartments(params?: Record<string, any>): Promise<DepartmentsApiResponse> {
    const response = await api.get(`${DEPARTMENTS_BASE_URL}/`, { params });
    return response.data;
  }

  static async getDepartment(id: number): Promise<Department> {
    const response = await api.get(`${DEPARTMENTS_BASE_URL}/${id}/`);
    return response.data;
  }

  static async createDepartment(data: DepartmentFormData): Promise<Department> {
    const response = await api.post(`${DEPARTMENTS_BASE_URL}/`, data);
    return response.data;
  }

  static async updateDepartment(id: number, data: Partial<DepartmentFormData>): Promise<Department> {
    const response = await api.patch(`${DEPARTMENTS_BASE_URL}/${id}/`, data);
    return response.data;
  }

  static async deleteDepartment(id: number): Promise<void> {
    await api.delete(`${DEPARTMENTS_BASE_URL}/${id}/`);
  }

  // Utility Methods
  static async searchEmployees(query: string): Promise<Employee[]> {
    const response = await this.getEmployees({ search: query });
    return response.results;
  }

  static async getActiveEmployees(): Promise<Employee[]> {
    const response = await this.getEmployees({ status: 'Active' });
    return response.results;
  }

  static async getEmployeesByDepartment(department: string): Promise<Employee[]> {
    const response = await this.getEmployees({ department });
    return response.results;
  }

  static async getActiveDepartments(): Promise<Department[]> {
    const response = await this.getDepartments({ is_active: true });
    return response.results;
  }
}

export default EmployeeService; 