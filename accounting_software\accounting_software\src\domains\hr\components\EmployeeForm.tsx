import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography,
  Autocomplete,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  ContactMail as ContactIcon,
  Home as HomeIcon,
  ContactPhone as EmergencyIcon,
} from '@mui/icons-material';
import {
  Employee,
  EmployeeFormData,
  EMPLOYMENT_TYPES,
  EMPLOYMENT_STATUSES,
  GENDER_OPTIONS,
} from '../../../shared/types/employee.types';
import EmployeeService from '../../../services/employee.service';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';

interface EmployeeFormProps {
  employee?: Employee | null;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const validationSchema = Yup.object({
  first_name: Yup.string().required('First name is required'),
  last_name: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  employee_id: Yup.string().required('Employee ID is required'),
  position: Yup.string().required('Position is required'),
  department: Yup.string().required('Department is required'),
  join_date: Yup.date().required('Join date is required'),
  employment_type: Yup.string().required('Employment type is required'),
  salary: Yup.number().required('Salary is required').min(0),
  status: Yup.string().required('Status is required'),
});

const EmployeeForm: React.FC<EmployeeFormProps> = ({ employee, onSuccess, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedPanels, setExpandedPanels] = useState<string[]>(['personal', 'employment']);

  const handlePanelChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedPanels(prev => 
      isExpanded 
        ? [...prev, panel]
        : prev.filter(p => p !== panel)
    );
  };

  const initialValues: EmployeeFormData = employee ? {
    name: employee.contact_employee.contact.name,
    email: employee.contact_employee.contact.email,
    phone: employee.contact_employee.contact.phone || '',
    address: employee.contact_employee.contact.address || '',
    website: employee.contact_employee.contact.website || '',
    tax_id: employee.contact_employee.contact.tax_id || '',
    notes: employee.contact_employee.contact.notes || '',
    is_active: employee.contact_employee.contact.is_active,
    first_name: employee.contact_employee.first_name || '',
    last_name: employee.contact_employee.last_name || '',
    date_of_birth: employee.contact_employee.date_of_birth || '',
    gender: employee.contact_employee.gender || '',
    mobile: employee.contact_employee.mobile || '',
    employee_id: employee.contact_employee.employee_id || '',
    position: employee.contact_employee.position || '',
    department: employee.contact_employee.department || '',
    street_address: employee.contact_employee.street_address || '',
    city: employee.contact_employee.city || '',
    state: employee.contact_employee.state || '',
    postal_code: employee.contact_employee.postal_code || '',
    country: employee.contact_employee.country || '',
    emergency_contact_name: employee.contact_employee.emergency_contact_name || '',
    emergency_contact_phone: employee.contact_employee.emergency_contact_phone || '',
    emergency_contact_relationship: employee.contact_employee.emergency_contact_relationship || '',
    join_date: employee.join_date,
    employment_type: employee.employment_type,
    salary: employee.salary,
    status: employee.status,
  } : {
    name: '',
    email: '',
    phone: '',
    address: '',
    website: '',
    tax_id: '',
    notes: '',
    is_active: true,
    first_name: '',
    last_name: '',
    date_of_birth: '',
    gender: '',
    mobile: '',
    employee_id: '',
    position: '',
    department: '',
    street_address: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    emergency_contact_relationship: '',
    join_date: new Date().toISOString().split('T')[0],
    employment_type: 'full-time' as const,
    salary: 0,
    status: 'Active' as const,
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting }) => {
      try {
        setLoading(true);
        setError(null);
        
        const fullName = `${values.first_name} ${values.last_name}`.trim();
        const submitData = { ...values, name: fullName };
        
        if (employee) {
          await EmployeeService.updateEmployee(employee.contact_employee.contact.id, submitData);
        } else {
          await EmployeeService.createEmployee(submitData);
        }
        
        if (onSuccess) {
          onSuccess();
        }
        
      } catch (err: any) {
        console.error('Error saving employee:', err);
        setError(err.response?.data?.detail || err.message || 'Failed to save employee');
      } finally {
        setLoading(false);
        setSubmitting(false);
      }
    },
  });

  // Auto-update full name when first/last name changes
  React.useEffect(() => {
    const fullName = `${formik.values.first_name} ${formik.values.last_name}`.trim();
    if (fullName && fullName !== formik.values.name) {
      formik.setFieldValue('name', fullName);
    }
  }, [formik.values.first_name, formik.values.last_name]);

  return (
    <Card>
      <CardHeader 
        title={employee ? "Edit Employee" : "Add New Employee"} 
        subheader={employee ? "Update employee information" : "Create a new employee record"}
      />
      <Divider />
      
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {/* Personal Information */}
          <Accordion 
            expanded={expandedPanels.includes('personal')} 
            onChange={handlePanelChange('personal')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <PersonIcon color="primary" />
                <Typography variant="h6">Personal Information</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    {...formik.getFieldProps('first_name')}
                    error={formik.touched.first_name && Boolean(formik.errors.first_name)}
                    helperText={formik.touched.first_name && formik.errors.first_name}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    {...formik.getFieldProps('last_name')}
                    error={formik.touched.last_name && Boolean(formik.errors.last_name)}
                    helperText={formik.touched.last_name && formik.errors.last_name}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    {...formik.getFieldProps('email')}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone"
                    {...formik.getFieldProps('phone')}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Employment Details */}
          <Accordion 
            expanded={expandedPanels.includes('employment')} 
            onChange={handlePanelChange('employment')}
            sx={{ mt: 1 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <WorkIcon color="primary" />
                <Typography variant="h6">Employment Details</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Employee ID"
                    {...formik.getFieldProps('employee_id')}
                    error={formik.touched.employee_id && Boolean(formik.errors.employee_id)}
                    helperText={formik.touched.employee_id && formik.errors.employee_id}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Position"
                    {...formik.getFieldProps('position')}
                    error={formik.touched.position && Boolean(formik.errors.position)}
                    helperText={formik.touched.position && formik.errors.position}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Department"
                    {...formik.getFieldProps('department')}
                    error={formik.touched.department && Boolean(formik.errors.department)}
                    helperText={formik.touched.department && formik.errors.department}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Join Date"
                    type="date"
                    InputLabelProps={{ shrink: true }}
                    {...formik.getFieldProps('join_date')}
                    error={formik.touched.join_date && Boolean(formik.errors.join_date)}
                    helperText={formik.touched.join_date && formik.errors.join_date}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={EMPLOYMENT_TYPES}
                    getOptionLabel={(option) => option.label}
                    value={EMPLOYMENT_TYPES.find(t => t.value === formik.values.employment_type) || null}
                    onChange={(_, newValue) => {
                      formik.setFieldValue('employment_type', newValue ? newValue.value : 'full-time');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Employment Type"
                        error={formik.touched.employment_type && Boolean(formik.errors.employment_type)}
                        helperText={formik.touched.employment_type && formik.errors.employment_type}
                        fullWidth
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={EMPLOYMENT_STATUSES}
                    getOptionLabel={(option) => option.label}
                    value={EMPLOYMENT_STATUSES.find(s => s.value === formik.values.status) || null}
                    onChange={(_, newValue) => {
                      formik.setFieldValue('status', newValue ? newValue.value : 'Active');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Status"
                        error={formik.touched.status && Boolean(formik.errors.status)}
                        helperText={formik.touched.status && formik.errors.status}
                        fullWidth
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormattedCurrencyInput
                    fullWidth
                    label="Salary"
                    name="salary"
                    value={formik.values.salary}
                    onChange={(e) => formik.setFieldValue('salary', parseFloat(e.target.value) || 0)}
                    currencySymbol="$"
                    error={formik.touched.salary && Boolean(formik.errors.salary)}
                    helperText={formik.touched.salary && formik.errors.salary}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Form Actions */}
          <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            {onCancel && (
              <Button 
                variant="outlined" 
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
            )}
            <Button 
              type="submit" 
              variant="contained" 
              color="primary" 
              disabled={formik.isSubmitting || loading}
              startIcon={loading ? <CircularProgress size={16} /> : null}
            >
              {loading ? 'Saving...' : (employee ? 'Update Employee' : 'Create Employee')}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
};

export default EmployeeForm; 