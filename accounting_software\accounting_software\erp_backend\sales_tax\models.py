from django.db import models
from django.conf import settings

class SalesTax(models.Model):
    TAX_TYPE_CHOICES = [
        ('input', 'Input Tax'),
        ('output', 'Output Tax'),
    ]
    tax_type = models.CharField(max_length=10, choices=TAX_TYPE_CHOICES)
    description = models.CharField(max_length=255)
    rate = models.DecimalField(max_digits=6, decimal_places=3)
    remarks = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Sales Tax'
        verbose_name_plural = 'Sales Taxes'
        ordering = ['tax_type', 'description']

    def __str__(self):
        return f"{self.get_tax_type_display()} - {self.description} ({self.rate}%)"
