# Generated by Django 4.2.21 on 2025-06-29 04:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_alter_goodsreturnnote_vendor'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0011_update_customer_references'),
    ]

    operations = [
        migrations.AddField(
            model_name='invoice',
            name='inventory_posted',
            field=models.BooleanField(default=False, help_text='Whether inventory has been reduced'),
        ),
        migrations.AddField(
            model_name='invoice',
            name='inventory_posted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices_inventory_posted', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='invoice',
            name='inventory_posted_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='invoice',
            name='warehouse',
            field=models.ForeignKey(blank=True, help_text='Warehouse from which goods will be shipped', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='invoices', to='inventory.warehouse'),
        ),
    ]
