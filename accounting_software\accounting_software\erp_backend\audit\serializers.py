"""
Audit Trail API Serializers

Serializers for audit trail data access and reporting.
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import AuditLog, UserSession

User = get_user_model()

class AuditLogSerializer(serializers.ModelSerializer):
    """
    Serializer for Audit Log entries.
    
    Provides read-only access to audit trail data for:
    - Compliance reporting
    - Security monitoring
    - User activity tracking
    """
    
    user_display = serializers.SerializerMethodField()
    action_display = serializers.SerializerMethodField()
    risk_level_display = serializers.SerializerMethodField()
    changes_summary = serializers.SerializerMethodField()
    old_values_dict = serializers.SerializerMethodField()
    new_values_dict = serializers.SerializerMethodField()
    metadata_dict = serializers.SerializerMethodField()
    
    class Meta:
        model = AuditLog
        fields = [
            'audit_id', 'timestamp', 'user', 'user_display', 'action', 'action_display',
            'description', 'content_type', 'object_id', 'old_values', 'new_values',
            'old_values_dict', 'new_values_dict', 'ip_address', 'user_agent',
            'session_key', 'module', 'risk_level', 'risk_level_display',
            'metadata', 'metadata_dict', 'is_financial', 'requires_approval',
            'is_approved', 'approved_by', 'approved_at', 'changes_summary'
        ]
        read_only_fields = '__all__'  # All fields are read-only
    
    def get_user_display(self, obj):
        """Get user display name."""
        if obj.user:
            return f"{obj.user.username} ({obj.user.get_full_name() or obj.user.email})"
        return "System/Anonymous"
    
    def get_action_display(self, obj):
        """Get human-readable action name."""
        return obj.get_action_display()
    
    def get_risk_level_display(self, obj):
        """Get human-readable risk level."""
        return obj.get_risk_level_display()
    
    def get_changes_summary(self, obj):
        """Get summary of changes."""
        return obj.get_changes_summary()
    
    def get_old_values_dict(self, obj):
        """Get old values as dictionary."""
        return obj.get_old_values_dict()
    
    def get_new_values_dict(self, obj):
        """Get new values as dictionary."""
        return obj.get_new_values_dict()
    
    def get_metadata_dict(self, obj):
        """Get metadata as dictionary."""
        return obj.get_metadata_dict()

class UserSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for User Session tracking.
    """
    
    user_display = serializers.SerializerMethodField()
    session_duration = serializers.SerializerMethodField()
    logout_reason_display = serializers.SerializerMethodField()
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user', 'user_display', 'session_key', 'login_time',
            'logout_time', 'last_activity', 'ip_address', 'user_agent',
            'is_active', 'logout_reason', 'logout_reason_display',
            'session_duration'
        ]
        read_only_fields = '__all__'
    
    def get_user_display(self, obj):
        """Get user display name."""
        return f"{obj.user.username} ({obj.user.get_full_name() or obj.user.email})"
    
    def get_session_duration(self, obj):
        """Get session duration in seconds."""
        duration = obj.get_session_duration()
        return int(duration.total_seconds())
    
    def get_logout_reason_display(self, obj):
        """Get human-readable logout reason."""
        return obj.get_logout_reason_display() if obj.logout_reason else None

class AuditSummarySerializer(serializers.Serializer):
    """
    Serializer for audit trail summary statistics.
    """
    
    total_logs = serializers.IntegerField()
    today_logs = serializers.IntegerField()
    week_logs = serializers.IntegerField()
    financial_logs = serializers.IntegerField()
    high_risk_logs = serializers.IntegerField()
    active_sessions = serializers.IntegerField()
    failed_logins_today = serializers.IntegerField()
    
    # User activity breakdown
    user_activity = serializers.DictField()
    module_activity = serializers.DictField()
    action_breakdown = serializers.DictField()
    risk_level_breakdown = serializers.DictField()

class ComplianceReportSerializer(serializers.Serializer):
    """
    Serializer for compliance report generation.
    """
    
    report_type = serializers.ChoiceField(choices=[
        ('USER_ACTIVITY', 'User Activity Report'),
        ('FINANCIAL_CHANGES', 'Financial Data Changes'),
        ('SECURITY_EVENTS', 'Security Events'),
        ('DATA_ACCESS', 'Data Access Report'),
        ('FAILED_LOGINS', 'Failed Login Attempts'),
    ])
    
    date_from = serializers.DateTimeField()
    date_to = serializers.DateTimeField()
    
    # Optional filters
    user_id = serializers.IntegerField(required=False)
    module = serializers.CharField(required=False)
    risk_level = serializers.CharField(required=False)
    is_financial = serializers.BooleanField(required=False)
    
    def validate(self, data):
        """Validate report parameters."""
        if data['date_from'] >= data['date_to']:
            raise serializers.ValidationError("date_from must be before date_to")
        
        return data 