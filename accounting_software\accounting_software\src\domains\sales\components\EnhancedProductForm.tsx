import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Card,
  CardContent,
  Chip,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Autocomplete,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  LocalOffer as PriceIcon,
  Storage as StorageIcon,
  Assignment as BatchIcon,
  Warning as WarningIcon,
  Add as AddIcon,
} from '@mui/icons-material';

// Enhanced interfaces for retail management
interface EnhancedProductFormData {
  name: string;
  description: string;
  shortDescription?: string;
  divisionId: string;
  categoryId: string;
  brandId?: string;
  productType: 'simple' | 'variable' | 'bundle';
  status: 'active' | 'inactive' | 'discontinued' | 'out_of_stock';
  isActive: boolean;
  requiresRefrigeration: boolean;
  requiresFreezing: boolean;
  shelfLife?: number;
  storageInstructions?: string;
  handlingInstructions?: string;
  hsnCode?: string;
  prescriptionRequired?: boolean;
  ageRestricted?: boolean;
  minimumAge?: number;
  images: string[];
  tags: string[];
  searchKeywords: string[];
  marketingDescription?: string;
  alternativeSupplierIds: string[];
}

interface EnhancedProductFormProps {
  onSubmit: (data: EnhancedProductFormData) => void;
  onCancel: () => void;
  initialData?: Partial<EnhancedProductFormData>;
  loading?: boolean;
}

const EnhancedProductForm: React.FC<EnhancedProductFormProps> = ({
  onSubmit,
  onCancel,
  initialData,
  loading = false,
}) => {
  // Form state
  const [formData, setFormData] = useState<EnhancedProductFormData>({
    name: '',
    description: '',
    shortDescription: '',
    divisionId: '',
    categoryId: '',
    brandId: '',
    productType: 'simple',
    status: 'active',
    isActive: true,
    requiresRefrigeration: false,
    requiresFreezing: false,
    images: [],
    tags: [],
    searchKeywords: [],
    alternativeSupplierIds: [],
    ...initialData,
  });

  // Expanded accordion state
  const [expanded, setExpanded] = useState<string>('basic');

  // Mock data - replace with actual API calls
  const divisions = [
    { id: 'div1', name: 'Fresh Produce', type: 'perishable', businessType: 'grocery' },
    { id: 'div2', name: 'Packaged Foods', type: 'non_perishable', businessType: 'grocery' },
    { id: 'div3', name: 'Pharmaceuticals', type: 'controlled_substance', businessType: 'pharmacy' },
  ];

  const categories = [
    { id: 'cat1', name: 'Fruits', divisionId: 'div1' },
    { id: 'cat2', name: 'Vegetables', divisionId: 'div1' },
    { id: 'cat3', name: 'Canned Goods', divisionId: 'div2' },
    { id: 'cat4', name: 'Prescription Drugs', divisionId: 'div3' },
  ];

  const brands = [
    { id: 'brand1', name: 'Local Farms' },
    { id: 'brand2', name: 'Premium Choice' },
    { id: 'brand3', name: 'Organic Valley' },
  ];

  // Handle form field changes
  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle accordion expansion
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : '');
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Get filtered categories based on selected division
  const filteredCategories = categories.filter(cat => cat.divisionId === formData.divisionId);

  // Get selected division details
  const selectedDivision = divisions.find(div => div.id === formData.divisionId);

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        ✨ Enhanced Product Creation
      </Typography>

      {/* Basic Information */}
      <Accordion expanded={expanded === 'basic'} onChange={handleAccordionChange('basic')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <InventoryIcon color="primary" />
            <Typography variant="h6">Basic Information</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="Product Name"
                value={formData.name}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                required
                placeholder="e.g., Organic Bananas - 1kg Pack"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>Product Type</InputLabel>
                <Select
                  value={formData.productType}
                  label="Product Type"
                  onChange={(e) => handleFieldChange('productType', e.target.value)}
                >
                  <MenuItem value="simple">Simple Product</MenuItem>
                  <MenuItem value="variable">Variable Product (with variants)</MenuItem>
                  <MenuItem value="bundle">Bundle/Kit</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Short Description"
                value={formData.shortDescription}
                onChange={(e) => handleFieldChange('shortDescription', e.target.value)}
                placeholder="Brief product description for listings"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Detailed Description"
                value={formData.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Comprehensive product description including features, benefits, and specifications"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Product Hierarchy */}
      <Accordion expanded={expanded === 'hierarchy'} onChange={handleAccordionChange('hierarchy')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <CategoryIcon color="primary" />
            <Typography variant="h6">Product Hierarchy</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required>
                <InputLabel>Division</InputLabel>
                <Select
                  value={formData.divisionId}
                  label="Division"
                  onChange={(e) => handleFieldChange('divisionId', e.target.value)}
                >
                  {divisions.map((division) => (
                    <MenuItem key={division.id} value={division.id}>
                      <Box>
                        <Typography variant="body1">{division.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {division.type} - {division.businessType}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth required disabled={!formData.divisionId}>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.categoryId}
                  label="Category"
                  onChange={(e) => handleFieldChange('categoryId', e.target.value)}
                >
                  {filteredCategories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Brand</InputLabel>
                <Select
                  value={formData.brandId || ''}
                  label="Brand"
                  onChange={(e) => handleFieldChange('brandId', e.target.value)}
                >
                  <MenuItem value="">No Brand</MenuItem>
                  {brands.map((brand) => (
                    <MenuItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            {selectedDivision && (
              <Grid item xs={12}>
                <Alert severity="info">
                  <Typography variant="body2">
                    <strong>Division Features:</strong> This division is classified as{' '}
                    <Chip size="small" label={selectedDivision.type} sx={{ mx: 0.5 }} /> for{' '}
                    <Chip size="small" label={selectedDivision.businessType} sx={{ mx: 0.5 }} /> business.
                    {selectedDivision.type === 'perishable' && ' Products will require expiry date tracking and batch management.'}
                    {selectedDivision.type === 'controlled_substance' && ' Products will require special regulatory compliance.'}
                  </Typography>
                </Alert>
              </Grid>
            )}
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Storage & Handling */}
      <Accordion expanded={expanded === 'storage'} onChange={handleAccordionChange('storage')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <StorageIcon color="primary" />
            <Typography variant="h6">Storage & Handling</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.requiresRefrigeration}
                    onChange={(e) => handleFieldChange('requiresRefrigeration', e.target.checked)}
                  />
                }
                label="Requires Refrigeration"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.requiresFreezing}
                    onChange={(e) => handleFieldChange('requiresFreezing', e.target.checked)}
                  />
                }
                label="Requires Freezing"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Shelf Life (days)"
                type="number"
                value={formData.shelfLife || ''}
                onChange={(e) => handleFieldChange('shelfLife', parseInt(e.target.value) || undefined)}
                placeholder="e.g., 7 for fresh produce"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Storage Instructions"
                value={formData.storageInstructions || ''}
                onChange={(e) => handleFieldChange('storageInstructions', e.target.value)}
                placeholder="e.g., Store in cool, dry place. Keep refrigerated at 2-8°C"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Regulatory & Compliance */}
      {selectedDivision?.type === 'controlled_substance' && (
        <Accordion expanded={expanded === 'regulatory'} onChange={handleAccordionChange('regulatory')}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={1}>
              <WarningIcon color="error" />
              <Typography variant="h6">Regulatory & Compliance</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="HSN Code"
                  value={formData.hsnCode || ''}
                  onChange={(e) => handleFieldChange('hsnCode', e.target.value)}
                  placeholder="e.g., 30049099"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.prescriptionRequired || false}
                      onChange={(e) => handleFieldChange('prescriptionRequired', e.target.checked)}
                    />
                  }
                  label="Prescription Required"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.ageRestricted || false}
                      onChange={(e) => handleFieldChange('ageRestricted', e.target.checked)}
                    />
                  }
                  label="Age Restricted"
                />
              </Grid>
              {formData.ageRestricted && (
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Minimum Age"
                    type="number"
                    value={formData.minimumAge || ''}
                    onChange={(e) => handleFieldChange('minimumAge', parseInt(e.target.value) || undefined)}
                    placeholder="e.g., 18"
                  />
                </Grid>
              )}
            </Grid>
          </AccordionDetails>
        </Accordion>
      )}

      {/* SEO & Marketing */}
      <Accordion expanded={expanded === 'marketing'} onChange={handleAccordionChange('marketing')}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box display="flex" alignItems="center" gap={1}>
            <PriceIcon color="primary" />
            <Typography variant="h6">SEO & Marketing</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                freeSolo
                options={[]}
                value={formData.tags}
                onChange={(event, newValue) => handleFieldChange('tags', newValue)}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Product Tags"
                    placeholder="Add tags (press Enter to add)"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                multiple
                freeSolo
                options={[]}
                value={formData.searchKeywords}
                onChange={(event, newValue) => handleFieldChange('searchKeywords', newValue)}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip variant="outlined" label={option} {...getTagProps({ index })} key={index} />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Search Keywords"
                    placeholder="Add search keywords (press Enter to add)"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Marketing Description"
                value={formData.marketingDescription || ''}
                onChange={(e) => handleFieldChange('marketingDescription', e.target.value)}
                placeholder="Marketing-focused description for promotional materials"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={loading || !formData.name || !formData.divisionId || !formData.categoryId}
          startIcon={<AddIcon />}
        >
          {loading ? 'Creating...' : 'Create Product'}
        </Button>
      </Box>

      {/* Feature Preview */}
      <Card sx={{ mt: 3, bgcolor: 'background.default' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom color="primary">
            🚀 Coming Soon: Advanced Features
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <BatchIcon color="action" />
                <Typography variant="body2">Batch Tracking</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <InventoryIcon color="action" />
                <Typography variant="body2">Multi-UOM Support</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <PriceIcon color="action" />
                <Typography variant="body2">Dynamic Pricing</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <WarningIcon color="action" />
                <Typography variant="body2">Expiry Alerts</Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default EnhancedProductForm;
