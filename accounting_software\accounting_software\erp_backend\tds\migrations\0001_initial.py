# Generated by Django 4.2.21 on 2025-06-07 19:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TDS',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tds_type', models.CharField(choices=[('payment_deduction', 'Tax Deducted at Source while Making Payment'), ('receipt_deduction', 'Tax Deducted at Source while Receiving Payment')], max_length=20)),
                ('description', models.CharField(max_length=255)),
                ('rate', models.DecimalField(decimal_places=3, max_digits=6)),
                ('section', models.CharField(blank=True, help_text='Income Tax Section (e.g., 194A, 194C, etc.)', max_length=50)),
                ('threshold_limit', models.DecimalField(blank=True, decimal_places=2, help_text='Minimum amount above which TDS applies', max_digits=12, null=True)),
                ('remarks', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'TDS',
                'verbose_name_plural': 'TDS',
                'ordering': ['tds_type', 'description'],
            },
        ),
    ]
