# Generated by Django 4.2.21 on 2025-06-14 11:18

import datetime
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('purchase', '0001_initial'),
        ('sales', '0005_product_expense_account_gl_product_income_account_gl_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('warehouse_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, unique=True)),
                ('location', models.CharField(help_text='Physical address/location', max_length=500)),
                ('description', models.TextField(blank=True)),
                ('warehouse_code', models.CharField(help_text='Short code for warehouse', max_length=20, unique=True)),
                ('warehouse_type', models.Char<PERSON><PERSON>(choices=[('MAIN', 'Main Warehouse'), ('BRANCH', 'Branch Warehouse'), ('RETAIL', 'Retail Store'), ('TRANSIT', 'Transit Warehouse'), ('VIRTUAL', 'Virtual Warehouse')], default='MAIN', max_length=20)),
                ('manager_name', models.CharField(blank=True, max_length=200)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('is_active', models.BooleanField(default=True)),
                ('allow_negative_stock', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='warehouses_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'warehouses',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UnitsOfMeasure',
            fields=[
                ('uom_id', models.AutoField(primary_key=True, serialize=False)),
                ('unit_name', models.CharField(help_text='Full name of the unit', max_length=100, unique=True)),
                ('abbreviation', models.CharField(help_text='Short abbreviation', max_length=10, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of the unit')),
                ('unit_type', models.CharField(choices=[('WEIGHT', 'Weight'), ('VOLUME', 'Volume'), ('LENGTH', 'Length'), ('AREA', 'Area'), ('COUNT', 'Count/Pieces'), ('TIME', 'Time'), ('OTHER', 'Other')], default='COUNT', max_length=20)),
                ('conversion_factor', models.DecimalField(decimal_places=6, default=Decimal('1.000000'), help_text='Factor to convert to base unit', max_digits=15)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('base_unit', models.ForeignKey(blank=True, help_text='Base unit for conversion (e.g., grams for weight)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.unitsofmeasure')),
            ],
            options={
                'verbose_name': 'Unit of Measure',
                'verbose_name_plural': 'Units of Measure',
                'db_table': 'units_of_measure',
                'ordering': ['unit_name'],
            },
        ),
        migrations.CreateModel(
            name='GoodsReceiptNote',
            fields=[
                ('grn_id', models.AutoField(primary_key=True, serialize=False)),
                ('grn_number', models.CharField(help_text='GRN reference number', max_length=50, unique=True)),
                ('receipt_date', models.DateField(default=datetime.date.today)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('RECEIVED', 'Received'), ('POSTED', 'Posted to Inventory'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Receipt notes and comments')),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='grns_posted', to=settings.AUTH_USER_MODEL)),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='goods_receipts', to='purchase.purchaseorder')),
                ('received_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='goods_received', to=settings.AUTH_USER_MODEL)),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='goods_receipts', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'grns',
                'ordering': ['-receipt_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VendorInvoice',
            fields=[
                ('vendor_invoice_id', models.AutoField(primary_key=True, serialize=False)),
                ('invoice_number', models.CharField(help_text="Vendor's invoice number", max_length=100)),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('status', models.CharField(choices=[('RECEIVED', 'Received'), ('VERIFIED', 'Verified'), ('APPROVED', 'Approved'), ('PAID', 'Paid'), ('DISPUTED', 'Disputed'), ('CANCELLED', 'Cancelled')], default='RECEIVED', max_length=20)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('description', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vendor_invoices_created', to=settings.AUTH_USER_MODEL)),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vendor_invoices', to='purchase.purchaseorder')),
            ],
            options={
                'db_table': 'vendor_invoices',
                'ordering': ['-invoice_date', '-created_at'],
                'unique_together': {('purchase_order', 'invoice_number')},
            },
        ),
        migrations.CreateModel(
            name='StockTransaction',
            fields=[
                ('stock_txn_id', models.AutoField(primary_key=True, serialize=False)),
                ('transaction_type', models.CharField(choices=[('RECEIPT', 'Goods Receipt'), ('ISSUE', 'Stock Issue'), ('TRANSFER', 'Warehouse Transfer'), ('ADJUSTMENT', 'Stock Adjustment'), ('SALE', 'Sale Transaction'), ('RETURN', 'Return Transaction'), ('DAMAGE', 'Damage/Loss'), ('OPENING', 'Opening Balance')], help_text='Type of stock transaction', max_length=20)),
                ('reference_type', models.CharField(choices=[('GRN', 'Goods Receipt Note'), ('SO', 'Sales Order'), ('PO', 'Purchase Order'), ('ADJ', 'Stock Adjustment'), ('TRF', 'Transfer'), ('RET', 'Return'), ('OPEN', 'Opening Balance')], help_text='Type of source document', max_length=20)),
                ('reference_id', models.PositiveIntegerField(help_text='ID of the source document')),
                ('quantity', models.DecimalField(decimal_places=4, help_text='Quantity moved (positive for in, negative for out)', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Cost per unit for this transaction', max_digits=15)),
                ('total_cost', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total cost of this transaction', max_digits=15)),
                ('txn_date', models.DateTimeField(help_text='Date and time of transaction')),
                ('description', models.TextField(blank=True, help_text='Transaction description')),
                ('batch_number', models.CharField(blank=True, help_text='Batch/lot number', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='stock_transactions_created', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='sales.product')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_transactions', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'stock_transactions',
                'ordering': ['-txn_date', '-created_at'],
                'indexes': [models.Index(fields=['product', 'warehouse'], name='stock_trans_product_53e12a_idx'), models.Index(fields=['reference_type', 'reference_id'], name='stock_trans_referen_883dfe_idx'), models.Index(fields=['txn_date'], name='stock_trans_txn_dat_87c716_idx')],
            },
        ),
        migrations.CreateModel(
            name='Inventory',
            fields=[
                ('inventory_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity_on_hand', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Current stock quantity', max_digits=15)),
                ('quantity_reserved', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Quantity reserved for sales orders', max_digits=15)),
                ('quantity_on_order', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Quantity on purchase orders', max_digits=15)),
                ('reorder_point', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Minimum stock level before reorder', max_digits=15)),
                ('reorder_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Standard reorder quantity', max_digits=15)),
                ('average_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Weighted average cost per unit', max_digits=15)),
                ('last_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Last purchase cost per unit', max_digits=15)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('last_transaction_date', models.DateTimeField(blank=True, null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_levels', to='sales.product')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_items', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'inventory',
                'ordering': ['product__name', 'warehouse__name'],
                'unique_together': {('product', 'warehouse')},
            },
        ),
        migrations.CreateModel(
            name='GoodsReceiptNoteItem',
            fields=[
                ('grn_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity_ordered', models.DecimalField(decimal_places=4, help_text='Quantity originally ordered', max_digits=15)),
                ('quantity_received', models.DecimalField(decimal_places=4, help_text='Quantity actually received', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, help_text='Cost per unit as per purchase order', max_digits=15)),
                ('total_cost', models.DecimalField(decimal_places=2, help_text='Total cost for this line item', max_digits=15)),
                ('condition', models.CharField(choices=[('GOOD', 'Good Condition'), ('DAMAGED', 'Damaged'), ('EXPIRED', 'Expired'), ('REJECTED', 'Rejected')], default='GOOD', max_length=20)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('grn', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.goodsreceiptnote')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grn_items', to='sales.product')),
            ],
            options={
                'db_table': 'grn_items',
                'ordering': ['line_order'],
                'unique_together': {('grn', 'product')},
            },
        ),
    ]
