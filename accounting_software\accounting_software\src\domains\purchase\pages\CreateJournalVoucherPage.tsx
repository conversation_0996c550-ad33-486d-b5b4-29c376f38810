import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Autocomplete,
  MenuItem,
} from '@mui/material';
import {
  Print as PrintIcon,
  ContentCopy as CopyIcon,
  Repeat as RepeatIcon,
  AttachFile as AttachFileIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { 
  FormHeaderFooter,
  CurrencyDisplay 
} from '../../../shared/components';
import { useCurrencyInfo } from '../../../domains/gl/hooks/useCurrencyInfo';

interface Account {
  id: number;
  account_number: string;
  account_name: string;
  account_type: string;
}

interface JournalLine {
  id: string;
  account_id: number | null;
  account_name: string;
  description: string;
  debit_amount: number;
  credit_amount: number;
}

const CreateJournalVoucherPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Currency information from company setup
  const { currencyInfo, loading: currencyLoading, error: currencyError } = useCurrencyInfo();
  
  // Form data
  const [entryNumber, setEntryNumber] = useState(() => 
    `JV-${Math.random().toString(36).substring(2, 9).toUpperCase()}`
  );
  const [transactionDate, setTransactionDate] = useState(dayjs());
  const [description, setDescription] = useState('');
  const [referenceNumber, setReferenceNumber] = useState('');
  
  // Journal lines
  const [journalLines, setJournalLines] = useState<JournalLine[]>([
    { id: '1', account_id: null, account_name: '', description: '', debit_amount: 0, credit_amount: 0 },
    { id: '2', account_id: null, account_name: '', description: '', debit_amount: 0, credit_amount: 0 },
  ]);
  
  // State
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [accountsLoading, setAccountsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Calculate totals
  const totalDebits = journalLines.reduce((sum, line) => sum + line.debit_amount, 0);
  const totalCredits = journalLines.reduce((sum, line) => sum + line.credit_amount, 0);
  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;

  // Header/Footer handlers
  const handlePrint = () => {
    window.print();
  };

  const handleCopy = () => {
    // Copy current form data logic can be added here
    console.log('Copy functionality to be implemented');
  };

  const handleMakeRecurring = () => {
    // Make recurring logic can be added here
    console.log('Make recurring functionality to be implemented');
  };

  const handleCancel = () => {
    navigate('/dashboard/purchases/bills');
  };

  const handleSaveAndClose = async () => {
    await handleSubmit();
    // Navigation will happen in handleSubmit on success
  };

  const checkForExistingJournalVoucher = async () => {
    const sourceType = searchParams.get('type');
    const sourceId = searchParams.get('id');
    const documentNumber = searchParams.get('document_number');
    
    if (sourceType && documentNumber) {
      try {
        const token = localStorage.getItem('token');
        
        // Map frontend types to backend valid choices for duplicate check
        const mapSourceDocumentType = (type: string | null): string => {
          if (!type) return '';
          // Map GRN_RETURN to GRN since backend only accepts GRN for goods receipt related documents
          if (type === 'GRN_RETURN') return 'GRN';
          return type;
        };
        
        const mappedSourceType = mapSourceDocumentType(sourceType);
        
        // Use document number (e.g., RET-000023) instead of ID for duplicate check
        const response = await fetch(`http://localhost:8000/api/gl/journal-entries/?source_document_type=${mappedSourceType}&source_document_id=${documentNumber}`, {
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          const existingEntries = data.results || data;
          
          if (existingEntries.length > 0) {
            const existingEntry = existingEntries[0];
            const documentType = sourceType === 'GRN_RETURN' ? 'GRN Return' : sourceType;
            setError(
              `A journal voucher already exists for ${documentType} ${documentNumber}. ` +
              `Journal Entry: ${existingEntry.entry_number} (Status: ${existingEntry.status}) ` +
              `created on ${new Date(existingEntry.created_at).toLocaleDateString()}. ` +
              `Each ${documentType} can only be converted to a journal voucher once.`
            );
            return;
          }
        }
      } catch (err) {
        console.error('Error checking for existing journal voucher:', err);
      }
    }
  };

  useEffect(() => {
    loadAccounts();
    checkForExistingJournalVoucher();
    
    // Pre-fill from URL params if available
    const type = searchParams.get('type');
    const documentNumber = searchParams.get('document_number');
    const vendorName = searchParams.get('vendor_name');
    const totalValue = searchParams.get('total_value');
    const poNumber = searchParams.get('po_number');
    const itemsDataStr = searchParams.get('items_data');
    
    if (type && documentNumber) {
      // Create concise description that stays under 500 characters
      let shortDescription = '';
      
      if (itemsDataStr) {
        try {
          const itemsData = JSON.parse(itemsDataStr);
          if (itemsData.items && itemsData.items.length > 0) {
            const totalTax = itemsData.items.reduce((sum: number, item: any) => sum + (item.tax_amount || 0), 0);
            const totalAmount = parseFloat(totalValue || '0');
            const netAmount = totalAmount - totalTax;
            const itemCount = itemsData.items.length;
            
            // Create short, concise description under 500 chars
            const docType = type === 'GRN_RETURN' ? 'Return Credit Note' : 'Purchase Bill';
            shortDescription = `${docType} - ${documentNumber} | PO: ${poNumber} | Vendor: ${vendorName} | ${itemCount} items | Net: $${netAmount.toFixed(2)}, Tax: $${totalTax.toFixed(2)}, Total: $${totalAmount.toFixed(2)}`;
          }
        } catch (error) {
          console.error('Error parsing items data:', error);
          // Fall back to simple description
        }
      }
      
      // Fallback to simple description if no items data or error
      if (!shortDescription) {
        const docType = type === 'GRN_RETURN' ? 'Return Credit Note' : 'Purchase Bill';
        shortDescription = `${docType} - ${documentNumber} | Vendor: ${vendorName || 'Unknown'} | Total: $${totalValue || '0.00'}`;
      }
      
      // Ensure description is under 500 characters
      if (shortDescription.length > 500) {
        shortDescription = shortDescription.substring(0, 497) + '...';
      }
      
      setDescription(shortDescription);
      setReferenceNumber(documentNumber);
      
      if (vendorName && totalValue) {
        let inventoryAmount = parseFloat(totalValue) * 0.85; // Default fallback
        let taxAmount = parseFloat(totalValue) * 0.15; // Default fallback
        
        // Calculate actual amounts from items data if available
        if (itemsDataStr) {
          try {
            const itemsData = JSON.parse(itemsDataStr);
            if (itemsData.items && itemsData.items.length > 0) {
              taxAmount = itemsData.items.reduce((sum: number, item: any) => sum + (item.tax_amount || 0), 0);
              inventoryAmount = parseFloat(totalValue) - taxAmount;
            }
          } catch (error) {
            console.error('Error calculating amounts from items data:', error);
          }
        }
        
        // Pre-populate journal lines - different for GRN vs GRN Return
        const isGrnReturn = type === 'GRN_RETURN';
        const journalEntries = [];
        
        if (isGrnReturn) {
          // GRN Return - Reverse entries (Credit Note)
          // 1. Debit: Accounts Payable (reduce liability)
          journalEntries.push({
            id: '1',
            account_id: null,
            account_name: '',
            description: `Accounts Payable - ${vendorName} (Return Credit)`,
            debit_amount: parseFloat(totalValue),
            credit_amount: 0,
          });
          
          // 2. Credit: Inventory Asset (reduce inventory)
          journalEntries.push({
            id: '2',
            account_id: null,
            account_name: '',
            description: `Inventory Return to ${vendorName}`,
            debit_amount: 0,
            credit_amount: inventoryAmount,
          });
          
          // 3. Credit: Input Tax (if applicable) - reduce input tax credit
          if (taxAmount > 0) {
            journalEntries.push({
              id: '3',
              account_id: null,
              account_name: '',
              description: `Input Tax Reversal on ${documentNumber}`,
              debit_amount: 0,
              credit_amount: taxAmount,
            });
          }
        } else {
          // Regular GRN - Standard entries (Purchase Bill)
          // 1. Debit: Inventory Asset
          journalEntries.push({
            id: '1',
            account_id: null,
            account_name: '',
            description: `Inventory from ${documentNumber}`,
            debit_amount: inventoryAmount,
            credit_amount: 0,
          });
          
          // 2. Debit: Input Tax (if applicable)
          if (taxAmount > 0) {
            journalEntries.push({
              id: '2',
              account_id: null,
              account_name: '',
              description: `Input Tax on ${documentNumber}`,
              debit_amount: taxAmount,
              credit_amount: 0,
            });
          }
          
          // 3. Credit: Accounts Payable
          journalEntries.push({
            id: String(journalEntries.length + 1),
            account_id: null,
            account_name: '',
            description: `Accounts Payable - ${vendorName}`,
            debit_amount: 0,
            credit_amount: parseFloat(totalValue),
          });
        }
        
        setJournalLines(journalEntries);
      }
    }
  }, [searchParams]);

  const loadAccounts = async () => {
    try {
      setAccountsLoading(true);
      const token = localStorage.getItem('token');
      
      // Use the fast COA service
      const response = await fetch('http://localhost:8000/api/gl/chart-of-accounts-fast/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const activeAccounts = data.accounts?.filter((account: any) => 
          account.is_active && !account.is_header_account
        ) || [];
        setAccounts(activeAccounts);
      } else {
        throw new Error('Failed to load accounts');
      }
    } catch (err) {
      console.error('Error loading accounts:', err);
      setError(err instanceof Error ? err.message : 'Failed to load accounts');
    } finally {
      setAccountsLoading(false);
    }
  };

  const handleAccountChange = (lineId: string, account: Account | null) => {
    setJournalLines(prev => prev.map(line => 
      line.id === lineId 
        ? { ...line, account_id: account?.id || null, account_name: account ? `${account.account_number} - ${account.account_name}` : '' }
        : line
    ));
  };

  const handleLineChange = (lineId: string, field: keyof JournalLine, value: any) => {
    setJournalLines(prev => prev.map(line => {
      if (line.id === lineId) {
        const updatedLine = { ...line, [field]: value };
        
        // Clear opposite amount when entering debit/credit
        if (field === 'debit_amount' && value > 0) {
          updatedLine.credit_amount = 0;
        } else if (field === 'credit_amount' && value > 0) {
          updatedLine.debit_amount = 0;
        }
        
        return updatedLine;
      }
      return line;
    }));
  };

  const addJournalLine = () => {
    const newId = (journalLines.length + 1).toString();
    setJournalLines(prev => [...prev, {
      id: newId,
      account_id: null,
      account_name: '',
      description: '',
      debit_amount: 0,
      credit_amount: 0,
    }]);
  };

  const removeJournalLine = (lineId: string) => {
    if (journalLines.length > 2) {
      setJournalLines(prev => prev.filter(line => line.id !== lineId));
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Validation
      if (!description.trim()) {
        throw new Error('Description is required');
      }

      if (!isBalanced) {
        throw new Error(`Total debits (${totalDebits.toFixed(2)}) must equal total credits (${totalCredits.toFixed(2)})`);
      }

      if (journalLines.some(line => !line.account_id)) {
        const missingAccountLines = journalLines
          .map((line, index) => !line.account_id ? `Line ${index + 1}` : null)
          .filter(Boolean)
          .join(', ');
        throw new Error(`Please select GL accounts for the following journal lines before saving: ${missingAccountLines}. Look for the dropdown menus in the "Account" column.`);
      }

      if (journalLines.some(line => line.debit_amount === 0 && line.credit_amount === 0)) {
        throw new Error('All journal lines must have either a debit or credit amount');
      }

      // Get source document info from URL params
      const sourceType = searchParams.get('type');
      const sourceId = searchParams.get('id');
      const sourceDocumentNumber = searchParams.get('document_number');

      // Map frontend types to backend valid choices
      const mapSourceDocumentType = (type: string | null): string => {
        if (!type) return '';
        
        // Map GRN_RETURN to GRN since backend only accepts GRN for goods receipt related documents
        if (type === 'GRN_RETURN') return 'GRN';
        
        // Return the type as-is if it's already a valid backend choice
        return type;
      };

      // Prepare data for API
      const journalData = {
        entry_number: entryNumber,
        transaction_date: transactionDate.format('YYYY-MM-DD'),
        description: description,
        reference_number: referenceNumber || '',
        status: 'DRAFT',
        entry_type: 'GENERAL',
        currency: 'USD',
        exchange_rate: 1.0,
        // Add source document tracking to prevent duplicates
        source_document_type: mapSourceDocumentType(sourceType),
        source_document_id: sourceDocumentNumber || '', // Use document number, not ID
        source_document_reference: sourceDocumentNumber || '',
        journal_lines: journalLines.map((line, index) => ({
          account: line.account_id,
          description: line.description,
          debit_amount: line.debit_amount,
          credit_amount: line.credit_amount,
          line_number: index + 1,
        })),
      };

      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/gl/journal-entries/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(journalData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Journal entry creation failed:', errorData);
        
        // Extract detailed error message
        let errorMessage = 'Failed to create journal entry';
        if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.non_field_errors) {
          errorMessage = errorData.non_field_errors.join(', ');
        } else if (typeof errorData === 'object') {
          // Handle field-specific errors
          const fieldErrors = Object.entries(errorData)
            .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
            .join('; ');
          if (fieldErrors) {
            errorMessage = fieldErrors;
          }
        }
        
        throw new Error(errorMessage);
      }

      setSuccess(searchParams.get('type') === 'GRN_RETURN' ? 'Credit Note created successfully!' : 'Journal Voucher created successfully!');
      
      // Redirect after success
      setTimeout(() => {
        navigate('/dashboard/purchases/bills');
      }, 2000);

    } catch (err) {
      console.error('Error creating journal voucher:', err);
      setError(err instanceof Error ? err.message : 'Failed to create journal voucher');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state if currency info is loading
  if (currencyLoading) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
          <Typography variant="h6">Loading currency information...</Typography>
        </Box>
      </LocalizationProvider>
    );
  }

  // Show error state if currency info failed to load
  if (currencyError) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            Failed to load currency information: {currencyError}
          </Alert>
        </Box>
      </LocalizationProvider>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}
        
        {/* Show helpful message if coming from GRN */}
        {searchParams.get('type') && searchParams.get('document_number') && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>📋 {searchParams.get('type') === 'GRN_RETURN' ? 'GRN Return Credit Note' : 'GRN Journal Voucher'} Pre-filled</strong><br/>
              The journal lines have been pre-populated with detailed information from {searchParams.get('document_number')}. 
              Please <strong>select the appropriate GL accounts</strong> for each line before saving.<br/>
              <br/>
              {searchParams.get('type') === 'GRN_RETURN' ? (
                <>
                  <strong>💡 GRN Return Accounts (Credit Note):</strong><br/>
                  • <strong>Accounts Payable:</strong> Debit to reduce vendor liability<br/>
                  • <strong>Inventory Asset:</strong> Credit to reduce inventory<br/>
                  • <strong>Input Tax:</strong> Credit to reverse tax credit (if applicable)
                </>
              ) : (
                <>
                  <strong>💡 GRN Purchase Accounts:</strong><br/>
                  • <strong>Inventory items:</strong> Use your Inventory Asset accounts (e.g., Raw Materials, Finished Goods)<br/>
                  • <strong>Tax amounts:</strong> Use Input Tax or VAT Receivable accounts<br/>
                  • <strong>Payable amount:</strong> Use Accounts Payable (Credit side)
                </>
              )}
            </Typography>
          </Alert>
        )}
        
        {/* Show validation helper if there are unselected accounts */}
        {journalLines.some(line => !line.account_id) && (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>⚠️ Action Required:</strong> Please select GL accounts for all journal lines to enable saving.
            </Typography>
          </Alert>
        )}

        {/* Form with Header/Footer */}
        <FormHeaderFooter
          title={searchParams.get('type') === 'GRN_RETURN' ? "CREATE CREDIT NOTE (GRN RETURN)" : "CREATE JOURNAL VOUCHER"}
          subtitle={searchParams.get('type') === 'GRN_RETURN' ? "Record return credit note with proper accounting reversal" : "Record journal entries with proper double-entry bookkeeping"}
          titleColor="primary"
          showAmount={true}
          amount={totalDebits}
          amountLabel="Total Amount"
          currencyCode={currencyInfo?.functional_currency}
          amountColor={isBalanced ? 'primary' : 'error.main'}
          onClose={handleCancel}
          onCancel={handleCancel}
          onPrint={handlePrint}
          onCopy={handleCopy}
          onMakeRecurring={handleMakeRecurring}
          onSave={handleSubmit}
          onSaveAndClose={handleSaveAndClose}
          isEditMode={false}
          loading={loading}
          saveLabel={searchParams.get('type') === 'GRN_RETURN' ? "Create Credit Note" : "Create Journal Voucher"}
          saveAndCloseLabel={searchParams.get('type') === 'GRN_RETURN' ? "Create Credit Note & Close" : "Create and Close"}
        >
          <form>
            {/* Basic Information */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Entry Number"
                  value={entryNumber}
                  onChange={(e) => setEntryNumber(e.target.value)}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Transaction Date"
                  value={transactionDate}
                  onChange={(newValue) => setTransactionDate(newValue || dayjs())}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Reference Number"
                  value={referenceNumber}
                  onChange={(e) => setReferenceNumber(e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  required
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>

            <Divider sx={{ my: 3 }} />

            {/* Journal Lines */}
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Journal Lines
                </Typography>
                <Button
                  variant="outlined"
                  onClick={addJournalLine}
                  size="small"
                >
                  Add Line
                </Button>
              </Box>

              {accountsLoading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                  <Typography sx={{ ml: 2 }}>Loading accounts...</Typography>
                </Box>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Account</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Debit</TableCell>
                        <TableCell>Credit</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {journalLines.map((line) => (
                        <TableRow key={line.id}>
                          <TableCell sx={{ 
                            minWidth: 250,
                            bgcolor: !line.account_id ? 'rgba(255, 152, 0, 0.08)' : 'transparent',
                            borderLeft: !line.account_id ? '3px solid #ff9800' : 'none'
                          }}>
                            <Autocomplete
                              options={accounts}
                              getOptionLabel={(option) => `${option.account_number} - ${option.account_name}`}
                              value={accounts.find(acc => acc.id === line.account_id) || null}
                              onChange={(_, newValue) => handleAccountChange(line.id, newValue)}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  placeholder={!line.account_id ? "Select account..." : "Select account..."}
                                  size="small"
                                  fullWidth
                                  sx={{
                                    '& .MuiOutlinedInput-root': {
                                      '&.Mui-focused fieldset': {
                                        borderColor: !line.account_id ? '#ff9800' : undefined,
                                      },
                                    },
                                  }}
                                />
                              )}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              fullWidth
                              size="small"
                              value={line.description}
                              onChange={(e) => handleLineChange(line.id, 'description', e.target.value)}
                              placeholder="Enter description..."
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              size="small"
                              type="number"
                              value={line.debit_amount || ''}
                              onChange={(e) => handleLineChange(line.id, 'debit_amount', parseFloat(e.target.value) || 0)}
                              inputProps={{ min: 0, step: 0.01 }}
                              sx={{ width: 120 }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              size="small"
                              type="number"
                              value={line.credit_amount || ''}
                              onChange={(e) => handleLineChange(line.id, 'credit_amount', parseFloat(e.target.value) || 0)}
                              inputProps={{ min: 0, step: 0.01 }}
                              sx={{ width: 120 }}
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              size="small"
                              color="error"
                              onClick={() => removeJournalLine(line.id)}
                              disabled={journalLines.length <= 2}
                            >
                              Remove
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}

              {/* Totals */}
              <Box sx={{ 
                mt: 2, 
                p: 2, 
                bgcolor: isBalanced ? 'success.light' : 'error.light',
                borderRadius: 1,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Totals
                </Typography>
                <Box sx={{ display: 'flex', gap: 4 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Debit: <CurrencyDisplay 
                      value={totalDebits}
                      currencyCode={currencyInfo?.functional_currency}
                      showBlankForZero={false}
                      component="span"
                    />
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Credit: <CurrencyDisplay 
                      value={totalCredits}
                      currencyCode={currencyInfo?.functional_currency}
                      showBlankForZero={false}
                      component="span"
                    />
                  </Typography>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontWeight: 600,
                      color: isBalanced ? 'success.dark' : 'error.dark'
                    }}
                  >
                    {isBalanced ? '✓ Balanced' : '✗ Not Balanced'}
                  </Typography>
                </Box>
              </Box>

              {/* Attachments */}
              <Box sx={{ mt: 3 }}>
                <Button
                  startIcon={<AttachFileIcon />}
                  variant="outlined"
                  component="label"
                >
                  Attach Files
                  <input type="file" hidden multiple />
                </Button>
              </Box>
            </Box>

          </form>
        </FormHeaderFooter>
      </Box>
    </LocalizationProvider>
  );
};

export default CreateJournalVoucherPage; 