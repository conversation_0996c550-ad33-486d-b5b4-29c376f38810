<!DOCTYPE html>
<html>
<head>
    <title>CMS API Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>CMS API Debug Tool</h1>
    
    <div class="test-section">
        <h3>1. Check Authentication Token</h3>
        <button onclick="checkToken()">Check Token</button>
        <div id="token-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Set Token Manually</h3>
        <button onclick="setToken()">Set Admin Token</button>
        <div id="set-token-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test CMS Accounts API</h3>
        <button onclick="testAccountsAPI()">Test Accounts</button>
        <div id="accounts-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Test Treasury API</h3>
        <button onclick="testTreasuryAPI()">Test Treasury</button>
        <div id="treasury-result"></div>
    </div>

    <div class="test-section">
        <h3>5. Test Transactions API</h3>
        <button onclick="testTransactionsAPI()">Test Transactions</button>
        <div id="transactions-result"></div>
    </div>

    <script>
        function checkToken() {
            const token = localStorage.getItem('token');
            const result = document.getElementById('token-result');
            if (token) {
                result.innerHTML = `<div class="success">✅ Token found: ${token.substring(0, 10)}...</div>`;
            } else {
                result.innerHTML = `<div class="error">❌ No token found in localStorage</div>`;
            }
        }

        function setToken() {
            const adminToken = 'e15bc01f831c5111f413f534ef82288744cb7d41';
            localStorage.setItem('token', adminToken);
            const result = document.getElementById('set-token-result');
            result.innerHTML = `<div class="success">✅ Admin token set successfully</div>`;
        }

        async function testAccountsAPI() {
            const result = document.getElementById('accounts-result');
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error('No token found. Please set token first.');
                }

                const response = await fetch('http://localhost:8000/api/cms/accounts/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                result.innerHTML = `
                    <div class="success">✅ Accounts API Success</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ Accounts API Error: ${error.message}</div>
                `;
            }
        }

        async function testTreasuryAPI() {
            const result = document.getElementById('treasury-result');
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error('No token found. Please set token first.');
                }

                const response = await fetch('http://localhost:8000/api/cms/dashboard/treasury/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                result.innerHTML = `
                    <div class="success">✅ Treasury API Success</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ Treasury API Error: ${error.message}</div>
                `;
            }
        }

        async function testTransactionsAPI() {
            const result = document.getElementById('transactions-result');
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    throw new Error('No token found. Please set token first.');
                }

                const response = await fetch('http://localhost:8000/api/cms/transactions/?limit=10&ordering=-transaction_date', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                result.innerHTML = `
                    <div class="success">✅ Transactions API Success</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">❌ Transactions API Error: ${error.message}</div>
                `;
            }
        }

        // Auto-check token on page load
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html> 