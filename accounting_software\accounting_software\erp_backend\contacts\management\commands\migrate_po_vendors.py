from django.core.management.base import BaseCommand
from django.db import transaction
from django.apps import apps


class Command(BaseCommand):
    help = 'Migrate Purchase Order vendor references from old vendors to contacts vendors'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('=== DRY RUN MODE - NO CHANGES WILL BE MADE ==='))
        else:
            self.stdout.write(self.style.SUCCESS('=== MIGRATING PURCHASE ORDER VENDORS ==='))

        try:
            # Get model references
            PurchaseVendor = apps.get_model('purchase', 'Vendor')
            ContactVendor = apps.get_model('contacts', 'Vendor')
            Contact = apps.get_model('contacts', 'Contact')
            PurchaseOrder = apps.get_model('purchase', 'PurchaseOrder')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting model references: {e}'))
            return

        # Step 1: Show current state
        self.stdout.write('\n📊 CURRENT STATE:')
        self.stdout.write(f'   - Old purchase vendors: {PurchaseVendor.objects.count()}')
        self.stdout.write(f'   - New contacts vendors: {ContactVendor.objects.count()}')
        self.stdout.write(f'   - Purchase orders: {PurchaseOrder.objects.count()}')
        
        # Step 2: Create mapping between old and new vendors
        self.stdout.write('\n🔍 ANALYZING VENDOR MAPPING:')
        
        vendor_mapping = {}
        unmapped_vendors = []
        
        for old_vendor in PurchaseVendor.objects.all():
            # Try to find matching contact vendor by email first, then by name
            contact_vendor = None
            
            # Try exact email match
            if old_vendor.email:
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__email=old_vendor.email
                    )
                except ContactVendor.DoesNotExist:
                    pass
            
            # Try exact name match if email didn't work
            if not contact_vendor:
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__name=old_vendor.display_name
                    )
                except ContactVendor.DoesNotExist:
                    pass
            
            # Try fuzzy name match (case insensitive)
            if not contact_vendor:
                try:
                    contact_vendor = ContactVendor.objects.select_related('contact').get(
                        contact__name__iexact=old_vendor.display_name
                    )
                except ContactVendor.DoesNotExist:
                    pass
            
            if contact_vendor:
                vendor_mapping[old_vendor.id] = contact_vendor
                self.stdout.write(f'   ✅ {old_vendor.display_name} ({old_vendor.email}) -> {contact_vendor.contact.name} ({contact_vendor.contact.email})')
            else:
                unmapped_vendors.append(old_vendor)
                self.stdout.write(f'   ❌ {old_vendor.display_name} ({old_vendor.email}) -> NO MATCH FOUND')
        
        self.stdout.write(f'\n📈 MAPPING RESULTS:')
        self.stdout.write(f'   - Successful mappings: {len(vendor_mapping)}')
        self.stdout.write(f'   - Unmapped vendors: {len(unmapped_vendors)}')
        
        # Step 3: Create missing vendors in contacts if needed
        if unmapped_vendors:
            self.stdout.write(f'\n➕ CREATING MISSING VENDORS IN CONTACTS:')
            
            for old_vendor in unmapped_vendors:
                if not self.dry_run:
                    with transaction.atomic():
                        # Create contact first
                        contact = Contact.objects.create(
                            name=old_vendor.display_name,
                            contact_type='vendor',
                            email=old_vendor.email,
                            phone=old_vendor.phone,
                            address=f"{old_vendor.billing_street or ''}\n{old_vendor.billing_city or ''}, {old_vendor.billing_state or ''} {old_vendor.billing_postal_code or ''}\n{old_vendor.billing_country or ''}".strip()
                        )
                        
                        # Create vendor
                        contact_vendor = ContactVendor.objects.create(
                            contact=contact,
                            vendor_code=str(old_vendor.vendor_id)[:50],  # Truncate if needed
                            credit_limit=old_vendor.credit_limit,
                            payment_terms=old_vendor.payment_terms.name if old_vendor.payment_terms else None,
                            vendor_category=old_vendor.vendor_category,
                            lead_time_days=old_vendor.lead_time_days,
                            minimum_order_amount=old_vendor.minimum_order_amount,
                            preferred_vendor=old_vendor.preferred_vendor,
                            first_name=old_vendor.first_name,
                            last_name=old_vendor.last_name,
                            company_name=old_vendor.company_name,
                            mobile=old_vendor.mobile,
                            billing_street=old_vendor.billing_street,
                            billing_city=old_vendor.billing_city,
                            billing_state=old_vendor.billing_state,
                            billing_postal_code=old_vendor.billing_postal_code,
                            billing_country=old_vendor.billing_country
                        )
                        
                        # Add to mapping
                        vendor_mapping[old_vendor.id] = contact_vendor
                        
                        self.stdout.write(f'   ✅ Created: {old_vendor.display_name} -> Contact ID {contact.id}')
                else:
                    self.stdout.write(f'   📝 Would create: {old_vendor.display_name} ({old_vendor.email})')
        
        # Step 4: Show purchase orders that would be updated
        self.stdout.write(f'\n🛒 PURCHASE ORDERS TO UPDATE:')
        
        pos_to_update = []
        for po in PurchaseOrder.objects.filter(vendor__isnull=False):
            if po.vendor_id in vendor_mapping:
                contact_vendor = vendor_mapping[po.vendor_id]
                pos_to_update.append((po, contact_vendor))
                self.stdout.write(f'   📦 PO {po.po_number}: {po.vendor.display_name} -> {contact_vendor.contact.name}')
            else:
                self.stdout.write(f'   ❌ PO {po.po_number}: Cannot map vendor {po.vendor.display_name}')
        
        self.stdout.write(f'\n📊 SUMMARY:')
        self.stdout.write(f'   - Purchase orders that can be migrated: {len(pos_to_update)}')
        self.stdout.write(f'   - Total old vendors: {PurchaseVendor.objects.count()}')
        self.stdout.write(f'   - Total new vendors: {ContactVendor.objects.count() + len(unmapped_vendors)}')
        
        if not self.dry_run:
            self.stdout.write(self.style.SUCCESS('\n✅ VENDOR MIGRATION COMPLETE'))
            self.stdout.write('Next step: Update purchase order foreign key references in Django migration')
        else:
            self.stdout.write(self.style.WARNING('\n=== DRY RUN COMPLETE ==='))
            self.stdout.write('Run without --dry-run to execute the migration') 