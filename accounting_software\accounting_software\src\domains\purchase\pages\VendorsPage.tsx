import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>po<PERSON>,
  Box,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Menu,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import { 
  Add as AddIcon, 
  TrendingUp, 
  Group, 
  AttachMoney, 
  Timeline,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Receipt as ReceiptIcon,
  Payment as PaymentIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Star as StarIcon,
  LocalShipping as LocalShippingIcon,
} from '@mui/icons-material';
import VendorForm from '../components/VendorForm';
import StatCard from '../../../shared/components/StatCard';
import DataTable, { Column } from '../../../shared/components/DataTable';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency } from '../../../shared/utils/formatters';
import { vendorService, Vendor, VendorStats } from '../../../services/vendor.service';
import { useCompany } from '../../../contexts/CompanyContext';

const VendorsPage: React.FC = () => {
  const { companyInfo } = useCompany();
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [preferredFilter, setPreferredFilter] = useState<string>('all');
  const [stats, setStats] = useState<VendorStats | null>(null);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuVendor, setMenuVendor] = useState<Vendor | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [viewVendor, setViewVendor] = useState<Vendor | null>(null);

  // Define columns for DataTable
  const columns: Column[] = [
    {
      field: 'display_name',
      headerName: 'Vendor Name',
      width: 250,
      renderCell: ({ row }) => (
        <Box display="flex" alignItems="center">
          <Avatar sx={{ mr: 2, bgcolor: getVendorTypeColor(row.vendor_type) + '.light' }}>
            {getVendorIcon(row)}
          </Avatar>
          <Box>
            <Typography variant="body1" fontWeight="medium">
              {row.display_name}
              {row.preferred_vendor && (
                <StarIcon sx={{ ml: 1, fontSize: 16, color: 'warning.main' }} />
              )}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {row.vendor_id}
            </Typography>
          </Box>
        </Box>
      ),
    },
    {
      field: 'vendor_type',
      headerName: 'Type',
      width: 120,
      renderCell: ({ row }) => (
        <Chip
          size="small"
          label={row.vendor_type}
          color={getVendorTypeColor(row.vendor_type)}
          variant="outlined"
        />
      ),
    },
    {
      field: 'vendor_category',
      headerName: 'Category',
      width: 150,
      renderCell: ({ row }) => (
        <Typography variant="body2">
          {row.vendor_category || 'General'}
        </Typography>
      ),
    },
    {
      field: 'email',
      headerName: 'Contact',
      width: 200,
      renderCell: ({ row }) => (
        <Box>
          <Typography variant="body2">{row.email}</Typography>
          <Typography variant="caption" color="text.secondary">
            {row.phone}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'billing_city',
      headerName: 'Location',
      width: 150,
      renderCell: ({ row }) => (
        <Box>
          <Typography variant="body2">{row.billing_city}</Typography>
          <Typography variant="caption" color="text.secondary">
            {row.billing_state}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'lead_time_days',
      headerName: 'Lead Time',
      width: 100,
      align: 'center',
      renderCell: ({ row }) => (
        <Chip
          size="small"
          label={`${row.lead_time_days} days`}
          color={row.lead_time_days <= 3 ? 'success' : row.lead_time_days <= 7 ? 'warning' : 'default'}
        />
      ),
    },
    {
      field: 'current_balance',
      headerName: 'Balance',
      width: 120,
      align: 'right',
      renderCell: ({ row }) => (
        <Typography
          variant="body2"
          color={row.current_balance > 0 ? 'error.main' : 'text.primary'}
          fontWeight="medium"
        >
          {formatCurrency(Math.abs(row.current_balance), row.currency)}
        </Typography>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 100,
      align: 'center',
      renderCell: ({ row }) => (
        <Chip
          size="small"
          label={row.status}
          color={row.status === 'active' ? 'success' : 'default'}
          icon={row.status === 'active' ? <CheckCircleIcon /> : <CancelIcon />}
        />
      ),
    },
  ];

  // Load vendors from API
  const loadVendors = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params: any = {
        page,
        search: searchTerm,
      };

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }
      if (typeFilter !== 'all') {
        params.vendor_type = typeFilter;
      }
      if (preferredFilter !== 'all') {
        params.preferred_vendor = preferredFilter === 'preferred';
      }

      const data = await vendorService.getVendors(params);
      setVendors(data.results || data);
      setTotalCount(data.count || 0);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load vendors');
      console.error('Load vendors error:', err);
    } finally {
      setLoading(false);
    }
  }, [page, searchTerm, statusFilter, typeFilter, preferredFilter]);

  // Load vendor statistics
  const loadStats = useCallback(async () => {
    try {
      const data = await vendorService.getVendorStats();
      setStats(data);
    } catch (err) {
      console.error('Load stats error:', err);
    }
  }, []);

  useEffect(() => {
    loadVendors();
  }, [loadVendors]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  const handleOpenDialog = useCallback(() => {
    setSelectedVendor(null);
    setOpenDialog(true);
  }, []);
  
  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedVendor(null);
  }, []);
  
  const handleEditVendor = useCallback((vendor: Vendor) => {
    // Transform vendor data to match form structure
    const vendorForForm = {
      id: vendor.id,
      firstName: vendor.first_name || '',
      lastName: vendor.last_name || '',
      displayName: vendor.display_name,
      companyName: vendor.company_name || '',
      email: vendor.email || '',
      phone: vendor.phone || '',
      mobile: vendor.mobile || '',
      billingAddress: {
        street: vendor.billing_street || '',
        city: vendor.billing_city || '',
        state: vendor.billing_state || '',
        postalCode: vendor.billing_postal_code || '',
        country: vendor.billing_country || 'India',
      },
      shippingAddress: {
        sameAsBilling: true,
        street: vendor.shipping_street || '',
        city: vendor.shipping_city || '',
        state: vendor.shipping_state || '',
        postalCode: vendor.shipping_postal_code || '',
        country: vendor.shipping_country || 'India',
      },
      paymentTerms: vendor.payment_terms || 'net_30',
      currency: vendor.currency || 'INR',
      creditLimit: vendor.credit_limit || 0,
      vendorCategory: vendor.vendor_category || '',
      leadTimeDays: vendor.lead_time_days || 0,
      minimumOrderAmount: vendor.minimum_order_amount || 0,
      preferredVendor: vendor.preferred_vendor || false,
      notes: vendor.notes || '',
    };
    
    setSelectedVendor(vendorForForm);
    setOpenDialog(true);
  }, []);
  
  const handleDeleteVendor = useCallback(async (vendor: Vendor) => {
    if (window.confirm(`Are you sure you want to delete ${vendor.display_name}?\n\nThis action cannot be undone.`)) {
      try {
        await vendorService.deleteVendor(vendor.id);
        alert(`Vendor "${vendor.display_name}" has been deleted successfully.`);
        loadVendors();
        loadStats();
      } catch (error) {
        console.error('Error deleting vendor:', error);
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete vendor';
        setError(errorMessage);
        alert(`Error: ${errorMessage}`);
      }
    }
  }, [loadVendors, loadStats]);
  
  const handleViewVendor = useCallback((vendor: Vendor) => {
    setViewVendor(vendor);
    setViewDialogOpen(true);
  }, []);
  
  const handleEmailVendor = useCallback((vendor: Vendor) => {
    if (vendor.email) {
      window.open(`mailto:${vendor.email}?subject=Purchase Order Inquiry&body=Dear ${vendor.display_name},%0D%0A%0D%0A`);
    } else {
      alert('No email address available for this vendor.');
    }
  }, []);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, vendor: Vendor) => {
    setAnchorEl(event.currentTarget);
    setMenuVendor(vendor);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuVendor(null);
  };

  const handleSaveVendor = useCallback(async () => {
    // This function is called after VendorForm successfully saves
    // We just need to refresh the data
    loadVendors();
    loadStats();
  }, [loadVendors, loadStats]);

  const getVendorIcon = (vendor: Vendor) => {
    switch (vendor.vendor_type) {
      case 'business':
        return <BusinessIcon />;
      case 'supplier':
        return <LocalShippingIcon />;
      case 'contractor':
        return <PersonIcon />;
      case 'individual':
      default:
        return <PersonIcon />;
    }
  };

  const getVendorTypeColor = (type: string) => {
    switch (type) {
      case 'business':
        return 'primary';
      case 'supplier':
        return 'success';
      case 'contractor':
        return 'warning';
      case 'individual':
        return 'info';
      default:
        return 'default';
    }
  };

  if (loading && vendors.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Vendors...</Typography>
      </Box>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4">
          🏭 Vendors
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleOpenDialog}
          >
            New Vendor
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Vendors"
              value={stats.total_vendors}
              icon={<Group />}
              trend={stats.new_vendors_30_days > 0 ? stats.new_vendors_30_days : undefined}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Active Vendors"
              value={stats.active_vendors}
              icon={<TrendingUp />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Payables"
              value={formatCurrency(Math.abs(stats.total_payables), companyInfo?.functionalCurrency || 'INR')}
              icon={<AttachMoney />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Preferred Vendors"
              value={stats.preferred_vendors}
              icon={<StarIcon />}
              color="info"
            />
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search vendors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={typeFilter}
                  label="Type"
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="individual">Individual</MenuItem>
                  <MenuItem value="business">Business</MenuItem>
                  <MenuItem value="supplier">Supplier</MenuItem>
                  <MenuItem value="contractor">Contractor</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Preferred</InputLabel>
                <Select
                  value={preferredFilter}
                  label="Preferred"
                  onChange={(e) => setPreferredFilter(e.target.value)}
                >
                  <MenuItem value="all">All Vendors</MenuItem>
                  <MenuItem value="preferred">Preferred Only</MenuItem>
                  <MenuItem value="regular">Regular Only</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadVendors}
                size="small"
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Vendors Table */}
      <Card>
        <CardContent>
          <DataTable
            columns={columns}
            rows={vendors || []}
            loading={loading}
            title="Vendors"
            onRefresh={loadVendors}
            checkboxSelection
            serverSide={true}
            totalCount={totalCount}
            page={page - 1} // DataTable uses 0-based indexing, but our API uses 1-based
            onPageChange={(newPage) => setPage(newPage + 1)} // Convert back to 1-based for API
            rowActions={[
              {
                icon: <EditIcon />,
                label: 'Edit',
                onClick: handleEditVendor,
              },
              {
                icon: <VisibilityIcon />,
                label: 'View',
                onClick: handleViewVendor,
              },
              {
                icon: <EmailIcon />,
                label: 'Email',
                onClick: handleEmailVendor,
                hide: (row) => !row.email,
              },
              {
                icon: <DeleteIcon />,
                label: 'Delete',
                onClick: handleDeleteVendor,
                color: 'error' as const,
              },
            ]}
          />
        </CardContent>
      </Card>

      {/* Vendor Form Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <VendorForm
          onClose={handleCloseDialog}
          onSave={handleSaveVendor}
          initialValues={selectedVendor || undefined}
        />
      </Dialog>

      {/* View Vendor Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2 }}>
          <Typography variant="h5" fontWeight="bold">
            Vendor Details
          </Typography>
        </DialogTitle>
        <DialogContent dividers sx={{ p: 3 }}>
          {viewVendor && (
            <Grid container spacing={3}>
              {/* Basic Information */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom color="primary">
                  Basic Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Vendor ID</Typography>
                <Typography variant="body1" fontWeight="medium">{viewVendor.vendor_id}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Display Name</Typography>
                <Typography variant="body1" fontWeight="medium">
                  {viewVendor.display_name}
                  {viewVendor.preferred_vendor && (
                    <StarIcon sx={{ ml: 1, fontSize: 16, color: 'warning.main' }} />
                  )}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Company</Typography>
                <Typography variant="body1">{viewVendor.company_name || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Type</Typography>
                <Chip 
                  label={viewVendor.vendor_type} 
                  color={getVendorTypeColor(viewVendor.vendor_type)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Category</Typography>
                <Typography variant="body1">{viewVendor.vendor_category || 'General'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Lead Time</Typography>
                <Typography variant="body1">{viewVendor.lead_time_days} days</Typography>
              </Grid>

              {/* Contact Information */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom color="primary">
                  Contact Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Email</Typography>
                <Typography variant="body1">{viewVendor.email || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Phone</Typography>
                <Typography variant="body1">{viewVendor.phone || 'N/A'}</Typography>
              </Grid>

              {/* Financial Information */}
              <Grid item xs={12} sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom color="primary">
                  Financial Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Current Balance</Typography>
                <Typography 
                  variant="body1" 
                  fontWeight="medium"
                  color={viewVendor.current_balance > 0 ? 'error.main' : 'text.primary'}
                >
                  {formatCurrency(Math.abs(viewVendor.current_balance), viewVendor.currency)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Credit Limit</Typography>
                <Typography variant="body1">
                  {viewVendor.credit_limit ? formatCurrency(viewVendor.credit_limit, viewVendor.currency) : 'No limit'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Minimum Order Amount</Typography>
                <Typography variant="body1">
                  {formatCurrency(viewVendor.minimum_order_amount, viewVendor.currency)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">Payment Terms</Typography>
                <Typography variant="body1">{viewVendor.payment_terms}</Typography>
              </Grid>

              {/* Notes */}
              {viewVendor.notes && (
                <>
                  <Grid item xs={12} sx={{ mt: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      Notes
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body1">{viewVendor.notes}</Typography>
                  </Grid>
                </>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
          {viewVendor && (
            <>
              <Button 
                variant="outlined" 
                startIcon={<EditIcon />}
                onClick={() => {
                  setViewDialogOpen(false);
                  handleEditVendor(viewVendor);
                }}
              >
                Edit
              </Button>
              {viewVendor.email && (
                <Button 
                  variant="contained" 
                  startIcon={<EmailIcon />}
                  onClick={() => handleEmailVendor(viewVendor)}
                >
                  Send Email
                </Button>
              )}
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          if (menuVendor) handleEditVendor(menuVendor);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Vendor</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          if (menuVendor) handleViewVendor(menuVendor);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          if (menuVendor && menuVendor.email) handleEmailVendor(menuVendor);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EmailIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Send Email</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => {
          if (menuVendor) handleDeleteVendor(menuVendor);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Vendor</ListItemText>
        </MenuItem>
      </Menu>
    </PageContainer>
  );
};

export default VendorsPage; 