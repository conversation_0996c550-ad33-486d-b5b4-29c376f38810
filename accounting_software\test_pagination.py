#!/usr/bin/env python3
"""
Test script to verify customer pagination is working correctly
"""
import requests
import json

def test_pagination():
    """Test customer pagination with different page sizes"""
    base_url = "http://localhost:8000/api/contacts/customers/"
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("🔍 Testing Customer Pagination")
    print("=" * 50)
    
    # Test different page sizes
    page_sizes = [5, 10, 25]
    
    for page_size in page_sizes:
        print(f"\n📄 Testing page_size={page_size}")
        url = f"{base_url}?page=1&page_size={page_size}"
        
        try:
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                total = data.get('count', 0)
                results_count = len(data.get('results', []))
                
                print(f"✅ SUCCESS!")
                print(f"   Total customers: {total}")
                print(f"   Results on page 1: {results_count}")
                print(f"   Expected on page 1: {min(page_size, total)}")
                
                # Calculate expected pages
                expected_pages = (total + page_size - 1) // page_size
                has_next = data.get('next') is not None
                expected_has_next = total > page_size
                
                print(f"   Has next page: {has_next}")
                print(f"   Expected next page: {expected_has_next}")
                print(f"   Expected total pages: {expected_pages}")
                
                # Test last page if there are multiple pages
                if expected_pages > 1:
                    last_page_url = f"{base_url}?page={expected_pages}&page_size={page_size}"
                    last_response = requests.get(last_page_url, headers=headers)
                    
                    if last_response.status_code == 200:
                        last_data = last_response.json()
                        last_results = len(last_data.get('results', []))
                        expected_last_results = total - (expected_pages - 1) * page_size
                        
                        print(f"   Last page ({expected_pages}) results: {last_results}")
                        print(f"   Expected last page results: {expected_last_results}")
                        
                        if last_results == expected_last_results:
                            print(f"   ✅ Last page calculation correct!")
                        else:
                            print(f"   ❌ Last page calculation incorrect!")
                
            else:
                print(f"❌ FAILED! Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
    
    print(f"\n🎯 Testing Navigation")
    print("-" * 30)
    
    # Test page navigation
    try:
        # Get first page
        response1 = requests.get(f"{base_url}?page=1&page_size=5", headers=headers)
        if response1.status_code == 200:
            data1 = response1.json()
            print(f"✅ Page 1: {len(data1.get('results', []))} results")
            
            # Get second page using next URL
            if data1.get('next'):
                response2 = requests.get(data1['next'], headers=headers)
                if response2.status_code == 200:
                    data2 = response2.json()
                    print(f"✅ Page 2 (via next): {len(data2.get('results', []))} results")
                    
                    # Verify different customers
                    page1_names = [r.get('display_name') for r in data1.get('results', [])]
                    page2_names = [r.get('display_name') for r in data2.get('results', [])]
                    
                    overlap = set(page1_names) & set(page2_names)
                    if not overlap:
                        print(f"✅ No overlap between pages - pagination working correctly!")
                    else:
                        print(f"❌ Found overlap between pages: {overlap}")
                        
    except Exception as e:
        print(f"❌ Navigation test failed: {e}")

if __name__ == "__main__":
    test_pagination()
