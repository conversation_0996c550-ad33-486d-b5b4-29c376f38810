export interface VendorFormData {
  // Basic Information
  title: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  suffix?: string;
  companyName?: string;
  displayName: string;
  email: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  website?: string;
  other?: string;
  nameOnCheques?: string;
  isSubVendor: boolean;
  
  // Billing Address
  billingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  
  // Shipping Address
  shippingAddress: {
    sameAsBilling: boolean;
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  
  // Payment Terms
  paymentTerms: string;
  creditLimit?: number;
  currency: string;
  taxId?: string;
  
  // Additional Information
  notes?: string;
  attachments?: File[];
}

export interface Vendor {
  id?: number;
  vendor_id?: string;
  display_name: string;
  vendor_type: 'individual' | 'business' | 'contractor' | 'supplier';
  company_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  fax?: string;
  website?: string;
  billing_street?: string;
  billing_city?: string;
  billing_state?: string;
  billing_postal_code?: string;
  billing_country?: string;
  shipping_street?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_postal_code?: string;
  shipping_country?: string;
  tax_registration_number?: string;
  pan_number?: string;
  gstin?: string;
  opening_balance?: number;
  opening_balance_date?: string;
  current_balance?: number;
  credit_limit?: number;
  payment_terms?: string;
  vendor_category?: string;
  lead_time_days?: number;
  minimum_order_amount?: number;
  status: 'active' | 'inactive';
  taxable?: boolean;
  tax_exempt_reason?: string;
  preferred_vendor?: boolean;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export type PaymentTermOption = {
  value: string;
  label: string;
  days: number;
};

export const PAYMENT_TERMS: PaymentTermOption[] = [
  { value: 'due_on_receipt', label: 'Due on Receipt', days: 0 },
  { value: 'net_7', label: 'Net 7', days: 7 },
  { value: 'net_15', label: 'Net 15', days: 15 },
  { value: 'net_30', label: 'Net 30', days: 30 },
  { value: 'net_45', label: 'Net 45', days: 45 },
  { value: 'net_60', label: 'Net 60', days: 60 },
  { value: 'custom', label: 'Custom', days: 0 }
];

export const COUNTRIES = [
  { value: 'US', label: 'United States' },
  { value: 'CA', label: 'Canada' },
  { value: 'UK', label: 'United Kingdom' },
  { value: 'AU', label: 'Australia' },
  { value: 'DE', label: 'Germany' },
  { value: 'FR', label: 'France' },
  { value: 'IN', label: 'India' },
  { value: 'CN', label: 'China' },
  { value: 'JP', label: 'Japan' },
  { value: 'BR', label: 'Brazil' },
  { value: 'MX', label: 'Mexico' },
  { value: 'ZA', label: 'South Africa' },
  { value: 'NG', label: 'Nigeria' },
  { value: 'EG', label: 'Egypt' },
  { value: 'SA', label: 'Saudi Arabia' }
]; 