# Generated by Django 4.2.21 on 2025-06-12 08:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cms', '0002_cashtransferjournal_cashtransactionjournal_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cashaccountbalance',
            name='cash_account',
            field=models.OneToOneField(blank=True, help_text='Optional CMS cash account (legacy)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='balance_record', to='cms.cashaccount'),
        ),
        migrations.AddConstraint(
            model_name='cashaccountbalance',
            constraint=models.UniqueConstraint(fields=('gl_account',), name='unique_gl_account_balance'),
        ),
    ]
