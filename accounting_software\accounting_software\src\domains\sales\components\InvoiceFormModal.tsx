import React from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  IconButton,
  Typography,
  Box
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import InvoiceForm from './InvoiceForm';
import { Invoice } from '../../../shared/types/invoice.types';

interface InvoiceFormModalProps {
  open: boolean;
  onClose: () => void;
  invoice?: Invoice;
  onSuccess?: () => void;
}

const InvoiceFormModal: React.FC<InvoiceFormModalProps> = ({ open, onClose, invoice, onSuccess }) => {
  const handleSuccess = () => {
    // Call both onSuccess and onClose if provided
    if (onSuccess) {
      onSuccess();
    }
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      scroll="paper"
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">{invoice ? 'Edit Invoice' : 'Create Invoice'}</Typography>
          <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <InvoiceForm onSuccess={handleSuccess} invoice={invoice} />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InvoiceFormModal; 