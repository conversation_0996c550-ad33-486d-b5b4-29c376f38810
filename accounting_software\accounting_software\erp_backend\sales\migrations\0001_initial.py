# Generated by Django 4.2.21 on 2025-06-12 07:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contacts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('display_name', models.CharField(max_length=200)),
                ('customer_type', models.CharField(choices=[('individual', 'Individual'), ('business', 'Business'), ('vendor', 'Vendor'), ('employee', 'Employee')], default='individual', max_length=20)),
                ('company_name', models.Char<PERSON><PERSON>(blank=True, max_length=200, null=True)),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('mobile', models.CharField(blank=True, max_length=20, null=True)),
                ('fax', models.CharField(blank=True, max_length=20, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('billing_street', models.TextField(blank=True, null=True)),
                ('billing_city', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_state', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('billing_country', models.CharField(default='India', max_length=100)),
                ('shipping_street', models.TextField(blank=True, null=True)),
                ('shipping_city', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_state', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('shipping_country', models.CharField(blank=True, max_length=100, null=True)),
                ('tax_registration_number', models.CharField(blank=True, max_length=50, null=True)),
                ('pan_number', models.CharField(blank=True, max_length=20, null=True)),
                ('gstin', models.CharField(blank=True, max_length=20, null=True)),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('opening_balance_date', models.DateField(blank=True, null=True)),
                ('current_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('credit_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('payment_terms', models.CharField(choices=[('net_15', 'Net 15'), ('net_30', 'Net 30'), ('net_45', 'Net 45'), ('net_60', 'Net 60'), ('due_on_receipt', 'Due on Receipt'), ('cash_on_delivery', 'Cash on Delivery')], default='net_30', max_length=20)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=10)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_exempt_reason', models.CharField(blank=True, max_length=200, null=True)),
                ('preferred_delivery_method', models.CharField(default='email', max_length=50)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'sales_customers',
                'ordering': ['display_name'],
            },
        ),
        migrations.CreateModel(
            name='Estimate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('estimate_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('estimate_number', models.CharField(max_length=50, unique=True)),
                ('estimate_date', models.DateField()),
                ('expiration_date', models.DateField()),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('sent', 'Sent'), ('viewed', 'Viewed'), ('accepted', 'Accepted'), ('rejected', 'Rejected'), ('expired', 'Expired'), ('invoiced', 'Converted to Invoice')], default='draft', max_length=20)),
                ('po_number', models.CharField(blank=True, max_length=100, null=True)),
                ('memo', models.TextField(blank=True, null=True)),
                ('message_to_customer', models.TextField(blank=True, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_date', models.DateTimeField(blank=True, null=True)),
                ('viewed_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'sales_estimates',
                'ordering': ['-estimate_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('invoice_number', models.CharField(max_length=50, unique=True)),
                ('invoice_date', models.DateField()),
                ('due_date', models.DateField()),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('sent', 'Sent'), ('viewed', 'Viewed'), ('partial', 'Partially Paid'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('void', 'Void')], default='draft', max_length=20)),
                ('payment_terms', models.CharField(blank=True, max_length=20, null=True)),
                ('po_number', models.CharField(blank=True, max_length=100, null=True)),
                ('memo', models.TextField(blank=True, null=True)),
                ('message_to_customer', models.TextField(blank=True, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_date', models.DateTimeField(blank=True, null=True)),
                ('viewed_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, null=True, blank=True, related_name='invoices', to='contacts.contact', help_text='Customer from contacts system')),
            ],
            options={
                'db_table': 'sales_invoices',
                'ordering': ['-invoice_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('parent_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sales.productcategory')),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
                'db_table': 'sales_product_categories',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('sku', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('product_type', models.CharField(choices=[('product', 'Product'), ('service', 'Service'), ('bundle', 'Bundle')], default='product', max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('cost_price', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=15, null=True)),
                ('income_account', models.CharField(blank=True, max_length=200, null=True)),
                ('sales_tax_category', models.CharField(blank=True, max_length=100, null=True)),
                ('expense_account', models.CharField(blank=True, max_length=200, null=True)),
                ('preferred_vendor', models.CharField(blank=True, max_length=200, null=True)),
                ('track_inventory', models.BooleanField(default=False)),
                ('inventory_asset_account', models.CharField(blank=True, max_length=200, null=True)),
                ('reorder_point', models.IntegerField(default=0)),
                ('quantity_on_hand', models.IntegerField(default=0)),
                ('quantity_on_purchase_order', models.IntegerField(default=0)),
                ('quantity_on_sales_order', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=10)),
                ('taxable', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.productcategory')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'sales_products',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('payment_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('check', 'Check'), ('credit_card', 'Credit Card'), ('bank_transfer', 'Bank Transfer'), ('online', 'Online Payment'), ('other', 'Other')], default='cash', max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('deposit_to_account', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, null=True, blank=True, related_name='payments', to='contacts.contact', help_text='Customer from contacts system')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='sales.invoice')),
            ],
            options={
                'db_table': 'sales_payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='InvoiceLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.invoice')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sales.product')),
            ],
            options={
                'db_table': 'sales_invoice_line_items',
                'ordering': ['line_order'],
            },
        ),
        migrations.CreateModel(
            name='EstimateLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('estimate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.estimate')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sales.product')),
            ],
            options={
                'db_table': 'sales_estimate_line_items',
                'ordering': ['line_order'],
            },
        ),
        migrations.AddField(
            model_name='estimate',
            name='converted_to_invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.invoice'),
        ),
        migrations.AddField(
            model_name='estimate',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='estimates_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='estimate',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.SET_NULL, null=True, blank=True, related_name='estimates', to='contacts.contact', help_text='Customer from contacts system'),
        ),
    ]
