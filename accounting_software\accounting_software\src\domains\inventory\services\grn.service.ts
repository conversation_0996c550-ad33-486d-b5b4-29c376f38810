// API Configuration
const API_BASE_URL = 'http://localhost:8000';

export interface GRNItem {
  grn_item_id?: number;
  id?: number; // Add ID field for better tracking
  product?: number | null; // Make product optional to allow items without product links
  product_name?: string;
  product_sku?: string;
  quantity_ordered: number | string;
  quantity_received: number | string;
  unit_cost: number | string;
  po_unit_price?: number | string;
  total_cost: number | string;
  condition: 'GOOD' | 'DAMAGED' | 'EXPIRED' | 'REJECTED';
  batch_number?: string;
  expiry_date?: string;
  notes?: string;
  line_order?: number;
  po_line_item_id?: number; // Track which PO line item this relates to
}

export interface GRN {
  grn_id?: number;
  grn_number?: string;
  purchase_order: number;
  purchase_order_number?: string;
  vendor_name?: string;
  warehouse: number;
  warehouse_name?: string;
  received_by?: number;
  received_by_name?: string;
  receipt_date: string;
  status: 'DRAFT' | 'RECEIVED' | 'POSTED' | 'CANCELLED';
  notes?: string;
  total_quantity?: number;
  total_value?: number;
  created_at?: string;
  updated_at?: string;
  posted_at?: string;
  posted_by?: number;
  items?: GRNItem[];
}

export interface GRNFormData {
  purchase_order: number;
  warehouse: number;
  receipt_date: string;
  notes?: string;
  items: GRNItem[];
}

export interface GRNStats {
  total_grns: number;
  draft_grns: number;
  received_grns: number;
  posted_grns: number;
  recent_grns: number;
  total_value: number;
}

export interface POLineItem {
  id: number;
  product_id: number | null;
  product_name: string;
  product_sku: string;
  description: string;
  quantity_ordered: number;
  quantity_received: number;
  quantity_remaining: number;
  unit_price: number;
  unit_of_measure: string;
  taxable: boolean;
  tax_rate: number;
  has_remaining: boolean;
}

export interface PendingPO {
  po_id: number;
  po_number: string;
  vendor_name: string;
  po_date: string;
  total_amount: number;
  status: string;
  line_items_count: number;
  total_ordered: number;
  total_received: number;
  remaining_quantity: number;
  grn_status: string;
  existing_grns_count: number;
  can_create_grn: boolean;
  line_items: POLineItem[];
}

class GRNService {
  private baseUrl = `${API_BASE_URL}/api/inventory/grns`;

  async getGRNs(params?: {
    page?: number;
    search?: string;
    status?: string;
    warehouse?: number;
    start_date?: string;
    end_date?: string;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);

    const response = await fetch(`${this.baseUrl}/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch GRNs');
    }

    return response.json();
  }

  async getGRNStats(): Promise<GRNStats> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stats/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch GRN stats');
    }

    return response.json();
  }

  async getGRN(id: number): Promise<GRN> {
    const token = localStorage.getItem('token');
    const url = `${this.baseUrl}/${id}/`;
    console.log('🌐 Fetching GRN from URL:', url);
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 GRN API Response:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ GRN API Error Response:', errorText);
      throw new Error(`Failed to fetch GRN: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📦 GRN Data received:', data);
    return data;
  }

  async createGRN(grnData: GRNFormData): Promise<GRN> {
    const token = localStorage.getItem('token');
    
    console.log('🚀 Creating GRN with data:', JSON.stringify(grnData, null, 2));
    
    const response = await fetch(this.baseUrl + '/', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(grnData),
    });

    if (!response.ok) {
      let errorMessage = 'Failed to create GRN';
      let errorDetails = {};
      
      try {
        const errorData = await response.json();
        console.error('❌ Backend error response:', JSON.stringify(errorData, null, 2));
        errorDetails = errorData;
        
        if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        } else if (typeof errorData === 'object') {
          // Format validation errors with better detail
          const fieldErrors = [];
          for (const [field, errors] of Object.entries(errorData)) {
            if (Array.isArray(errors)) {
              // Handle array of errors
              const detailedErrors = errors.map((error, index) => {
                                 if (typeof error === 'object' && error !== null) {
                   // Handle nested object errors (like items array validation)
                   const nestedErrors = Object.entries(error as Record<string, any>).map(([subField, subError]) => {
                     return `${subField}: ${Array.isArray(subError) ? subError.join(', ') : subError}`;
                   }).join(', ');
                   return `Item ${index + 1} - ${nestedErrors}`;
                 }
                return error;
              });
              fieldErrors.push(`${field}: ${detailedErrors.join('; ')}`);
                         } else if (typeof errors === 'object' && errors !== null) {
               // Handle nested object errors
               const nestedErrors = Object.entries(errors as Record<string, any>).map(([subField, subError]) => {
                 return `${subField}: ${Array.isArray(subError) ? subError.join(', ') : subError}`;
               }).join(', ');
               fieldErrors.push(`${field}: ${nestedErrors}`);
            } else {
              fieldErrors.push(`${field}: ${errors}`);
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = `Validation errors: ${fieldErrors.join('; ')}`;
          }
        }
      } catch (parseError) {
        console.error('❌ Could not parse error response:', parseError);
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      }
      
      throw new Error(errorMessage);
    }

    return response.json();
  }

  async updateGRN(id: number, grnData: Partial<GRNFormData>): Promise<GRN> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(grnData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update GRN');
    }

    return response.json();
  }

  async deleteGRN(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete GRN');
    }
  }

  async postToInventory(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/${id}/post_to_inventory/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to post GRN to inventory');
    }
  }

  async getPendingPOs(): Promise<PendingPO[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/pending_pos/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch pending purchase orders');
    }

    return response.json();
  }
}

export const grnService = new GRNService();

// Additional standalone functions

// Mark GRN as received
export const markAsReceived = async (grnId: number): Promise<GRN> => {
  const token = localStorage.getItem('token');
  const response = await fetch(`${API_BASE_URL}/api/inventory/grns/${grnId}/`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`
    },
    body: JSON.stringify({ status: 'RECEIVED' })
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.detail || 'Failed to mark GRN as received');
  }

  return response.json();
};

// Post GRN to inventory 