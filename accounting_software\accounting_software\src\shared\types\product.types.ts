export type ProductType = 'product' | 'service' | 'bundle';
export type ProductStatus = 'active' | 'inactive';

export interface BundleItem {
  productId: string;
  quantity: number;
  name?: string; // For display purposes
}

export interface ProductFormData {
  id?: string;
  name: string;
  sku: string;
  description?: string;
  type: ProductType;
  category: string;
  salesPrice: number;
  costPrice: number;
  currency: string;
  trackInventory?: boolean;
  inventoryQuantity?: number;
  lowStockAlert?: boolean;
  lowStockThreshold?: number;
  status: ProductStatus;
  incomeAccountId: string;
  expenseAccountId: string;
  inventoryAccountId?: string;
  bundleItems?: BundleItem[]; // For bundle products
  displayBundleItems?: boolean; // Whether to display bundle items on transactions
}

export interface Product extends ProductFormData {
  id: string;
  createdAt: string;
  updatedAt: string;
  totalSales?: number;
  lastSoldDate?: string;
  averageRating?: number;
  reviewCount?: number;
}

export const PRODUCT_CATEGORIES = [
  { value: 'electronics', label: 'Electronics' },
  { value: 'clothing', label: 'Clothing' },
  { value: 'food', label: 'Food & Beverages' },
  { value: 'furniture', label: 'Furniture' },
  { value: 'books', label: 'Books' },
  { value: 'software', label: 'Software' },
  { value: 'consulting', label: 'Consulting' },
  { value: 'design', label: 'Design Services' },
  { value: 'maintenance', label: 'Maintenance & Repair' },
  { value: 'training', label: 'Training & Education' },
  { value: 'other', label: 'Other' },
];

export const CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
  { code: 'NGN', name: 'Nigerian Naira', symbol: '₦' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
]; 