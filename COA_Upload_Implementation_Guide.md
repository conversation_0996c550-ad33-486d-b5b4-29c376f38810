# Chart of Accounts Upload Implementation Guide

## Overview
This implementation provides a complete solution for uploading Chart of Accounts (COA) data via CSV files into your ERP system. The solution includes backend processing, frontend interface, validation, and error handling.

## ✅ **Implementation Status: COMPLETE**

### Backend Components ✅

#### 1. **Django Management Command** (`gl/management/commands/import_coa.py`)
- **Purpose**: Command-line interface for importing COA from CSV
- **Usage**: `python manage.py import_coa --file path/to/coa.csv`
- **Features**:
  - Comprehensive validation
  - Dry-run mode for testing
  - Detailed error reporting
  - Transaction safety (rollback on errors)
  - Duplicate detection
  - Parent account validation

#### 2. **API Endpoints** (`gl/views.py`)
- **`POST /api/gl/coa/upload/`** - Upload and process CSV files
- **`GET /api/gl/coa/template/`** - Get template information and format rules
- **`GET /api/gl/account-types-list/`** - Get available account types
- **`GET /api/gl/detail-types-list/`** - Get available detail types
- **`GET /api/gl/accounts-list/`** - Get existing accounts

#### 3. **URL Configuration** (`gl/urls.py`)
- Added all new COA upload endpoints
- Properly secured with authentication

### Frontend Components ✅

#### 1. **COA Upload Form** (`src/domains/gl/components/COAUploadForm.tsx`)
- **Features**:
  - File selection with validation (CSV only, max 5MB)
  - Dry-run/validation mode toggle
  - Real-time upload progress
  - Detailed error and warning display
  - Template download functionality
  - Format information dialog
  - Success callbacks for data refresh

#### 2. **Integration with Chart of Accounts Page**
- Added as third tab "Upload COA" (superuser only)
- Automatically refreshes account list after successful upload
- Seamless integration with existing interface

### File Format & Templates ✅

#### 1. **Format Documentation** (`COA_Upload_Format_Guide.md`)
- Complete field specifications
- Available account types and detail types
- Validation rules and best practices
- Sample data and examples

#### 2. **CSV Template** (`COA_Template.csv`)
- Ready-to-use template with 38 sample accounts
- Covers all major business account categories
- Proper Indian business context (INR, GST, TDS)
- Balanced opening balances

## 🚀 **How to Use**

### For End Users:

1. **Access the Upload Interface**:
   - Navigate to General Ledger → Chart of Accounts
   - Click on the "Upload COA" tab (superuser only)

2. **Download Template**:
   - Click "Download Template" to get the CSV format
   - Or click "View Format" to see detailed specifications

3. **Prepare Your Data**:
   - Use the template as a starting point
   - Follow the format guidelines in the documentation
   - Ensure all required fields are filled

4. **Validate First**:
   - Upload your CSV file
   - Keep "Validate Only" checked
   - Click "Validate File" to check for errors

5. **Import Data**:
   - Once validation passes
   - Uncheck "Validate Only"
   - Click "Upload & Import" to import the accounts

### For Developers:

1. **API Integration**:
```javascript
// Upload COA file
const formData = new FormData();
formData.append('file', csvFile);
formData.append('dry_run', 'false');

const response = await fetch('/api/gl/coa/upload/', {
  method: 'POST',
  headers: { 'Authorization': `Token ${token}` },
  body: formData
});
```

2. **Command Line Usage**:
```bash
# Validate file
python manage.py import_coa --file coa.csv --dry-run

# Import file
python manage.py import_coa --file coa.csv --user admin
```

## 📋 **CSV Format Specification**

### Required Fields:
- `account_number` - Unique identifier
- `account_name` - Descriptive name
- `account_type_code` - ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE
- `detail_type_code` - Specific classification

### Optional Fields:
- `parent_account_number` - For hierarchical structure
- `description` - Detailed description
- `opening_balance` - Starting balance (decimal)
- `opening_balance_date` - Date (YYYY-MM-DD)
- `currency` - ISO code (default: INR)
- `is_active` - TRUE/FALSE (default: TRUE)
- `is_header_account` - TRUE/FALSE (default: FALSE)
- `tax_line` - Tax reporting line
- `bank_account_number` - Bank account number
- `bank_routing_number` - Bank routing number

### Example CSV Row:
```csv
1000,Cash in Hand,ASSET,CASH_BANK,,Petty cash and cash on hand,5000.00,2024-01-01,INR,TRUE,FALSE
```

## 🔧 **Validation Rules**

1. **File Validation**:
   - Must be CSV format
   - Maximum 5MB file size
   - UTF-8 encoding support

2. **Data Validation**:
   - Account numbers must be unique
   - Account type codes must exist in system
   - Detail type codes must match account type
   - Parent accounts must exist before children
   - Opening balances must be valid decimals
   - Dates must be in YYYY-MM-DD format

3. **Business Rules**:
   - No circular parent-child references
   - Account numbers cannot duplicate existing accounts
   - Detail types must be compatible with account types

## 🛡️ **Security & Permissions**

- **Upload access**: Superuser only
- **API authentication**: Token-based authentication required
- **File validation**: Strict file type and size limits
- **Transaction safety**: Database rollback on errors
- **Input sanitization**: All CSV data is validated and sanitized

## 📊 **Error Handling**

### Frontend:
- Real-time file validation
- Detailed error messages with row numbers
- Warning display for non-critical issues
- Progress indicators during upload

### Backend:
- Comprehensive validation before processing
- Transaction rollback on any error
- Detailed error logging
- Graceful handling of malformed data

## 🔄 **Integration Points**

1. **Chart of Accounts Page**: Seamless tab integration
2. **Account Management**: Automatic refresh after upload
3. **Authentication System**: Respects user permissions
4. **GL System**: Full integration with existing GL models

## 📈 **Performance Considerations**

- **Batch Processing**: Efficient bulk account creation
- **Database Optimization**: Single transaction for consistency
- **Memory Management**: Streaming CSV processing
- **Caching**: Account type and detail type caching during processing

## 🧪 **Testing**

### Test Scenarios:
1. **Valid CSV Upload**: Complete successful import
2. **Validation Mode**: Dry-run testing without import
3. **Error Handling**: Invalid data, duplicate accounts, missing references
4. **File Validation**: Wrong format, oversized files
5. **Permission Testing**: Non-superuser access restrictions

### Sample Test Data:
- Use the provided `COA_Template.csv` for testing
- Modify values to test various validation scenarios
- Test hierarchical account structures

## 🔮 **Future Enhancements**

Potential improvements for future versions:

1. **Export Functionality**: Export existing COA to CSV
2. **Update Mode**: Update existing accounts via CSV
3. **Bulk Operations**: Delete/deactivate accounts via CSV
4. **Advanced Mapping**: Custom field mapping for different CSV formats
5. **Import History**: Track and audit COA imports
6. **Scheduled Imports**: Automated periodic imports
7. **Excel Support**: Support for Excel file formats

## 📞 **Support**

For technical issues:
1. Check Django logs for detailed error messages
2. Verify account types and detail types exist in system
3. Ensure proper user permissions
4. Validate CSV format against template
5. Contact system administrator for complex issues

## 🎯 **Success Metrics**

The implementation successfully provides:
- ✅ Complete CSV upload functionality
- ✅ Comprehensive validation and error handling
- ✅ User-friendly interface with progress feedback
- ✅ Secure, permission-based access
- ✅ Full integration with existing GL system
- ✅ Professional documentation and templates
- ✅ Scalable architecture for future enhancements

This implementation transforms COA management from manual account creation to efficient bulk operations, significantly improving productivity for accounting setup and maintenance tasks. 