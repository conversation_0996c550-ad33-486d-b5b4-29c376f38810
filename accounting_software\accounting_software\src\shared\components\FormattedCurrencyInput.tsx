import React, { forwardRef, useState } from 'react';
import { TextField, TextFieldProps } from '@mui/material';
import { NumericFormat, NumericFormatProps } from 'react-number-format';

interface CustomProps {
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
  currencySymbol?: string;
  decimalScale?: number;
  allowNegative?: boolean;
}

const NumericFormatCustom = forwardRef<
  NumericFormatProps,
  CustomProps & NumericFormatProps
>(function NumericFormatCustom(props, ref) {
  const { onChange, currencySymbol = '', decimalScale = 2, allowNegative = false, ...other } = props;

  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value || '', // Keep empty string for zero values
          },
        });
      }}
      thousandSeparator=","
      decimalSeparator="."
      decimalScale={decimalScale}
      fixedDecimalScale={false} // Don't force decimal places on display
      allowNegative={allowNegative}
      placeholder="" // Empty placeholder like QuickBooks
    />
  );
});

interface FormattedCurrencyInputProps extends Omit<TextFieldProps, 'onChange'> {
  value: number | string;
  onChange: (event: { target: { name: string; value: string } }) => void;
  name: string;
  currencySymbol?: string;
  decimalScale?: number;
  allowNegative?: boolean;
}

const FormattedCurrencyInput: React.FC<FormattedCurrencyInputProps> = ({
  value,
  onChange,
  name,
  currencySymbol = '',
  decimalScale = 2,
  allowNegative = false,
  ...textFieldProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  
  // Convert value to number and check if it's zero or empty
  const numericValue = typeof value === 'string' ? parseFloat(value) || 0 : value || 0;
  const isZeroOrEmpty = numericValue === 0;
  
  // For display: always show blank if zero (QuickBooks style)
  const displayValue = isZeroOrEmpty ? '' : numericValue;

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    if (textFieldProps.onFocus) {
      textFieldProps.onFocus(event);
    }
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    if (textFieldProps.onBlur) {
      textFieldProps.onBlur(event);
    }
  };

  return (
    <TextField
      {...textFieldProps}
      name={name}
      value={displayValue}
      onChange={onChange}
      onFocus={handleFocus}
      onBlur={handleBlur}
      InputProps={{
        ...textFieldProps.InputProps,
        inputComponent: NumericFormatCustom as any,
        inputProps: {
          ...textFieldProps.InputProps?.inputProps,
          currencySymbol,
          decimalScale,
          allowNegative,
        },
      }}
      sx={{
        ...textFieldProps.sx,
        '& .MuiInputBase-input': {
          textAlign: 'right',
        },
      }}
    />
  );
};

export default FormattedCurrencyInput; 