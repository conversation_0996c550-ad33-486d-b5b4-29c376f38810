import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { PurchaseProduct, PurchaseProductFormData } from '../shared/types/purchase.types';

interface PurchaseProductContextType {
  purchaseProducts: PurchaseProduct[];
  addPurchaseProduct: (productData: PurchaseProductFormData) => Promise<PurchaseProduct>;
  updatePurchaseProduct: (product: PurchaseProduct) => Promise<void>;
  deletePurchaseProduct: (productId: string) => Promise<void>;
  getPurchaseProductById: (productId: string) => PurchaseProduct | undefined;
  isLoading: boolean;
  error: string | null;
  loadProducts: () => Promise<void>;
}

// Create a default context value to prevent undefined errors
const defaultContextValue: PurchaseProductContextType = {
  purchaseProducts: [],
  addPurchaseProduct: async () => { throw new Error('PurchaseProductProvider not initialized'); },
  updatePurchaseProduct: async () => { throw new Error('PurchaseProductProvider not initialized'); },
  deletePurchaseProduct: async () => { throw new Error('PurchaseProductProvider not initialized'); },
  getPurchaseProductById: () => undefined,
  loadProducts: async () => { throw new Error('PurchaseProductProvider not initialized'); },
  isLoading: false,
  error: null,
};

const PurchaseProductContext = createContext<PurchaseProductContextType>(defaultContextValue);

// Mock data for purchase products (fallback)
const mockPurchaseProducts: PurchaseProduct[] = [
  {
    id: 'PP001',
    name: 'Raw Material A',
    sku: 'RM-A-001',
    description: 'High-quality raw material for manufacturing',
    type: 'product',
    category: 'raw_materials',
    purchasePrice: 25.50,
    vendorId: 'V001',
    vendorName: 'Office Supplies Co.',
    vendorSKU: 'VS-RM-A-001',
    leadTime: 7,
    minimumOrderQuantity: 10,
    preferredVendor: true,
    trackInventory: true,
    inventoryQuantity: 150,
    lowStockAlert: true,
    lowStockThreshold: 30,
    status: 'active',

    notes: 'Essential raw material for production line A',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-03-15T14:30:00Z',
  },
  {
    id: 'PP002',
    name: 'Component B',
    sku: 'COMP-B-002',
    description: 'Precision-engineered component',
    type: 'product',
    category: 'components',
    purchasePrice: 45.75,
    vendorId: 'V002',
    vendorName: 'Tech Hardware Inc.',
    vendorSKU: 'TH-COMP-B-002',
    leadTime: 14,
    minimumOrderQuantity: 5,
    preferredVendor: true,
    trackInventory: true,
    inventoryQuantity: 75,
    lowStockAlert: true,
    lowStockThreshold: 15,
    status: 'active',

    notes: 'Used in assembly of product X',
    createdAt: '2024-01-20T11:15:00Z',
    updatedAt: '2024-03-12T09:45:00Z',
  },
  {
    id: 'PP003',
    name: 'Office Desk',
    sku: 'FURN-DESK-003',
    description: 'Standard office desk',
    type: 'product',
    category: 'office_supplies',
    purchasePrice: 350.00,
    vendorId: 'V003',
    vendorName: 'Furniture Depot',
    vendorSKU: 'FD-DESK-STD',
    leadTime: 21,
    minimumOrderQuantity: 1,
    preferredVendor: false,
    trackInventory: true,
    inventoryQuantity: 10,
    lowStockAlert: false,
    lowStockThreshold: 0,
    status: 'active',

    notes: 'Standard office furniture',
    createdAt: '2024-02-01T13:20:00Z',
    updatedAt: '2024-03-10T16:10:00Z',
  },
  {
    id: 'PP004',
    name: 'Cleaning Service',
    sku: 'SERV-CLEAN-004',
    description: 'Monthly office cleaning service',
    type: 'service',
    category: 'services',
    purchasePrice: 650.00,
    vendorId: 'V004',
    vendorName: 'Cleaning Services LLC',
    leadTime: 3,
    preferredVendor: true,
    trackInventory: false,
    status: 'active',

    notes: 'Monthly service contract',
    createdAt: '2024-02-10T09:30:00Z',
    updatedAt: '2024-03-08T11:25:00Z',
  },
  {
    id: 'PP005',
    name: 'Marketing Package',
    sku: 'SERV-MKTG-005',
    description: 'Digital marketing services bundle',
    type: 'bundle',
    category: 'services',
    purchasePrice: 4000.00,
    vendorId: 'V005',
    vendorName: 'Marketing Agency',
    leadTime: 7,
    preferredVendor: true,
    trackInventory: false,
    status: 'active',
    bundleItems: [
      { productId: 'PP006', name: 'Social Media Management', quantity: 1 },
      { productId: 'PP007', name: 'SEO Services', quantity: 1 },
      { productId: 'PP008', name: 'Content Creation', quantity: 1 }
    ],
    displayBundleItems: true,
    notes: 'Quarterly marketing package',
    createdAt: '2024-02-15T14:45:00Z',
    updatedAt: '2024-03-05T10:15:00Z',
  },
];

export const PurchaseProductProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [purchaseProducts, setPurchaseProducts] = useState<PurchaseProduct[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Load products from API with pagination handling
  const loadProducts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      let allProducts: any[] = [];
      let nextUrl: string | null = 'http://localhost:8000/api/sales/products/?page_size=1000';
      
      // Fetch all pages of products
      while (nextUrl) {
        console.log('🔍 Fetching products from:', nextUrl);
        const response = await fetch(nextUrl, {
          headers: {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ API Error Response:', errorText);
          throw new Error(`API error: ${response.status} ${response.statusText}\n${errorText}`);
        }

        const data = await response.json();
        console.log('✅ API Response:', data);
        
        // Handle both paginated and direct array responses
        if (data.results) {
          // Paginated response
          allProducts = [...allProducts, ...data.results];
          nextUrl = data.next; // Continue to next page if available
        } else if (Array.isArray(data)) {
          // Direct array response
          allProducts = data;
          nextUrl = null; // No more pages
        } else {
          // Single object or unexpected format
          allProducts = [data];
          nextUrl = null;
        }
      }

      console.log(`✅ Loaded ${allProducts.length} products from API:`, allProducts);
      
      // Transform API data to match our interface
      const transformedProducts = allProducts.map((product: any) => {
        console.log('Processing product:', {
          id: product.id,
          name: product.name,
          sku: product.sku,
          total_quantity_on_hand: product.total_quantity_on_hand,
          quantity_on_hand: product.quantity_on_hand,
          track_inventory: product.track_inventory,
          reorder_point: product.reorder_point,
        });
        
        const transformed = {
          id: product.id || product.product_id,
          name: product.name,
          sku: product.sku || '',
          description: product.description || '',
          type: product.product_type || 'product',
          category: product.category?.name || product.category || '',
          purchasePrice: parseFloat(product.cost_price || product.unit_price || 0),
          vendorId: product.preferred_vendor || '',
          vendorName: product.preferred_vendor || '',
          vendorSKU: '',
          leadTime: 0,
          minimumOrderQuantity: product.reorder_point || 1,
          preferredVendor: false,
          trackInventory: product.track_inventory || false,
          inventoryQuantity: parseFloat(product.total_quantity_on_hand || '0'),
          lowStockAlert: parseFloat(product.total_quantity_on_hand || '0') <= (product.reorder_point || 0),
          lowStockThreshold: product.reorder_point || 0,
          status: product.status || 'active',
          notes: '',
          attachments: [],
          createdAt: product.created_at || new Date().toISOString(),
          updatedAt: product.updated_at || new Date().toISOString(),
        };
        console.log(`✅ Transformed product ${product.name}:`, {
          ...transformed,
          inventoryDetails: {
            total_quantity_on_hand: product.total_quantity_on_hand,
            quantity_on_hand: product.quantity_on_hand,
            track_inventory: product.track_inventory,
            reorder_point: product.reorder_point,
          }
        });
        return transformed;
      });
      
      console.log('✅ All transformed products:', transformedProducts);
      setPurchaseProducts(transformedProducts);
    } catch (err) {
      console.error('❌ Error loading products:', err);
      setError(err instanceof Error ? err.message : 'Failed to load products from server');
      setPurchaseProducts([]); // Don't use mock data, show empty state instead
    } finally {
      setIsLoading(false);
    }
  };

  // Load products on mount
  useEffect(() => {
    loadProducts();
  }, []);

  const addPurchaseProduct = async (productData: PurchaseProductFormData): Promise<PurchaseProduct> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      
      // Transform our data to match the API format
      const apiProductData = {
        name: productData.name,
        sku: productData.sku,
        description: productData.description,
        product_type: productData.type,
        category: null, // Will need to handle category mapping
        unit_price: productData.purchasePrice,
        cost_price: productData.purchasePrice,
        preferred_vendor: productData.vendorName,
        track_inventory: productData.trackInventory,
        reorder_point: productData.minimumOrderQuantity,
        quantity_on_hand: productData.inventoryQuantity || 0,
        status: productData.status,
        taxable: true,
        // Workflow fields
        workflow_status: productData.workflowStatus || 'pending_sales_review',
        created_by_department: productData.createdByDepartment || 'purchase',
        purchase_completed: productData.purchaseCompleted || true,
        sales_completed: productData.salesCompleted || false,
        workflow_notes: productData.workflowNotes || '',
        // Note: GL account assignments (income_account_gl, expense_account_gl, inventory_asset_account_gl) 
        // are handled by the Accounting Department through the Django admin interface
      };

      console.log('🚀 Creating product via API:', apiProductData);

      const response = await fetch('http://localhost:8000/api/sales/products/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiProductData),
      });

      if (response.ok) {
        const createdProduct = await response.json();
        console.log('✅ Product created successfully:', createdProduct);
        
        // Transform the response back to our format
        const newProduct: PurchaseProduct = {
          id: createdProduct.id || createdProduct.product_id || uuidv4(),
          name: createdProduct.name,
          sku: createdProduct.sku || '',
          description: createdProduct.description || '',
          type: createdProduct.product_type || 'product',
          category: createdProduct.category?.name || '',
          purchasePrice: parseFloat(createdProduct.cost_price || createdProduct.unit_price || 0),
          vendorId: productData.vendorId || '',
          vendorName: createdProduct.preferred_vendor || productData.vendorName || '',
          vendorSKU: productData.vendorSKU || '',
          leadTime: productData.leadTime || 0,
          minimumOrderQuantity: createdProduct.reorder_point || productData.minimumOrderQuantity || 1,
          preferredVendor: productData.preferredVendor || false,
          trackInventory: createdProduct.track_inventory || false,
          inventoryQuantity: createdProduct.quantity_on_hand || 0,
          lowStockAlert: productData.lowStockAlert || false,
          lowStockThreshold: createdProduct.reorder_point || 0,
          status: createdProduct.status || 'active',
          notes: productData.notes || '',
          attachments: productData.attachments || [],
          createdAt: createdProduct.created_at || new Date().toISOString(),
          updatedAt: createdProduct.updated_at || new Date().toISOString(),
        };

        // Add to local state
        setPurchaseProducts((prevProducts) => [...prevProducts, newProduct]);
        
        // Show success message
        alert(`✅ Product "${newProduct.name}" created successfully!\n\nSKU: ${newProduct.sku}\nStatus: ${productData.workflowStatus || 'pending_sales_review'}\n\nThe product has been saved to the database and is now pending Sales Department review.`);
        
        return newProduct;
      } else {
        const errorData = await response.text();
        console.error('❌ API Error:', response.status, errorData);
        throw new Error(`Failed to create product: ${response.status} ${response.statusText}\n${errorData}`);
      }
    } catch (err) {
      console.error('❌ Error creating product:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create product';
      setError(errorMessage);
      
      // Show error alert
      alert(`❌ Error creating product:\n\n${errorMessage}\n\nPlease check your connection and try again.`);
      
      // Fallback: add to local state only
      const now = new Date().toISOString();
      const fallbackProduct: PurchaseProduct = {
        ...productData,
        id: `PP${uuidv4().substring(0, 8)}`,
        createdAt: now,
        updatedAt: now,
      };
      
      setPurchaseProducts((prevProducts) => [...prevProducts, fallbackProduct]);
      return fallbackProduct;
    } finally {
      setIsLoading(false);
    }
  };

  const updatePurchaseProduct = async (updatedProduct: PurchaseProduct): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      
      const apiProductData = {
        name: updatedProduct.name,
        sku: updatedProduct.sku,
        description: updatedProduct.description,
        product_type: updatedProduct.type,
        unit_price: updatedProduct.purchasePrice,
        cost_price: updatedProduct.purchasePrice,
        preferred_vendor: updatedProduct.vendorName,
        track_inventory: updatedProduct.trackInventory,
        reorder_point: updatedProduct.minimumOrderQuantity,
        quantity_on_hand: updatedProduct.inventoryQuantity,
        status: updatedProduct.status,
        // Note: GL account assignments are handled by the Accounting Department
      };

      const response = await fetch(`http://localhost:8000/api/sales/products/${updatedProduct.id}/`, {
        method: 'PUT',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiProductData),
      });

      if (response.ok) {
        console.log('✅ Product updated successfully');
        setPurchaseProducts((prevProducts) =>
          prevProducts.map((product) =>
            product.id === updatedProduct.id
              ? { ...updatedProduct, updatedAt: new Date().toISOString() }
              : product
          )
        );
        alert(`✅ Product "${updatedProduct.name}" updated successfully!`);
      } else {
        throw new Error(`Failed to update product: ${response.status} ${response.statusText}`);
      }
    } catch (err) {
      console.error('❌ Error updating product:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update product';
      setError(errorMessage);
      alert(`❌ Error updating product:\n\n${errorMessage}`);
      
      // Fallback: update local state only
      setPurchaseProducts((prevProducts) =>
        prevProducts.map((product) =>
          product.id === updatedProduct.id
            ? { ...updatedProduct, updatedAt: new Date().toISOString() }
            : product
        )
      );
    } finally {
      setIsLoading(false);
    }
  };

  const deletePurchaseProduct = async (productId: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`http://localhost:8000/api/sales/products/${productId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (response.ok) {
        console.log('✅ Product deleted successfully');
        setPurchaseProducts((prevProducts) => prevProducts.filter((product) => product.id !== productId));
        alert('✅ Product deleted successfully!');
      } else {
        throw new Error(`Failed to delete product: ${response.status} ${response.statusText}`);
      }
    } catch (err) {
      console.error('❌ Error deleting product:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete product';
      setError(errorMessage);
      alert(`❌ Error deleting product:\n\n${errorMessage}`);
      
      // Fallback: remove from local state only
      setPurchaseProducts((prevProducts) => prevProducts.filter((product) => product.id !== productId));
    } finally {
      setIsLoading(false);
    }
  };

  const getPurchaseProductById = (productId: string) => {
    return purchaseProducts.find((product) => product.id === productId);
  };

  const value = {
    purchaseProducts,
    addPurchaseProduct,
    updatePurchaseProduct,
    deletePurchaseProduct,
    getPurchaseProductById,
    loadProducts,
    isLoading,
    error,
  };

  return <PurchaseProductContext.Provider value={value}>{children}</PurchaseProductContext.Provider>;
};

export const usePurchaseProducts = (): PurchaseProductContextType => {
  const context = useContext(PurchaseProductContext);
  
  // The context should never be undefined now since we have a default value
  // But we can still check if it's the default (uninitialized) context
  if (context === defaultContextValue) {
    console.error('❌ PurchaseProductProvider not found in component tree!');
    console.error('❌ Make sure your component is wrapped with <PurchaseProductProvider>');
    // Don't throw an error, just use the default context with helpful error messages
  }
  
  return context;
}; 