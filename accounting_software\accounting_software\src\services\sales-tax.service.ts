export interface SalesTaxOption {
  id: number;
  tax_type: 'input' | 'output';
  description: string;
  rate: number;
  remarks?: string;
}

// Create a separate API instance for Sales Tax endpoints
const salesTaxApi = {
  async get(url: string) {
    const token = localStorage.getItem('token');
    console.log('Sales Tax API GET request:', { url: `http://localhost:8000/api${url}`, hasToken: !!token });
    
    const response = await fetch(`http://localhost:8000/api${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      }
    });
    
    console.log('Sales Tax API GET response:', { status: response.status, statusText: response.statusText, url });
    
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      console.error('Sales Tax API GET error:', { status: response.status, statusText: response.statusText, errorText });
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    const data = await response.json();
    console.log('Sales Tax API GET success:', { url, dataKeys: Object.keys(data) });
    return { data };
  },
  
  async post(url: string, requestData: any) {
    const token = localStorage.getItem('token');
    console.log('Sales Tax API POST request:', { url: `http://localhost:8000/api${url}`, requestData });
    
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      },
      body: JSON.stringify(requestData)
    });
    
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      console.error('Sales Tax API POST error:', { status: response.status, statusText: response.statusText, errorText, sentData: requestData });
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    return { data: await response.json() };
  },
  
  async patch(url: string, requestData: any) {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      },
      body: JSON.stringify(requestData)
    });
    if (!response.ok) {
      let errorText = 'Failed to read error response';
      try {
        const errorData = await response.json();
        errorText = JSON.stringify(errorData);
      } catch {
        errorText = await response.text().catch(() => 'Failed to read error response');
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }
    return { data: await response.json() };
  },
  
  async delete(url: string) {
    const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api${url}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      }
    });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }
};

class SalesTaxService {
  private baseURL = '/sales-tax';

  /**
   * Get all sales tax options
   */
  async getAllSalesTaxes(): Promise<SalesTaxOption[]> {
    try {
      const response = await salesTaxApi.get(this.baseURL + '/');
      // Handle both paginated and non-paginated responses
      if (response.data && typeof response.data === 'object' && 'results' in response.data) {
        // Paginated response - return the results array
        return response.data.results;
      } else if (Array.isArray(response.data)) {
        // Direct array response
        return response.data;
      } else {
        // Unexpected format - return empty array to prevent errors
        console.warn(`Unexpected response format from ${this.baseURL}:`, response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching sales taxes:', error);
      throw error;
    }
  }

  /**
   * Get input tax options only
   */
  async getInputTaxes(): Promise<SalesTaxOption[]> {
    try {
      const response = await salesTaxApi.get(this.baseURL + '/?tax_type=input');
      // Handle both paginated and non-paginated responses
      if (response.data && typeof response.data === 'object' && 'results' in response.data) {
        return response.data.results;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn(`Unexpected response format from ${this.baseURL}:`, response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching input taxes:', error);
      throw error;
    }
  }

  /**
   * Get output tax options only  
   */
  async getOutputTaxes(): Promise<SalesTaxOption[]> {
    try {
      const response = await salesTaxApi.get(this.baseURL + '/?tax_type=output');
      // Handle both paginated and non-paginated responses
      if (response.data && typeof response.data === 'object' && 'results' in response.data) {
        return response.data.results;
      } else if (Array.isArray(response.data)) {
        return response.data;
      } else {
        console.warn(`Unexpected response format from ${this.baseURL}:`, response.data);
        return [];
      }
    } catch (error) {
      console.error('Error fetching output taxes:', error);
      throw error;
    }
  }

  /**
   * Create a new sales tax
   */
  async createSalesTax(salesTax: Omit<SalesTaxOption, 'id'>): Promise<SalesTaxOption> {
    try {
      const response = await salesTaxApi.post(this.baseURL + '/', salesTax);
      return response.data;
    } catch (error) {
      console.error('Error creating sales tax:', error);
      throw error;
    }
  }

  /**
   * Update an existing sales tax
   */
  async updateSalesTax(id: number, salesTax: Partial<SalesTaxOption>): Promise<SalesTaxOption> {
    try {
      const response = await salesTaxApi.patch(`${this.baseURL}/${id}/`, salesTax);
      return response.data;
    } catch (error) {
      console.error('Error updating sales tax:', error);
      throw error;
    }
  }

  /**
   * Delete a sales tax
   */
  async deleteSalesTax(id: number): Promise<void> {
    try {
      await salesTaxApi.delete(`${this.baseURL}/${id}/`);
    } catch (error) {
      console.error('Error deleting sales tax:', error);
      throw error;
    }
  }
}

export const salesTaxService = new SalesTaxService();
export default salesTaxService; 