from django.core.management.base import BaseCommand
from django.db import transaction
from gl.models import AccountType, DetailType, Account


class Command(BaseCommand):
    help = 'Set up current vs non-current detail types for proper balance sheet classification'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Current vs Non-Current Detail Types...'))
        
        with transaction.atomic():
            # Get Asset and Liability account types
            try:
                asset_type = AccountType.objects.get(type='ASSET')
                liability_type = AccountType.objects.get(type='LIABILITY')
            except AccountType.DoesNotExist:
                self.stdout.write(self.style.ERROR('Asset or Liability account types not found. Please create them first.'))
                return

            # Current Assets Detail Types
            current_asset_types = [
                ('CASH_BANK', 'Cash and Bank', 'Cash on hand and bank deposits', 'CURRENT'),
                ('AR_TRADE', 'Accounts Receivable - Trade', 'Amounts owed by customers from sales', 'CURRENT'),
                ('AR_OTHER', 'Accounts Receivable - Other', 'Non-trade receivables and advances', 'CURRENT'),
                ('INVENTORY', 'Inventory', 'Stock of goods for sale', 'CURRENT'),
                ('PREPAID_EXP', 'Prepaid Expenses', 'Expenses paid in advance', 'CURRENT'),
                ('SHORT_INVEST', 'Short-term Investments', 'Investments maturing within 1 year', 'CURRENT'),
                ('OTHER_CURRENT_ASSETS', 'Other Current Assets', 'Other assets convertible within 1 year', 'CURRENT'),
            ]

            # Non-Current Assets Detail Types
            non_current_asset_types = [
                ('PPE_LAND', 'Property, Plant & Equipment - Land', 'Land and buildings', 'NON_CURRENT'),
                ('PPE_MACHINERY', 'Property, Plant & Equipment - Machinery', 'Machinery and equipment', 'NON_CURRENT'),
                ('PPE_FURNITURE', 'Property, Plant & Equipment - Furniture', 'Furniture and fixtures', 'NON_CURRENT'),
                ('PPE_VEHICLES', 'Property, Plant & Equipment - Vehicles', 'Company vehicles', 'NON_CURRENT'),
                ('INTANGIBLE', 'Intangible Assets', 'Patents, trademarks, software licenses', 'NON_CURRENT'),
                ('LONG_INVEST', 'Long-term Investments', 'Investments held for more than 1 year', 'NON_CURRENT'),
                ('GOODWILL', 'Goodwill', 'Goodwill from acquisitions', 'NON_CURRENT'),
                ('OTHER_NONCURRENT_ASSETS', 'Other Non-Current Assets', 'Other long-term assets', 'NON_CURRENT'),
            ]

            # Current Liabilities Detail Types
            current_liability_types = [
                ('AP_TRADE', 'Accounts Payable - Trade', 'Amounts owed to suppliers', 'CURRENT'),
                ('AP_OTHER', 'Accounts Payable - Other', 'Non-trade payables and advances', 'CURRENT'),
                ('ACCRUED_EXP', 'Accrued Expenses', 'Expenses incurred but not yet paid', 'CURRENT'),
                ('SHORT_DEBT', 'Short-term Debt', 'Loans and borrowings due within 1 year', 'CURRENT'),
                ('CURRENT_LTD', 'Current Portion of Long-term Debt', 'Current portion of long-term loans', 'CURRENT'),
                ('TAX_PAYABLE', 'Taxes Payable', 'Income tax and other taxes owed', 'CURRENT'),
                ('OTHER_CURRENT_LIAB', 'Other Current Liabilities', 'Other liabilities due within 1 year', 'CURRENT'),
            ]

            # Non-Current Liabilities Detail Types
            non_current_liability_types = [
                ('LONG_DEBT', 'Long-term Debt', 'Loans and borrowings due after 1 year', 'NON_CURRENT'),
                ('DEFERRED_TAX', 'Deferred Tax Liabilities', 'Future tax obligations', 'NON_CURRENT'),
                ('EMPLOYEE_BENEFITS', 'Employee Benefits', 'Pension and other long-term benefits', 'NON_CURRENT'),
                ('OTHER_NONCURRENT_LIAB', 'Other Non-Current Liabilities', 'Other long-term obligations', 'NON_CURRENT'),
            ]

            # Create Asset Detail Types
            sort_order = 10
            for code, name, description, classification in current_asset_types + non_current_asset_types:
                detail_type, created = DetailType.objects.get_or_create(
                    account_type=asset_type,
                    code=code,
                    defaults={
                        'name': name,
                        'description': description,
                        'balance_sheet_classification': classification,
                        'sort_order': sort_order,
                        'is_active': True,
                    }
                )
                if created:
                    self.stdout.write(f'Created Asset Detail Type: {name}')
                else:
                    # Update existing
                    detail_type.balance_sheet_classification = classification
                    detail_type.save()
                    self.stdout.write(f'Updated Asset Detail Type: {name}')
                sort_order += 10

            # Create Liability Detail Types
            sort_order = 10
            for code, name, description, classification in current_liability_types + non_current_liability_types:
                detail_type, created = DetailType.objects.get_or_create(
                    account_type=liability_type,
                    code=code,
                    defaults={
                        'name': name,
                        'description': description,
                        'balance_sheet_classification': classification,
                        'sort_order': sort_order,
                        'is_active': True,
                    }
                )
                if created:
                    self.stdout.write(f'Created Liability Detail Type: {name}')
                else:
                    # Update existing
                    detail_type.balance_sheet_classification = classification
                    detail_type.save()
                    self.stdout.write(f'Updated Liability Detail Type: {name}')
                sort_order += 10

            # Fix the header account issue - "Bank Accounts-Saving Accounts" should NOT be a header
            try:
                saving_account = Account.objects.get(account_name__icontains='Bank Accounts-Saving Accounts')
                if saving_account.is_header_account:
                    saving_account.is_header_account = False
                    saving_account.save()
                    self.stdout.write(f'Fixed header account: {saving_account.account_name} - now set as regular account')
            except Account.DoesNotExist:
                self.stdout.write('Bank Accounts-Saving Accounts not found')
            except Account.MultipleObjectsReturned:
                # Handle multiple matches
                saving_accounts = Account.objects.filter(account_name__icontains='Bank Accounts-Saving Accounts')
                for acc in saving_accounts:
                    if acc.is_header_account:
                        acc.is_header_account = False
                        acc.save()
                        self.stdout.write(f'Fixed header account: {acc.account_name} - now set as regular account')

            self.stdout.write(self.style.SUCCESS('Successfully set up Current vs Non-Current Detail Types!'))
            
            # Print summary
            self.stdout.write('\n=== SUMMARY ===')
            self.stdout.write(f'Asset Detail Types: {DetailType.objects.filter(account_type=asset_type).count()}')
            self.stdout.write(f'Liability Detail Types: {DetailType.objects.filter(account_type=liability_type).count()}')
            self.stdout.write(f'Current Classifications: {DetailType.objects.filter(balance_sheet_classification="CURRENT").count()}')
            self.stdout.write(f'Non-Current Classifications: {DetailType.objects.filter(balance_sheet_classification="NON_CURRENT").count()}') 