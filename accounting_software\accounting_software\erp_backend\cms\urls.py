"""
Cash Management System URL Configuration

URL patterns for all Cash Management System endpoints including:
- Cash accounts management
- Cash transactions and transfers  
- Bank reconciliation
- Cash flow forecasting
- Treasury reporting and dashboards

Following RESTful API design principles with proper versioning.
"""

from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

# Create a router for ViewSets
router = DefaultRouter()

# Register ViewSets with the router
router.register(r'account-types', views.CashAccountTypeViewSet, basename='cash-account-type')
router.register(r'accounts', views.CashAccountViewSet, basename='cash-account')
router.register(r'transactions', views.CashTransactionViewSet, basename='cash-transaction')
router.register(r'transfers', views.CashTransferViewSet, basename='cash-transfer')
router.register(r'bank-statements', views.BankStatementViewSet, basename='bank-statement')
router.register(r'bank-entries', views.BankStatementEntryViewSet, basename='bank-statement-entry')
router.register(r'forecasts', views.CashFlowForecastViewSet, basename='cash-flow-forecast')

# Define URL patterns
urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
    
    # Additional standalone endpoints
    path('reports/cash-flow/', views.cash_flow_report, name='cash-flow-report'),
    path('dashboard/treasury/', views.treasury_dashboard, name='treasury-dashboard'),
    
    # GL Integration endpoints
    path('gl/cash-accounts/', views.get_gl_cash_accounts, name='gl-cash-accounts'),
    path('gl/expense-accounts/', views.get_gl_expense_accounts, name='gl-expense-accounts'),
    path('gl/income-accounts/', views.get_gl_income_accounts, name='gl-income-accounts'),
    
    # TDS endpoints
    path('tds/create-defaults/', views.create_default_tds_rates, name='create-default-tds-rates'),
    
    # Debug endpoint
    path('debug/gl-accounts/', views.debug_gl_accounts, name='debug-gl-accounts'),

    # Special endpoints for GL integration
    path('api/accounts/', views.get_gl_cash_accounts, name='get-gl-cash-accounts'),
    path('api/expense-accounts/', views.get_gl_expense_accounts, name='get-gl-expense-accounts'),
    path('api/income-accounts/', views.get_gl_income_accounts, name='get-gl-income-accounts'),
    path('debug/gl-accounts/', views.debug_gl_accounts, name='debug-gl-accounts'),
    path('create-tds-rates/', views.create_default_tds_rates, name='create-default-tds-rates'),
    
    # New payment creation endpoint
    path('api/payments/create/', views.create_payment, name='create-payment'),
    
    # New receipt creation endpoint
    path('api/receipts/create/', views.create_receipt, name='create-receipt'),
]

# Add app namespace for URL reversing
app_name = 'cms' 