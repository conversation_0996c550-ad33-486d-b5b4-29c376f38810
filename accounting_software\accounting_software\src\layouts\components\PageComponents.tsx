import React, { ReactNode } from 'react';
import { Box, styled, Typography } from '@mui/material';

const PageContainerStyled = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(2),
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  border: `1px solid ${theme.palette.divider}`,
  width: '100%',
  boxSizing: 'border-box',
  margin: 0,
  overflow: 'hidden',
}));

export const PageHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
}));

interface PageContainerProps {
  children: ReactNode;
  title?: string;
  actions?: ReactNode;
}

export const PageContainer: React.FC<PageContainerProps> = ({ 
  children, 
  title, 
  actions 
}) => {
  return (
    <PageContainerStyled>
      {(title || actions) && (
        <PageHeader>
          {title && (
            <Typography variant="h5" fontWeight="bold">
              {title}
            </Typography>
          )}
          {actions && (
            <Box>
              {actions}
            </Box>
          )}
        </PageHeader>
      )}
      {children}
    </PageContainerStyled>
  );
};