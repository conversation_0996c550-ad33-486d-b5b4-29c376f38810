import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Alert,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  Card,
  CardContent,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CompleteIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { InventoryTransfer, transferService } from '../services/transfer.service';
import { inventoryService } from '../services/inventory.service';
import InventoryTransferForm from '../components/InventoryTransferForm';

const InventoryTransfersPage: React.FC = () => {
  const navigate = useNavigate();
  const [transfers, setTransfers] = useState<InventoryTransfer[]>([]);
  const [warehouses, setWarehouses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTransfer, setSelectedTransfer] = useState<InventoryTransfer | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editTransfer, setEditTransfer] = useState<InventoryTransfer | null>(null);

  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [warehouseFilter, setWarehouseFilter] = useState('');

  useEffect(() => {
    loadTransfers();
    loadWarehouses();
  }, []);

  const loadTransfers = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await transferService.getTransfers({
        search: searchTerm,
        status: statusFilter || undefined,
        from_warehouse: warehouseFilter ? Number(warehouseFilter) : undefined,
      });
      setTransfers(response.results || response || []);
    } catch (err: any) {
      setError(err.message || 'Failed to load transfers');
    } finally {
      setLoading(false);
    }
  };

  const loadWarehouses = async () => {
    try {
      const response = await inventoryService.getWarehouses();
      setWarehouses(response.results || response || []);
    } catch (err) {
      console.error('Error loading warehouses:', err);
    }
  };

  useEffect(() => {
    const delayedLoad = setTimeout(() => {
      loadTransfers();
    }, 300);

    return () => clearTimeout(delayedLoad);
  }, [searchTerm, statusFilter, warehouseFilter]);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, transfer: InventoryTransfer) => {
    setAnchorEl(event.currentTarget);
    setSelectedTransfer(transfer);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedTransfer(null);
  };

  const handleEdit = () => {
    if (selectedTransfer) {
      setEditTransfer(selectedTransfer);
      setShowForm(true);
    }
    handleMenuClose();
  };

  const handleDelete = async () => {
    if (!selectedTransfer?.transfer_id) return;

    if (window.confirm('Are you sure you want to delete this transfer?')) {
      try {
        await transferService.deleteTransfer(selectedTransfer.transfer_id);
        loadTransfers();
      } catch (err: any) {
        setError(err.message || 'Failed to delete transfer');
      }
    }
    handleMenuClose();
  };

  const handleSetPending = async () => {
    if (!selectedTransfer?.transfer_id) return;

    try {
      await transferService.setPending(selectedTransfer.transfer_id);
      loadTransfers();
    } catch (err: any) {
      setError(err.message || 'Failed to set transfer to pending');
    }
    handleMenuClose();
  };

  const handleComplete = async () => {
    if (!selectedTransfer?.transfer_id) return;

    if (window.confirm('Are you sure you want to complete this transfer? This action cannot be undone.')) {
      try {
        await transferService.completeTransfer(selectedTransfer.transfer_id);
        loadTransfers();
      } catch (err: any) {
        setError(err.message || 'Failed to complete transfer');
      }
    }
    handleMenuClose();
  };

  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case 'DRAFT': return 'default';
      case 'PENDING': return 'warning';
      case 'IN_TRANSIT': return 'info';
      case 'COMPLETED': return 'success';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const canEdit = (transfer: InventoryTransfer): boolean => {
    return transfer.status === 'DRAFT';
  };

  const canSetPending = (transfer: InventoryTransfer): boolean => {
    return transfer.status === 'DRAFT';
  };

  const canComplete = (transfer: InventoryTransfer): boolean => {
    return transfer.status === 'PENDING';
  };

  const canDelete = (transfer: InventoryTransfer): boolean => {
    return transfer.status === 'DRAFT';
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditTransfer(null);
    loadTransfers();
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setEditTransfer(null);
  };

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Inventory Transfers
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage transfers between warehouses
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadTransfers}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setShowForm(true)}
          >
            New Transfer
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search transfers"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value="DRAFT">Draft</MenuItem>
                  <MenuItem value="PENDING">Pending</MenuItem>
                  <MenuItem value="IN_TRANSIT">In Transit</MenuItem>
                  <MenuItem value="COMPLETED">Completed</MenuItem>
                  <MenuItem value="CANCELLED">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Warehouse</InputLabel>
                <Select
                  value={warehouseFilter}
                  onChange={(e) => setWarehouseFilter(e.target.value)}
                  label="Warehouse"
                >
                  <MenuItem value="">All Warehouses</MenuItem>
                  {warehouses.map(warehouse => (
                    <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                      {warehouse.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setWarehouseFilter('');
                }}
              >
                Clear
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Transfers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Transfer #</TableCell>
              <TableCell>From Warehouse</TableCell>
              <TableCell>To Warehouse</TableCell>
              <TableCell>Transfer Date</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Items</TableCell>
              <TableCell>Total Quantity</TableCell>
              <TableCell>Created By</TableCell>
              <TableCell width={50}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {transfers.map((transfer) => (
              <TableRow key={transfer.transfer_id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {transfer.transfer_number}
                  </Typography>
                </TableCell>
                <TableCell>{transfer.from_warehouse_name}</TableCell>
                <TableCell>{transfer.to_warehouse_name}</TableCell>
                <TableCell>
                  {new Date(transfer.transfer_date).toLocaleDateString()}
                </TableCell>
                <TableCell>
                  <Chip
                    label={transfer.status}
                    color={getStatusColor(transfer.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>{transfer.total_items || 0}</TableCell>
                <TableCell>
                  {Number(transfer.total_quantity || 0).toFixed(4)}
                </TableCell>
                <TableCell>{transfer.created_by_name}</TableCell>
                <TableCell>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, transfer)}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
            {transfers.length === 0 && !loading && (
              <TableRow>
                <TableCell colSpan={9} align="center">
                  <Typography color="text.secondary">
                    No transfers found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedTransfer && canEdit(selectedTransfer) && (
          <MenuItem onClick={handleEdit}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit</ListItemText>
          </MenuItem>
        )}
        
        {selectedTransfer && canSetPending(selectedTransfer) && (
          <MenuItem onClick={handleSetPending}>
            <ListItemIcon>
              <SendIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Set Pending</ListItemText>
          </MenuItem>
        )}
        
        {selectedTransfer && canComplete(selectedTransfer) && (
          <MenuItem onClick={handleComplete}>
            <ListItemIcon>
              <CompleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Complete Transfer</ListItemText>
          </MenuItem>
        )}
        
        {selectedTransfer && canDelete(selectedTransfer) && (
          <MenuItem onClick={handleDelete}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete</ListItemText>
          </MenuItem>
        )}
      </Menu>

      {/* Transfer Form */}
      <InventoryTransferForm
        open={showForm}
        onClose={handleCloseForm}
        onSuccess={handleFormSuccess}
        transfer={editTransfer}
      />
    </PageContainer>
  );
};

export default InventoryTransfersPage; 