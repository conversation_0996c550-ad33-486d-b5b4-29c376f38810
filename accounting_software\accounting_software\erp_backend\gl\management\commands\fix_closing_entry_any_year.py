"""
Universal Django management command to fix closing entries for any fiscal year
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Sum
from gl.models import JournalEntry, JournalEntryLine, Account
from account.models import Company
from decimal import Decimal


class Command(BaseCommand):
    help = 'Fix closing entry for any fiscal year to include all revenue and expense accounts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--year',
            type=int,
            required=True,
            help='Fiscal year to fix closing entry for (required)',
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Actually perform the fix (without this, it\'s a dry run)',
        )

    def handle(self, *args, **options):
        year = options['year']
        confirm = options['confirm']
        
        self.stdout.write(
            self.style.SUCCESS(f'🔧 Fixing Closing Entry for Fiscal Year {year}')
        )
        
        # Get company for fiscal year settings
        company = Company.objects.first()
        if not company:
            self.stdout.write(
                self.style.ERROR('❌ No company configured in the system')
            )
            return

        # Calculate fiscal year period
        period_start = company.fiscal_year_start.replace(year=year)
        period_end = company.fiscal_year_end.replace(year=year)
        
        # Adjust for fiscal years that span calendar years
        if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
           (company.fiscal_year_end.month == company.fiscal_year_start.month and 
            company.fiscal_year_end.day < company.fiscal_year_start.day):
            period_end = period_end.replace(year=year + 1)
        
        self.stdout.write(f'📊 Fiscal Year {year}: {period_start} to {period_end}')
        
        # Find the existing closing entry
        entry_number = f'CLOSE-{year}'
        closing_entry = JournalEntry.objects.filter(entry_number=entry_number).first()
        
        if not closing_entry:
            self.stdout.write(
                self.style.ERROR(f'❌ No closing entry found with number: {entry_number}')
            )
            self.stdout.write('   Create the entry first using: python manage.py fix_retained_earnings_closing')
            return
        
        # Get required accounts
        retained_earnings = Account.objects.filter(
            account_name__icontains='retained earnings'
        ).first()
        
        income_summary = Account.objects.filter(account_number='39999').first()
        
        if not retained_earnings or not income_summary:
            self.stdout.write(
                self.style.ERROR('❌ Required accounts not found (Retained Earnings or Income Summary)')
            )
            return
        
        # Calculate what the closing entry should be
        revenue_accounts = Account.objects.filter(
            account_type__type='REVENUE',
            is_active=True,
            is_header_account=False
        ).exclude(account_number='39999')
        
        expense_accounts = Account.objects.filter(
            account_type__type='EXPENSE',
            is_active=True,
            is_header_account=False
        ).exclude(account_number='39999')
        
        total_revenue = Decimal('0.00')
        total_expenses = Decimal('0.00')
        
        self.stdout.write('\n=== REVENUE ACCOUNTS ===')
        for account in revenue_accounts:
            debits = account.journal_lines.filter(
                journal_entry__transaction_date__gte=period_start,
                journal_entry__transaction_date__lte=period_end,
                journal_entry__status='POSTED'
            ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
            
            credits = account.journal_lines.filter(
                journal_entry__transaction_date__gte=period_start,
                journal_entry__transaction_date__lte=period_end,
                journal_entry__status='POSTED'
            ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
            
            net_revenue = credits - debits
            if net_revenue != 0:
                total_revenue += net_revenue
                self.stdout.write(f'  {account.account_number} - {account.account_name}: {net_revenue:,.2f}')
        
        self.stdout.write('\n=== EXPENSE ACCOUNTS ===')
        for account in expense_accounts:
            debits = account.journal_lines.filter(
                journal_entry__transaction_date__gte=period_start,
                journal_entry__transaction_date__lte=period_end,
                journal_entry__status='POSTED'
            ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
            
            credits = account.journal_lines.filter(
                journal_entry__transaction_date__gte=period_start,
                journal_entry__transaction_date__lte=period_end,
                journal_entry__status='POSTED'
            ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
            
            net_expense = debits - credits
            if net_expense != 0:
                total_expenses += net_expense
                self.stdout.write(f'  {account.account_number} - {account.account_name}: {net_expense:,.2f}')
        
        net_income = total_revenue - total_expenses
        
        self.stdout.write(f'\n=== SUMMARY ===')
        self.stdout.write(f'💰 Total Revenue: {total_revenue:,.2f}')
        self.stdout.write(f'💸 Total Expenses: {total_expenses:,.2f}')
        self.stdout.write(f'📊 Net Income: {net_income:,.2f}')
        
        if not confirm:
            self.stdout.write(
                self.style.WARNING('\n🔍 DRY RUN - Use --confirm to update closing entry')
            )
            self.stdout.write('   Closing entry should have:')
            if total_revenue > 0:
                self.stdout.write(f'   - Dr. Revenue accounts: {total_revenue:,.2f}')
            if total_expenses > 0:
                self.stdout.write(f'   - Cr. Expense accounts: {total_expenses:,.2f}')
            if net_income > 0:
                self.stdout.write(f'   - Cr. Retained Earnings: {net_income:,.2f}')
            elif net_income < 0:
                self.stdout.write(f'   - Dr. Retained Earnings: {abs(net_income):,.2f}')
            return
        
        # Update the closing entry
        try:
            with transaction.atomic():
                # Clear existing lines
                closing_entry.journal_lines.all().delete()
                
                line_number = 1
                
                # Close revenue accounts (Debit revenue accounts)
                for account in revenue_accounts:
                    debits = account.journal_lines.filter(
                        journal_entry__transaction_date__gte=period_start,
                        journal_entry__transaction_date__lte=period_end,
                        journal_entry__status='POSTED'
                    ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
                    
                    credits = account.journal_lines.filter(
                        journal_entry__transaction_date__gte=period_start,
                        journal_entry__transaction_date__lte=period_end,
                        journal_entry__status='POSTED'
                    ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
                    
                    net_revenue = credits - debits
                    if net_revenue > 0:
                        JournalEntryLine.objects.create(
                            journal_entry=closing_entry,
                            line_number=line_number,
                            account=account,
                            description=f'Close FY{year} Revenue',
                            debit_amount=net_revenue,
                            credit_amount=Decimal('0.00')
                        )
                        line_number += 1
                
                # Close expense accounts (Credit expense accounts)
                for account in expense_accounts:
                    debits = account.journal_lines.filter(
                        journal_entry__transaction_date__gte=period_start,
                        journal_entry__transaction_date__lte=period_end,
                        journal_entry__status='POSTED'
                    ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
                    
                    credits = account.journal_lines.filter(
                        journal_entry__transaction_date__gte=period_start,
                        journal_entry__transaction_date__lte=period_end,
                        journal_entry__status='POSTED'
                    ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
                    
                    net_expense = debits - credits
                    if net_expense > 0:
                        JournalEntryLine.objects.create(
                            journal_entry=closing_entry,
                            line_number=line_number,
                            account=account,
                            description=f'Close FY{year} Expense',
                            debit_amount=Decimal('0.00'),
                            credit_amount=net_expense
                        )
                        line_number += 1
                
                # Transfer net income to Retained Earnings
                if net_income > 0:
                    # Profit: Credit Retained Earnings
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        line_number=line_number,
                        account=retained_earnings,
                        description=f'FY{year} Net Income',
                        debit_amount=Decimal('0.00'),
                        credit_amount=net_income
                    )
                elif net_income < 0:
                    # Loss: Debit Retained Earnings
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        line_number=line_number,
                        account=retained_earnings,
                        description=f'FY{year} Net Loss',
                        debit_amount=abs(net_income),
                        credit_amount=Decimal('0.00')
                    )
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Updated closing entry: {closing_entry.entry_number}')
                )
                self.stdout.write(
                    self.style.SUCCESS(f'📝 Created {line_number} journal lines')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error updating closing entry: {e}')
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(f'🎉 Closing entry for FY{year} has been fixed!')
        ) 