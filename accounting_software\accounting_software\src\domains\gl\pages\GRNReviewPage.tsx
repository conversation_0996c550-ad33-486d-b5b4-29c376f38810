import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Card,
  CardContent,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';
import { grnService, GRN, GRNItem, GRNStats } from '../../inventory/services/grn.service';

// Extended GRN interface for review page with additional financial fields
interface GRNReview extends GRN {
  po_number?: string;
  vendor_name?: string;
  received_date?: string;
  tax_amount?: number;
  grand_total?: number;
  currency?: string;
}



const GRNReviewPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [grns, setGrns] = useState<GRNReview[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedGRN, setSelectedGRN] = useState<GRNReview | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [stats, setStats] = useState<GRNStats | null>(null);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadGRNs();
    loadStats();
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadGRNs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem('token');
      const queryParams = new URLSearchParams();
      
      queryParams.append('page', (page + 1).toString());
      if (searchTerm) queryParams.append('search', searchTerm);
      if (statusFilter) queryParams.append('status', statusFilter);

      const response = await fetch(`http://localhost:8000/api/inventory/grns/?${queryParams}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load GRNs');
      }

      const data = await response.json();
      setGrns(data.results || []);
      setTotalCount(data.count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRNs');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/inventory/grns/stats/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load GRN statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Failed to load GRN stats:', err);
    }
  };

  const handleViewGRN = async (grn: GRNReview) => {
    try {
      console.log('🔍 Loading GRN details for:', grn);
      
      // Use the grn_id field instead of id, and use the grnService
      if (!grn.grn_id) {
        throw new Error('GRN ID is missing');
      }
      
      const data = await grnService.getGRN(grn.grn_id);
      console.log('✅ GRN details loaded:', data);
      
      // Map the service data to the review format
      const reviewData: GRNReview = {
        ...data,
        po_number: data.purchase_order_number || '',
        vendor_name: data.vendor_name || '',
        received_date: data.receipt_date || '',
        tax_amount: 0, // Calculate from items if needed
        grand_total: data.total_value || 0,
        currency: 'USD', // Default currency
      };
      
      setSelectedGRN(reviewData);
      setViewDialogOpen(true);
    } catch (err) {
      console.error('❌ Failed to load GRN details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load GRN details');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      draft: { color: 'default' as const, label: 'Draft' },
      posted: { color: 'success' as const, label: 'Posted' },
      cancelled: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const columns = [
    {
      field: 'grn_number',
      headerName: 'GRN Number',
      flex: 1,
      renderCell: ({ row }: { row: GRN }) => (
        <Typography variant="body2" fontWeight="medium">
          {row.grn_number}
        </Typography>
      ),
    },
    {
      field: 'po_number',
      headerName: 'PO Number',
      flex: 1,
    },
    {
      field: 'vendor_name',
      headerName: 'Vendor',
      flex: 1.5,
    },
    {
      field: 'received_date',
      headerName: 'Received Date',
      flex: 1,
      renderCell: ({ row }: { row: GRNReview }) => formatDate(row.received_date || row.receipt_date || ''),
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell: ({ row }: { row: GRNReview }) => getStatusChip(row.status),
    },
    {
      field: 'total_quantity',
      headerName: 'Total Qty',
      flex: 0.8,
      renderCell: ({ row }: { row: GRNReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right' }}>
          {Number(row.total_quantity || 0).toFixed(2)}
        </Typography>
      ),
    },
    {
      field: 'total_value',
      headerName: 'Subtotal',
      flex: 1,
      renderCell: ({ row }: { row: GRNReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'medium' }}>
          {formatCurrency(Number(row.total_value || 0))}
        </Typography>
      ),
    },
    {
      field: 'tax_amount',
      headerName: 'Tax Amount',
      flex: 1,
      renderCell: ({ row }: { row: GRNReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', color: 'primary.main' }}>
          {formatCurrency(Number(row.tax_amount || 0))}
        </Typography>
      ),
    },
    {
      field: 'grand_total',
      headerName: 'Grand Total',
      flex: 1.2,
      renderCell: ({ row }: { row: GRNReview }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'bold' }}>
          {formatCurrency(Number(row.grand_total || row.total_value || 0))}
        </Typography>
      ),
    },
  ];

  const rowActions = [
    {
      icon: <ViewIcon />,
      label: 'View Details',
      onClick: (row: GRNReview) => handleViewGRN(row),
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold" display="flex" alignItems="center" gap={1}>
            <InventoryIcon />
            Goods Receipt Notes Review
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Financial review of goods receipt notes for accounting purposes
          </Typography>
        </Box>
      </PageHeader>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <BusinessIcon color="primary" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total GRNs</Typography>
                    <Typography variant="h6" fontWeight="bold">{stats.total_grns}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <CalendarIcon color="success" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Posted</Typography>
                    <Typography variant="h6" fontWeight="bold">{stats.posted_grns}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <AccountBalanceIcon color="info" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Value</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {formatCurrency(stats.total_value)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrendingUpIcon color="warning" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Tax</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {formatCurrency(0)} {/* Tax calculation not available in GRN stats */}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <DataTable
        columns={columns}
        rows={grns}
        loading={loading}
        totalCount={totalCount}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        rowActions={rowActions}
        serverSide={true}
        showToolbar={true}
        title="Goods Receipt Notes"
      />

      {/* View GRN Dialog */}
      <Dialog 
        open={viewDialogOpen} 
        onClose={() => setViewDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" display="flex" alignItems="center" gap={1}>
            <InventoryIcon />
            GRN Details - {selectedGRN?.grn_number}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedGRN && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>Basic Information</Typography>
                    <Typography variant="body2" color="text.secondary">PO Number</Typography>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      {selectedGRN.po_number}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Vendor</Typography>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      {selectedGRN.vendor_name}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Received Date</Typography>
                    <Typography variant="body1" gutterBottom>
                      {formatDate(selectedGRN.received_date)}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Status</Typography>
                    <Box sx={{ mb: 2 }}>
                      {getStatusChip(selectedGRN.status)}
                    </Box>
                    
                    {selectedGRN.warehouse_name && (
                      <>
                        <Typography variant="body2" color="text.secondary">Warehouse</Typography>
                        <Typography variant="body1" gutterBottom>
                          {selectedGRN.warehouse_name}
                        </Typography>
                      </>
                    )}
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>Financial Summary</Typography>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Total Quantity:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {Number(selectedGRN.total_quantity || 0).toFixed(2)}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Subtotal:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(Number(selectedGRN.total_value || 0))}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Tax Amount:</Typography>
                      <Typography variant="body2" color="primary.main">
                        {formatCurrency(Number(selectedGRN.tax_amount || 0))}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" sx={{ pt: 1, borderTop: 1, borderColor: 'divider' }}>
                      <Typography variant="h6">Grand Total:</Typography>
                      <Typography variant="h6" fontWeight="bold">
                        {formatCurrency(Number(selectedGRN.grand_total || 0))}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">Currency:</Typography>
                      <Typography variant="body1">{selectedGRN.currency}</Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                {selectedGRN.items && selectedGRN.items.length > 0 && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Line Items</Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Product</TableCell>
                              <TableCell align="right">Quantity</TableCell>
                              <TableCell align="right">Unit Price</TableCell>
                              <TableCell align="right">Tax Amount</TableCell>
                              <TableCell align="right">Total Amount</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {selectedGRN.items.map((item, index) => (
                              <TableRow key={item.grn_item_id || index}>
                                <TableCell>{item.product_name}</TableCell>
                                <TableCell align="right">
                                  {Number(item.quantity_received || 0).toFixed(2)}
                                </TableCell>
                                <TableCell align="right">
                                  {formatCurrency(Number(item.unit_cost || item.po_unit_price || 0))}
                                </TableCell>
                                <TableCell align="right">
                                  {formatCurrency(0)} {/* Tax amount not available in GRN items */}
                                </TableCell>
                                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                                  {formatCurrency(Number(item.total_cost || 0))}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                  </Grid>
                )}
                
                {selectedGRN.notes && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Notes</Typography>
                      <Typography variant="body1">{selectedGRN.notes}</Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default GRNReviewPage; 