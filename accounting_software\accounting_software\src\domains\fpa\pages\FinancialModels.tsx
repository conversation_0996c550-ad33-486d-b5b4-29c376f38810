import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  TextField,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Save,
  PlayArrow,
  Assessment,
  TrendingUp,
  Info,
  Calculate,
  ModelTraining,
  Settings,
  Timeline,
} from '@mui/icons-material';

// Sample financial model data
const baseScenarioData = [
  { year: 'Year 1', revenue: 1000000, expenses: 750000, netIncome: 250000 },
  { year: 'Year 2', revenue: 1200000, expenses: 850000, netIncome: 350000 },
  { year: 'Year 3', revenue: 1440000, expenses: 960000, netIncome: 480000 },
  { year: 'Year 4', revenue: 1728000, expenses: 1080000, netIncome: 648000 },
  { year: 'Year 5', revenue: 2073600, expenses: 1215000, netIncome: 858600 },
];

const sensitivityData = [
  { parameter: 'Revenue Growth Rate', baseCase: 20, optimistic: 30, pessimistic: 10, impact: 'High' },
  { parameter: 'Cost Growth Rate', baseCase: 13, optimistic: 8, pessimistic: 18, impact: 'High' },
  { parameter: 'Tax Rate', baseCase: 25, optimistic: 20, pessimistic: 30, impact: 'Medium' },
  { parameter: 'Discount Rate', baseCase: 10, optimistic: 8, pessimistic: 12, impact: 'Medium' },
];

const FinancialModels: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [modelName, setModelName] = useState('Growth Model 2024');
  const [selectedScenario, setSelectedScenario] = useState('base');
  
  // Model parameters
  const [revenueGrowth, setRevenueGrowth] = useState(20);
  const [expenseGrowth, setExpenseGrowth] = useState(13);
  const [taxRate, setTaxRate] = useState(25);
  const [discountRate, setDiscountRate] = useState(10);

  // Scenario cards data
  const scenarios = [
    { 
      name: 'Base Case', 
      key: 'base', 
      color: '#2196F3',
      description: 'Most likely scenario based on current trends',
      metrics: { npv: 1850000, irr: 24, payback: 3.2 }
    },
    { 
      name: 'Optimistic', 
      key: 'optimistic', 
      color: '#4CAF50',
      description: 'Best case scenario with favorable conditions',
      metrics: { npv: 2650000, irr: 32, payback: 2.8 }
    },
    { 
      name: 'Pessimistic', 
      key: 'pessimistic', 
      color: '#F44336',
      description: 'Worst case scenario with challenging conditions',
      metrics: { npv: 950000, irr: 16, payback: 4.1 }
    },
  ];

  const ModelParameterCard = ({ title, value, setValue, min, max, unit = '%' }: any) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
          <Tooltip title="Adjust this parameter to see impact on financial projections">
            <IconButton size="small">
              <Info fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        
        <Box sx={{ px: 2, mb: 2 }}>
          <Slider
            value={value}
            onChange={(e, newValue) => setValue(newValue)}
            min={min}
            max={max}
            step={0.5}
            marks={[
              { value: min, label: `${min}${unit}` },
              { value: max, label: `${max}${unit}` },
            ]}
            valueLabelDisplay="on"
            valueLabelFormat={(val) => `${val}${unit}`}
          />
        </Box>
        
        <TextField
          fullWidth
          size="small"
          label="Value"
          value={value}
          onChange={(e) => setValue(parseFloat(e.target.value) || 0)}
          InputProps={{
            endAdornment: unit,
          }}
        />
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: '#f5f7fa', minHeight: '100vh' }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2c3e50' }}>
            Financial Models
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Build and analyze sophisticated financial models with scenario planning
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button variant="outlined" startIcon={<Calculate />}>
            Recalculate
          </Button>
          <Button variant="outlined" startIcon={<Settings />}>
            Model Settings
          </Button>
          <Button variant="contained" startIcon={<Save />} sx={{ background: 'linear-gradient(45deg, #FF6B6B 30%, #FF8E53 90%)' }}>
            Save Model
          </Button>
        </Box>
      </Box>

      {/* Model Info */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Model Name"
                value={modelName}
                onChange={(e) => setModelName(e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Model Type</InputLabel>
                <Select value="dcf" label="Model Type">
                  <MenuItem value="dcf">DCF (Discounted Cash Flow)</MenuItem>
                  <MenuItem value="growth">Growth Model</MenuItem>
                  <MenuItem value="valuation">Company Valuation</MenuItem>
                  <MenuItem value="budget">Budget Model</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Time Horizon</InputLabel>
                <Select value="5years" label="Time Horizon">
                  <MenuItem value="3years">3 Years</MenuItem>
                  <MenuItem value="5years">5 Years</MenuItem>
                  <MenuItem value="10years">10 Years</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)}>
          <Tab icon={<ModelTraining />} label="Model Builder" />
          <Tab icon={<Assessment />} label="Scenarios" />
          <Tab icon={<TrendingUp />} label="Sensitivity Analysis" />
          <Tab icon={<Timeline />} label="Projections" />
        </Tabs>
      </Paper>

      {/* Model Builder Tab */}
      {selectedTab === 0 && (
        <Box>
          {/* Parameter Controls */}
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Model Parameters
          </Typography>
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={3}>
              <ModelParameterCard
                title="Revenue Growth"
                value={revenueGrowth}
                setValue={setRevenueGrowth}
                min={0}
                max={50}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <ModelParameterCard
                title="Expense Growth"
                value={expenseGrowth}
                setValue={setExpenseGrowth}
                min={0}
                max={40}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <ModelParameterCard
                title="Tax Rate"
                value={taxRate}
                setValue={setTaxRate}
                min={0}
                max={50}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <ModelParameterCard
                title="Discount Rate"
                value={discountRate}
                setValue={setDiscountRate}
                min={0}
                max={20}
              />
            </Grid>
          </Grid>

          {/* Financial Projections Placeholder */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Financial Projections
              </Typography>
              <Box sx={{ height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f8f9fa', borderRadius: 2 }}>
                <Typography variant="h6" color="text.secondary">
                  📊 Interactive Financial Charts
                  <br />
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Real-time projections based on your model parameters will be displayed here
                  </Typography>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Scenarios Tab */}
      {selectedTab === 1 && (
        <Box>
          {/* Scenario Cards */}
          <Grid container spacing={3} mb={4}>
            {scenarios.map((scenario) => (
              <Grid item xs={12} md={4} key={scenario.key}>
                <Card 
                  sx={{ 
                    height: '100%',
                    border: selectedScenario === scenario.key ? `3px solid ${scenario.color}` : '1px solid #e0e0e0',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 3,
                    }
                  }}
                  onClick={() => setSelectedScenario(scenario.key)}
                >
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                        {scenario.name}
                      </Typography>
                      <Chip 
                        label="Active" 
                        size="small" 
                        sx={{ 
                          backgroundColor: scenario.color, 
                          color: 'white',
                          visibility: selectedScenario === scenario.key ? 'visible' : 'hidden'
                        }} 
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" mb={3}>
                      {scenario.description}
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Typography variant="h6" sx={{ fontWeight: 'bold', color: scenario.color }}>
                            ${(scenario.metrics.npv / 1000000).toFixed(1)}M
                          </Typography>
                          <Typography variant="caption">NPV</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Typography variant="h6" sx={{ fontWeight: 'bold', color: scenario.color }}>
                            {scenario.metrics.irr}%
                          </Typography>
                          <Typography variant="caption">IRR</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={4}>
                        <Box textAlign="center">
                          <Typography variant="h6" sx={{ fontWeight: 'bold', color: scenario.color }}>
                            {scenario.metrics.payback}
                          </Typography>
                          <Typography variant="caption">Payback</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Scenario Comparison Chart Placeholder */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Scenario Comparison
              </Typography>
              <Box sx={{ height: 350, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f8f9fa', borderRadius: 2 }}>
                <Typography variant="h6" color="text.secondary">
                  📊 Scenario Comparison Charts
                  <br />
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Side-by-side comparison of NPV, IRR, and payback across scenarios
                  </Typography>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Sensitivity Analysis Tab */}
      {selectedTab === 2 && (
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Sensitivity Analysis
          </Typography>
          
          <Card>
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                      <TableCell sx={{ fontWeight: 'bold' }}>Parameter</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Base Case</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Optimistic</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Pessimistic</TableCell>
                      <TableCell align="center" sx={{ fontWeight: 'bold' }}>Impact</TableCell>
                      <TableCell align="center" sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sensitivityData.map((row) => (
                      <TableRow key={row.parameter} sx={{ '&:hover': { backgroundColor: '#f5f5f5' } }}>
                        <TableCell sx={{ fontWeight: 500 }}>{row.parameter}</TableCell>
                        <TableCell align="right">{row.baseCase}%</TableCell>
                        <TableCell align="right" sx={{ color: '#4CAF50', fontWeight: 'bold' }}>
                          {row.optimistic}%
                        </TableCell>
                        <TableCell align="right" sx={{ color: '#F44336', fontWeight: 'bold' }}>
                          {row.pessimistic}%
                        </TableCell>
                        <TableCell align="center">
                          <Chip 
                            label={row.impact} 
                            size="small"
                            color={row.impact === 'High' ? 'error' : 'warning'}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <Button size="small" startIcon={<PlayArrow />}>
                            Test
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>

          {/* Sensitivity Chart Placeholder */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                NPV Sensitivity to Key Variables
              </Typography>
              <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f8f9fa', borderRadius: 2 }}>
                <Typography variant="h6" color="text.secondary">
                  📈 Sensitivity Analysis Charts
                  <br />
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Interactive tornado charts and sensitivity graphs will be displayed here
                  </Typography>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Projections Tab */}
      {selectedTab === 3 && (
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Detailed Financial Projections
          </Typography>
          
          <Card>
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                      <TableCell sx={{ fontWeight: 'bold' }}>Year</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Revenue</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Expenses</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>EBITDA</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Net Income</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>Cash Flow</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>NPV</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {baseScenarioData.map((row, index) => (
                      <TableRow key={row.year} sx={{ '&:hover': { backgroundColor: '#f5f5f5' } }}>
                        <TableCell sx={{ fontWeight: 500 }}>{row.year}</TableCell>
                        <TableCell align="right">${row.revenue.toLocaleString()}</TableCell>
                        <TableCell align="right">${row.expenses.toLocaleString()}</TableCell>
                        <TableCell align="right">${(row.netIncome * 1.2).toLocaleString()}</TableCell>
                        <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                          ${row.netIncome.toLocaleString()}
                        </TableCell>
                        <TableCell align="right">${(row.netIncome * 0.85).toLocaleString()}</TableCell>
                        <TableCell align="right">
                          ${(row.netIncome * 0.85 / Math.pow(1 + discountRate/100, index + 1)).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default FinancialModels;