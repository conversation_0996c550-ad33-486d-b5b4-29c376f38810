import React from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Visibility, GetApp } from '@mui/icons-material';

const RecentPurchases: React.FC = () => {
  const purchases = [
    {
      id: 'BILL-001',
      vendor: 'Office Supplies Co.',
      date: '2024-03-15',
      amount: 1250.00,
      status: 'Paid',
    },
    {
      id: 'BILL-002',
      vendor: 'Tech Hardware Inc.',
      date: '2024-03-12',
      amount: 3450.75,
      status: 'Pending',
    },
    {
      id: 'PO-001',
      vendor: 'Furniture Depot',
      date: '2024-03-10',
      amount: 5200.00,
      status: 'Approved',
    },
    {
      id: 'BILL-004',
      vendor: 'Cleaning Services LLC',
      date: '2024-03-08',
      amount: 750.50,
      status: 'Overdue',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'overdue':
        return 'error';
      case 'approved':
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Vendor</TableCell>
            <TableCell>Date</TableCell>
            <TableCell align="right">Amount</TableCell>
            <TableCell>Status</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {purchases.map((purchase) => (
            <TableRow key={purchase.id} hover>
              <TableCell>{purchase.id}</TableCell>
              <TableCell>{purchase.vendor}</TableCell>
              <TableCell>{new Date(purchase.date).toLocaleDateString()}</TableCell>
              <TableCell align="right">
                ${purchase.amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </TableCell>
              <TableCell>
                <Chip
                  label={purchase.status}
                  color={getStatusColor(purchase.status) as any}
                  size="small"
                />
              </TableCell>
              <TableCell align="right">
                <Tooltip title="View">
                  <IconButton size="small">
                    <Visibility fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Download">
                  <IconButton size="small">
                    <GetApp fontSize="small" />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default RecentPurchases; 