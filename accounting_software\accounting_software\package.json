{"name": "accounting_software", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.3", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@mui/x-date-pickers": "^7.29.4", "@reduxjs/toolkit": "^2.2.1", "@types/uuid": "^10.0.0", "axios": "^1.6.7", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "formik": "^2.4.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "notistack": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-number-format": "^5.4.4", "react-redux": "^9.1.0", "react-router-dom": "^6.22.1", "recharts": "^2.15.1", "uuid": "^11.1.0", "yup": "^1.3.3", "zod": "^3.25.42"}, "devDependencies": {"@types/node": "^20.11.19", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.0"}}