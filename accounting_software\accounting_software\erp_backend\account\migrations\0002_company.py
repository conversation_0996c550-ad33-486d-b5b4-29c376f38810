# Generated by Django 4.2.21 on 2025-06-01 04:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='Company name', max_length=255)),
                ('registration_number', models.CharField(help_text='Company registration number', max_length=100, unique=True)),
                ('tax_id', models.CharField(help_text='Tax ID/VAT number', max_length=100)),
                ('address', models.TextField(help_text='Company address')),
                ('phone', models.CharField(help_text='Contact phone number', max_length=20)),
                ('email', models.EmailField(help_text='Contact email', max_length=254)),
                ('accounting_year_start', models.DateField(help_text='Start date of accounting year')),
                ('accounting_year_end', models.DateField(help_text='End date of accounting year')),
                ('fiscal_year_start', models.DateField(help_text='Start date of fiscal year')),
                ('fiscal_year_end', models.DateField(help_text='End date of fiscal year')),
                ('sales_tax_registered', models.BooleanField(default=False, help_text='Whether the company is registered for sales tax')),
                ('sales_tax_number', models.CharField(blank=True, help_text='Sales tax registration number', max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Companies',
                'ordering': ['name'],
            },
        ),
    ]
