// Employee Types for HR Module
export interface Contact {
  id: number;
  name: string;
  contact_type: 'customer' | 'vendor' | 'employee' | 'other';
  email: string;
  phone?: string;
  address?: string;
  website?: string;
  tax_id?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ContactEmployee {
  contact: Contact;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  gender?: string;
  mobile?: string;
  employee_id?: string;
  position?: string;
  department?: string;
  street_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  created_at: string;
  updated_at: string;
}

export interface Employee {
  contact_employee: ContactEmployee;
  join_date: string;
  employment_type: 'full-time' | 'part-time' | 'contract' | 'intern';
  salary: number;
  status: 'Active' | 'On Leave' | 'Terminated';
  created_at: string;
  updated_at: string;
  
  // Computed properties
  full_name: string;
  email: string;
  phone?: string;
}

export interface EmployeeFormData {
  // Contact fields
  name: string;
  email: string;
  phone?: string;
  address?: string;
  website?: string;
  tax_id?: string;
  notes?: string;
  is_active: boolean;
  
  // Contact Employee fields
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  gender?: string;
  mobile?: string;
  employee_id?: string;
  position?: string;
  department?: string;
  street_address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  
  // HR Employee fields
  join_date: string;
  employment_type: 'full-time' | 'part-time' | 'contract' | 'intern';
  salary: number;
  status: 'Active' | 'On Leave' | 'Terminated';
}

export interface Department {
  id: number;
  name: string;
  code: string;
  description?: string;
  manager?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DepartmentFormData {
  name: string;
  code: string;
  description?: string;
  manager?: string;
  is_active: boolean;
}

// API Response types
export interface EmployeesApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Employee[];
}

export interface DepartmentsApiResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Department[];
}

// Constants
export const EMPLOYMENT_TYPES = [
  { value: 'full-time', label: 'Full Time' },
  { value: 'part-time', label: 'Part Time' },
  { value: 'contract', label: 'Contract' },
  { value: 'intern', label: 'Intern' }
] as const;

export const EMPLOYMENT_STATUSES = [
  { value: 'Active', label: 'Active' },
  { value: 'On Leave', label: 'On Leave' },
  { value: 'Terminated', label: 'Terminated' }
] as const;

export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer-not-to-say', label: 'Prefer not to say' }
] as const; 