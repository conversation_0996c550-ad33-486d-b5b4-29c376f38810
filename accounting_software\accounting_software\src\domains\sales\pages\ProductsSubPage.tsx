import React, { useState, useCallback } from 'react';
import {
  <PERSON>po<PERSON>,
  <PERSON>,
  Grid,
  Chip,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { 
  Inventory, 
  AttachMoney, 
  LocalShipping,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, ResponsiveContainer,
} from 'recharts';
import StatCard from '../../../shared/components/StatCard';
import DataTable, { RowAction, commonRowActions } from '../../../shared/components/DataTable';
import { PageContainer } from '../../../layouts/components/PageComponents';
import { useProducts } from '../../../contexts/ProductContext';
import { Product, PRODUCT_CATEGORIES } from '../../../shared/types/product.types';
import { useTheme } from '@mui/material/styles';
import ProductFormModal from '../components/ProductFormModal';

const ProductsSubPage: React.FC = () => {
  const theme = useTheme();
  const { products, getCategoryName, deleteProduct, updateProduct } = useProducts();
  const [isLoading] = useState(false);
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Filter only products (not services)
  const physicalProducts = products.filter(p => p.type === 'product');

  // Calculate product-specific statistics
  const productStats = {
    totalProducts: physicalProducts.length,
    totalValue: physicalProducts.reduce((sum, product) => {
      if (product.trackInventory) {
        return sum + (product.costPrice * (product.inventoryQuantity || 0));
      }
      return sum;
    }, 0),
    lowStockCount: physicalProducts.filter(p => 
      p.trackInventory && 
      p.lowStockAlert && 
      (p.inventoryQuantity || 0) <= (p.lowStockThreshold || 0)
    ).length,
    averagePrice: physicalProducts.length > 0 
      ? physicalProducts.reduce((sum, p) => sum + p.salesPrice, 0) / physicalProducts.length 
      : 0,
    categoryCounts: Object.entries(
      physicalProducts.reduce((acc, product) => {
        const category = product.category;
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    ).map(([category, count]) => ({
      name: getCategoryName(category),
      value: count,
    })),
  };

  // Handle edit product
  const handleEditProduct = useCallback((product: Product) => {
    setSelectedProduct(product);
    setIsProductFormOpen(true);
  }, []);

  // Handle delete product
  const handleDeleteProduct = useCallback((product: Product) => {
    if (window.confirm(`Are you sure you want to delete ${product.name}?`)) {
      try {
        deleteProduct(product.id);
      } catch (error) {
        console.error('Error deleting product:', error);
      }
    }
  }, [deleteProduct]);

  // Handle view product
  const handleViewProduct = useCallback((product: Product) => {
    console.log('View product details:', product);
  }, []);

  // Custom row actions for the data table
  const productRowActions: RowAction[] = [
    commonRowActions.edit(handleEditProduct),
    commonRowActions.delete(handleDeleteProduct),
    {
      icon: <VisibilityIcon fontSize="small" color="info" />,
      label: 'View Details',
      tooltip: 'View product details',
      onClick: handleViewProduct,
    },
  ];

  // Handle close product form
  const handleCloseProductForm = useCallback(() => {
    setIsProductFormOpen(false);
    setSelectedProduct(null);
  }, []);

  // Get unique categories for the searchable dropdown
  const categoryOptions = PRODUCT_CATEGORIES.map(category => ({
    value: category.value,
    label: category.label
  }));

  // Handle cell change for searchable dropdown
  const handleCellChange = (row: Product, field: string, value: any) => {
    if (field === 'category') {
      const updatedProduct = { ...row, category: value };
      updateProduct(updatedProduct);
    }
  };

  return (
    <PageContainer
      title="📦 Products"
      actions={
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setIsProductFormOpen(true)}
        >
          Add Product
        </Button>
      }
    >
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Products"
            value={productStats.totalProducts}
            icon={<Inventory />}
            trend={+5}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Inventory Value"
            value={`$${productStats.totalValue.toLocaleString()}`}
            icon={<AttachMoney />}
            trend={+12}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Low Stock Items"
            value={productStats.lowStockCount}
            icon={<WarningIcon />}
            trend={-2}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Average Price"
            value={`$${productStats.averagePrice.toFixed(2)}`}
            icon={<LocalShipping />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Products by Category
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={productStats.categoryCounts}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value" fill={theme.palette.primary.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Inventory Status
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">In Stock</Typography>
                  <Typography variant="body2" color="success.main" fontWeight="bold">
                    {physicalProducts.filter(p => p.trackInventory && (p.inventoryQuantity || 0) > (p.lowStockThreshold || 0)).length}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Low Stock</Typography>
                  <Typography variant="body2" color="warning.main" fontWeight="bold">
                    {productStats.lowStockCount}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <Typography variant="body2">Out of Stock</Typography>
                  <Typography variant="body2" color="error.main" fontWeight="bold">
                    {physicalProducts.filter(p => p.trackInventory && (p.inventoryQuantity || 0) === 0).length}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">No Tracking</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {physicalProducts.filter(p => !p.trackInventory).length}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Products Table */}
      <Box sx={{ mt: 4 }}>
        <DataTable
          title="Product List"
          columns={[
            { field: 'name', headerName: 'Product Name', flex: 1 },
            { field: 'sku', headerName: 'SKU', flex: 0.5 },
            { 
              field: 'category', 
              headerName: 'Category', 
              flex: 0.8,
              valueGetter: ({ row }) => getCategoryName(row.category),
              searchable: true,
              options: categoryOptions,
              optionLabel: 'label',
              optionValue: 'value'
            },
            { 
              field: 'salesPrice', 
              headerName: 'Sales Price', 
              flex: 0.5,
              type: 'currency',
              align: 'right',
              currencyCode: ({ row }) => row.currency || 'USD'
            },
            { 
              field: 'inventoryQuantity', 
              headerName: 'Stock', 
              flex: 0.4,
              align: 'center',
              renderCell: ({ row }) => {
                if (!row.trackInventory) {
                  return <Chip label="N/A" size="small" variant="outlined" />;
                }
                const qty = row.inventoryQuantity || 0;
                const threshold = row.lowStockThreshold || 0;
                const color = qty === 0 ? 'error' : qty <= threshold ? 'warning' : 'success';
                return <Chip label={qty} size="small" color={color} />;
              }
            },
            { 
              field: 'status', 
              headerName: 'Status', 
              flex: 0.5,
              renderCell: ({ row }) => (
                <Chip 
                  label={row.status === 'active' ? 'Active' : 'Inactive'} 
                  color={row.status === 'active' ? 'success' : 'error'}
                  size="small"
                />
              )
            },
          ]}
          rows={physicalProducts}
          loading={isLoading}
          checkboxSelection
          rowActions={productRowActions}
          onCellChange={handleCellChange}
        />
      </Box>

      {/* Product Form Modal */}
      <ProductFormModal
        open={isProductFormOpen}
        onClose={handleCloseProductForm}
        product={selectedProduct}
      />
    </PageContainer>
  );
};

export default ProductsSubPage; 