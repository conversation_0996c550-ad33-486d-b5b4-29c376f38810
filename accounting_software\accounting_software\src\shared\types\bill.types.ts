import { Vendor } from './purchase.types';
import { TaxRate } from './tax.types';

export type BillStatus = 'draft' | 'received' | 'pending' | 'partial' | 'paid' | 'overdue' | 'cancelled';
export type RecurringFrequency = 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';

export interface BillItem {
  id?: string;
  productId: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  taxRateId?: string;
  taxRate?: number;
  taxAmount?: number;
  subtotal: number;
  total: number;
  type: 'product' | 'service' | 'expense';
}

export interface FileAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  file?: File;
  url?: string;
  uploadedAt?: string;
}

export interface RecurringBillSettings {
  enabled: boolean;
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  interval: number;
  startDate: string;
  endDate?: string;
  dayOfMonth?: number; // Only used for monthly frequency
}

export interface BillFormData {
  vendorId: number | string; // Support both number (from API) and string (for compatibility)
  vendorName?: string;
  billNumber: string;
  billDate: string;
  dueDate: string;
  paymentTerms: string;
  currency: string;
  items: BillItem[];
  notes?: string;
  terms?: string;
  subtotal: number;
  taxAmount: number;
  taxTotal?: number;
  total: number;
  status?: BillStatus;
  date?: string;
  lineItems?: BillItem[];
  attachments?: FileAttachment[];
  recurringSettings?: RecurringBillSettings;
}

export interface Bill extends BillFormData {
  id: string;
  createdAt: string;
  updatedAt: string;
  status: BillStatus;
  vendor: Vendor;
  paidAmount: number;
  balanceDue: number;
  lastReminderSent?: string;
  parentBillId?: string; // For recurring bills, reference to the original template
  recurringSequence?: number; // For recurring bills, which number in the sequence
}

export interface BillPayment {
  id: string;
  billId: string;
  amount: number;
  paymentDate: string;
  paymentMethod: string;
  reference?: string;
  notes?: string;
  createdAt: string;
}

export const PAYMENT_METHODS = [
  { value: 'cash', label: 'Cash' },
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'credit_card', label: 'Credit Card' },
  { value: 'debit_card', label: 'Debit Card' },
  { value: 'paypal', label: 'PayPal' },
  { value: 'cheque', label: 'Cheque' },
  { value: 'other', label: 'Other' },
];

export const RECURRING_FREQUENCIES = [
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'yearly', label: 'Yearly' },
  { value: 'custom', label: 'Custom' },
];

export const getStatusColor = (status: BillStatus): string => {
  switch (status) {
    case 'draft':
      return '#9e9e9e'; // Gray
    case 'received':
      return '#2196f3'; // Blue
    case 'pending':
      return '#673ab7'; // Deep Purple
    case 'partial':
      return '#ff9800'; // Orange
    case 'paid':
      return '#4caf50'; // Green
    case 'overdue':
      return '#f44336'; // Red
    case 'cancelled':
      return '#795548'; // Brown
    default:
      return '#9e9e9e'; // Gray
  }
};

export const getStatusLabel = (status: BillStatus): string => {
  return status.charAt(0).toUpperCase() + status.slice(1);
}; 