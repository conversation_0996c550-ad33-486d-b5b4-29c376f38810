import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  FormHelperText,
  FormGroup,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const roles = [
  { value: 'admin', label: 'Administrator' },
  { value: 'accountant', label: 'Accountant' },
  { value: 'sales_manager', label: 'Sales Manager' },
  { value: 'purchase_manager', label: 'Purchase Manager' },
  { value: 'inventory_manager', label: 'Inventory Manager' },
  { value: 'hr_manager', label: 'HR Manager' },
];

const modules = [
  'accounting',
  'sales',
  'purchases',
  'inventory',
  'hr',
];

const accessLevels = [
  { value: 'FULL', label: 'Full Access', color: 'success' },
  { value: 'MODERATE', label: 'Moderate Access', color: 'primary' },
  { value: 'BASIC', label: 'Basic Access', color: 'default' },
];

const validationSchema = Yup.object({
  firstName: Yup.string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters'),
  lastName: Yup.string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters'),
  username: Yup.string()
    .required('Username is required')
    .min(4, 'Username must be at least 4 characters'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: Yup.string()
    .required('Please confirm your password')
    .oneOf([Yup.ref('password')], 'Passwords must match'),
  role: Yup.string(),
  permissions: Yup.array().of(Yup.object({
    module: Yup.string().required(),
    accessLevel: Yup.string().required()
  })),
});

interface AddUserFormProps {
  onSubmit: (values: any) => void;
  onCancel: () => void;
  initialValues?: {
    firstName?: string;
    lastName?: string;
    username?: string;
    email?: string;
    role?: string;
    permissions?: Array<{
      module: string;
      accessLevel: string;
    }>;
  };
}

const AddUserForm: React.FC<AddUserFormProps> = ({ onSubmit, onCancel, initialValues }) => {
  const [selectedModules, setSelectedModules] = useState<Record<string, string>>(
    initialValues?.permissions?.reduce((acc, perm) => ({
      ...acc,
      [perm.module]: perm.accessLevel
    }), {}) || {}
  );

  const formik = useFormik({
    initialValues: {
      firstName: initialValues?.firstName || '',
      lastName: initialValues?.lastName || '',
      username: initialValues?.username || '',
      email: initialValues?.email || '',
      password: '',
      confirmPassword: '',
      role: initialValues?.role || '',
      permissions: initialValues?.permissions || [],
    },
    validationSchema,
    onSubmit: (values) => {
      const permissionsArray = Object.entries(selectedModules).map(([mod, level]) => ({
        module: mod,
        accessLevel: level
      }));

      const dataToSendToBackend = {
        username: values.username,
        email: values.email,
        password: values.password,
        first_name: values.firstName,
        last_name: values.lastName,
        role: values.role,
        permissions: permissionsArray
      };

      console.log("AddUserForm submitting data:", dataToSendToBackend);
      onSubmit(dataToSendToBackend);
    },
  });

  const handleModuleAccessChange = (module: string, accessLevel: string) => {
    const updatedSelectedModules = { ...selectedModules, [module]: accessLevel };
    setSelectedModules(updatedSelectedModules);

    const permissionsArray = Object.entries(updatedSelectedModules).map(([mod, level]) => ({
        module: mod,
        accessLevel: level
    }));

    formik.setFieldValue('permissions', permissionsArray);
  };

  return (
    <Box sx={{ p: 2 }}>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              name="firstName"
              label="First Name"
              value={formik.values.firstName}
              onChange={formik.handleChange}
              error={formik.touched.firstName && Boolean(formik.errors.firstName)}
              helperText={formik.touched.firstName && formik.errors.firstName}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              name="lastName"
              label="Last Name"
              value={formik.values.lastName}
              onChange={formik.handleChange}
              error={formik.touched.lastName && Boolean(formik.errors.lastName)}
              helperText={formik.touched.lastName && formik.errors.lastName}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              name="username"
              label="Username"
              value={formik.values.username}
              onChange={formik.handleChange}
              error={formik.touched.username && Boolean(formik.errors.username)}
              helperText={formik.touched.username && formik.errors.username}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              name="email"
              label="Email"
              type="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              name="password"
              label="Password"
              type="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              error={formik.touched.password && Boolean(formik.errors.password)}
              helperText={formik.touched.password && formik.errors.password}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              name="confirmPassword"
              label="Confirm Password"
              type="password"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
              helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControl fullWidth error={formik.touched.role && Boolean(formik.errors.role)}>
              <InputLabel>Role</InputLabel>
              <Select
                name="role"
                value={formik.values.role}
                onChange={formik.handleChange}
                label="Role"
              >
                {roles.map((role) => (
                  <MenuItem key={role.value} value={role.value}>
                    {role.label}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.role && formik.errors.role && (
                <FormHelperText>{formik.errors.role}</FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2 }}>Module Permissions</Typography>
            {modules.map((module) => (
              <Accordion key={module} sx={{ mb: 1 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography sx={{ textTransform: 'capitalize' }}>{module}</Typography>
                  {selectedModules[module] && (
                    <Chip 
                      label={selectedModules[module]}
                      size="small"
                      color={accessLevels.find(level => level.value === selectedModules[module])?.color as any}
                      sx={{ ml: 2 }}
                    />
                  )}
                </AccordionSummary>
                <AccordionDetails>
                  <FormGroup>
                    {accessLevels.map((level) => (
                      <FormControlLabel
                        key={level.value}
                        control={
                          <Checkbox
                            checked={selectedModules[module] === level.value}
                            onChange={() => handleModuleAccessChange(module, level.value)}
                            name={`${module}-${level.value}`}
                          />
                        }
                        label={level.label}
                      />
                    ))}
                  </FormGroup>
                </AccordionDetails>
              </Accordion>
            ))}
            {formik.touched.permissions && formik.errors.permissions &&
             typeof formik.errors.permissions === 'string' && (
                <FormHelperText error>{formik.errors.permissions}</FormHelperText>
            )}
          </Grid>

          <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button onClick={onCancel} color="secondary" variant="outlined">
              Cancel
            </Button>
            <Button type="submit" variant="contained" color="primary">
              {initialValues ? 'Update User' : 'Add User'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
};

export default AddUserForm;