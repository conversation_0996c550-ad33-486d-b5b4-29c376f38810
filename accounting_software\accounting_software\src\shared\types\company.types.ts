export interface CompanyFormData {
  companyName: string;
  registrationNumber: string;
  taxId: string;
  address: string;
  phone: string;
  email: string;
  functionalCurrency: string;
  reportingCurrency: string;
  accountingYear: {
    start: string;
    end: string;
  };
  fiscalYear: {
    start: string;
    end: string;
  };
  salesTaxRegistered: boolean;
  salesTaxNumber?: string;
}

export interface Company extends Omit<CompanyFormData, 'companyName'> {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}