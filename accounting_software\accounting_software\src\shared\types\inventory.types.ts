// Enhanced Inventory Management Types for Retail Business

export type BusinessType = 'grocery' | 'pharmacy' | 'general_retail' | 'restaurant' | 'wholesale';

export type DivisionType = 'perishable' | 'non_perishable' | 'controlled_substance' | 'frozen' | 'refrigerated';

export type InventoryValuationMethod = 'fifo' | 'lifo' | 'weighted_average' | 'specific_identification';

export type UnitOfMeasure = {
  id: string;
  name: string;
  abbreviation: string;
  type: 'weight' | 'volume' | 'count' | 'length' | 'area';
  baseUnit?: string; // For conversions
  conversionFactor?: number; // How many base units = 1 of this unit
};

export type ProductDivision = {
  id: string;
  name: string;
  type: DivisionType;
  description: string;
  businessType: BusinessType;
  requiresExpiryDate: boolean;
  requiresBatchTracking: boolean;
  defaultShelfLife?: number; // in days
  temperatureRange?: {
    min: number;
    max: number;
    unit: 'celsius' | 'fahrenheit';
  };
  storageInstructions?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type ProductCategory = {
  id: string;
  name: string;
  code: string;
  description: string;
  divisionId: string;
  parentCategoryId?: string; // For sub-categories
  level: number; // 1 = main category, 2 = sub-category, etc.
  imageUrl?: string;
  taxCategoryId?: string;
  marginPercentage?: number; // Default margin for products in this category
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
};

export type ProductBrand = {
  id: string;
  name: string;
  code: string;
  description?: string;
  manufacturerId?: string;
  logoUrl?: string;
  website?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type ProductVariant = {
  id: string;
  name: string;
  type: 'size' | 'color' | 'flavor' | 'strength' | 'pack_size' | 'other';
  value: string;
  sortOrder: number;
};

export type ProductSKU = {
  id: string;
  productId: string;
  sku: string;
  barcode?: string;
  qrCode?: string;
  
  // Variant information
  variants: ProductVariant[];
  
  // Unit of Measure
  purchaseUOM: string; // UOM ID for purchasing
  salesUOM: string; // UOM ID for sales
  conversionFactor: number; // How many sales units = 1 purchase unit
  
  // Pricing
  costPrice: number;
  sellingPrice: number;
  mrp?: number; // Maximum Retail Price
  wholesalePrice?: number;
  memberPrice?: number;
  
  // Physical attributes
  weight?: number;
  weightUnit?: string;
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  
  // Inventory settings
  trackInventory: boolean;
  reorderPoint: number;
  reorderQuantity: number;
  maxStockLevel?: number;
  minStockLevel?: number;
  
  // Status
  isActive: boolean;
  isDiscontinued: boolean;
  createdAt: string;
  updatedAt: string;
};

export type EnhancedProduct = {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  
  // Hierarchy
  divisionId: string;
  categoryId: string;
  brandId?: string;
  
  // Product details
  productType: 'simple' | 'variable' | 'bundle';
  genericName?: string; // For pharmacy
  activeIngredients?: string[]; // For pharmacy/chemicals
  
  // Regulatory
  hsnCode?: string; // Harmonized System of Nomenclature
  drugLicenseRequired?: boolean;
  prescriptionRequired?: boolean;
  ageRestricted?: boolean;
  minimumAge?: number;
  
  // Storage & Handling
  storageInstructions?: string;
  handlingInstructions?: string;
  shelfLife?: number; // in days
  requiresRefrigeration: boolean;
  requiresFreezing: boolean;
  
  // Images and media
  images: string[];
  primaryImageUrl?: string;
  
  // SEO and marketing
  tags: string[];
  searchKeywords: string[];
  marketingDescription?: string;
  
  // Supplier information
  primarySupplierId?: string;
  alternativeSupplierIds: string[];
  
  // Accounting
  incomeAccountId?: string;
  expenseAccountId?: string;
  inventoryAccountId?: string;
  
  // Status and metadata
  status: 'active' | 'inactive' | 'discontinued' | 'out_of_stock';
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  
  // Related data (populated when needed)
  division?: ProductDivision;
  category?: ProductCategory;
  brand?: ProductBrand;
  skus?: ProductSKU[];
};

export type InventoryBatch = {
  id: string;
  skuId: string;
  batchNumber: string;
  lotNumber?: string;
  
  // Purchase information
  purchaseOrderId?: string;
  supplierId: string;
  purchaseDate: string;
  purchaseCost: number;
  purchaseQuantity: number;
  
  // Expiry and quality
  manufacturingDate?: string;
  expiryDate?: string;
  qualityGrade?: 'A' | 'B' | 'C';
  qualityNotes?: string;
  
  // Current status
  currentQuantity: number;
  reservedQuantity: number; // Allocated but not yet sold
  availableQuantity: number; // currentQuantity - reservedQuantity
  
  // Location
  locationId?: string;
  warehouseSection?: string;
  binLocation?: string;
  
  // Status
  status: 'active' | 'expired' | 'damaged' | 'recalled' | 'sold_out';
  isActive: boolean;
  
  // Metadata
  createdAt: string;
  updatedAt: string;
};

export type InventoryLocation = {
  id: string;
  name: string;
  code: string;
  type: 'warehouse' | 'store' | 'transit' | 'supplier' | 'customer';
  address?: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contactInfo?: {
    phone?: string;
    email?: string;
    manager?: string;
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type InventoryMovement = {
  id: string;
  skuId: string;
  batchId?: string;
  
  // Movement details
  movementType: 'purchase' | 'sale' | 'transfer' | 'adjustment' | 'damage' | 'expiry' | 'return';
  quantity: number;
  unitCost?: number;
  totalCost?: number;
  
  // Location information
  fromLocationId?: string;
  toLocationId?: string;
  
  // Reference documents
  referenceType?: 'purchase_order' | 'sales_order' | 'transfer_order' | 'adjustment' | 'damage_report';
  referenceId?: string;
  referenceNumber?: string;
  
  // Additional information
  notes?: string;
  reason?: string;
  approvedBy?: string;
  
  // Metadata
  movementDate: string;
  createdBy: string;
  createdAt: string;
};

export type StockTake = {
  id: string;
  stockTakeNumber: string;
  title: string;
  description?: string;
  
  // Scope
  locationIds: string[];
  categoryIds?: string[];
  divisionIds?: string[];
  includeExpiredItems: boolean;
  
  // Status and dates
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  plannedDate: string;
  startedDate?: string;
  completedDate?: string;
  
  // Results summary
  totalItemsPlanned?: number;
  totalItemsCounted?: number;
  totalVariances?: number;
  totalVarianceValue?: number;
  
  // Metadata
  createdBy: string;
  assignedTo?: string[];
  createdAt: string;
  updatedAt: string;
};

export type StockTakeItem = {
  id: string;
  stockTakeId: string;
  skuId: string;
  batchId?: string;
  locationId: string;
  
  // Expected vs Actual
  expectedQuantity: number;
  countedQuantity?: number;
  varianceQuantity?: number;
  varianceValue?: number;
  variancePercentage?: number;
  
  // Count details
  countedBy?: string;
  countedDate?: string;
  countMethod: 'manual' | 'barcode_scan' | 'rfid';
  notes?: string;
  
  // Status
  status: 'pending' | 'counted' | 'verified' | 'adjusted';
  
  // Metadata
  createdAt: string;
  updatedAt: string;
};

export type InventoryValuation = {
  id: string;
  skuId: string;
  locationId?: string;
  valuationDate: string;
  
  // Quantities
  totalQuantity: number;
  availableQuantity: number;
  reservedQuantity: number;
  
  // Valuation by method
  fifoValue: number;
  lifoValue: number;
  weightedAverageValue: number;
  
  // Current method value
  currentMethod: InventoryValuationMethod;
  currentValue: number;
  averageCost: number;
  
  // Metadata
  calculatedAt: string;
  calculatedBy: string;
};

// Form types for UI components
export type ProductDivisionFormData = Omit<ProductDivision, 'id' | 'createdAt' | 'updatedAt'>;
export type ProductCategoryFormData = Omit<ProductCategory, 'id' | 'createdAt' | 'updatedAt'>;
export type ProductBrandFormData = Omit<ProductBrand, 'id' | 'createdAt' | 'updatedAt'>;
export type EnhancedProductFormData = Omit<EnhancedProduct, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
export type ProductSKUFormData = Omit<ProductSKU, 'id' | 'createdAt' | 'updatedAt'>;

// Filter types
export type ProductFilters = {
  search?: string;
  divisionId?: string;
  categoryId?: string;
  brandId?: string;
  status?: string;
  businessType?: BusinessType;
  hasStock?: boolean;
  lowStock?: boolean;
  nearExpiry?: boolean;
  expired?: boolean;
  priceRange?: {
    min: number;
    max: number;
  };
};

// Constants
export const BUSINESS_TYPES: { value: BusinessType; label: string }[] = [
  { value: 'grocery', label: 'Grocery Store' },
  { value: 'pharmacy', label: 'Pharmacy' },
  { value: 'general_retail', label: 'General Retail' },
  { value: 'restaurant', label: 'Restaurant' },
  { value: 'wholesale', label: 'Wholesale' },
];

export const DIVISION_TYPES: { value: DivisionType; label: string; description: string }[] = [
  { value: 'perishable', label: 'Perishable', description: 'Items with limited shelf life' },
  { value: 'non_perishable', label: 'Non-Perishable', description: 'Items with long shelf life' },
  { value: 'controlled_substance', label: 'Controlled Substance', description: 'Regulated items requiring special handling' },
  { value: 'frozen', label: 'Frozen', description: 'Items requiring freezer storage' },
  { value: 'refrigerated', label: 'Refrigerated', description: 'Items requiring refrigeration' },
];

export const VALUATION_METHODS: { value: InventoryValuationMethod; label: string; description: string }[] = [
  { value: 'fifo', label: 'FIFO', description: 'First In, First Out - Best for perishables' },
  { value: 'lifo', label: 'LIFO', description: 'Last In, First Out - For specific scenarios' },
  { value: 'weighted_average', label: 'Weighted Average', description: 'Average cost method' },
  { value: 'specific_identification', label: 'Specific Identification', description: 'For high-value unique items' },
];

export const COMMON_UOMS: UnitOfMeasure[] = [
  // Count
  { id: 'pcs', name: 'Pieces', abbreviation: 'pcs', type: 'count' },
  { id: 'dozen', name: 'Dozen', abbreviation: 'doz', type: 'count', baseUnit: 'pcs', conversionFactor: 12 },
  { id: 'carton', name: 'Carton', abbreviation: 'ctn', type: 'count' },
  
  // Weight
  { id: 'kg', name: 'Kilogram', abbreviation: 'kg', type: 'weight' },
  { id: 'g', name: 'Gram', abbreviation: 'g', type: 'weight', baseUnit: 'kg', conversionFactor: 0.001 },
  { id: 'lb', name: 'Pound', abbreviation: 'lb', type: 'weight', baseUnit: 'kg', conversionFactor: 0.453592 },
  
  // Volume
  { id: 'l', name: 'Liter', abbreviation: 'L', type: 'volume' },
  { id: 'ml', name: 'Milliliter', abbreviation: 'ml', type: 'volume', baseUnit: 'l', conversionFactor: 0.001 },
  { id: 'gal', name: 'Gallon', abbreviation: 'gal', type: 'volume', baseUnit: 'l', conversionFactor: 3.78541 },
]; 