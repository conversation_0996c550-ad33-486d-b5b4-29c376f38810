import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Avatar,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  AlertTitle,
  Stack,
  Tooltip,
  CircularProgress,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  TablePagination,
  Snackbar,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  Tabs,
  Tab,
  FormControlLabel,
  Checkbox,
  Badge,
} from '@mui/material';
import {
  Add as AddIcon,
  Upload as UploadIcon,
  Visibility as ViewIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  GetApp as ExportIcon,
  Compare as CompareIcon,
  Link as LinkIcon,
  LinkOff as UnlinkIcon,
  AccountBalance as BankIcon,
  Receipt as ReceiptIcon,
  SwapHoriz as MatchIcon,
  PlaylistAddCheck as ReconcileIcon,
  FileUpload as ImportIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon,
  Security as SecurityIcon,
  AssignmentTurnedIn as ApprovalIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

// Bank Reconciliation Interfaces
interface CashAccount {
  id: number;
  account_name: string;
  account_number: string;
  bank_name: string;
  currency: string;
  current_balance: number;
  last_reconciled_date?: string;
  last_reconciled_balance?: number;
  is_active: boolean;
}

interface BankStatement {
  id: number;
  statement_number: string;
  cash_account: CashAccount;
  statement_date: string;
  period_from: string;
  period_to: string;
  opening_balance: number;
  closing_balance: number;
  is_reconciled: boolean;
  reconciled_by?: string;
  reconciled_at?: string;
  entries_count: number;
  matched_entries: number;
  created_at: string;
}

interface BankStatementEntry {
  id: number;
  bank_statement: number;
  entry_date: string;
  entry_type: 'DEBIT' | 'CREDIT';
  amount: number;
  description: string;
  reference_number?: string;
  bank_code?: string;
  running_balance: number;
  is_matched: boolean;
  matched_transaction?: number;
  matched_at?: string;
}

interface CashTransaction {
  id: number;
  transaction_number: string;
  transaction_date: string;
  transaction_type: string;
  amount: number;
  description: string;
  status: string;
  is_reconciled: boolean;
}

interface ReconciliationSummary {
  statement_opening_balance: number;
  statement_closing_balance: number;
  book_opening_balance: number;
  book_closing_balance: number;
  total_deposits: number;
  total_withdrawals: number;
  unmatched_bank_entries: number;
  unmatched_book_entries: number;
  reconciliation_difference: number;
  is_balanced: boolean;
}

const BankReconciliationPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAccount, setSelectedAccount] = useState<number>(0);
  const [selectedTab, setSelectedTab] = useState(0);
  const [reconciliationStep, setReconciliationStep] = useState(0);
  
  // Data state
  const [accounts, setAccounts] = useState<CashAccount[]>([]);
  const [statements, setStatements] = useState<BankStatement[]>([]);
  const [selectedStatement, setSelectedStatement] = useState<BankStatement | null>(null);
  const [bankEntries, setBankEntries] = useState<BankStatementEntry[]>([]);
  const [bookTransactions, setBookTransactions] = useState<CashTransaction[]>([]);
  const [reconciliationSummary, setReconciliationSummary] = useState<ReconciliationSummary | null>(null);
  
  // Dialog states
  const [openImportDialog, setOpenImportDialog] = useState(false);
  const [openMatchDialog, setOpenMatchDialog] = useState(false);
  const [selectedBankEntry, setSelectedBankEntry] = useState<BankStatementEntry | null>(null);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importProgress, setImportProgress] = useState(0);
  
  // Filter states
  const [showMatchedOnly, setShowMatchedOnly] = useState(false);
  const [showUnmatchedOnly, setShowUnmatchedOnly] = useState(false);
  const [dateRange, setDateRange] = useState({
    from: dayjs().subtract(30, 'day'),
    to: dayjs(),
  });

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning',
  });

  const { currencyInfo } = useCurrencyInfo();

  const reconciliationSteps = [
    'Import Statement',
    'Match Transactions',
    'Review Differences',
    'Complete Reconciliation'
  ];

  useEffect(() => {
    loadAccounts();
  }, []);

  useEffect(() => {
    if (selectedAccount) {
      loadStatements();
    }
  }, [selectedAccount]);

  useEffect(() => {
    if (selectedStatement) {
      loadReconciliationData();
    }
  }, [selectedStatement]);

  const loadAccounts = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/accounts/?requires_reconciliation=true', {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load accounts');
      }

      const data = await response.json();
      setAccounts(data.results || data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load accounts');
    } finally {
      setLoading(false);
    }
  };

  const loadStatements = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/bank-statements/?cash_account=${selectedAccount}`, {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load statements');
      }

      const data = await response.json();
      setStatements(data.results || data);
    } catch (err) {
      console.error('Load statements error:', err);
    }
  };

  const loadReconciliationData = async () => {
    try {
      const token = localStorage.getItem('token');
      
      // Load bank statement entries
      const entriesResponse = await fetch(
        `http://localhost:8000/api/cms/bank-statement-entries/?bank_statement=${selectedStatement?.id}`,
        { headers: { 'Authorization': `Token ${token}` } }
      );
      
      // Load book transactions
      const transactionsResponse = await fetch(
        `http://localhost:8000/api/cms/transactions/?cash_account=${selectedAccount}&transaction_date__gte=${selectedStatement?.period_from}&transaction_date__lte=${selectedStatement?.period_to}`,
        { headers: { 'Authorization': `Token ${token}` } }
      );

      const [entriesData, transactionsData] = await Promise.all([
        entriesResponse.json(),
        transactionsResponse.json(),
      ]);

      setBankEntries(entriesData.results || entriesData);
      setBookTransactions(transactionsData.results || transactionsData);

      // Calculate reconciliation summary
      calculateReconciliationSummary(entriesData.results || entriesData, transactionsData.results || transactionsData);

    } catch (err) {
      console.error('Load reconciliation data error:', err);
    }
  };

  const calculateReconciliationSummary = (entries: BankStatementEntry[], transactions: CashTransaction[]) => {
    const summary: ReconciliationSummary = {
      statement_opening_balance: selectedStatement?.opening_balance || 0,
      statement_closing_balance: selectedStatement?.closing_balance || 0,
      book_opening_balance: 0, // Should be calculated from previous reconciliation
      book_closing_balance: 0, // Should be calculated from transactions
      total_deposits: entries.filter(e => e.entry_type === 'CREDIT').reduce((sum, e) => sum + e.amount, 0),
      total_withdrawals: entries.filter(e => e.entry_type === 'DEBIT').reduce((sum, e) => sum + e.amount, 0),
      unmatched_bank_entries: entries.filter(e => !e.is_matched).length,
      unmatched_book_entries: transactions.filter(t => !t.is_reconciled).length,
      reconciliation_difference: 0,
      is_balanced: false,
    };

    summary.reconciliation_difference = Math.abs(summary.statement_closing_balance - summary.book_closing_balance);
    summary.is_balanced = summary.reconciliation_difference < 0.01; // Allow for small rounding differences

    setReconciliationSummary(summary);
  };

  const handleImportStatement = async () => {
    if (!importFile) return;

    try {
      setImportProgress(0);
      const token = localStorage.getItem('token');
      const formData = new FormData();
      formData.append('file', importFile);
      formData.append('cash_account', selectedAccount.toString());
      formData.append('import_format', 'CSV'); // Could be dynamic based on file type

      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch('http://localhost:8000/api/cms/bank-statements/import/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
        body: formData,
      });

      clearInterval(progressInterval);
      setImportProgress(100);

      if (!response.ok) {
        throw new Error('Failed to import statement');
      }

      const data = await response.json();
      
      setSnackbar({
        open: true,
        message: `Statement imported successfully! ${data.entries_count} entries processed.`,
        severity: 'success',
      });

      setOpenImportDialog(false);
      setImportFile(null);
      setImportProgress(0);
      loadStatements();
      setReconciliationStep(1);

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to import statement',
        severity: 'error',
      });
      setImportProgress(0);
    }
  };

  const handleMatchTransaction = async (bankEntryId: number, transactionId: number) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/bank-statement-entries/${bankEntryId}/match/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ transaction_id: transactionId }),
      });

      if (!response.ok) {
        throw new Error('Failed to match transaction');
      }

      setSnackbar({
        open: true,
        message: 'Transaction matched successfully',
        severity: 'success',
      });

      loadReconciliationData();
      setOpenMatchDialog(false);

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to match transaction',
        severity: 'error',
      });
    }
  };

  const handleCompleteReconciliation = async () => {
    if (!selectedStatement || !reconciliationSummary?.is_balanced) {
      setSnackbar({
        open: true,
        message: 'Cannot complete reconciliation with unmatched items',
        severity: 'warning',
      });
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/bank-statements/${selectedStatement.id}/reconcile/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to complete reconciliation');
      }

      setSnackbar({
        open: true,
        message: 'Bank reconciliation completed successfully!',
        severity: 'success',
      });

      loadStatements();
      setReconciliationStep(3);

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to complete reconciliation',
        severity: 'error',
      });
    }
  };

  const formatCurrency = (amount: number, currency?: string) => {
    if (!currencyInfo) return '0.00';
    
    const currencyCode = currency || currencyInfo.functional_currency;
    const locale = currencyCode === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getMatchStatusColor = (isMatched: boolean) => {
    return isMatched ? 'success' : 'warning';
  };

  const getMatchStatusIcon = (isMatched: boolean) => {
    return isMatched ? <CheckCircleIcon /> : <WarningIcon />;
  };

  const filteredBankEntries = bankEntries.filter(entry => {
    if (showMatchedOnly && !entry.is_matched) return false;
    if (showUnmatchedOnly && entry.is_matched) return false;
    return true;
  });

  const filteredBookTransactions = bookTransactions.filter(transaction => {
    if (showMatchedOnly && !transaction.is_reconciled) return false;
    if (showUnmatchedOnly && transaction.is_reconciled) return false;
    return true;
  });

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Bank Reconciliation...</Typography>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              🏦 Bank Reconciliation
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Match bank statements with book transactions
            </Typography>
            {currencyInfo && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                📊 {currencyInfo.company_name} • Base Currency: {currencyInfo.functional_currency} ({currencyInfo.functional_currency_symbol})
              </Typography>
            )}
          </Box>
          <Button
            variant="contained"
            startIcon={<ImportIcon />}
            onClick={() => setOpenImportDialog(true)}
            disabled={!selectedAccount}
          >
            Import Statement
          </Button>
        </Box>

        {/* Account Selection */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Select Account</InputLabel>
                  <Select
                    value={selectedAccount}
                    label="Select Account"
                    onChange={(e) => setSelectedAccount(Number(e.target.value))}
                  >
                    {accounts.map((account) => (
                      <MenuItem key={account.id} value={account.id}>
                        {account.account_name} - {account.bank_name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              {selectedAccount > 0 && (
                <Grid item xs={12} md={8}>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Avatar sx={{ bgcolor: 'primary.light' }}>
                      <BankIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="body1" fontWeight="medium">
                        {accounts.find(a => a.id === selectedAccount)?.account_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Current Balance: {formatCurrency(accounts.find(a => a.id === selectedAccount)?.current_balance || 0)}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>

        {selectedAccount > 0 && (
          <>
            {/* Reconciliation Stepper */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Stepper activeStep={reconciliationStep} alternativeLabel>
                  {reconciliationSteps.map((label) => (
                    <Step key={label}>
                      <StepLabel>{label}</StepLabel>
                    </Step>
                  ))}
                </Stepper>
              </CardContent>
            </Card>

            {/* Bank Statements List */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📋 Bank Statements
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Statement</TableCell>
                        <TableCell>Period</TableCell>
                        <TableCell align="right">Opening Balance</TableCell>
                        <TableCell align="right">Closing Balance</TableCell>
                        <TableCell align="center">Entries</TableCell>
                        <TableCell align="center">Status</TableCell>
                        <TableCell align="center">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {statements.map((statement) => (
                        <TableRow 
                          key={statement.id} 
                          hover 
                          selected={selectedStatement?.id === statement.id}
                          onClick={() => setSelectedStatement(statement)}
                          sx={{ cursor: 'pointer' }}
                        >
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {statement.statement_number}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {dayjs(statement.statement_date).format('MMM DD, YYYY')}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {dayjs(statement.period_from).format('MMM DD')} - {dayjs(statement.period_to).format('MMM DD, YYYY')}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(statement.opening_balance)}
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(statement.closing_balance)}
                          </TableCell>
                          <TableCell align="center">
                            <Badge badgeContent={statement.matched_entries} color="success">
                              <Chip
                                size="small"
                                label={statement.entries_count}
                                variant="outlined"
                              />
                            </Badge>
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              size="small"
                              label={statement.is_reconciled ? 'Reconciled' : 'Pending'}
                              color={statement.is_reconciled ? 'success' : 'warning'}
                              icon={statement.is_reconciled ? <CheckCircleIcon /> : <WarningIcon />}
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Tooltip title="View Details">
                              <IconButton size="small">
                                <ViewIcon />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>

            {selectedStatement && (
              <>
                {/* Reconciliation Summary */}
                {reconciliationSummary && (
                  <Card sx={{ mb: 3 }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        📊 Reconciliation Summary
                      </Typography>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={3}>
                          <Card variant="outlined">
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" color="primary">
                                {formatCurrency(reconciliationSummary.statement_closing_balance)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Statement Balance
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <Card variant="outlined">
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography variant="h6" color="secondary">
                                {formatCurrency(reconciliationSummary.book_closing_balance)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Book Balance
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <Card variant="outlined">
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography 
                                variant="h6" 
                                color={reconciliationSummary.reconciliation_difference === 0 ? 'success.main' : 'error.main'}
                              >
                                {formatCurrency(reconciliationSummary.reconciliation_difference)}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Difference
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                        <Grid item xs={12} md={3}>
                          <Card variant="outlined">
                            <CardContent sx={{ textAlign: 'center' }}>
                              <Typography 
                                variant="h6" 
                                color={reconciliationSummary.is_balanced ? 'success.main' : 'warning.main'}
                              >
                                {reconciliationSummary.unmatched_bank_entries + reconciliationSummary.unmatched_book_entries}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Unmatched Items
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      </Grid>
                      
                      {reconciliationSummary.is_balanced && (
                        <Alert severity="success" sx={{ mt: 2 }}>
                          <AlertTitle>Ready for Reconciliation</AlertTitle>
                          All transactions are matched and the reconciliation is balanced.
                          <Button 
                            variant="contained" 
                            color="success" 
                            sx={{ ml: 2 }}
                            onClick={handleCompleteReconciliation}
                            startIcon={<ReconcileIcon />}
                          >
                            Complete Reconciliation
                          </Button>
                        </Alert>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Reconciliation Tabs */}
                <Card>
                  <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
                    <Tab label="Bank Statement Entries" />
                    <Tab label="Book Transactions" />
                    <Tab label="Matching" />
                  </Tabs>
                  
                  <CardContent>
                    {/* Filters */}
                    <Box display="flex" gap={2} mb={2}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={showMatchedOnly}
                            onChange={(e) => setShowMatchedOnly(e.target.checked)}
                          />
                        }
                        label="Matched Only"
                      />
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={showUnmatchedOnly}
                            onChange={(e) => setShowUnmatchedOnly(e.target.checked)}
                          />
                        }
                        label="Unmatched Only"
                      />
                    </Box>

                    {selectedTab === 0 && (
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Date</TableCell>
                              <TableCell>Type</TableCell>
                              <TableCell>Description</TableCell>
                              <TableCell align="right">Amount</TableCell>
                              <TableCell align="right">Balance</TableCell>
                              <TableCell align="center">Status</TableCell>
                              <TableCell align="center">Actions</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {filteredBankEntries.map((entry) => (
                              <TableRow key={entry.id} hover>
                                <TableCell>
                                  {dayjs(entry.entry_date).format('MMM DD, YYYY')}
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    size="small"
                                    label={entry.entry_type}
                                    color={entry.entry_type === 'CREDIT' ? 'success' : 'error'}
                                    icon={entry.entry_type === 'CREDIT' ? <TrendingUpIcon /> : <TrendingDownIcon />}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2">
                                    {entry.description}
                                  </Typography>
                                  {entry.reference_number && (
                                    <Typography variant="caption" color="text.secondary">
                                      Ref: {entry.reference_number}
                                    </Typography>
                                  )}
                                </TableCell>
                                <TableCell align="right">
                                  <Typography
                                    fontWeight="medium"
                                    color={entry.entry_type === 'CREDIT' ? 'success.main' : 'error.main'}
                                  >
                                    {entry.entry_type === 'CREDIT' ? '+' : '-'}
                                    {formatCurrency(entry.amount)}
                                  </Typography>
                                </TableCell>
                                <TableCell align="right">
                                  {formatCurrency(entry.running_balance)}
                                </TableCell>
                                <TableCell align="center">
                                  <Chip
                                    size="small"
                                    label={entry.is_matched ? 'Matched' : 'Unmatched'}
                                    color={getMatchStatusColor(entry.is_matched)}
                                    icon={getMatchStatusIcon(entry.is_matched)}
                                  />
                                </TableCell>
                                <TableCell align="center">
                                  {!entry.is_matched && (
                                    <Button
                                      size="small"
                                      variant="outlined"
                                      onClick={() => {
                                        setSelectedBankEntry(entry);
                                        setOpenMatchDialog(true);
                                      }}
                                      startIcon={<MatchIcon />}
                                    >
                                      Match
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}

                    {selectedTab === 1 && (
                      <TableContainer>
                        <Table>
                          <TableHead>
                            <TableRow>
                              <TableCell>Date</TableCell>
                              <TableCell>Transaction</TableCell>
                              <TableCell>Type</TableCell>
                              <TableCell>Description</TableCell>
                              <TableCell align="right">Amount</TableCell>
                              <TableCell align="center">Status</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {filteredBookTransactions.map((transaction) => (
                              <TableRow key={transaction.id} hover>
                                <TableCell>
                                  {dayjs(transaction.transaction_date).format('MMM DD, YYYY')}
                                </TableCell>
                                <TableCell>
                                  <Typography variant="body2" fontWeight="medium">
                                    {transaction.transaction_number}
                                  </Typography>
                                </TableCell>
                                <TableCell>
                                  <Chip
                                    size="small"
                                    label={transaction.transaction_type}
                                    variant="outlined"
                                  />
                                </TableCell>
                                <TableCell>
                                  {transaction.description}
                                </TableCell>
                                <TableCell align="right">
                                  <Typography fontWeight="medium">
                                    {formatCurrency(transaction.amount)}
                                  </Typography>
                                </TableCell>
                                <TableCell align="center">
                                  <Chip
                                    size="small"
                                    label={transaction.is_reconciled ? 'Reconciled' : 'Pending'}
                                    color={transaction.is_reconciled ? 'success' : 'warning'}
                                  />
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}

                    {selectedTab === 2 && (
                      <Box textAlign="center" py={4}>
                        <CompareIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                        <Typography variant="h6" gutterBottom>
                          Transaction Matching
                        </Typography>
                        <Typography color="text.secondary" mb={3}>
                          Use the Bank Statement Entries tab to match individual transactions
                        </Typography>
                        <Button 
                          variant="outlined" 
                          onClick={() => setSelectedTab(0)}
                        >
                          Go to Bank Entries
                        </Button>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </>
            )}
          </>
        )}

        {/* Import Statement Dialog */}
        <Dialog open={openImportDialog} onClose={() => setOpenImportDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            <Box display="flex" alignItems="center">
              <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                <ImportIcon />
              </Avatar>
              Import Bank Statement
            </Box>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <input
                accept=".csv,.xlsx,.xls"
                style={{ display: 'none' }}
                id="statement-file-input"
                type="file"
                onChange={(e) => setImportFile(e.target.files?.[0] || null)}
              />
              <label htmlFor="statement-file-input">
                <Button
                  variant="outlined"
                  component="span"
                  fullWidth
                  sx={{ mb: 2, py: 2 }}
                  startIcon={<UploadIcon />}
                >
                  {importFile ? importFile.name : 'Choose Statement File (CSV, Excel)'}
                </Button>
              </label>

              {importProgress > 0 && (
                <Box sx={{ mb: 2 }}>
                  <LinearProgress variant="determinate" value={importProgress} />
                  <Typography variant="body2" textAlign="center" sx={{ mt: 1 }}>
                    Importing... {importProgress}%
                  </Typography>
                </Box>
              )}

              <Alert severity="info">
                <AlertTitle>Import Instructions</AlertTitle>
                Please ensure your statement file contains columns for date, description, amount, and balance.
                Supported formats: CSV, Excel (.xlsx, .xls)
              </Alert>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenImportDialog(false)}>Cancel</Button>
            <Button 
              variant="contained" 
              onClick={handleImportStatement}
              disabled={!importFile || importProgress > 0}
            >
              Import Statement
            </Button>
          </DialogActions>
        </Dialog>

        {/* Match Transaction Dialog */}
        <Dialog open={openMatchDialog} onClose={() => setOpenMatchDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            <Box display="flex" alignItems="center">
              <Avatar sx={{ mr: 2, bgcolor: 'success.light' }}>
                <MatchIcon />
              </Avatar>
              Match Bank Entry
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedBankEntry && (
              <Box>
                <Card variant="outlined" sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Bank Entry to Match
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Date</Typography>
                        <Typography variant="body1">{dayjs(selectedBankEntry.entry_date).format('MMM DD, YYYY')}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Amount</Typography>
                        <Typography variant="body1" fontWeight="medium">
                          {formatCurrency(selectedBankEntry.amount)}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Description</Typography>
                        <Typography variant="body1">{selectedBankEntry.description}</Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>

                <Typography variant="h6" gutterBottom>
                  Select Matching Transaction
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Transaction</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell align="right">Amount</TableCell>
                        <TableCell align="center">Action</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {bookTransactions
                        .filter(t => !t.is_reconciled)
                        .filter(t => Math.abs(t.amount - selectedBankEntry.amount) < 0.01) // Amount tolerance
                        .map((transaction) => (
                        <TableRow key={transaction.id} hover>
                          <TableCell>
                            {dayjs(transaction.transaction_date).format('MMM DD, YYYY')}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {transaction.transaction_number}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {transaction.description}
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell align="center">
                            <Button
                              size="small"
                              variant="contained"
                              color="success"
                              onClick={() => handleMatchTransaction(selectedBankEntry.id, transaction.id)}
                            >
                              Match
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenMatchDialog(false)}>Close</Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default BankReconciliationPage; 