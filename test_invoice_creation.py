#!/usr/bin/env python3
"""
Test script to reproduce the invoice creation error
"""
import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000/api"
USERNAME = "testapi"  # Replace with actual username
PASSWORD = "testpass123"  # Replace with actual password

def get_auth_token():
    """Get authentication token"""
    response = requests.post(f"{BASE_URL}/../api-token-auth/", {
        "username": USERNAME,
        "password": PASSWORD
    })
    if response.status_code == 200:
        return response.json()["token"]
    else:
        print(f"Failed to get token: {response.status_code} - {response.text}")
        return None

def test_invoice_creation():
    """Test invoice creation with minimal data"""
    token = get_auth_token()
    if not token:
        return
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    # Get a customer first
    customers_response = requests.get(f"{BASE_URL}/contacts/customers/", headers=headers)
    if customers_response.status_code != 200:
        print(f"Failed to get customers: {customers_response.status_code} - {customers_response.text}")
        return
    
    customers = customers_response.json()
    if not customers.get('results'):
        print("No customers found. Please create a customer first.")
        return
    
    customer_id = customers['results'][0]['id']
    print(f"Using customer ID: {customer_id}")
    
    # Create invoice data
    invoice_data = {
        "customer": customer_id,
        "warehouse": None,  # No warehouse for non-inventory items
        "invoice_date": datetime.now().strftime("%Y-%m-%d"),
        "due_date": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
        "status": "draft",
        "line_items": [
            {
                "product": None,  # Non-product item
                "description": "Test Service",
                "quantity": 1.0,
                "unit_price": 100.0,
                "discount_percent": 0.0,
                "line_total": 100.0,
                "taxable": True,
                "tax_rate": 10.0,
                "tax_amount": 10.0,
                "line_order": 0
            }
        ],
        "subtotal": 100.0,
        "tax_total": 10.0,
        "total_amount": 110.0,
        "notes": "Test invoice",
        "terms": "Net 30"
    }
    
    print("Sending invoice data:")
    print(json.dumps(invoice_data, indent=2))
    
    # Create invoice
    response = requests.post(f"{BASE_URL}/sales/invoices/", 
                           headers=headers, 
                           json=invoice_data)
    
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Body: {response.text}")
    
    if response.status_code == 201:
        print("Invoice created successfully!")
        invoice = response.json()
        print(f"Invoice ID: {invoice.get('id')}")
        print(f"Invoice Number: {invoice.get('invoice_number')}")
    else:
        print("Failed to create invoice")
        try:
            error_data = response.json()
            print("Error details:")
            print(json.dumps(error_data, indent=2))
        except:
            print("Could not parse error response as JSON")

if __name__ == "__main__":
    test_invoice_creation()
