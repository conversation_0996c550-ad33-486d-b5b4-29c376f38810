#!/usr/bin/env python3
"""
Test script to reproduce the invoice creation error
"""
import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000/api"
USERNAME = "testapi"  # Replace with actual username
PASSWORD = "testpass123"  # Replace with actual password

def get_auth_token():
    """Get authentication token"""
    response = requests.post(f"{BASE_URL}/../api-token-auth/", {
        "username": USERNAME,
        "password": PASSWORD
    })
    if response.status_code == 200:
        return response.json()["token"]
    else:
        print(f"Failed to get token: {response.status_code} - {response.text}")
        return None

def test_invoice_creation():
    """Test invoice creation with minimal data"""
    token = get_auth_token()
    if not token:
        return
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    # Use the test customer we just created
    customer_id = 51  # ID of the test customer we created
    print(f"Using customer ID: {customer_id}")
    
    # Create invoice data
    invoice_data = {
        "customer": customer_id,
        "warehouse": None,  # No warehouse for non-inventory items
        "invoice_date": datetime.now().strftime("%Y-%m-%d"),
        "due_date": (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
        "status": "draft",
        "line_items": [
            {
                "product": None,  # Non-product item
                "description": "Test Service",
                "quantity": 1.0,
                "unit_price": 100.0,
                "discount_percent": 0.0,
                "line_total": 100.0,
                "taxable": True,
                "tax_rate": 10.0,
                "tax_amount": 10.0,
                "line_order": 0
            }
        ],
        "subtotal": 100.0,
        "tax_total": 10.0,
        "total_amount": 110.0,
        "notes": "Test invoice",
        "terms": "Net 30"
    }
    
    print("Sending invoice data:")
    print(json.dumps(invoice_data, indent=2))
    
    # Create invoice
    response = requests.post(f"{BASE_URL}/sales/invoices/", 
                           headers=headers, 
                           json=invoice_data)
    
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    print(f"Response Body (first 1000 chars): {response.text[:1000]}")

    if response.status_code == 201:
        print("Invoice created successfully!")
        invoice = response.json()
        print(f"Invoice ID: {invoice.get('id')}")
        print(f"Invoice Number: {invoice.get('invoice_number')}")
    else:
        print("Failed to create invoice")
        try:
            error_data = response.json()
            print("Error details:")
            print(json.dumps(error_data, indent=2))
        except Exception as e:
            print(f"Could not parse error response as JSON: {e}")
            print(f"Content-Type: {response.headers.get('content-type')}")
            if 'html' in response.headers.get('content-type', '').lower():
                print("Response appears to be HTML (likely an error page)")
                # Try to extract error from HTML
                import re
                error_match = re.search(r'<title>(.*?)</title>', response.text, re.IGNORECASE)
                if error_match:
                    print(f"HTML Title: {error_match.group(1)}")
            else:
                print("Raw response text:")
                print(response.text)

if __name__ == "__main__":
    test_invoice_creation()
