import { Customer } from './customer.types';
import { TaxRate } from './tax.types';

export type InvoiceStatus = 'draft' | 'sent' | 'viewed' | 'partial' | 'paid' | 'overdue' | 'cancelled';
export type RecurringFrequency = 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';

export interface InvoiceItem {
  id?: string;
  productId: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
  discountType?: 'percentage' | 'fixed';
  taxRateId?: string;
  taxRate?: number;
  taxAmount?: number;
  subtotal: number;
  total: number;
  type: 'product' | 'service' | 'bundle';
}

export interface FileAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
  file?: File;
  url?: string;
  uploadedAt?: string;
}

export interface RecurringInvoiceSettings {
  isRecurring: boolean;
  frequency: RecurringFrequency;
  interval: number; // e.g., every 2 weeks, every 3 months
  startDate: string;
  endDate?: string; // Optional end date
  maxOccurrences?: number; // Optional max number of invoices to generate
  dayOfMonth?: number; // For monthly/quarterly/yearly, which day of the month
  dayOfWeek?: number; // For weekly, which day of the week (0-6, Sunday-Saturday)
  sendAutomatically: boolean; // Whether to automatically send the invoice
}

export interface InvoiceFormData {
  customerId: string;
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string;
  items: InvoiceItem[];
  notes?: string;
  terms?: string;
  subtotal: number;
  discountAmount?: number;
  discountType?: 'percentage' | 'fixed';
  taxAmount: number;
  total: number;
  paymentTerms: string;
  reference?: string;
  attachments?: FileAttachment[];
  recurringSettings?: RecurringInvoiceSettings;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  customerId: string;
  customerName: string;
  date: string;
  dueDate: string;
  status: InvoiceStatus;
  subtotal: number;
  taxTotal: number;
  total: number;
  amountPaid: number;
  amountDue: number;
  notes?: string;
  terms?: string;
  attachments?: string[];
  lineItems: InvoiceItem[];
  createdAt: string;
  updatedAt: string;
  paymentDate?: string;
  paymentMethod?: string;
  paymentReference?: string;
  recurringInvoiceId?: string;
  isRecurring?: boolean;
  nextInvoiceDate?: string;
}

export interface InvoicePayment {
  id: string;
  invoiceId: string;
  amount: number;
  paymentDate: string;
  paymentMethod: string;
  reference?: string;
  notes?: string;
  createdAt: string;
}

export const PAYMENT_METHODS = [
  { value: 'cash', label: 'Cash' },
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'credit_card', label: 'Credit Card' },
  { value: 'debit_card', label: 'Debit Card' },
  { value: 'paypal', label: 'PayPal' },
  { value: 'cheque', label: 'Cheque' },
  { value: 'other', label: 'Other' },
];

export const RECURRING_FREQUENCIES = [
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'quarterly', label: 'Quarterly' },
  { value: 'yearly', label: 'Yearly' },
  { value: 'custom', label: 'Custom' },
];

export const getStatusColor = (status: InvoiceStatus): string => {
  switch (status) {
    case 'draft':
      return '#9e9e9e'; // Gray
    case 'sent':
      return '#2196f3'; // Blue
    case 'viewed':
      return '#673ab7'; // Deep Purple
    case 'partial':
      return '#ff9800'; // Orange
    case 'paid':
      return '#4caf50'; // Green
    case 'overdue':
      return '#f44336'; // Red
    case 'cancelled':
      return '#795548'; // Brown
    default:
      return '#9e9e9e'; // Gray
  }
};

export const getStatusLabel = (status: InvoiceStatus): string => {
  return status.charAt(0).toUpperCase() + status.slice(1);
}; 