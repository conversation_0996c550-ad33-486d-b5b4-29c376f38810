"""
Django Management Command: Import Chart of Accounts from CSV

This command imports Chart of Accounts data from a CSV file with proper
validation and error handling.

Usage: python manage.py import_coa --file path/to/coa.csv
"""

import csv
import os
from decimal import Decimal, InvalidOperation
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.db import transaction
from gl.models import AccountType, DetailType, Account

User = get_user_model()

class Command(BaseCommand):
    help = 'Import Chart of Accounts from CSV file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            required=True,
            help='Path to the CSV file containing COA data'
        )
        parser.add_argument(
            '--user',
            type=str,
            default='admin',
            help='Username to set as created_by (default: admin)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Validate the file without importing data'
        )

    def handle(self, *args, **options):
        file_path = options['file']
        username = options['user']
        dry_run = options['dry_run']

        # Validate file exists
        if not os.path.exists(file_path):
            raise CommandError(f'File not found: {file_path}')

        # Get user for created_by field
        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            user = User.objects.filter(is_superuser=True).first()
            if not user:
                raise CommandError(f'User "{username}" not found and no superuser available')
            self.stdout.write(
                self.style.WARNING(f'User "{username}" not found, using "{user.username}"')
            )

        # Process the CSV file
        self.stdout.write(f'Processing file: {file_path}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No data will be imported'))

        try:
            with transaction.atomic():
                accounts_created, errors = self.process_csv(file_path, user, dry_run)
                
                if errors:
                    self.stdout.write(self.style.ERROR(f'\nFound {len(errors)} errors:'))
                    for error in errors:
                        self.stdout.write(self.style.ERROR(f'  - {error}'))
                    
                    if not dry_run:
                        raise CommandError('Import failed due to errors')
                
                if dry_run:
                    self.stdout.write(
                        self.style.SUCCESS(f'\nValidation complete: {accounts_created} accounts validated')
                    )
                    # Rollback transaction in dry run
                    transaction.set_rollback(True)
                else:
                    self.stdout.write(
                        self.style.SUCCESS(f'\nImport complete: {accounts_created} accounts imported')
                    )

        except Exception as e:
            raise CommandError(f'Import failed: {str(e)}')

    def process_csv(self, file_path, user, dry_run=False):
        """Process the CSV file and create accounts."""
        accounts_created = 0
        errors = []
        
        # Required fields
        required_fields = ['account_number', 'account_name', 'account_type_code', 'detail_type_code']
        
        # Cache for account types and detail types
        account_types_cache = {}
        detail_types_cache = {}
        
        # Track account numbers for duplicate checking
        account_numbers_seen = set()
        
        with open(file_path, 'r', encoding='utf-8') as csvfile:
            # Detect delimiter
            sample = csvfile.read(1024)
            csvfile.seek(0)
            sniffer = csv.Sniffer()
            delimiter = sniffer.sniff(sample).delimiter
            
            reader = csv.DictReader(csvfile, delimiter=delimiter)
            
            # Validate headers
            if not all(field in reader.fieldnames for field in required_fields):
                missing_fields = [field for field in required_fields if field not in reader.fieldnames]
                errors.append(f'Missing required columns: {", ".join(missing_fields)}')
                return 0, errors
            
            for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is row 1)
                try:
                    # Validate required fields
                    for field in required_fields:
                        if not row.get(field, '').strip():
                            errors.append(f'Row {row_num}: Missing required field "{field}"')
                            continue
                    
                    account_number = row['account_number'].strip()
                    account_name = row['account_name'].strip()
                    account_type_code = row['account_type_code'].strip().upper()
                    detail_type_code = row['detail_type_code'].strip().upper()
                    
                    # Check for duplicate account numbers
                    if account_number in account_numbers_seen:
                        errors.append(f'Row {row_num}: Duplicate account number "{account_number}"')
                        continue
                    
                    # Check if account already exists
                    if Account.objects.filter(account_number=account_number).exists():
                        errors.append(f'Row {row_num}: Account number "{account_number}" already exists')
                        continue
                    
                    account_numbers_seen.add(account_number)
                    
                    # Get or validate account type
                    if account_type_code not in account_types_cache:
                        try:
                            account_type = AccountType.objects.get(code=account_type_code)
                            account_types_cache[account_type_code] = account_type
                        except AccountType.DoesNotExist:
                            errors.append(f'Row {row_num}: Invalid account type code "{account_type_code}"')
                            continue
                    
                    account_type = account_types_cache[account_type_code]
                    
                    # Get or validate detail type
                    detail_type_key = f'{account_type_code}_{detail_type_code}'
                    if detail_type_key not in detail_types_cache:
                        try:
                            detail_type = DetailType.objects.get(
                                account_type=account_type,
                                code=detail_type_code
                            )
                            detail_types_cache[detail_type_key] = detail_type
                        except DetailType.DoesNotExist:
                            errors.append(
                                f'Row {row_num}: Invalid detail type code "{detail_type_code}" '
                                f'for account type "{account_type_code}"'
                            )
                            continue
                    
                    detail_type = detail_types_cache[detail_type_key]
                    
                    # Parse optional fields
                    parent_account = None
                    if row.get('parent_account_number', '').strip():
                        parent_account_number = row['parent_account_number'].strip()
                        try:
                            parent_account = Account.objects.get(account_number=parent_account_number)
                        except Account.DoesNotExist:
                            errors.append(
                                f'Row {row_num}: Parent account "{parent_account_number}" not found'
                            )
                            continue
                    
                    # Parse opening balance
                    opening_balance = Decimal('0.00')
                    if row.get('opening_balance', '').strip():
                        try:
                            opening_balance = Decimal(row['opening_balance'].strip())
                        except (InvalidOperation, ValueError):
                            errors.append(f'Row {row_num}: Invalid opening balance format')
                            continue
                    
                    # Parse opening balance date
                    opening_balance_date = None
                    if row.get('opening_balance_date', '').strip():
                        try:
                            opening_balance_date = datetime.strptime(
                                row['opening_balance_date'].strip(), '%Y-%m-%d'
                            ).date()
                        except ValueError:
                            errors.append(
                                f'Row {row_num}: Invalid date format for opening_balance_date '
                                '(use YYYY-MM-DD)'
                            )
                            continue
                    
                    # Parse boolean fields
                    is_active = self.parse_boolean(row.get('is_active', 'TRUE'))
                    is_header_account = self.parse_boolean(row.get('is_header_account', 'FALSE'))
                    
                    # Get other optional fields
                    description = row.get('description', '').strip()
                    currency = row.get('currency', 'INR').strip().upper()
                    tax_line = row.get('tax_line', '').strip()
                    bank_account_number = row.get('bank_account_number', '').strip()
                    bank_routing_number = row.get('bank_routing_number', '').strip()
                    
                    # Create account if not in dry run mode
                    if not dry_run:
                        Account.objects.create(
                            account_number=account_number,
                            account_name=account_name,
                            account_type=account_type,
                            detail_type=detail_type,
                            parent_account=parent_account,
                            description=description,
                            opening_balance=opening_balance,
                            opening_balance_date=opening_balance_date,
                            currency=currency,
                            is_active=is_active,
                            is_header_account=is_header_account,
                            tax_line=tax_line,
                            bank_account_number=bank_account_number,
                            bank_routing_number=bank_routing_number,
                            created_by=user
                        )
                    
                    accounts_created += 1
                    
                    if accounts_created % 10 == 0:
                        self.stdout.write(f'Processed {accounts_created} accounts...')
                
                except Exception as e:
                    errors.append(f'Row {row_num}: Unexpected error - {str(e)}')
        
        return accounts_created, errors
    
    def parse_boolean(self, value):
        """Parse boolean values from CSV."""
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            value = value.strip().upper()
            return value in ['TRUE', '1', 'YES', 'Y']
        
        return bool(value) 