import api from './api';

export interface DeliveryNoteItem {
  id?: number;
  product: number;
  product_name?: string;
  product_sku?: string;
  sales_order_line_item: number;
  sales_order_line_description?: string;
  description: string;
  quantity_ordered: number;
  quantity_delivered: number;
  unit_of_measure: string;
  unit_price: number;
  line_total: number;
  taxable: boolean;
  tax_rate: number;
  tax_amount: number;
  batch_number?: string;
  expiry_date?: string;
  notes?: string;
  line_order: number;
}

export interface DeliveryNote {
  id?: number;
  dn_id?: string;
  dn_number?: string;
  sales_order: number;
  sales_order_number?: string;
  customer: number;
  customer_name?: string;
  warehouse: number;
  warehouse_name?: string;
  delivery_date: string;
  expected_delivery_date?: string;
  delivered_by?: string;
  delivery_method?: string;
  tracking_number?: string;
  subtotal: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  currency: string;
  status: 'draft' | 'pending' | 'delivered' | 'invoiced' | 'cancelled';
  posted: boolean;
  posted_date?: string;
  posted_by?: number;
  notes?: string;
  customer_signature?: string;
  line_items?: DeliveryNoteItem[];
  created_at?: string;
  updated_at?: string;
  created_by?: number;
}

export interface DeliveryNoteStats {
  total_deliveries: number;
  total_value: number;
  status_counts: Record<string, number>;
  posted_count: number;
  unposted_count: number;
}

export interface DeliveryNoteFilters {
  status?: string;
  customer?: number;
  sales_order?: number;
  warehouse?: number;
  posted?: boolean;
  start_date?: string;
  end_date?: string;
  search?: string;
  ordering?: string;
}

class DeliveryNoteService {
  private baseUrl = '/sales/delivery-notes';

  async getAll(filters?: DeliveryNoteFilters): Promise<{ results: DeliveryNote[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await api.get(`${this.baseUrl}/?${params.toString()}`);
    return response.data;
  }

  async getById(id: number): Promise<DeliveryNote> {
    const response = await api.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async create(deliveryNote: Partial<DeliveryNote>): Promise<DeliveryNote> {
    const response = await api.post(`${this.baseUrl}/`, deliveryNote);
    return response.data;
  }

  async update(id: number, deliveryNote: Partial<DeliveryNote>): Promise<DeliveryNote> {
    const response = await api.put(`${this.baseUrl}/${id}/`, deliveryNote);
    return response.data;
  }

  async partialUpdate(id: number, deliveryNote: Partial<DeliveryNote>): Promise<DeliveryNote> {
    const response = await api.patch(`${this.baseUrl}/${id}/`, deliveryNote);
    return response.data;
  }

  async delete(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/${id}/`);
  }

  async getStats(): Promise<DeliveryNoteStats> {
    const response = await api.get(`${this.baseUrl}/stats/`);
    return response.data;
  }

  async getPendingPost(): Promise<DeliveryNote[]> {
    const response = await api.get(`${this.baseUrl}/pending_post/`);
    return response.data;
  }

  async postDelivery(id: number): Promise<{ status: string }> {
    const response = await api.post(`${this.baseUrl}/${id}/post_delivery/`);
    return response.data;
  }

  async markDelivered(id: number): Promise<{ status: string }> {
    const response = await api.post(`${this.baseUrl}/${id}/mark_delivered/`);
    return response.data;
  }

  // Helper method to calculate line totals
  calculateLineTotal(item: Partial<DeliveryNoteItem>): number {
    return (item.quantity_delivered || 0) * (item.unit_price || 0);
  }

  // Helper method to calculate tax amount
  calculateTaxAmount(item: Partial<DeliveryNoteItem>): number {
    if (!item.taxable) return 0;
    const lineTotal = this.calculateLineTotal(item);
    return lineTotal * ((item.tax_rate || 0) / 100);
  }

  // Helper method to calculate delivery note totals
  calculateDeliveryNoteTotals(lineItems: DeliveryNoteItem[]): {
    subtotal: number;
    tax_amount: number;
    total_amount: number;
  } {
    const subtotal = lineItems.reduce((sum, item) => sum + item.line_total, 0);
    const tax_amount = lineItems.reduce((sum, item) => sum + item.tax_amount, 0);
    const total_amount = subtotal + tax_amount;

    return { subtotal, tax_amount, total_amount };
  }
}

export default new DeliveryNoteService(); 