import React from 'react';
import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Dayjs } from 'dayjs';

export interface FormDatePickerProps extends Omit<DatePickerProps<Dayjs>, 'renderInput'> {
  fullWidth?: boolean;
  required?: boolean;
  size?: 'small' | 'medium';
  variant?: 'outlined' | 'filled' | 'standard';
  helperText?: string;
  error?: boolean;
}

const FormDatePicker: React.FC<FormDatePickerProps> = ({
  fullWidth = true,
  required = false,
  size = 'medium',
  variant = 'outlined',
  helperText,
  error = false,
  label,
  ...props
}) => {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        label={label}
        slotProps={{
          textField: {
            fullWidth,
            required,
            size,
            variant,
            helperText,
            error,
          }
        }}
        {...props}
      />
    </LocalizationProvider>
  );
};

export default FormDatePicker; 