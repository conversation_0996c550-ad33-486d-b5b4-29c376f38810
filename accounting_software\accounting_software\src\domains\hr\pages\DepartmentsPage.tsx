import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  InputAdornment,
  Chip,
  Typography,
  CircularProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import DepartmentForm from './components/DepartmentForm';

interface Department {
  id: number;
  name: string;
  code: string;
  description: string;
  manager: number | null;
  manager_name: string | null;
  parent_department: number | null;
  parent_department_name: string | null;
  is_active: boolean;
  employee_count: number;
  active_employee_count: number;
  created_at: string;
  updated_at: string;
}

const DepartmentsPage: React.FC = () => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddDepartmentOpen, setIsAddDepartmentOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [editDepartment, setEditDepartment] = useState<Department | null>(null);
  const [actionLoading, setActionLoading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const fetchDepartments = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch('http://localhost:8000/api/hr/departments/', {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          enqueueSnackbar('Session expired. Please log in again.', { variant: 'warning' });
          navigate('/login');
          return;
        }
        throw new Error('Failed to fetch departments');
      }
      
      const data = await response.json();
      setDepartments(data.results || data);
    } catch (err: any) {
      setError(err.message || 'Error fetching departments');
      enqueueSnackbar(err.message || 'Error fetching departments', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [navigate, enqueueSnackbar]);

  useEffect(() => {
    fetchDepartments();
  }, [fetchDepartments]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleDepartmentAdded = () => {
    setIsAddDepartmentOpen(false);
    setEditDepartment(null);
    fetchDepartments();
  };

  const handleDelete = async () => {
    if (deleteId == null) return;
    setActionLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch(`http://localhost:8000/api/hr/departments/${deleteId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('token');
          enqueueSnackbar('Session expired. Please log in again.', { variant: 'warning' });
          navigate('/login');
          return;
        }
        throw new Error('Failed to delete department');
      }

      enqueueSnackbar('Department deleted successfully', { variant: 'success' });
      setDeleteId(null);
      fetchDepartments();
    } catch (err: any) {
      enqueueSnackbar(err.message || 'Error deleting department', { variant: 'error' });
    } finally {
      setActionLoading(false);
    }
  };

  const handleEdit = (department: Department) => {
    setEditDepartment(department);
    setIsAddDepartmentOpen(true);
  };

  const handleFormSubmit = async (data: any) => {
    if (editDepartment) {
      // Edit mode: send PUT request
      setActionLoading(true);
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const response = await fetch(`http://localhost:8000/api/hr/departments/${editDepartment.id}/`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${token}`,
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem('token');
            enqueueSnackbar('Session expired. Please log in again.', { variant: 'warning' });
            navigate('/login');
            return;
          }
          throw new Error('Failed to update department');
        }

        enqueueSnackbar('Department updated successfully', { variant: 'success' });
        setIsAddDepartmentOpen(false);
        setEditDepartment(null);
        fetchDepartments();
      } catch (err: any) {
        enqueueSnackbar(err.message || 'Error updating department', { variant: 'error' });
      } finally {
        setActionLoading(false);
      }
    } else {
      // Add mode
      handleDepartmentAdded();
    }
  };

  const filteredDepartments = departments.filter(department =>
    department.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    department.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (department.description || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Departments
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setIsAddDepartmentOpen(true)}
        >
          Add Department
        </Button>
      </Box>

      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search departments..."
          value={searchTerm}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <Button variant="outlined">Filter</Button>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ color: 'error.main', textAlign: 'center', my: 4 }}>{error}</Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Code</TableCell>
                <TableCell>Manager</TableCell>
                <TableCell>Parent Department</TableCell>
                <TableCell>Employees</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredDepartments.map((department) => (
                <TableRow key={department.id}>
                  <TableCell component="th" scope="row">
                    {department.name}
                  </TableCell>
                  <TableCell>{department.code}</TableCell>
                  <TableCell>{department.manager_name || '-'}</TableCell>
                  <TableCell>{department.parent_department_name || '-'}</TableCell>
                  <TableCell>
                    {department.active_employee_count} / {department.employee_count}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={department.is_active ? "Active" : "Inactive"}
                      color={department.is_active ? "success" : "default"}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton size="small" sx={{ mr: 1 }} onClick={() => handleEdit(department)}>
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton size="small" onClick={() => setDeleteId(department.id)}>
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <Dialog
        open={isAddDepartmentOpen}
        onClose={() => { setIsAddDepartmentOpen(false); setEditDepartment(null); }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{editDepartment ? 'Edit Department' : 'Add New Department'}</DialogTitle>
        <DialogContent>
          <DepartmentForm
            onCancel={() => { setIsAddDepartmentOpen(false); setEditDepartment(null); }}
            onSubmit={handleFormSubmit}
            initialValues={editDepartment}
            loading={actionLoading}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={deleteId !== null} onClose={() => setDeleteId(null)}>
        <DialogTitle>Delete Department</DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>Are you sure you want to delete this department?</Box>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button onClick={() => setDeleteId(null)} disabled={actionLoading}>Cancel</Button>
            <Button color="error" onClick={handleDelete} disabled={actionLoading} variant="contained">Delete</Button>
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default DepartmentsPage; 