#!/usr/bin/env python3
"""
Debug script to test payment_terms field validation
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"
TOKEN = "e0d1048e1f14689c6eba6616845a074bad2167bc"

def test_payment_terms_validation():
    """Test different payment_terms values to see what causes the error"""
    
    headers = {
        "Authorization": f"Token {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test cases for payment_terms
    test_cases = [
        {
            "name": "String payment_terms",
            "payment_terms": "Net 30"
        },
        {
            "name": "Array payment_terms (likely the issue)",
            "payment_terms": ["Net 30"]
        },
        {
            "name": "Empty string payment_terms",
            "payment_terms": ""
        },
        {
            "name": "Null payment_terms",
            "payment_terms": None
        },
        {
            "name": "No payment_terms field",
            "skip_payment_terms": True
        }
    ]
    
    for test_case in test_cases:
        print(f"\n{'='*50}")
        print(f"Testing: {test_case['name']}")
        print(f"{'='*50}")
        
        # Base invoice data
        invoice_data = {
            "customer": 51,
            "invoice_date": "2025-06-29",
            "due_date": "2025-07-29",
            "status": "draft",
            "line_items": [
                {
                    "description": "Test Service",
                    "quantity": 1.0,
                    "unit_price": 100.0,
                    "discount_percent": 0.0,
                    "line_total": 100.0,
                    "taxable": True,
                    "tax_rate": 10.0,
                    "tax_amount": 10.0,
                    "line_order": 0
                }
            ]
        }
        
        # Add payment_terms based on test case
        if not test_case.get('skip_payment_terms'):
            invoice_data['payment_terms'] = test_case['payment_terms']
        
        print(f"Sending data: {json.dumps(invoice_data, indent=2)}")
        
        # Make request
        response = requests.post(
            f"{BASE_URL}/sales/invoices/",
            headers=headers,
            json=invoice_data
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ SUCCESS - Invoice created")
            result = response.json()
            print(f"Invoice ID: {result.get('id')}")
            print(f"Payment Terms: {result.get('payment_terms')}")
        else:
            print("❌ FAILED")
            try:
                error_data = response.json()
                print(f"Error: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw response: {response.text[:500]}")

if __name__ == "__main__":
    test_payment_terms_validation()
