from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
# from rest_framework.decorators import api_view, permission_classes # Removed as payroll views are removed
# from rest_framework.response import Response # Removed as payroll views are removed
from .models import Employee, Attendance, Department
from .serializers import EmployeeSerializer, AttendanceSerializer, DepartmentSerializer

# from django.http import HttpResponse # Removed as export view is removed
# import csv # Removed as export view is removed
# from datetime import datetime # Removed as payroll view is removed
# from django.utils.timezone import make_aware # Removed as payroll view is removed
# from decimal import Decimal # Removed as payroll view is removed

class EmployeeViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows employees to be viewed or edited.
    """
    queryset = Employee.objects.select_related('contact_employee', 'contact_employee__contact').all().order_by('contact_employee__contact__name')
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Optionally filter employees by department or status
        """
        queryset = Employee.objects.select_related('contact_employee', 'contact_employee__contact').all()
        department = self.request.query_params.get('department', None)
        status = self.request.query_params.get('status', None)
        
        if department is not None:
            queryset = queryset.filter(contact_employee__department=department)
        if status is not None:
            queryset = queryset.filter(status=status)
            
        return queryset.order_by('contact_employee__contact__name')

class AttendanceViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows attendance records to be viewed or edited.
    """
    queryset = Attendance.objects.select_related('employee', 'employee__contact_employee', 'employee__contact_employee__contact').all().order_by('date', 'employee__contact_employee__contact__name')
    serializer_class = AttendanceSerializer
    permission_classes = [IsAuthenticated]

    # You might add custom logic here later for filtering by date, employee, etc. 

class DepartmentViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows departments to be viewed or edited.
    """
    queryset = Department.objects.all().order_by('name')
    serializer_class = DepartmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Optionally filter departments by active status
        """
        queryset = Department.objects.all()
        is_active = self.request.query_params.get('is_active', None)
        
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)
            
        return queryset.order_by('name') 