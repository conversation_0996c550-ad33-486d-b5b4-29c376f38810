"""
Cash Management System Django Admin Configuration

Beautiful admin interface for managing cash accounts, transactions,
and treasury operations with proper organization and search capabilities.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Sum, Count
from .models import (
    CashAccountType, CashAccount, CashTransaction, CashTransfer,
    BankStatement, BankStatementEntry, CashFlowForecast
)


@admin.register(CashAccountType)
class CashAccountTypeAdmin(admin.ModelAdmin):
    """Admin interface for Cash Account Types."""
    
    list_display = [
        'code', 'name', 'requires_bank_details', 'allows_overdraft',
        'requires_reconciliation', 'accounts_count', 'sort_order', 'is_active'
    ]
    list_filter = [
        'requires_bank_details', 'allows_overdraft', 'requires_reconciliation',
        'is_investment_account', 'is_active'
    ]
    search_fields = ['code', 'name', 'description']
    ordering = ['sort_order', 'name']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'description')
        }),
        ('Account Behavior', {
            'fields': (
                'requires_bank_details', 'allows_overdraft', 
                'requires_reconciliation', 'is_investment_account'
            )
        }),
        ('Display Settings', {
            'fields': ('sort_order', 'is_active')
        }),
    )
    
    def accounts_count(self, obj):
        """Display count of cash accounts using this type."""
        count = obj.cash_accounts.count()
        if count > 0:
            url = reverse('admin:cms_cashaccount_changelist') + f'?account_type__id__exact={obj.id}'
            return format_html('<a href="{}">{} accounts</a>', url, count)
        return '0 accounts'
    accounts_count.short_description = 'Accounts'


@admin.register(CashAccount)
class CashAccountAdmin(admin.ModelAdmin):
    """Admin interface for Cash Accounts."""
    
    list_display = [
        'account_name', 'account_number', 'account_type', 'currency',
        'formatted_balance', 'bank_name', 'is_active', 'is_default'
    ]
    list_filter = [
        'account_type', 'currency', 'is_active', 'is_default', 
        'requires_approval', 'created_at'
    ]
    search_fields = [
        'account_name', 'account_number', 'bank_name', 'description'
    ]
    ordering = ['account_name']
    
    fieldsets = (
        ('Account Information', {
            'fields': ('account_name', 'account_number', 'account_type', 'description')
        }),
        ('Banking Details', {
            'fields': (
                'bank_name', 'bank_branch', 'bank_routing_number',
                'bank_swift_code', 'bank_iban'
            )
        }),
        ('Financial Settings', {
            'fields': (
                'currency', 'opening_balance', 'opening_balance_date',
                'current_balance', 'credit_limit', 'interest_rate'
            )
        }),
        ('Management', {
            'fields': (
                'account_manager', 'gl_account', 
                'is_active', 'is_default', 'requires_approval'
            )
        }),
        ('Reconciliation', {
            'fields': ('last_reconciled_date', 'last_reconciled_balance')
        }),
    )
    
    readonly_fields = ['current_balance', 'created_by', 'created_at', 'updated_at']
    
    def formatted_balance(self, obj):
        """Display formatted balance with currency."""
        balance = obj.get_current_balance()
        if balance < 0:
            return format_html(
                '<span style="color: red;">{} {}</span>',
                obj.currency, f'{balance:,.2f}'
            )
        return f'{obj.currency} {balance:,.2f}'
    formatted_balance.short_description = 'Current Balance'
    formatted_balance.admin_order_field = 'current_balance'
    
    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new accounts."""
        if not change:  # Creating new record
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(CashTransaction)
class CashTransactionAdmin(admin.ModelAdmin):
    """Admin interface for Cash Transactions."""
    
    list_display = [
        'transaction_number', 'transaction_date', 'transaction_type',
        'cash_account', 'formatted_amount', 'description', 'status'
    ]
    list_filter = [
        'transaction_type', 'currency', 'status', 'requires_approval',
        'transaction_date', 'created_at'
    ]
    search_fields = [
        'transaction_number', 'description', 'reference_number',
        'customer', 'vendor', 'employee'
    ]
    ordering = ['-transaction_date', '-transaction_number']
    date_hierarchy = 'transaction_date'
    
    fieldsets = (
        ('Transaction Information', {
            'fields': (
                'transaction_number', 'transaction_date', 'transaction_type',
                'cash_account', 'status'
            )
        }),
        ('Amount and Currency', {
            'fields': ('amount', 'currency', 'exchange_rate')
        }),
        ('Description and References', {
            'fields': ('description', 'reference_number', 'memo')
        }),
        ('Related Parties', {
            'fields': ('customer', 'vendor', 'employee')
        }),
        ('Banking and GL', {
            'fields': ('bank_reference', 'cleared_date', 'gl_journal_entry')
        }),
        ('Approval Workflow', {
            'fields': (
                'requires_approval', 'approved_by', 'approved_at'
            )
        }),
    )
    
    readonly_fields = [
        'created_by', 'posted_by', 'created_at', 'updated_at', 'posted_at'
    ]
    
    def formatted_amount(self, obj):
        """Display formatted amount with currency."""
        return f'{obj.currency} {obj.amount:,.2f}'
    formatted_amount.short_description = 'Amount'
    formatted_amount.admin_order_field = 'amount'
    
    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new transactions."""
        if not change:  # Creating new record
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(CashTransfer)
class CashTransferAdmin(admin.ModelAdmin):
    """Admin interface for Cash Transfers."""
    
    list_display = [
        'transfer_number', 'transfer_date', 'from_account', 'to_account',
        'formatted_amount', 'status'
    ]
    list_filter = ['status', 'transfer_date', 'created_at']
    search_fields = [
        'transfer_number', 'description', 'reference_number'
    ]
    ordering = ['-transfer_date', '-transfer_number']
    date_hierarchy = 'transfer_date'
    
    fieldsets = (
        ('Transfer Information', {
            'fields': (
                'transfer_number', 'transfer_date', 'status'
            )
        }),
        ('Accounts', {
            'fields': ('from_account', 'to_account')
        }),
        ('Amount and Exchange', {
            'fields': ('amount', 'exchange_rate', 'destination_amount')
        }),
        ('Fees', {
            'fields': ('transfer_fee', 'fee_account')
        }),
        ('Description and References', {
            'fields': ('description', 'reference_number', 'memo')
        }),
        ('GL Integration', {
            'fields': ('gl_journal_entry',)
        }),
    )
    
    readonly_fields = [
        'destination_amount', 'created_by', 'posted_by',
        'created_at', 'updated_at', 'posted_at'
    ]
    
    def formatted_amount(self, obj):
        """Display formatted transfer amount."""
        return f'{obj.from_account.currency} {obj.amount:,.2f}'
    formatted_amount.short_description = 'Amount'
    formatted_amount.admin_order_field = 'amount'
    
    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new transfers."""
        if not change:  # Creating new record
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


class BankStatementEntryInline(admin.TabularInline):
    """Inline admin for Bank Statement Entries."""
    
    model = BankStatementEntry
    extra = 0
    fields = [
        'entry_date', 'entry_type', 'amount', 'description',
        'reference_number', 'is_matched', 'matched_transaction'
    ]
    readonly_fields = ['is_matched', 'matched_transaction']


@admin.register(BankStatement)
class BankStatementAdmin(admin.ModelAdmin):
    """Admin interface for Bank Statements."""
    
    list_display = [
        'statement_number', 'cash_account', 'statement_date',
        'formatted_opening_balance', 'formatted_closing_balance',
        'entries_count', 'reconciliation_status'
    ]
    list_filter = [
        'cash_account', 'is_reconciled', 'statement_date', 'created_at'
    ]
    search_fields = ['statement_number', 'cash_account__account_name']
    ordering = ['-statement_date']
    date_hierarchy = 'statement_date'
    
    fieldsets = (
        ('Statement Information', {
            'fields': (
                'statement_number', 'cash_account', 'statement_date'
            )
        }),
        ('Period', {
            'fields': ('period_from', 'period_to')
        }),
        ('Balances', {
            'fields': ('opening_balance', 'closing_balance')
        }),
        ('Import Information', {
            'fields': ('import_file', 'import_format')
        }),
        ('Reconciliation', {
            'fields': ('is_reconciled', 'reconciled_by', 'reconciled_at')
        }),
    )
    
    readonly_fields = [
        'imported_by', 'created_at', 'updated_at'
    ]
    
    inlines = [BankStatementEntryInline]
    
    def formatted_opening_balance(self, obj):
        """Display formatted opening balance."""
        return f'{obj.cash_account.currency} {obj.opening_balance:,.2f}'
    formatted_opening_balance.short_description = 'Opening Balance'
    
    def formatted_closing_balance(self, obj):
        """Display formatted closing balance."""
        return f'{obj.cash_account.currency} {obj.closing_balance:,.2f}'
    formatted_closing_balance.short_description = 'Closing Balance'
    
    def entries_count(self, obj):
        """Display count of statement entries."""
        count = obj.entries.count()
        if count > 0:
            url = reverse('admin:cms_bankstatemententry_changelist') + f'?bank_statement__id__exact={obj.id}'
            return format_html('<a href="{}">{} entries</a>', url, count)
        return '0 entries'
    entries_count.short_description = 'Entries'
    
    def reconciliation_status(self, obj):
        """Display reconciliation status with color coding."""
        if obj.is_reconciled:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Reconciled</span>'
            )
        else:
            matched = obj.entries.filter(is_matched=True).count()
            total = obj.entries.count()
            if total == 0:
                return format_html(
                    '<span style="color: orange;">No Entries</span>'
                )
            percentage = (matched / total) * 100 if total > 0 else 0
            return format_html(
                '<span style="color: red;">{}/{} ({:.1f}%)</span>',
                matched, total, percentage
            )
    reconciliation_status.short_description = 'Status'
    
    def save_model(self, request, obj, form, change):
        """Set imported_by field when creating new statements."""
        if not change:  # Creating new record
            obj.imported_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(BankStatementEntry)
class BankStatementEntryAdmin(admin.ModelAdmin):
    """Admin interface for Bank Statement Entries."""
    
    list_display = [
        'bank_statement', 'entry_date', 'entry_type',
        'formatted_amount', 'description', 'match_status'
    ]
    list_filter = [
        'entry_type', 'is_matched', 'entry_date',
        'bank_statement__cash_account'
    ]
    search_fields = ['description', 'reference_number', 'bank_code']
    ordering = ['bank_statement', 'entry_date']
    date_hierarchy = 'entry_date'
    
    fieldsets = (
        ('Entry Information', {
            'fields': (
                'bank_statement', 'entry_date', 'entry_type'
            )
        }),
        ('Transaction Details', {
            'fields': (
                'amount', 'description', 'reference_number', 'bank_code'
            )
        }),
        ('Reconciliation', {
            'fields': (
                'is_matched', 'matched_transaction',
                'matched_by', 'matched_at'
            )
        }),
        ('Running Balance', {
            'fields': ('running_balance',)
        }),
    )
    
    readonly_fields = ['matched_by', 'matched_at']
    
    def formatted_amount(self, obj):
        """Display formatted amount with type indicator."""
        currency = obj.bank_statement.cash_account.currency
        if obj.entry_type == 'DEBIT':
            return format_html(
                '<span style="color: red;">-{} {}</span>',
                currency, f'{obj.amount:,.2f}'
            )
        else:
            return format_html(
                '<span style="color: green;">+{} {}</span>',
                currency, f'{obj.amount:,.2f}'
            )
    formatted_amount.short_description = 'Amount'
    formatted_amount.admin_order_field = 'amount'
    
    def match_status(self, obj):
        """Display match status with color coding."""
        if obj.is_matched:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Matched</span>'
            )
        else:
            return format_html(
                '<span style="color: red;">✗ Unmatched</span>'
            )
    match_status.short_description = 'Status'


@admin.register(CashFlowForecast)
class CashFlowForecastAdmin(admin.ModelAdmin):
    """Admin interface for Cash Flow Forecasts."""
    
    list_display = [
        'forecast_name', 'forecast_type', 'cash_account',
        'period_from', 'period_to', 'formatted_net_flow',
        'confidence_level', 'is_active'
    ]
    list_filter = [
        'forecast_type', 'cash_account', 'is_active',
        'period_from', 'created_at'
    ]
    search_fields = ['forecast_name', 'assumptions']
    ordering = ['-period_from']
    date_hierarchy = 'period_from'
    
    fieldsets = (
        ('Forecast Information', {
            'fields': (
                'forecast_name', 'forecast_type', 'cash_account'
            )
        }),
        ('Period', {
            'fields': ('period_from', 'period_to')
        }),
        ('Projections', {
            'fields': (
                'projected_receipts', 'projected_payments', 'net_cash_flow'
            )
        }),
        ('Analysis', {
            'fields': ('confidence_level', 'assumptions')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
    )
    
    readonly_fields = [
        'net_cash_flow', 'created_by', 'created_at', 'updated_at'
    ]
    
    def formatted_net_flow(self, obj):
        """Display formatted net cash flow with color coding."""
        currency = obj.cash_account.currency
        if obj.net_cash_flow < 0:
            return format_html(
                '<span style="color: red;">{} {}</span>',
                currency, f'{obj.net_cash_flow:,.2f}'
            )
        elif obj.net_cash_flow > 0:
            return format_html(
                '<span style="color: green;">{} {}</span>',
                currency, f'{obj.net_cash_flow:,.2f}'
            )
        else:
            return f'{currency} {obj.net_cash_flow:,.2f}'
    formatted_net_flow.short_description = 'Net Cash Flow'
    formatted_net_flow.admin_order_field = 'net_cash_flow'
    
    def save_model(self, request, obj, form, change):
        """Set created_by field when creating new forecasts."""
        if not change:  # Creating new record
            obj.created_by = request.user
        super().save_model(request, obj, form, change) 