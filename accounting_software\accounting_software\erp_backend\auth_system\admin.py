from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import Role, RolePermission, UserProfile, ModulePermission


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ['role_name', 'description', 'is_active', 'is_system_role', 'parent_role']
    list_filter = ['is_active', 'is_system_role']
    search_fields = ['role_name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(RolePermission)
class RolePermissionAdmin(admin.ModelAdmin):
    list_display = ['role', 'permission', 'is_active', 'can_delegate', 'granted_by']
    list_filter = ['is_active', 'can_delegate', 'role']
    search_fields = ['role__role_name', 'permission__name']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'full_name', 'employee_id', 'department', 'role', 'is_active']
    list_filter = ['is_active', 'role', 'department']
    search_fields = ['user__username', 'full_name', 'employee_id']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(ModulePermission)
class ModulePermissionAdmin(admin.ModelAdmin):
    list_display = ['module', 'permission_type', 'description', 'risk_level', 'is_active']
    list_filter = ['module', 'permission_type', 'risk_level', 'is_active']
    search_fields = ['description']


# Extend the default User admin to show profile information
class UserProfileInline(admin.StackedInline):
    model = UserProfile
    fk_name = 'user'  # Specify which ForeignKey to use since UserProfile has multiple FKs to User
    can_delete = False
    verbose_name_plural = 'Profile'


class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)


# Re-register UserAdmin
admin.site.unregister(User)
admin.site.register(User, UserAdmin) 