# Generated by Django 4.2.21 on 2025-06-12 09:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentTerm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="E.g., 'Net 30', 'Due on Receipt'", max_length=100, unique=True)),
                ('code', models.CharField(help_text="E.g., 'net_30', 'due_on_receipt'", max_length=50, unique=True)),
                ('days', models.PositiveIntegerField(help_text='Number of days from invoice date')),
                ('description', models.TextField(blank=True, help_text='Optional description', null=True)),
                ('is_default', models.<PERSON>oleanField(default=False, help_text='Is this the default payment term?')),
                ('is_active', models.BooleanField(default=True, help_text='Is this payment term active?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_terms_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Payment Term',
                'verbose_name_plural': 'Payment Terms',
                'db_table': 'sales_payment_terms',
                'ordering': ['days', 'name'],
            },
        ),
    ]
