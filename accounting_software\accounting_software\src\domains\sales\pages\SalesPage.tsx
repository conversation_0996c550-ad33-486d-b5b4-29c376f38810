import React from 'react';
import { Box, Grid, Paper, Typography } from '@mui/material';
import SalesStats from '../components/SalesStats';
import TopCustomers from '../components/TopCustomers';
import SalesChart from '../components/SalesChart';

const SalesPage: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        Sales Dashboard
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <SalesStats />
        </Grid>

        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 2 }}>
            <SalesChart />
          </Paper>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 2 }}>
            <TopCustomers />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SalesPage;