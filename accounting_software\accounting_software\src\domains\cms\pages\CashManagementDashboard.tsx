import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  LinearProgress,
  Alert,
  AlertTitle,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  CreditCard as CreditCardIcon,
  Savings as SavingsIcon,
  AccountBalanceWallet as WalletIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Visibility as ViewIcon,
  Analytics as AnalyticsIcon,
  Timeline as TimelineIcon,
  Schedule as ScheduleIcon,
  CurrencyExchange as CurrencyIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

// Treasury Dashboard Interfaces
interface CashAccount {
  id: number;
  account_name: string;
  account_number: string;
  account_type: string;
  account_type_name: string;
  bank_name?: string;
  currency: string;
  current_balance: number;
  opening_balance: number;
  credit_limit?: number;
  is_active: boolean;
  is_default: boolean;
  last_reconciled_date?: string;
  requires_approval: boolean;
}

interface TreasuryMetrics {
  total_cash_position: number;
  total_liquid_assets: number;
  available_credit: number;
  cash_flow_this_month: number;
  cash_flow_forecast_30_days: number;
  operating_cash_ratio: number;
  quick_ratio: number;
  current_ratio: number;
  days_cash_on_hand: number;
  working_capital: number;
}

interface CashFlowData {
  period: string;
  opening_balance: number;
  total_receipts: number;
  total_payments: number;
  net_cash_flow: number;
  closing_balance: number;
  forecast_confidence: number;
}

interface RecentTransaction {
  id: number;
  transaction_date: string;
  transaction_type: string;
  account_name: string;
  amount: number;
  currency: string;
  description: string;
  status: string;
  requires_approval: boolean;
}

interface BankingAlert {
  id: number;
  alert_type: 'low_balance' | 'high_volume' | 'reconciliation' | 'approval_required' | 'forecast_warning';
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error';
  account_name?: string;
  amount?: number;
  currency?: string;
  created_at: string;
}

const CashManagementDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('THIS_MONTH');
  const [refreshing, setRefreshing] = useState(false);

  // State for dashboard data
  const [accounts, setAccounts] = useState<CashAccount[]>([]);
  const [treasuryMetrics, setTreasuryMetrics] = useState<TreasuryMetrics | null>(null);
  const [cashFlowData, setCashFlowData] = useState<CashFlowData[]>([]);
  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([]);
  const [bankingAlerts, setBankingAlerts] = useState<BankingAlert[]>([]);

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadDashboardData();
  }, [selectedPeriod]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      };

      // Load all dashboard data in parallel
      const [
        accountsResponse,
        metricsResponse,
        cashFlowResponse,
        transactionsResponse,
        alertsResponse
      ] = await Promise.all([
        fetch('http://localhost:8000/api/cms/accounts/', { headers }),
        fetch(`http://localhost:8000/api/cms/dashboard/treasury/?period=${selectedPeriod}`, { headers }),
        fetch(`http://localhost:8000/api/cms/reports/cash-flow/?period=${selectedPeriod}`, { headers }),
        fetch('http://localhost:8000/api/cms/transactions/?limit=10&ordering=-transaction_date', { headers }),
        fetch('http://localhost:8000/api/cms/dashboard/alerts/', { headers }),
      ]);

      if (!accountsResponse.ok || !metricsResponse.ok || !cashFlowResponse.ok || 
          !transactionsResponse.ok || !alertsResponse.ok) {
        throw new Error('Failed to load dashboard data');
      }

      const [accountsData, metricsData, cashFlowDataResult, transactionsData, alertsData] = await Promise.all([
        accountsResponse.json(),
        metricsResponse.json(),
        cashFlowResponse.json(),
        transactionsResponse.json(),
        alertsResponse.json(),
      ]);

      setAccounts(accountsData.results || accountsData);
      setTreasuryMetrics(metricsData);
      setCashFlowData(cashFlowDataResult.cash_flow_periods || []);
      setRecentTransactions(transactionsData.results || transactionsData);
      setBankingAlerts(alertsData.results || alertsData);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
      console.error('Dashboard loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatCurrency = (amount: number, currency?: string) => {
    const currencyCode = currency || currencyInfo?.functional_currency || 'USD';
    const symbol = currencyCode === 'USD' ? '$' : currencyCode === 'EUR' ? '€' : currencyCode;
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      currencyDisplay: 'symbol'
    }).format(amount).replace(currencyCode, symbol);
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'low_balance': return <WarningIcon color="warning" />;
      case 'high_volume': return <TrendingUpIcon color="info" />;
      case 'reconciliation': return <SecurityIcon color="error" />;
      case 'approval_required': return <ScheduleIcon color="warning" />;
      case 'forecast_warning': return <TimelineIcon color="warning" />;
      default: return <ErrorIcon color="error" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'approved': return 'info';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getAccountTypeIcon = (accountType: string) => {
    const type = accountType.toLowerCase();
    if (type.includes('checking')) return <AccountBalanceIcon />;
    if (type.includes('savings')) return <SavingsIcon />;
    if (type.includes('credit')) return <CreditCardIcon />;
    if (type.includes('petty') || type.includes('cash')) return <WalletIcon />;
    if (type.includes('investment')) return <TrendingUpIcon />;
    return <MoneyIcon />;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Treasury Dashboard...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        <AlertTitle>Error Loading Dashboard</AlertTitle>
        {error}
        <Button onClick={loadDashboardData} startIcon={<RefreshIcon />} sx={{ mt: 1 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              💰 Cash Management Dashboard
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Treasury Overview & Cash Flow Analytics
            </Typography>
          </Box>
          <Box display="flex" gap={2} alignItems="center">
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Period</InputLabel>
              <Select
                value={selectedPeriod}
                label="Period"
                onChange={(e) => setSelectedPeriod(e.target.value)}
              >
                <MenuItem value="TODAY">Today</MenuItem>
                <MenuItem value="THIS_WEEK">This Week</MenuItem>
                <MenuItem value="THIS_MONTH">This Month</MenuItem>
                <MenuItem value="THIS_QUARTER">This Quarter</MenuItem>
                <MenuItem value="THIS_YEAR">This Year</MenuItem>
              </Select>
            </FormControl>
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              startIcon={refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
              variant="outlined"
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {/* Banking Alerts */}
        {bankingAlerts.length > 0 && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>Banking Alerts ({bankingAlerts.length})</AlertTitle>
            <List dense>
              {bankingAlerts.slice(0, 3).map((alert) => (
                <ListItem key={alert.id} sx={{ pl: 0 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    {getAlertIcon(alert.alert_type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={alert.title}
                    secondary={alert.message}
                  />
                </ListItem>
              ))}
            </List>
            {bankingAlerts.length > 3 && (
              <Button size="small" sx={{ mt: 1 }}>
                View All {bankingAlerts.length} Alerts
              </Button>
            )}
          </Alert>
        )}

        {/* Treasury Metrics Cards */}
        {treasuryMetrics && (
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={3}>
              <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                      <MoneyIcon />
                    </Avatar>
                    <Typography variant="h6">Total Cash Position</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatCurrency(treasuryMetrics.total_cash_position)}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                    Available Liquidity
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                      <TrendingUpIcon />
                    </Avatar>
                    <Typography variant="h6">Cash Flow (30d)</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatCurrency(treasuryMetrics.cash_flow_forecast_30_days)}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                    Projected Net Flow
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                      <CreditCardIcon />
                    </Avatar>
                    <Typography variant="h6">Available Credit</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatCurrency(treasuryMetrics.available_credit)}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                    Credit Facilities
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={3}>
              <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mr: 2 }}>
                      <ScheduleIcon />
                    </Avatar>
                    <Typography variant="h6">Days Cash on Hand</Typography>
                  </Box>
                  <Typography variant="h4" fontWeight="bold">
                    {Math.round(treasuryMetrics.days_cash_on_hand)}
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                    Days of Operations
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        <Grid container spacing={3}>
          {/* Cash Accounts Summary */}
          <Grid item xs={12} lg={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" component="h2">
                    💳 Cash Accounts
                  </Typography>
                  <Button size="small" startIcon={<ViewIcon />}>
                    View All
                  </Button>
                </Box>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Account</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell align="right">Balance</TableCell>
                        <TableCell align="center">Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {accounts.slice(0, 8).map((account) => (
                        <TableRow key={account.id} hover>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <Avatar sx={{ width: 32, height: 32, mr: 2, bgcolor: 'primary.light' }}>
                                {getAccountTypeIcon(account.account_type_name)}
                              </Avatar>
                              <Box>
                                <Typography variant="body2" fontWeight="medium">
                                  {account.account_name}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {account.account_number}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {account.account_type_name}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography
                              variant="body2"
                              fontWeight="medium"
                              color={account.current_balance < 0 ? 'error.main' : 'text.primary'}
                            >
                              {formatCurrency(account.current_balance, account.currency)}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              size="small"
                              label={account.is_active ? 'Active' : 'Inactive'}
                              color={account.is_active ? 'success' : 'default'}
                              icon={account.is_active ? <CheckCircleIcon /> : <ErrorIcon />}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Transactions */}
          <Grid item xs={12} lg={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" component="h2">
                    📋 Recent Transactions
                  </Typography>
                  <Button size="small" startIcon={<ViewIcon />}>
                    View All
                  </Button>
                </Box>
                <List dense>
                  {recentTransactions.slice(0, 6).map((transaction) => (
                    <ListItem key={transaction.id} divider>
                      <ListItemIcon>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'secondary.light' }}>
                          <ReceiptIcon />
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="body2" fontWeight="medium">
                              {transaction.description}
                            </Typography>
                            <Typography
                              variant="body2"
                              fontWeight="bold"
                              color={transaction.transaction_type === 'RECEIPT' ? 'success.main' : 'text.primary'}
                            >
                              {transaction.transaction_type === 'RECEIPT' ? '+' : '-'}
                              {formatCurrency(transaction.amount, transaction.currency)}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="caption" color="text.secondary">
                              {transaction.account_name} • {dayjs(transaction.transaction_date).format('MMM DD')}
                            </Typography>
                            <Chip
                              size="small"
                              label={transaction.status}
                              color={getStatusColor(transaction.status) as any}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Cash Flow Forecast */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="h2" gutterBottom>
                  📈 Cash Flow Forecast
                </Typography>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  Projected cash flows for the next 12 periods
                </Typography>
                
                {cashFlowData.length > 0 ? (
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Period</TableCell>
                          <TableCell align="right">Opening Balance</TableCell>
                          <TableCell align="right">Receipts</TableCell>
                          <TableCell align="right">Payments</TableCell>
                          <TableCell align="right">Net Flow</TableCell>
                          <TableCell align="right">Closing Balance</TableCell>
                          <TableCell align="center">Confidence</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {cashFlowData.slice(0, 6).map((period, index) => (
                          <TableRow key={index} hover>
                            <TableCell>
                              <Typography variant="body2" fontWeight="medium">
                                {period.period}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              {formatCurrency(period.opening_balance)}
                            </TableCell>
                            <TableCell align="right">
                              <Typography color="success.main">
                                {formatCurrency(period.total_receipts)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography color="error.main">
                                {formatCurrency(period.total_payments)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography
                                fontWeight="medium"
                                color={period.net_cash_flow >= 0 ? 'success.main' : 'error.main'}
                              >
                                {formatCurrency(period.net_cash_flow)}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              <Typography fontWeight="medium">
                                {formatCurrency(period.closing_balance)}
                              </Typography>
                            </TableCell>
                            <TableCell align="center">
                              <Box display="flex" alignItems="center" justifyContent="center">
                                <LinearProgress
                                  variant="determinate"
                                  value={period.forecast_confidence}
                                  sx={{ width: 50, mr: 1 }}
                                />
                                <Typography variant="caption">
                                  {Math.round(period.forecast_confidence)}%
                                </Typography>
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                ) : (
                  <Typography color="text.secondary" textAlign="center" py={4}>
                    No forecast data available
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </LocalizationProvider>
  );
};

export default CashManagementDashboard; 