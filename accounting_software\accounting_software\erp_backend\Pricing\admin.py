from django.contrib import admin
from .models import PriceList, PriceListItem, DiscountRule, CustomerPriceList

@admin.register(PriceList)
class PriceListAdmin(admin.ModelAdmin):
    list_display = ('name', 'currency', 'valid_from', 'valid_to', 'is_active', 'is_default')
    list_filter = ('is_active', 'is_default', 'currency')
    search_fields = ('name', 'description')
    date_hierarchy = 'valid_from'

@admin.register(PriceListItem)
class PriceListItemAdmin(admin.ModelAdmin):
    list_display = ('product', 'price_list', 'unit_price', 'min_quantity', 'discount_percent')
    list_filter = ('price_list',)
    search_fields = ('product__name', 'product__code')
    raw_id_fields = ('product',)  # Better for performance with many products

@admin.register(DiscountRule)
class DiscountRuleAdmin(admin.ModelAdmin):
    list_display = ('name', 'discount_percent', 'customer_group', 'product_category', 'is_active')
    list_filter = ('is_active', 'customer_group', 'product_category')
    search_fields = ('name', 'description')

@admin.register(CustomerPriceList)
class CustomerPriceListAdmin(admin.ModelAdmin):
    list_display = ('customer', 'price_list', 'effective_date', 'expiry_date')
    list_filter = ('price_list',)
    raw_id_fields = ('customer',)  # Better for performance with many customers