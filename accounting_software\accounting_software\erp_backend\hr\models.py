from django.db import models
from django.utils import timezone
from contacts.models import Employee as ContactEmployee


class Employee(models.Model):
    """HR-specific employee information linked to contacts Employee"""
    contact_employee = models.OneToOneField(
        ContactEmployee, 
        on_delete=models.CASCADE, 
        primary_key=True,
        related_name='hr_details'
    )
    
    # Employment Details (HR-specific fields)
    join_date = models.DateField(help_text="Join Date (required)")
    employment_type = models.CharField(
        max_length=20, 
        choices=[
            ('full-time', 'Full Time'),
            ('part-time', 'Part Time'),
            ('contract', 'Contract'),
            ('intern', 'Intern')
        ],
        help_text="Employment Type (required)"
    )
    salary = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        help_text="Salary (required)"
    )
    status = models.CharField(
        max_length=20, 
        choices=[
            ('Active', 'Active'),
            ('On Leave', 'On Leave'),
            ('Terminated', 'Terminated')
        ],
        default='Active',
        help_text="Employment Status"
    )
    
    # HR Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'hr_employees'

    def __str__(self):
        return f"HR Details: {self.contact_employee.contact.name} - {self.contact_employee.position or 'No Position'}"

    @property
    def full_name(self):
        """Get full name from contact employee"""
        if self.contact_employee.first_name and self.contact_employee.last_name:
            return f"{self.contact_employee.first_name} {self.contact_employee.last_name}"
        return self.contact_employee.contact.name

    @property
    def email(self):
        """Get email from contact"""
        return self.contact_employee.contact.email

    @property
    def phone(self):
        """Get phone from contact"""
        return self.contact_employee.contact.phone

class PayrollRun(models.Model):
    """ Represents a single payroll processing run """
    run_date = models.DateTimeField(auto_now_add=True)
    start_date = models.DateField()
    end_date = models.DateField()
    status = models.CharField(max_length=20, choices=[('Pending', 'Pending'), ('Completed', 'Completed'), ('Failed', 'Failed')], default='Pending')
    total_employees_paid = models.IntegerField(default=0)
    total_gross_pay = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_deductions = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_net_pay = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    def __str__(self):
        return f"Payroll Run {self.start_date} to {self.end_date} ({self.status})"

class Payslip(models.Model):
    """ Represents a payslip for a single employee for a specific payroll run """
    payroll_run = models.ForeignKey(PayrollRun, on_delete=models.CASCADE, related_name='payslips')
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='payslips')
    pay_period_start_date = models.DateField()
    pay_period_end_date = models.DateField()
    base_pay = models.DecimalField(max_digits=10, decimal_places=2)
    bonus = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    deductions = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    taxes = models.DecimalField(max_digits=10, decimal_places=2, default=0) # Placeholder - tax logic needed
    gross_pay = models.DecimalField(max_digits=10, decimal_places=2)
    net_pay = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"Payslip for {self.employee.full_name} - {self.pay_period_start_date} to {self.pay_period_end_date}"

    class Meta:
        unique_together = ('payroll_run', 'employee') # Ensure one payslip per employee per run

class Attendance(models.Model):
    """ Represents an attendance record for an employee on a specific date """
    STATUS_CHOICES = [
        ('Present', 'Present'),
        ('Absent', 'Absent'),
        ('Leave', 'Leave'),
        ('Holiday', 'Holiday'),
        ('Half Day', 'Half Day'),
    ]
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendance')
    date = models.DateField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    check_in_time = models.TimeField(null=True, blank=True)
    check_out_time = models.TimeField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)

    class Meta:
        unique_together = ('employee', 'date') # Ensure only one attendance record per employee per day
        ordering = ['date', 'employee__contact_employee__last_name', 'employee__contact_employee__first_name'] # Default ordering

    def __str__(self):
        return f"{self.employee.full_name} - {self.date} ({self.status})"

class Department(models.Model):
    name = models.CharField(max_length=100, blank=False, null=False, help_text="Department Name (required)")
    code = models.CharField(max_length=20, blank=False, null=False, unique=True, help_text="Department Code (required, unique)")
    description = models.TextField(blank=True, null=True, help_text="Department Description (optional)")
    manager = models.ForeignKey(Employee, on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', help_text="Department Manager (optional)")
    parent_department = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='child_departments', help_text="Parent Department (optional)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True, help_text="Whether the department is active")

    class Meta:
        ordering = ['name']
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return f"{self.name} ({self.code})"

    def get_employee_count(self):
        """Get the number of employees in this department"""
        return Employee.objects.filter(department=self.name).count()

    def get_active_employee_count(self):
        """Get the number of active employees in this department"""
        return Employee.objects.filter(department=self.name, status='Active').count() 