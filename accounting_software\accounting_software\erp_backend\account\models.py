from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class UserRole(models.Model):
    """
    This model defines different roles that users can have in the accounting system.
    Each user can have one role that determines their access level and permissions.
    """
    ROLE_CHOICES = [
        ('admin', 'Administrator'),
        ('accountant', 'Accountant'),
        ('sales_manager', 'Sales Manager'),
        ('purchase_manager', 'Purchase Manager'),
        ('inventory_manager', 'Inventory Manager'),
        ('hr_manager', 'HR Manager'),
    ]

    # Link to Django's built-in User model (one user = one role)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='role')
    # The actual role assigned to the user
    role = models.CharField(max_length=50, choices=ROLE_CHOICES)
    # Timestamp when the role was created
    created_at = models.DateTimeField(auto_now_add=True)
    # Timestamp when the role was last updated
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.role}"

class UserPermission(models.Model):
    """
    This model defines specific permissions for users on different modules.
    Users can have different access levels (Full, Moderate, Basic) for each module.
    """
    MODULE_CHOICES = [
        ('accounting', 'Accounting'),
        ('sales', 'Sales'),
        ('purchases', 'Purchases'),
        ('inventory', 'Inventory'),
        ('hr', 'HR'),
    ]

    ACCESS_LEVEL_CHOICES = [
        ('FULL', 'Full Access'),      # Can create, read, update, delete
        ('MODERATE', 'Moderate Access'),  # Can create, read, update
        ('BASIC', 'Basic Access'),    # Can only read
    ]

    # Link to the user who gets the permission
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='permissions')
    # Which module the permission applies to (sales, accounting, etc.)
    module = models.CharField(max_length=50, choices=MODULE_CHOICES)
    # What level of access the user has in that module
    access_level = models.CharField(max_length=20, choices=ACCESS_LEVEL_CHOICES)
    # Timestamp when the permission was created
    created_at = models.DateTimeField(auto_now_add=True)
    # Timestamp when the permission was last updated
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        # Ensure one user can only have one permission per module
        unique_together = ('user', 'module')

    def __str__(self):
        return f"{self.user.username} - {self.module} ({self.access_level})"

class Company(models.Model):
    """
    This model represents a company in the accounting system.
    Each company has basic information, tax details, financial year settings, and currency preferences.
    Only superusers can create and edit company information.
    """
    
    # Define available currencies for the company to choose from
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar ($)'),
        ('EUR', 'Euro (€)'),
        ('GBP', 'British Pound (£)'),
        ('JPY', 'Japanese Yen (¥)'),
        ('CAD', 'Canadian Dollar (C$)'),
        ('AUD', 'Australian Dollar (A$)'),
        ('INR', 'Indian Rupee (₹)'),
        ('CNY', 'Chinese Yuan (¥)'),
        ('NGN', 'Nigerian Naira (₦)'),
        ('ZAR', 'South African Rand (R)'),
    ]

    # Basic Company Information
    name = models.CharField(
        max_length=255, 
        help_text="The legal name of the company as registered with authorities"
    )
    registration_number = models.CharField(
        max_length=100, 
        unique=True, 
        help_text="Official company registration number (must be unique in system)"
    )
    tax_id = models.CharField(
        max_length=100, 
        help_text="Tax identification number or VAT registration number"
    )
    
    # Contact Information
    address = models.TextField(
        help_text="Complete business address including street, city, state, and postal code"
    )
    phone = models.CharField(
        max_length=20, 
        help_text="Primary business phone number"
    )
    email = models.EmailField(
        help_text="Primary business email address for official communications"
    )
    
    # Currency Settings (NEW FIELDS)
    functional_currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='USD',
        help_text="Primary currency for day-to-day business operations and record keeping"
    )
    reporting_currency = models.CharField(
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='USD',
        help_text="Currency used for financial reporting and consolidation (often same as functional)"
    )
    
    # Financial Year Settings
    accounting_year_start = models.DateField(
        help_text="First day of the company's accounting year (e.g., January 1)"
    )
    accounting_year_end = models.DateField(
        help_text="Last day of the company's accounting year (e.g., December 31)"
    )
    fiscal_year_start = models.DateField(
        help_text="First day of the company's fiscal year for tax purposes"
    )
    fiscal_year_end = models.DateField(
        help_text="Last day of the company's fiscal year for tax purposes"
    )
    
    # Tax Registration Information
    sales_tax_registered = models.BooleanField(
        default=False, 
        help_text="Check if company is registered to collect and remit sales tax/VAT"
    )
    sales_tax_number = models.CharField(
        max_length=100, 
        blank=True, 
        null=True, 
        help_text="Sales tax or VAT registration number (required if registered for sales tax)"
    )
    
    # System Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        help_text="Date and time when the company record was created"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Date and time when the company record was last modified"
    )

    class Meta:
        verbose_name_plural = "Companies"
        ordering = ['name']  # Sort companies alphabetically by name

    def __str__(self):
        """Return the company name when the object is printed or displayed"""
        return self.name

    def clean(self):
        """
        Custom validation method that runs before saving the company.
        This ensures business rules are followed:
        1. If sales tax registered, must have sales tax number
        2. Accounting year end must be after start
        3. Fiscal year end must be after start
        """
        from django.core.exceptions import ValidationError
        
        # Check if sales tax number is provided when company is registered for sales tax
        if self.sales_tax_registered and not self.sales_tax_number:
            raise ValidationError({
                'sales_tax_number': 'Sales tax number is required when company is registered for sales tax'
            })
        
        # Ensure accounting year end date is after start date
        if self.accounting_year_start >= self.accounting_year_end:
            raise ValidationError({
                'accounting_year_end': 'Accounting year end date must be after the start date'
            })
        
        # Ensure fiscal year end date is after start date
        if self.fiscal_year_start >= self.fiscal_year_end:
            raise ValidationError({
                'fiscal_year_end': 'Fiscal year end date must be after the start date'
            })

    def get_currency_symbol(self, currency_type='functional'):
        """
        Helper method to get the currency symbol for display purposes.
        
        Args:
            currency_type (str): Either 'functional' or 'reporting'
            
        Returns:
            str: The currency symbol (e.g., '$', '€', '£')
        """
        currency_symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥',
            'CAD': 'C$',
            'AUD': 'A$',
            'INR': '₹',
            'CNY': '¥',
            'NGN': '₦',
            'ZAR': 'R',
        }
        
        if currency_type == 'functional':
            return currency_symbols.get(self.functional_currency, '$')
        else:
            return currency_symbols.get(self.reporting_currency, '$')
