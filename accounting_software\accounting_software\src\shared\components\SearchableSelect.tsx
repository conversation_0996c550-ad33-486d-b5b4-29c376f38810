import React from 'react';
import { Autocomplete, TextField, Typography } from '@mui/material';

// Define a more specific type for options
interface OptionType {
  [key: string]: any;
}

interface SearchableSelectProps<T extends OptionType> {
  options: readonly T[];
  value: T | null;
  onChange: (value: T | null) => void;
  getOptionLabel: (option: T) => string;
  label: string;
  error?: boolean;
  helperText?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  size?: 'small' | 'medium';
  groupBy?: (option: T) => string;
  renderGroup?: (params: any) => React.ReactNode;
  renderOption?: (props: React.HTMLAttributes<HTMLLIElement>, option: T, state: object) => React.ReactNode;
  noOptionsText?: string;
  loadingText?: string;
  loading?: boolean;
  disableClearable?: boolean;
}

/**
 * A searchable select component that wraps Material-UI's Autocomplete
 * with common functionality needed for dropdown fields in the application.
 */
const SearchableSelect = <T extends OptionType>({
  options,
  value,
  onChange,
  getOptionLabel,
  label,
  error = false,
  helperText,
  placeholder,
  required = false,
  disabled = false,
  fullWidth = true,
  size = 'medium',
  groupBy,
  renderGroup,
  renderOption,
  noOptionsText = 'No options',
  loadingText = 'Loading...',
  loading = false,
  disableClearable = false,
}: SearchableSelectProps<T>): React.ReactElement => {
  // Default renderGroup if groupBy is provided but renderGroup is not
  const defaultRenderGroup = (params: any) => (
    <div key={params.key}>
      <Typography
        variant="caption"
        sx={{
          fontWeight: 'bold',
          p: 1,
          display: 'block',
          color: 'text.secondary',
        }}
      >
        {params.group}
      </Typography>
      {params.children}
    </div>
  );

  // Safe type handling for onChange
  const handleChange = (_: React.SyntheticEvent, newValue: T | null) => {
    onChange(newValue);
  };

  return (
    <Autocomplete
      options={options}
      value={value}
      onChange={handleChange}
      getOptionLabel={(option) => {
        // Handle the case where option might be a string (from freeSolo)
        if (typeof option === 'string') {
          return option;
        }
        return getOptionLabel(option as T);
      }}
      isOptionEqualToValue={(option, value) => {
        if (!option || !value) return option === value;
        // Use a unique identifier or the entire object for comparison
        return JSON.stringify(option) === JSON.stringify(value);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          error={error}
          helperText={helperText}
          required={required}
          fullWidth={fullWidth}
          size={size}
        />
      )}
      disabled={disabled}
      groupBy={groupBy as ((option: any) => string) | undefined}
      renderGroup={renderGroup || (groupBy ? defaultRenderGroup : undefined)}
      renderOption={renderOption as any}
      noOptionsText={noOptionsText}
      loadingText={loadingText}
      loading={loading}
      disableClearable={disableClearable}
      multiple={false}
      freeSolo={false}
    />
  );
};

export default SearchableSelect; 