import React from 'react';
import {
  Box,
  FormControlLabel,
  Switch,
  Grid,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Collapse,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { RecurringBillSettings } from '../../../shared/types/bill.types';

interface RecurringBillFormProps {
  value?: RecurringBillSettings;
  onChange: (settings: RecurringBillSettings | undefined) => void;
}

const RecurringBillForm: React.FC<RecurringBillFormProps> = ({ value, onChange }) => {
  const handleChange = (field: keyof RecurringBillSettings, newValue: any) => {
    if (!value && field === 'enabled') {
      // Initialize with default values when enabling
      onChange({
        enabled: true,
        frequency: 'monthly',
        interval: 1,
        startDate: new Date().toISOString(),
        endDate: undefined,
        dayOfMonth: 1,
      });
      return;
    }

    if (!value) return;

    const updatedSettings = { ...value, [field]: newValue };
    
    // If disabling, clear all settings
    if (field === 'enabled' && !newValue) {
      onChange(undefined);
      return;
    }

    onChange(updatedSettings);
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Recurring Bill Settings
      </Typography>
      
      <FormControlLabel
        control={
          <Switch
            checked={value?.enabled || false}
            onChange={(e) => handleChange('enabled', e.target.checked)}
          />
        }
        label="Make this a recurring bill"
      />

      <Collapse in={value?.enabled}>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Frequency</InputLabel>
              <Select
                value={value?.frequency || 'monthly'}
                label="Frequency"
                onChange={(e) => handleChange('frequency', e.target.value)}
              >
                <MenuItem value="weekly">Weekly</MenuItem>
                <MenuItem value="monthly">Monthly</MenuItem>
                <MenuItem value="quarterly">Quarterly</MenuItem>
                <MenuItem value="yearly">Yearly</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type="number"
              label="Interval"
              value={value?.interval || 1}
              onChange={(e) => handleChange('interval', parseInt(e.target.value))}
              inputProps={{ min: 1 }}
              helperText={`Bill will repeat every ${value?.interval || 1} ${value?.frequency || 'month(s)'}`}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <DatePicker
              label="Start Date"
              value={value?.startDate ? dayjs(value.startDate) : null}
              onChange={(date) => handleChange('startDate', date?.toISOString())}
              slotProps={{
                textField: {
                  fullWidth: true,
                  helperText: 'When should this recurring bill start?'
                }
              }}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <DatePicker
              label="End Date (Optional)"
              value={value?.endDate ? dayjs(value.endDate) : null}
              onChange={(date) => handleChange('endDate', date?.toISOString())}
              slotProps={{
                textField: {
                  fullWidth: true,
                  helperText: 'Leave blank for no end date'
                }
              }}
            />
          </Grid>

          {value?.frequency === 'monthly' && (
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Day of Month"
                value={value?.dayOfMonth || 1}
                onChange={(e) => handleChange('dayOfMonth', parseInt(e.target.value))}
                inputProps={{ min: 1, max: 31 }}
                helperText="Which day of the month should the bill be created?"
              />
            </Grid>
          )}
        </Grid>
      </Collapse>
    </Box>
  );
};

export default RecurringBillForm; 