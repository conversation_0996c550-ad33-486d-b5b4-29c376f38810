// Customer Service for API interactions
export interface CustomerOption {
  id: number;
  fullName: string;
  displayName: string;
  email?: string;
  phone?: string;
  customerType: 'individual' | 'business' | 'vendor' | 'employee';
  companyName?: string;
  status: 'active' | 'inactive';
}

interface CustomerResponse {
  id: number;
  customer_id: string;
  display_name: string;
  customer_type: 'individual' | 'business' | 'vendor' | 'employee';
  company_name?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  status: 'active' | 'inactive';
}

class CustomerService {
  private baseUrl = 'http://localhost:8000/api/contacts/customers/';

  /**
   * Get all customers from the API
   */
  async getAllCustomers(): Promise<CustomerResponse[]> {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${this.baseUrl}?page_size=1000&ordering=display_name`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }
  }

  /**
   * Get active customers only (for dropdowns)
   */
  async getActiveCustomers(): Promise<CustomerOption[]> {
    try {
      const customers = await this.getAllCustomers();
      // Filter active customers and transform to dropdown format
      return customers
        .filter(customer => customer.status === 'active')
        .map(customer => ({
          id: customer.id,
          fullName: customer.display_name,
          displayName: customer.display_name,
          email: customer.email,
          phone: customer.phone,
          customerType: customer.customer_type,
          companyName: customer.company_name,
          status: customer.status
        }));
    } catch (error) {
      console.error('Error fetching active customers:', error);
      throw error;
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(id: number): Promise<CustomerResponse | null> {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${this.baseUrl}${id}/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching customer by ID:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const customerService = new CustomerService();
export { CustomerService }; 