import { Invoice } from '../shared/types/invoice.types';

// This is a mock email service. In a real application, you would integrate with
// an email service provider like SendGrid, Mailgun, or AWS SES.
class EmailService {
  /**
   * Send an invoice via email
   * @param invoice The invoice to send
   * @param recipientEmail The recipient's email address
   * @param attachmentBlob The PDF attachment blob
   * @param message Optional custom message
   */
  async sendInvoiceEmail(
    invoice: Invoice, 
    recipientEmail: string, 
    attachmentBlob: Blob,
    message?: string
  ): Promise<boolean> {
    try {
      console.log(`Sending invoice ${invoice.invoiceNumber} to ${recipientEmail}`);
      
      // In a real implementation, you would:
      // 1. Convert the blob to base64 or upload it to a temporary storage
      // 2. Call your email service API with the recipient, subject, body, and attachment
      
      // Mock successful email sending
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log('Email sent successfully');
          resolve(true);
        }, 1500);
      });
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  /**
   * Generate the email body for an invoice
   * @param invoice The invoice data
   * @param message Optional custom message
   */
  generateInvoiceEmailBody(invoice: Invoice, message?: string): string {
    const companyName = 'Your Company Name'; // This would come from your company settings
    
    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .invoice-details { margin-bottom: 20px; }
            .message { margin-bottom: 20px; }
            .cta-button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h2>Invoice from ${companyName}</h2>
            </div>
            
            <div class="invoice-details">
              <p>Dear ${invoice.customer.displayName},</p>
              <p>Please find attached invoice #${invoice.invoiceNumber} for ${invoice.currency} ${invoice.total.toFixed(2)}.</p>
              <p><strong>Due Date:</strong> ${new Date(invoice.dueDate).toLocaleDateString()}</p>
            </div>
            
            ${message ? `<div class="message">${message}</div>` : ''}
            
            <p>You can view the details in the attached PDF.</p>
            
            <p>
              <a href="#" class="cta-button">Pay Now</a>
            </p>
            
            <div class="footer">
              <p>If you have any questions, please contact <NAME_EMAIL>.</p>
              <p>Thank you for your business!</p>
              <p>${companyName}</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }
}

export const emailService = new EmailService(); 