# Generated by Django 4.2.21 on 2025-06-12 22:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('po_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('po_number', models.CharField(max_length=50, unique=True)),
                ('po_date', models.DateField()),
                ('expected_date', models.DateField(blank=True, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_received', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('sent', 'Sent'), ('acknowledged', 'Acknowledged'), ('partial', 'Partially Received'), ('received', 'Received'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('payment_terms', models.CharField(blank=True, max_length=20, null=True)),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('memo', models.TextField(blank=True, null=True)),
                ('ship_to_address', models.TextField(blank=True, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('email_sent_date', models.DateTimeField(blank=True, null=True)),
                ('acknowledged_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_orders_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'purchase_orders',
                'ordering': ['-po_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vendor_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('display_name', models.CharField(max_length=200)),
                ('vendor_type', models.CharField(choices=[('individual', 'Individual'), ('business', 'Business'), ('contractor', 'Contractor'), ('supplier', 'Supplier')], default='business', max_length=20)),
                ('company_name', models.CharField(blank=True, max_length=200, null=True)),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('mobile', models.CharField(blank=True, max_length=20, null=True)),
                ('fax', models.CharField(blank=True, max_length=20, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('billing_street', models.TextField(blank=True, null=True)),
                ('billing_city', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_state', models.CharField(blank=True, max_length=100, null=True)),
                ('billing_postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('billing_country', models.CharField(default='India', max_length=100)),
                ('shipping_street', models.TextField(blank=True, null=True)),
                ('shipping_city', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_state', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('shipping_country', models.CharField(blank=True, max_length=100, null=True)),
                ('tax_registration_number', models.CharField(blank=True, max_length=50, null=True)),
                ('pan_number', models.CharField(blank=True, max_length=20, null=True)),
                ('gstin', models.CharField(blank=True, max_length=20, null=True)),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('opening_balance_date', models.DateField(blank=True, null=True)),
                ('current_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('credit_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('payment_terms', models.CharField(default='net_30', max_length=50)),
                ('currency', models.CharField(default='INR', max_length=10)),
                ('vendor_category', models.CharField(blank=True, help_text='E.g., Raw Materials, Services, Equipment', max_length=100, null=True)),
                ('lead_time_days', models.PositiveIntegerField(default=0, help_text='Standard lead time in days')),
                ('minimum_order_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=10)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_exempt_reason', models.CharField(blank=True, max_length=200, null=True)),
                ('preferred_vendor', models.BooleanField(default=False, help_text='Is this a preferred vendor?')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendors_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'purchase_vendors',
                'ordering': ['display_name'],
            },
        ),
        migrations.CreateModel(
            name='VendorPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('payment_date', models.DateField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('check', 'Check'), ('credit_card', 'Credit Card'), ('bank_transfer', 'Bank Transfer'), ('online', 'Online Payment'), ('other', 'Other')], default='bank_transfer', max_length=20)),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('paid_from_account', models.CharField(blank=True, max_length=200, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_payments_created', to=settings.AUTH_USER_MODEL)),
                ('purchase_order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='purchase.purchaseorder')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='purchase.vendor')),
            ],
            options={
                'db_table': 'purchase_vendor_payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderLineItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField()),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('quantity_received', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('quantity_pending', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('taxable', models.BooleanField(default=True)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='purchase.purchaseorder')),
            ],
            options={
                'db_table': 'purchase_order_line_items',
                'ordering': ['line_order'],
            },
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='vendor',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='purchase.vendor'),
        ),
        migrations.CreateModel(
            name='PaymentTerm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="E.g., 'Net 30', 'Due on Receipt'", max_length=100, unique=True)),
                ('code', models.CharField(help_text="E.g., 'net_30', 'due_on_receipt'", max_length=50, unique=True)),
                ('days', models.PositiveIntegerField(help_text='Number of days from invoice date')),
                ('description', models.TextField(blank=True, help_text='Optional description', null=True)),
                ('is_default', models.BooleanField(default=False, help_text='Is this the default payment term?')),
                ('is_active', models.BooleanField(default=True, help_text='Is this payment term active?')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='purchase_payment_terms_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Payment Term',
                'verbose_name_plural': 'Payment Terms',
                'db_table': 'purchase_payment_terms',
                'ordering': ['days', 'name'],
            },
        ),
    ]
