import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Box,
  Button,
  Paper,
  Typography,
  Alert,
  IconButton,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  InputAdornment,
  Chip,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Inventory as InventoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  PostAdd as PostAddIcon,
  Warehouse as WarehouseIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { invoiceService, Invoice, InvoiceItem, InvoiceCustomer, Warehouse } from '../../../services/invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';
import { customerService } from '../../../services/customer.service';

const CreateInvoicePage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);

  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [customers, setCustomers] = useState<InvoiceCustomer[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [inventoryAvailability, setInventoryAvailability] = useState<Record<number, { available_quantity: number; on_hand_quantity: number; reserved_quantity: number; }>>({});
  const [inventoryLoading, setInventoryLoading] = useState<Record<number, boolean>>({});
  const [postingDialogOpen, setPostingDialogOpen] = useState(false);
  const [postingLoading, setPostingLoading] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState<Partial<Invoice>>({
    customer: 0,
    warehouse: null,
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
    status: 'draft',
    line_items: [] as InvoiceItem[],
    subtotal: 0,
    tax_total: 0,
    total_amount: 0,
    notes: '',
    terms: 'Payment due within 30 days',
  });

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Load warehouses, customers, and products
        const warehousesData = await invoiceService.getWarehouses();
        setWarehouses(warehousesData);
        
        try {
          const customersData = await customerService.getActiveCustomers();
          setCustomers(customersData.map(customer => ({
            id: customer.id,
            display_name: customer.displayName,
            email: customer.email,
            phone: customer.phone,
            payment_terms: undefined // Add payment terms if available
          })));
        } catch (err) {
          console.warn('Failed to load customers:', err);
          // Fallback to empty array - customers must be added from contacts module
          setCustomers([]);
        }
        
        try {
          const productsData = await invoiceService.getProducts();
          setProducts(productsData);
        } catch (err) {
          console.warn('Failed to load products:', err);
          // Fallback to empty array - products must be added from sales module
          setProducts([]);
        }

        // If editing, load the invoice
        if (isEditing && id) {
          const invoice = await invoiceService.getInvoice(parseInt(id));
          setFormData(invoice);
        }
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isEditing]);

  // Load inventory availability for a product
  const loadInventoryAvailability = async (productId: number) => {
    if (!formData.warehouse) return;
    
    try {
      setInventoryLoading(prev => ({ ...prev, [productId]: true }));
      const availability = await invoiceService.getProductInventory(productId, formData.warehouse);
      setInventoryAvailability(prev => ({ ...prev, [productId]: availability }));
    } catch (err) {
      console.error('Error loading inventory availability:', err);
    } finally {
      setInventoryLoading(prev => ({ ...prev, [productId]: false }));
    }
  };

  // Handlers
  const handleFieldChange = (field: keyof Invoice, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-calculate due date when customer changes
    if (field === 'customer' && value) {
      const customer = customers.find(c => c.id === value);
      if (customer?.payment_terms && formData.invoice_date) {
        const dueDate = invoiceService.calculateDueDate(formData.invoice_date, customer.payment_terms);
        setFormData(prev => ({ ...prev, due_date: dueDate }));
      }
    }

    // Clear inventory data when warehouse changes
    if (field === 'warehouse') {
      setInventoryAvailability({});
      // Reload inventory for existing products
      formData.line_items?.forEach(item => {
        if (item.product) {
          loadInventoryAvailability(item.product);
        }
      });
    }
  };

  const addLineItem = () => {
    const newItem: InvoiceItem = {
      product: null,  // Initialize as null instead of undefined
      description: '',
      quantity: 1,
      unit_price: 0,
      line_total: 0,
      tax_rate: 0,
      tax_amount: 0,
      product_name: '',
      product_sku: ''
    };
    
    setFormData(prev => ({
      ...prev,
      line_items: [...(prev.line_items || []), newItem]
    }));
  };

  const updateLineItem = (index: number, field: keyof InvoiceItem, value: any) => {
    setFormData(prev => {
      const items = [...(prev.line_items || [])];
      items[index] = { ...items[index], [field]: value };
      
      // Auto-calculate line total
      if (field === 'quantity' || field === 'unit_price') {
        const quantity = field === 'quantity' ? value : items[index].quantity;
        const unitPrice = field === 'unit_price' ? value : items[index].unit_price;
        items[index].line_total = quantity * unitPrice;
        
        // Calculate tax
        const taxRate = items[index].tax_rate || 0;
        items[index].tax_amount = (items[index].line_total * taxRate) / 100;
      }
      
      // Recalculate totals
      const totals = invoiceService.calculateTotals(items);
      
      return {
        ...prev,
        line_items: items,
        ...totals
      };
    });
  };

  const removeLineItem = (index: number) => {
    setFormData(prev => {
      const items = (prev.line_items || []).filter((_, i) => i !== index);
      const totals = invoiceService.calculateTotals(items);
      
      return {
        ...prev,
        line_items: items,
        ...totals
      };
    });
  };

  const handleProductSelect = (index: number, product: any) => {
    if (product) {
      updateLineItem(index, 'product', product.id);
      updateLineItem(index, 'product_name', product.name);
      updateLineItem(index, 'product_sku', product.sku);
      updateLineItem(index, 'description', product.description || product.name);
      updateLineItem(index, 'unit_price', product.sales_price || 0);
      
      // Load inventory if product tracks inventory
      if (product.track_inventory && formData.warehouse) {
        loadInventoryAvailability(product.id);
      }
    }
  };

  const validateInventory = async (): Promise<boolean> => {
    if (!formData.warehouse || !formData.line_items?.length) return true;

    const inventoryTrackedItems = formData.line_items.filter(item => {
      const product = products.find(p => p.id === item.product);
      return product?.track_inventory;
    });

    if (inventoryTrackedItems.length === 0) return true;

    // For each inventory tracked item, check if we have sufficient quantity
    for (const item of inventoryTrackedItems) {
      const availability = inventoryAvailability[item.product!];
      if (!availability) {
        setError(`Inventory data not available for product ${item.product_name || 'Unknown'}`);
        return false;
      }

      if (availability.available_quantity < item.quantity) {
        setError(`Insufficient inventory for ${item.product_name || 'Unknown'} (requested: ${item.quantity}, available: ${availability.available_quantity})`);
        return false;
      }
    }

    return true;
  };

  const handleSave = async (saveAndClose = false) => {
    try {
      setLoading(true);
      setError(null);

      // Validation
      if (!formData.customer) {
        throw new Error('Please select a customer');
      }
      if (!formData.line_items?.length) {
        throw new Error('Please add at least one line item');
      }

      // Check if warehouse is required for inventory-tracked items
      const hasInventoryItems = formData.line_items.some(item => {
        const product = products.find(p => p.id === item.product);
        return product?.track_inventory;
      });

      if (hasInventoryItems && !formData.warehouse) {
        throw new Error('Please select a warehouse for inventory-tracked products');
      }

      // Validate inventory availability
      const inventoryValid = await validateInventory();
      if (!inventoryValid) {
        return; // Error already set by validateInventory
      }

      // Format line items properly - only send fields that exist in the backend model
      const formattedItems = formData.line_items!.map(item => ({
        product: item.product === undefined ? null : item.product,  // Convert undefined to null
        description: item.description || '',
        quantity: Number(item.quantity || 0),
        unit_price: Number(item.unit_price || 0),
        discount_percent: Number(item.discount_percent || 0),
        line_total: Number(item.line_total || 0),
        taxable: item.taxable !== undefined ? item.taxable : true,
        tax_rate: Number(item.tax_rate || 0),
        tax_amount: Number(item.tax_amount || 0),
        line_order: Number(item.line_order || 0)
        // Note: product_name and product_sku are read-only fields from the Product relation
      }));

      const invoiceData = {
        customer: formData.customer!,
        warehouse: formData.warehouse,
        invoice_date: formData.invoice_date!,
        due_date: formData.due_date!,
        status: formData.status!,
        line_items: formattedItems,
        subtotal: Number(formData.subtotal || 0),
        tax_total: Number(formData.tax_total || 0),
        total_amount: Number(formData.total_amount || 0),
        notes: formData.notes || '',
        terms: formData.terms || '',
        invoice_number: formData.invoice_number || undefined
      };

      let savedInvoice: Invoice;
      if (isEditing && id) {
        savedInvoice = await invoiceService.updateInvoice(parseInt(id), invoiceData);
      } else {
        savedInvoice = await invoiceService.createInvoice(invoiceData);
      }

      // Update form data with saved invoice
      setFormData(savedInvoice);

      if (saveAndClose) {
        navigate('/dashboard/sales/invoices');
      } else {
        // Stay on page but show success message
        setError(null);
        // Could show a success snackbar here
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save invoice');
    } finally {
      setLoading(false);
    }
  };

  const handlePostToInventory = async () => {
    if (!formData.id) return;
    
    try {
      setPostingLoading(true);
      await invoiceService.postToInventory(formData.id);
      
      // Reload the invoice to get updated posting status
      const updatedInvoice = await invoiceService.getInvoice(formData.id);
      setFormData(updatedInvoice);
      
      setPostingDialogOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post invoice to inventory');
    } finally {
      setPostingLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/sales/invoices');
  };

  const getAvailableQuantity = (productId: number): number => {
    const availability = inventoryAvailability[productId];
    if (!availability) return 0;
    return availability.available_quantity || 0;
  };

  const getInventoryStatus = (item: InvoiceItem) => {
    if (!item.product) return null;
    
    const product = products.find(p => p.id === item.product);
    if (!product?.track_inventory) {
      return (
        <Chip size="small" label="Service" color="default" />
      );
    }

    if (!formData.warehouse) {
      return (
        <Chip size="small" label="Select Warehouse" color="warning" icon={<WarningIcon />} />
      );
    }

    const available = getAvailableQuantity(item.product);
    const isLoading = inventoryLoading[item.product];
    
    if (isLoading) {
      return <CircularProgress size={16} />;
    }

    const isInStock = available >= (item.quantity || 0);
    
    return (
      <Tooltip title={`Available: ${available} units`}>
        <Chip 
          size="small" 
          label={`Stock: ${available}`}
          color={isInStock ? 'success' : 'error'}
          icon={isInStock ? <CheckCircleIcon /> : <WarningIcon />}
        />
      </Tooltip>
    );
  };

  const selectedCustomer = customers.find(c => c.id === formData.customer);
  const selectedWarehouse = warehouses.find(w => w.warehouse_id === formData.warehouse);

  return (
    <Box sx={{ 
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1300,
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: 'white',
    }}>
      {/* Header */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        p: 2,
        borderBottom: 1,
        borderColor: 'divider'
      }}>
        <Typography variant="h5">
          {isEditing ? 'Edit Invoice' : 'Create New Invoice'}
          {formData.inventory_posted && (
            <Chip 
              size="small" 
              label="Posted to Inventory" 
              color="success" 
              icon={<CheckCircleIcon />}
              sx={{ ml: 2 }}
            />
          )}
        </Typography>
        <IconButton onClick={handleCancel}>
          <CloseIcon />
        </IconButton>
      </Box>
      
      {/* Main Content */}
      <Box sx={{ 
        flexGrow: 1, 
        overflow: 'auto', 
        p: 3,
        backgroundColor: '#f5f5f5'
      }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Invoice Details */}
        <Card sx={{ mb: 3 }}>
          <CardHeader title="Invoice Details" />
          <CardContent>
            <Grid container spacing={3}>
                          <Grid item xs={12} md={4}>
                <Autocomplete
                  options={customers}
                  getOptionLabel={(option) => option.display_name}
                  value={selectedCustomer || null}
                  onChange={(_, value) => handleFieldChange('customer', value?.id || 0)}
                  renderInput={(params) => (
                    <TextField {...params} label="Customer" required fullWidth />
                )}
                isOptionEqualToValue={(option, value) => option.id === value.id}
                renderOption={(props, option) => (
                  <li {...props} key={option.id}>
                    {option.display_name}
                  </li>
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Invoice Number"
                value={formData.invoice_number || ''}
                onChange={(e) => handleFieldChange('invoice_number', e.target.value)}
                placeholder="Auto-generated if left empty"
                helperText="Leave empty for auto-generation"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <ReceiptIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Autocomplete
                  options={warehouses}
                  getOptionLabel={(option) => option.name}
                  value={selectedWarehouse || null}
                  onChange={(_, value) => handleFieldChange('warehouse', value?.warehouse_id)}
                  renderInput={(params) => (
                    <TextField 
                      {...params} 
                      label="Warehouse" 
                      fullWidth 
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <InputAdornment position="start">
                            <WarehouseIcon />
                          </InputAdornment>
                        ),
                      }}
                      helperText="Required for inventory-tracked products"
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Invoice Date"
                  value={dayjs(formData.invoice_date)}
                  onChange={(date) => handleFieldChange('invoice_date', date?.format('YYYY-MM-DD'))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="Due Date"
                  value={dayjs(formData.due_date)}
                  onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                  slotProps={{ textField: { fullWidth: true, required: true } }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={(e) => handleFieldChange('status', e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="sent">Sent</MenuItem>
                    <MenuItem value="paid">Paid</MenuItem>
                    <MenuItem value="overdue">Overdue</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Line Items */}
        <Card sx={{ mb: 3 }}>
          <CardHeader 
            title="Line Items" 
            action={
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={addLineItem}
              >
                Add Item
              </Button>
            }
          />
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Description</TableCell>
                    <TableCell width={100}>Quantity</TableCell>
                    <TableCell width={120}>Unit Price</TableCell>
                    <TableCell width={80}>Tax %</TableCell>
                    <TableCell width={150}>Inventory Status</TableCell>
                    <TableCell width={120}>Total</TableCell>
                    <TableCell width={50}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formData.line_items?.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Autocomplete
                          options={products}
                          getOptionLabel={(option) => `${option.name} (${option.sku || 'No SKU'})`}
                          onChange={(_, value) => handleProductSelect(index, value)}
                          value={products.find(p => p.id === item.product) || null}
                          isOptionEqualToValue={(option, value) => option.id === value.id}
                          renderOption={(props, option) => (
                            <li {...props} key={option.id}>
                              {option.name} ({option.sku || 'No SKU'})
                            </li>
                          )}
                          renderInput={(params) => (
                            <TextField 
                              {...params} 
                              placeholder="Select product or enter description"
                              value={item.description}
                              onChange={(e) => updateLineItem(index, 'description', e.target.value)}
                            />
                          )}
                        />
                        {item.product_sku && (
                          <Typography variant="caption" color="text.secondary">
                            SKU: {item.product_sku}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateLineItem(index, 'quantity', Number(e.target.value))}
                          inputProps={{ min: 0, step: 0.01 }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => updateLineItem(index, 'unit_price', Number(e.target.value))}
                          inputProps={{ min: 0, step: 0.01 }}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          type="number"
                          value={item.tax_rate || 0}
                          onChange={(e) => updateLineItem(index, 'tax_rate', Number(e.target.value))}
                          inputProps={{ min: 0, max: 100, step: 0.01 }}
                        />
                      </TableCell>
                      <TableCell>
                        {getInventoryStatus(item)}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {formatCurrency(item.line_total + (item.tax_amount || 0))}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => removeLineItem(index)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {!formData.line_items?.length && (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                        <Typography color="text.secondary">
                          No items added. Click "Add Item" to start.
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            {/* Totals */}
            {(formData.line_items?.length || 0) > 0 && (
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Box sx={{ minWidth: 300 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Subtotal:</Typography>
                    <Typography>{formatCurrency(formData.subtotal || 0)}</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Tax:</Typography>
                    <Typography>{formatCurrency(formData.tax_total || 0)}</Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="h6">Total:</Typography>
                    <Typography variant="h6">{formatCurrency(formData.total_amount || 0)}</Typography>
                  </Box>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Notes and Terms */}
        <Card>
          <CardHeader title="Additional Information" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Notes"
                  multiline
                  rows={4}
                  value={formData.notes}
                  onChange={(e) => handleFieldChange('notes', e.target.value)}
                  placeholder="Internal notes (not shown to customer)"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Terms & Conditions"
                  multiline
                  rows={4}
                  value={formData.terms}
                  onChange={(e) => handleFieldChange('terms', e.target.value)}
                  placeholder="Payment terms and conditions"
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>
      
      {/* Footer */}
      <Box sx={{ 
        p: 2, 
        borderTop: 1, 
        borderColor: 'divider',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <Button
          variant="outlined"
          onClick={handleCancel}
          startIcon={<ArrowBackIcon />}
        >
          Cancel
        </Button>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {formData.id && formData.status === 'paid' && !formData.inventory_posted && (
            <Button
              variant="outlined"
              onClick={() => setPostingDialogOpen(true)}
              startIcon={<PostAddIcon />}
              color="primary"
            >
              Post to Inventory
            </Button>
          )}
        <Button
          variant="outlined"
            onClick={() => handleSave(false)}
            disabled={loading}
            startIcon={<SaveIcon />}
          >
            Save Draft
        </Button>
        <Button
          variant="contained"
            onClick={() => handleSave(true)}
            disabled={loading}
          startIcon={<SaveIcon />}
        >
            {loading ? 'Saving...' : 'Save & Close'}
        </Button>
        </Box>
      </Box>

      {/* Post to Inventory Dialog */}
      <Dialog open={postingDialogOpen} onClose={() => setPostingDialogOpen(false)}>
        <DialogTitle>Post Invoice to Inventory</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to post this invoice to inventory?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This will create stock transactions and update inventory levels. This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPostingDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handlePostToInventory} 
            variant="contained" 
            disabled={postingLoading}
            startIcon={postingLoading ? <CircularProgress size={16} /> : <PostAddIcon />}
          >
            {postingLoading ? 'Posting...' : 'Post to Inventory'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CreateInvoicePage; 