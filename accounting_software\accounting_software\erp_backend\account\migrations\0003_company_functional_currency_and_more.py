# Generated by Django 4.2.21 on 2025-06-06 11:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0002_company'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='functional_currency',
            field=models.CharField(choices=[('USD', 'US Dollar ($)'), ('EUR', 'Euro (€)'), ('GBP', 'British Pound (£)'), ('JPY', 'Japanese Yen (¥)'), ('CAD', 'Canadian Dollar (C$)'), ('AUD', 'Australian Dollar (A$)'), ('INR', 'Indian Rupee (₹)'), ('CNY', 'Chinese Yuan (¥)'), ('NGN', 'Nigerian Naira (₦)'), ('ZAR', 'South African Rand (R)')], default='USD', help_text='Primary currency for day-to-day business operations and record keeping', max_length=3),
        ),
        migrations.AddField(
            model_name='company',
            name='reporting_currency',
            field=models.CharField(choices=[('USD', 'US Dollar ($)'), ('EUR', 'Euro (€)'), ('GBP', 'British Pound (£)'), ('JPY', 'Japanese Yen (¥)'), ('CAD', 'Canadian Dollar (C$)'), ('AUD', 'Australian Dollar (A$)'), ('INR', 'Indian Rupee (₹)'), ('CNY', 'Chinese Yuan (¥)'), ('NGN', 'Nigerian Naira (₦)'), ('ZAR', 'South African Rand (R)')], default='USD', help_text='Currency used for financial reporting and consolidation (often same as functional)', max_length=3),
        ),
        migrations.AlterField(
            model_name='company',
            name='accounting_year_end',
            field=models.DateField(help_text="Last day of the company's accounting year (e.g., December 31)"),
        ),
        migrations.AlterField(
            model_name='company',
            name='accounting_year_start',
            field=models.DateField(help_text="First day of the company's accounting year (e.g., January 1)"),
        ),
        migrations.AlterField(
            model_name='company',
            name='address',
            field=models.TextField(help_text='Complete business address including street, city, state, and postal code'),
        ),
        migrations.AlterField(
            model_name='company',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='Date and time when the company record was created'),
        ),
        migrations.AlterField(
            model_name='company',
            name='email',
            field=models.EmailField(help_text='Primary business email address for official communications', max_length=254),
        ),
        migrations.AlterField(
            model_name='company',
            name='fiscal_year_end',
            field=models.DateField(help_text="Last day of the company's fiscal year for tax purposes"),
        ),
        migrations.AlterField(
            model_name='company',
            name='fiscal_year_start',
            field=models.DateField(help_text="First day of the company's fiscal year for tax purposes"),
        ),
        migrations.AlterField(
            model_name='company',
            name='name',
            field=models.CharField(help_text='The legal name of the company as registered with authorities', max_length=255),
        ),
        migrations.AlterField(
            model_name='company',
            name='phone',
            field=models.CharField(help_text='Primary business phone number', max_length=20),
        ),
        migrations.AlterField(
            model_name='company',
            name='registration_number',
            field=models.CharField(help_text='Official company registration number (must be unique in system)', max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='sales_tax_number',
            field=models.CharField(blank=True, help_text='Sales tax or VAT registration number (required if registered for sales tax)', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='sales_tax_registered',
            field=models.BooleanField(default=False, help_text='Check if company is registered to collect and remit sales tax/VAT'),
        ),
        migrations.AlterField(
            model_name='company',
            name='tax_id',
            field=models.CharField(help_text='Tax identification number or VAT registration number', max_length=100),
        ),
        migrations.AlterField(
            model_name='company',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='Date and time when the company record was last modified'),
        ),
    ]
