"""
URL Configuration for General Ledger Module

This defines all the API endpoints for the General Ledger functionality:
- Account Types and Detail Types management
- Chart of Accounts with hierarchical structure
- Journal Entries with posting workflow
- Reporting endpoints (trial balance, account balances)
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for ViewSets
router = DefaultRouter()

# Register ViewSets with the router
router.register(r'account-types', views.AccountTypeViewSet, basename='accounttype')
router.register(r'detail-types', views.DetailTypeViewSet, basename='detailtype')
router.register(r'accounts', views.AccountViewSet, basename='account')
router.register(r'journal-entries', views.JournalEntryViewSet, basename='journalentry')

# New enhanced endpoints
router.register(r'projects', views.ProjectViewSet, basename='project')
router.register(r'products', views.ProductViewSet, basename='product')
router.register(r'voucher-types', views.VoucherTypeViewSet, basename='vouchertype')
router.register(r'sales-tax', views.SalesTaxViewSet, basename='salestax')
router.register(r'tds', views.TDSViewSet, basename='tds')
router.register(r'enhanced-journal-entries', views.EnhancedJournalEntryViewSet, basename='enhancedjournalentry')
router.register(r'recurring-journal-entries', views.RecurringJournalEntryViewSet, basename='recurringjournalentry')

# URL patterns
urlpatterns = [
    # Include all router-generated URLs
    path('', include(router.urls)),
    
    # Additional standalone endpoints
    path('company-currency-info/', views.company_currency_info, name='company-currency-info'),
    path('chart-of-accounts-fast/', views.chart_of_accounts_fast, name='chart-of-accounts-fast'),
    path('accounts/<int:account_id>/ledger/', views.account_ledger, name='account-ledger'),
    path('financial-statements/', views.financial_statements, name='financial-statements'),
    
    # COA Upload endpoints
    path('coa/upload/', views.upload_coa, name='upload-coa'),
    path('coa/template/', views.get_coa_template, name='coa-template'),
    path('account-types-list/', views.get_account_types, name='account-types-list'),
    path('detail-types-list/', views.get_detail_types, name='detail-types-list'),
    path('accounts-list/', views.get_accounts, name='accounts-list'),
] 