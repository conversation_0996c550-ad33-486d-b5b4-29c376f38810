// API Configuration
const API_BASE_URL = 'http://localhost:8000';

export interface GRNReturnItem {
  grn_return_item_id?: number;
  product?: number | null;
  product_name?: string;
  product_sku?: string;
  original_grn_item?: number;
  original_grn_number?: string;
  quantity_received: number | string;
  quantity_returned: number | string;
  unit_cost: number | string;
  return_value?: number | string;
  return_reason: 'DEFECTIVE' | 'WRONG_ITEM' | 'EXCESS_QTY' | 'QUALITY_ISSUE' | 'NOT_ORDERED' | 'EXPIRED' | 'OTHER';
  batch_number?: string;
  condition_notes?: string;
  photos_attached?: boolean;
  line_order?: number;
}

export interface GRNReturn {
  grn_return_id?: number;
  grn_return_number?: string;
  original_grn: number;
  original_grn_number?: string;
  vendor?: number;
  vendor_name?: string;
  warehouse: number;
  warehouse_name?: string;
  returned_by?: number;
  returned_by_name?: string;
  return_date: string;
  return_reason: 'DEFECTIVE' | 'WRONG_ITEM' | 'EXCESS_QTY' | 'QUALITY_ISSUE' | 'NOT_ORDERED' | 'EXPIRED' | 'OTHER';
  status: 'DRAFT' | 'APPROVED' | 'RETURNED' | 'POSTED' | 'CANCELLED';
  notes?: string;
  total_quantity?: number;
  total_value?: number;
  expected_credit_amount?: number;
  actual_credit_amount?: number;
  credit_received_date?: string;
  created_at?: string;
  updated_at?: string;
  approved_at?: string;
  approved_by?: number;
  approved_by_name?: string;
  posted_at?: string;
  posted_by?: number;
  posted_by_name?: string;
  items?: GRNReturnItem[];
}

export interface GRNReturnFormData {
  original_grn: number;
  warehouse: number;
  return_date: string;
  return_reason: 'DEFECTIVE' | 'WRONG_ITEM' | 'EXCESS_QTY' | 'QUALITY_ISSUE' | 'NOT_ORDERED' | 'EXPIRED' | 'OTHER';
  notes?: string;
  items: GRNReturnItem[];
}

export interface GRNReturnStats {
  total_returns: number;
  draft_returns: number;
  approved_returns: number;
  returned_returns: number;
  posted_returns: number;
  recent_returns: number;
  total_value: number;
  expected_credit: number;
}

export interface ReturnableGRNItem {
  grn_item_id: number;
  product: number | null;
  product_name: string;
  product_sku: string;
  quantity: number;
  returned_quantity: number;
  available_quantity: number;
  unit_price: string;
  batch_number: string;
  condition: string;
}

export interface ReturnableGRNDebugResponse {
  returnable_grns: ReturnableGRN[];
  debug_info?: {
    total_grns_in_db: number;
    status_breakdown: { [key: string]: number };
    grns_with_issues: Array<{
      grn_id: number;
      grn_number: string;
      status: string;
      vendor_name: string;
      po_number: string;
      warehouse_name: string;
      issues: string[];
      returnable_items: any[];
    }>;
    help: string;
  };
  message?: string;
}

export interface ReturnableGRN {
  grn_id: number;
  grn_number: string;
  vendor_id: number | null;
  vendor_name: string;
  po_number: string;
  warehouse_id: number | null;
  warehouse_name: string;
  receipt_date: string;
  status: string;
  total_returnable_qty: number;
  total_returnable_value: number;
  returnable_items: ReturnableGRNItem[];
  existing_returns_count: number;
}

class GRNReturnService {
  private baseUrl = `${API_BASE_URL}/api/inventory/grn-returns`;

  async getGRNReturns(params?: {
    page?: number;
    search?: string;
    status?: string;
    warehouse?: number;
    vendor?: number;
    return_reason?: string;
    start_date?: string;
    end_date?: string;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.vendor) queryParams.append('vendor', params.vendor.toString());
    if (params?.return_reason) queryParams.append('return_reason', params.return_reason);
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);

    const response = await fetch(`${this.baseUrl}/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch GRN Returns');
    }

    return response.json();
  }

  async getGRNReturnStats(): Promise<GRNReturnStats> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stats/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch GRN Return stats');
    }

    return response.json();
  }

  async getGRNReturn(id: number): Promise<GRNReturn> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch GRN Return');
    }

    return response.json();
  }

  async createGRNReturn(returnData: GRNReturnFormData): Promise<GRNReturn> {
    const token = localStorage.getItem('token');
    
    console.log('🚀 Creating GRN Return with data:', JSON.stringify(returnData, null, 2));
    
    const response = await fetch(this.baseUrl + '/', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(returnData),
    });

    if (!response.ok) {
      let errorMessage = 'Failed to create GRN Return';
      let errorDetails = {};
      
      try {
        const errorData = await response.json();
        console.error('❌ Backend error response:', JSON.stringify(errorData, null, 2));
        errorDetails = errorData;
        
        if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        } else if (typeof errorData === 'object') {
          // Format validation errors
          const fieldErrors = [];
          for (const [field, errors] of Object.entries(errorData)) {
            if (Array.isArray(errors)) {
              const detailedErrors = errors.map((error, index) => {
                if (typeof error === 'object' && error !== null) {
                  const nestedErrors = Object.entries(error as Record<string, any>).map(([subField, subError]) => {
                    return `${subField}: ${Array.isArray(subError) ? subError.join(', ') : subError}`;
                  }).join(', ');
                  return `Item ${index + 1} - ${nestedErrors}`;
                }
                return error;
              });
              fieldErrors.push(`${field}: ${detailedErrors.join('; ')}`);
            } else if (typeof errors === 'object' && errors !== null) {
              const nestedErrors = Object.entries(errors as Record<string, any>).map(([subField, subError]) => {
                return `${subField}: ${Array.isArray(subError) ? subError.join(', ') : subError}`;
              }).join(', ');
              fieldErrors.push(`${field}: ${nestedErrors}`);
            } else {
              fieldErrors.push(`${field}: ${errors}`);
            }
          }
          if (fieldErrors.length > 0) {
            errorMessage = `Validation errors: ${fieldErrors.join('; ')}`;
          }
        }
      } catch (e) {
        console.error('❌ Error parsing error response:', e);
      }
      
      const error = new Error(errorMessage) as any;
      error.details = errorDetails;
      throw error;
    }

    const result = await response.json();
    console.log('✅ GRN Return created successfully:', result);
    return result;
  }

  async updateGRNReturn(id: number, returnData: Partial<GRNReturnFormData>): Promise<GRNReturn> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(returnData),
    });

    if (!response.ok) {
      throw new Error('Failed to update GRN Return');
    }

    return response.json();
  }

  async deleteGRNReturn(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete GRN Return');
    }
  }

  async approveGRNReturn(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/approve/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to approve GRN Return');
    }
  }

  async markAsReturned(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/mark_returned/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to mark GRN Return as returned');
    }
  }

  async postToInventory(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/${id}/post_to_inventory/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to post GRN Return to inventory');
    }
  }

  async getReturnableGRNs(): Promise<ReturnableGRN[] | ReturnableGRNDebugResponse> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/returnable_grns/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch returnable GRNs');
    }

    const data = await response.json();
    
    // Handle new debug response format
    if (data && typeof data === 'object' && 'returnable_grns' in data) {
      console.log('🔍 DEBUG: API Response with debug info:', data);
      
      if (data.debug_info) {
        console.log('📊 DEBUG: Database status:', data.debug_info);
        
        if (data.debug_info.grns_with_issues && data.debug_info.grns_with_issues.length > 0) {
          console.warn('⚠️ GRNs with issues found:', data.debug_info.grns_with_issues);
        }
      }
      
      if (data.message) {
        console.log('💬 API Message:', data.message);
      }
      
      return data as ReturnableGRNDebugResponse;
    }
    
    // Handle old format for backward compatibility
    return Array.isArray(data) ? data : [];
  }
}

export const grnReturnService = new GRNReturnService(); 