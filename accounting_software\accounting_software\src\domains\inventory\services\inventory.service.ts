// API Configuration
const API_BASE_URL = 'http://localhost:8000';

export interface InventoryItem {
  inventory_id: number;
  product: number;
  product_name: string;
  product_sku: string;
  warehouse: number;
  warehouse_name: string;
  quantity_on_hand: number;
  quantity_reserved: number;
  quantity_available: number;
  reorder_point: number;
  maximum_stock: number;
  average_cost: number;
  last_cost: number;
  last_transaction_date?: string;
  created_at: string;
  updated_at: string;
}

export interface InventoryStats {
  total_items: number;
  low_stock_items: number;
  out_of_stock_items: number;
  total_value: number;
}

export interface StockTransaction {
  stock_txn_id: number;
  product: number;
  product_name: string;
  product_sku: string;
  warehouse: number;
  warehouse_name: string;
  transaction_type: 'RECEIPT' | 'ISSUE' | 'TRANSFER' | 'ADJUSTMENT' | 'SALE' | 'RETURN' | 'DAMAGE' | 'OPENING';
  reference_type: 'GRN' | 'SO' | 'PO' | 'ADJ' | 'TRF' | 'RET' | 'OPEN';
  reference_id: number;
  quantity: number;
  unit_cost: number;
  total_cost: number;
  txn_date: string;
  description: string;
  batch_number?: string;
  created_at: string;
  created_by: number;
  created_by_name: string;
}

export interface Warehouse {
  warehouse_id: number;
  name: string;
  code: string;
  address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

class InventoryService {
  private baseUrl = `${API_BASE_URL}/api/inventory`;

  // Inventory Management
  async getInventory(params?: {
    page?: number;
    search?: string;
    warehouse?: number;
    product?: number;
    low_stock?: boolean;
    out_of_stock?: boolean;
    page_size?: number;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.product) queryParams.append('product', params.product.toString());
    if (params?.low_stock) queryParams.append('low_stock', 'true');
    if (params?.out_of_stock) queryParams.append('out_of_stock', 'true');
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const response = await fetch(`${this.baseUrl}/inventory/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch inventory');
    }

    return response.json();
  }

  async getInventoryStats(): Promise<InventoryStats> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/inventory/stats/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch inventory stats');
    }

    return response.json();
  }

  // Stock Transactions
  async getStockTransactions(params?: {
    page?: number;
    search?: string;
    transaction_type?: string;
    reference_type?: string;
    warehouse?: number;
    product?: number;
    start_date?: string;
    end_date?: string;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.transaction_type) queryParams.append('transaction_type', params.transaction_type);
    if (params?.reference_type) queryParams.append('reference_type', params.reference_type);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.product) queryParams.append('product', params.product.toString());
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);

    const response = await fetch(`${this.baseUrl}/stock-transactions/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch stock transactions');
    }

    return response.json();
  }

  async getAllStockTransactions(): Promise<StockTransaction[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stock-transactions/?no_pagination=true`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch all stock transactions');
    }

    const data = await response.json();
    // When no_pagination=true, the response is directly an array
    return Array.isArray(data) ? data : (data.results || []);
  }

  async getStockTransactionSummary() {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stock-transactions/summary/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch stock transaction summary');
    }

    return response.json();
  }

  // Warehouses
  async getWarehouses(params?: {
    page?: number;
    search?: string;
    is_active?: boolean;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());

    const response = await fetch(`${this.baseUrl}/warehouses/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouses');
    }

    return response.json();
  }

  async getAllWarehouses(): Promise<Warehouse[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/warehouses/?is_active=true&page_size=100`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouses');
    }

    const data = await response.json();
    return data.results || data;
  }

  async createWarehouse(warehouseData: Partial<Warehouse>): Promise<Warehouse> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/warehouses/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(warehouseData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to create warehouse');
    }

    return response.json();
  }

  async updateWarehouse(id: number, warehouseData: Partial<Warehouse>): Promise<Warehouse> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/warehouses/${id}/`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(warehouseData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update warehouse');
    }

    return response.json();
  }

  async deleteWarehouse(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/warehouses/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete warehouse');
    }
  }
}

export const inventoryService = new InventoryService(); 