# Generated by Django 4.2.21 on 2025-06-14 11:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Role',
            fields=[
                ('role_id', models.AutoField(primary_key=True, serialize=False)),
                ('role_name', models.CharField(help_text='Name of the role', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of the role')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this role is active')),
                ('is_system_role', models.BooleanField(default=False, help_text='Whether this is a system-defined role')),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='roles_created', to=settings.AUTH_USER_MODEL)),
                ('parent_role', models.ForeignKey(blank=True, help_text='Parent role for role hierarchy', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_roles', to='auth_system.role')),
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
                'db_table': 'roles',
                'ordering': ['role_name'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(blank=True, help_text='Full display name', max_length=200)),
                ('employee_id', models.CharField(blank=True, help_text='Employee ID', max_length=50, unique=True)),
                ('department', models.CharField(blank=True, help_text='Department', max_length=100)),
                ('position', models.CharField(blank=True, help_text='Job position', max_length=100)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('mobile', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether user account is active')),
                ('password_expires_at', models.DateTimeField(blank=True, null=True)),
                ('last_password_change', models.DateTimeField(blank=True, null=True)),
                ('failed_login_attempts', models.PositiveIntegerField(default=0)),
                ('account_locked_until', models.DateTimeField(blank=True, null=True)),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('language', models.CharField(default='en', max_length=10)),
                ('date_format', models.CharField(default='YYYY-MM-DD', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('additional_roles', models.ManyToManyField(blank=True, help_text='Additional roles assigned to this user', related_name='additional_users', to='auth_system.role')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_profiles_created', to=settings.AUTH_USER_MODEL)),
                ('role', models.ForeignKey(help_text='Primary role for this user', on_delete=django.db.models.deletion.PROTECT, related_name='users', to='auth_system.role')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
                'db_table': 'user_profiles',
            },
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this permission is active')),
                ('granted_at', models.DateTimeField(auto_now_add=True)),
                ('can_delegate', models.BooleanField(default=False, help_text='Whether users with this role can delegate this permission')),
                ('granted_by', models.ForeignKey(help_text='User who granted this permission', on_delete=django.db.models.deletion.PROTECT, related_name='permissions_granted', to=settings.AUTH_USER_MODEL)),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_permissions', to='auth.permission')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_permissions', to='auth_system.role')),
            ],
            options={
                'verbose_name': 'Role Permission',
                'verbose_name_plural': 'Role Permissions',
                'db_table': 'role_permissions',
                'unique_together': {('role', 'permission')},
            },
        ),
        migrations.CreateModel(
            name='ModulePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module', models.CharField(choices=[('GL', 'General Ledger'), ('AR', 'Accounts Receivable'), ('AP', 'Accounts Payable'), ('SALES', 'Sales Management'), ('PURCHASE', 'Purchase Management'), ('INVENTORY', 'Inventory Management'), ('HR', 'Human Resources'), ('CMS', 'Cash Management'), ('REPORTS', 'Reports & Analytics'), ('ADMIN', 'System Administration')], max_length=20)),
                ('permission_type', models.CharField(choices=[('VIEW', 'View'), ('CREATE', 'Create'), ('EDIT', 'Edit'), ('DELETE', 'Delete'), ('APPROVE', 'Approve'), ('POST', 'Post Transactions'), ('REVERSE', 'Reverse Transactions'), ('EXPORT', 'Export Data'), ('IMPORT', 'Import Data'), ('ADMIN', 'Administrative Access')], max_length=20)),
                ('description', models.CharField(max_length=200)),
                ('risk_level', models.CharField(choices=[('LOW', 'Low Risk'), ('MEDIUM', 'Medium Risk'), ('HIGH', 'High Risk'), ('CRITICAL', 'Critical Risk')], default='LOW', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('django_permission', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='module_permission', to='auth.permission')),
            ],
            options={
                'verbose_name': 'Module Permission',
                'verbose_name_plural': 'Module Permissions',
                'db_table': 'module_permissions',
                'ordering': ['module', 'permission_type'],
                'unique_together': {('module', 'permission_type')},
            },
        ),
    ]
