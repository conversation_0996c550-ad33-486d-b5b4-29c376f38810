import React, { useEffect, useState } from 'react';
import {
  <PERSON>, Card, CardHeader, CardContent, <PERSON><PERSON><PERSON>, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Snackbar, Alert, IconButton, Chip, InputAdornment, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, AccountBalance as LiabilityIcon, TrendingUp as AssetIcon } from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';

// TDS type
interface TDS {
  id: number;
  tds_type: 'payment_deduction' | 'receipt_deduction';
  tds_type_display: string;
  description: string;
  rate: number;
  section: string;
  threshold_limit: number | null;
  remarks: string;
  created_by_username?: string;
  created_at?: string;
  updated_at?: string;
  is_liability: boolean;
  is_asset: boolean;
}

const API_URL = 'http://localhost:8000/api/tds/';

const TDSPage: React.FC = () => {
  const { user } = useAuth();
  const isSuperUser = user?.is_superuser || false;

  const [tdsEntries, setTdsEntries] = useState<TDS[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  console.log('TDSPage - Current user:', user);
  console.log('TDSPage - Is superuser:', isSuperUser);
  const [openDialog, setOpenDialog] = useState(false);
  const [editing, setEditing] = useState<TDS | null>(null);
  const [form, setForm] = useState<Omit<TDS, 'id' | 'tds_type_display' | 'is_liability' | 'is_asset'> & { id?: number }>({ 
    tds_type: 'payment_deduction', 
    description: '', 
    rate: 0, 
    section: '', 
    threshold_limit: null, 
    remarks: '' 
  });
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });

  // Fetch TDS entries
  const fetchTDS = async () => {
    setError(null);
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      console.log('Fetching with token:', token ? 'Present' : 'Missing');
      const res = await fetch(API_URL, {
        headers: { 'Content-Type': 'application/json', ...(token && { 'Authorization': `Token ${token}` }) },
      });
      console.log('API Response status:', res.status);
      if (!res.ok) {
        let errorText = 'Unknown error';
        try {
          const errorData = await res.json();
          errorText = errorData.detail || JSON.stringify(errorData);
        } catch {
          errorText = await res.text();
        }
        console.log('API Error response:', errorText);
        if (res.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to access TDS data.');
        } else {
          throw new Error(`Failed to fetch TDS data: ${res.status} - ${errorText}`);
        }
      }
      const data = await res.json();
      console.log('TDS data received:', data);
      // Handle paginated response from Django REST Framework
      const tdsArray = Array.isArray(data) ? data : (data.results || []);
      setTdsEntries(tdsArray);
    } catch (err: any) {
      console.error('Error fetching TDS data:', err);
      setError(err.message || 'Failed to load TDS data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { fetchTDS(); }, []);

  // Handlers
  const handleOpenDialog = (tdsType: 'payment_deduction' | 'receipt_deduction') => {
    setEditing(null);
    setForm({ tds_type: tdsType, description: '', rate: 0, section: '', threshold_limit: null, remarks: '' });
    setOpenDialog(true);
  };
  const handleEdit = (tds: TDS) => {
    setEditing(tds);
    setForm({ 
      tds_type: tds.tds_type,
      description: tds.description,
      rate: tds.rate,
      section: tds.section,
      threshold_limit: tds.threshold_limit,
      remarks: tds.remarks
    });
    setOpenDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditing(null);
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(f => ({ 
      ...f, 
      [name]: name === 'rate' || name === 'threshold_limit' ? 
        (value === '' ? null : parseFloat(value) || 0) : value 
    }));
  };
  const handleSelectChange = (name: string, value: string) => {
    setForm(f => ({ ...f, [name]: value }));
  };
  const handleSave = async () => {
    if (!form.description || form.rate <= 0) {
      setSnackbar({ open: true, message: 'Description and Rate (greater than 0) are required.', severity: 'error' });
      return;
    }
    try {
      const token = localStorage.getItem('token');
      const method = editing ? 'PUT' : 'POST';
      const url = editing ? `${API_URL}${editing.id}/` : API_URL;
      const res = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json', ...(token && { 'Authorization': `Token ${token}` }) },
        body: JSON.stringify(form),
      });
      if (!res.ok) {
        let errorText = 'Unknown error';
        try {
          const errorData = await res.json();
          errorText = errorData.detail || JSON.stringify(errorData);
        } catch {
          errorText = await res.text();
        }
        if (res.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to save TDS data.');
        } else {
          throw new Error(`Failed to save TDS: ${res.status} - ${errorText}`);
        }
      }
      setSnackbar({ open: true, message: editing ? 'TDS updated.' : 'TDS created.', severity: 'success' });
      setOpenDialog(false);
      fetchTDS();
    } catch (err: any) {
      setSnackbar({ open: true, message: err.message || 'Failed to save.', severity: 'error' });
    }
  };
  const handleDelete = async (id: number) => {
    if (!window.confirm('Delete this TDS entry?')) return;
    try {
      const token = localStorage.getItem('token');
      const res = await fetch(`${API_URL}${id}/`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json', ...(token && { 'Authorization': `Token ${token}` }) },
      });
      if (!res.ok) {
        let errorText = 'Unknown error';
        try {
          const errorData = await res.json();
          errorText = errorData.detail || JSON.stringify(errorData);
        } catch {
          errorText = await res.text();
        }
        if (res.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to delete TDS data.');
        } else {
          throw new Error(`Failed to delete TDS: ${res.status} - ${errorText}`);
        }
      }
      setSnackbar({ open: true, message: 'TDS deleted.', severity: 'success' });
      fetchTDS();
    } catch (err: any) {
      setSnackbar({ open: true, message: err.message || 'Failed to delete.', severity: 'error' });
    }
  };

  // Split TDS by type
  const paymentDeductions = Array.isArray(tdsEntries) ? tdsEntries.filter(t => t?.tds_type === 'payment_deduction') : [];
  const receiptDeductions = Array.isArray(tdsEntries) ? tdsEntries.filter(t => t?.tds_type === 'receipt_deduction') : [];

  const formatCurrency = (amount: number | null) => {
    if (amount === null || amount === undefined) return 'No limit';
    return new Intl.NumberFormat('en-IN', { 
      style: 'currency', 
      currency: 'INR',
      minimumFractionDigits: 0 
    }).format(amount);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardHeader title={<Typography variant="h5">TDS (Tax Deducted at Source) Setup</Typography>} />
        <CardContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          {loading && <Typography>Loading TDS data...</Typography>}
          {!loading && !error && (
            <>
              {/* TDS while making payments - LIABILITY */}
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2, mb: 1 }}>
                <LiabilityIcon color="error" sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Tax Deducted at Source while Making Payment
                </Typography>
                <Chip label="LIABILITY" color="error" size="small" sx={{ ml: 2 }} />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                TDS deducted by your company when making payments to vendors/contractors. This creates a liability to deposit with the government.
              </Typography>
              <TableContainer sx={{ mb: 3 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Description</TableCell>
                      <TableCell>Section</TableCell>
                      <TableCell>Rate (%)</TableCell>
                      <TableCell>Threshold Limit</TableCell>
                      <TableCell>Remarks</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paymentDeductions.map(tds => (
                      <TableRow key={tds.id}>
                        <TableCell>{tds.description}</TableCell>
                        <TableCell>
                          <Chip label={tds.section} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell>{tds.rate}%</TableCell>
                        <TableCell>{formatCurrency(tds.threshold_limit)}</TableCell>
                        <TableCell>{tds.remarks}</TableCell>
                        <TableCell>
                          {isSuperUser && (
                            <>
                              <IconButton size="small" onClick={() => handleEdit(tds)}><EditIcon /></IconButton>
                              <IconButton size="small" onClick={() => handleDelete(tds.id)}><DeleteIcon /></IconButton>
                            </>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                    {isSuperUser && (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          <Button startIcon={<AddIcon />} onClick={() => handleOpenDialog('payment_deduction')} size="small">
                            Add Payment Deduction
                          </Button>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* TDS while receiving payments - ASSET */}
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 4, mb: 1 }}>
                <AssetIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Tax Deducted at Source while Receiving Payment
                </Typography>
                <Chip label="ADVANCE TAX / ASSET" color="success" size="small" sx={{ ml: 2 }} />
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                TDS deducted by clients when making payments to your company. This becomes advance tax credit that can be claimed against your tax liability.
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Description</TableCell>
                      <TableCell>Section</TableCell>
                      <TableCell>Rate (%)</TableCell>
                      <TableCell>Threshold Limit</TableCell>
                      <TableCell>Remarks</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {receiptDeductions.map(tds => (
                      <TableRow key={tds.id}>
                        <TableCell>{tds.description}</TableCell>
                        <TableCell>
                          <Chip label={tds.section} size="small" variant="outlined" />
                        </TableCell>
                        <TableCell>{tds.rate}%</TableCell>
                        <TableCell>{formatCurrency(tds.threshold_limit)}</TableCell>
                        <TableCell>{tds.remarks}</TableCell>
                        <TableCell>
                          {isSuperUser && (
                            <>
                              <IconButton size="small" onClick={() => handleEdit(tds)}><EditIcon /></IconButton>
                              <IconButton size="small" onClick={() => handleDelete(tds.id)}><DeleteIcon /></IconButton>
                            </>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                    {isSuperUser && (
                      <TableRow>
                        <TableCell colSpan={6} align="center">
                          <Button startIcon={<AddIcon />} onClick={() => handleOpenDialog('receipt_deduction')} size="small">
                            Add Receipt Deduction
                          </Button>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog for Add/Edit */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editing ? 'Edit TDS' : `Add ${form.tds_type === 'payment_deduction' ? 'Payment Deduction' : 'Receipt Deduction'} TDS`}
        </DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel>TDS Type</InputLabel>
            <Select
              value={form.tds_type}
              onChange={(e) => handleSelectChange('tds_type', e.target.value)}
              disabled={editing !== null} // Don't allow changing type when editing
            >
              <MenuItem value="payment_deduction">Tax Deducted while Making Payment (Liability)</MenuItem>
              <MenuItem value="receipt_deduction">Tax Deducted while Receiving Payment (Asset)</MenuItem>
            </Select>
          </FormControl>
          <TextField
            label="Description"
            name="description"
            value={form.description}
            onChange={handleChange}
            fullWidth
            margin="normal"
            required
            placeholder="e.g., TDS on Professional Fees"
          />
          <TextField
            label="Income Tax Section"
            name="section"
            value={form.section}
            onChange={handleChange}
            fullWidth
            margin="normal"
            placeholder="e.g., 194J, 194C, 194A"
          />
          <TextField
            label="Rate (%)"
            name="rate"
            value={form.rate || ''}
            onChange={handleChange}
            type="number"
            fullWidth
            margin="normal"
            required
            inputProps={{ step: '0.001', min: 0 }}
          />
          <TextField
            label="Threshold Limit"
            name="threshold_limit"
            value={form.threshold_limit || ''}
            onChange={handleChange}
            type="number"
            fullWidth
            margin="normal"
            InputProps={{
              startAdornment: <InputAdornment position="start">₹</InputAdornment>,
            }}
            placeholder="Minimum amount above which TDS applies"
            helperText="Leave empty if no threshold limit"
          />
          <TextField
            label="Remarks"
            name="remarks"
            value={form.remarks}
            onChange={handleChange}
            fullWidth
            margin="normal"
            multiline
            rows={3}
            placeholder="Additional notes about this TDS type"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">{editing ? 'Update' : 'Save'}</Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSnackbar(s => ({ ...s, open: false }))} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TDSPage; 