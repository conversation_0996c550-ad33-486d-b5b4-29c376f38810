import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Bill, BillFormData, BillStatus } from '../shared/types/bill.types';
import { Vendor } from '../shared/types/purchase.types';

interface BillContextType {
  bills: Bill[];
  addBill: (billData: BillFormData) => Promise<Bill>;
  updateBill: (bill: Bill) => Promise<void>;
  deleteBill: (billId: string) => Promise<void>;
  getBillById: (billId: string) => Bill | undefined;
  isLoading: boolean;
  error: string | null;
  refreshBills: () => Promise<void>;
}

const BillContext = createContext<BillContextType | undefined>(undefined);

export const useBills = () => {
  const context = useContext(BillContext);
  if (!context) {
    throw new Error('useBills must be used within a BillProvider');
  }
  return context;
};

export const BillProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [bills, setBills] = useState<Bill[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch bills from API (disabled since no bills endpoint exists)
  const fetchBills = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Since there's no bills endpoint, just set empty array
      setBills([]);
    } catch (err) {
      console.error('Error fetching bills:', err);
      setError(err instanceof Error ? err.message : 'Failed to load bills');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBills();
  }, []);

  const addBill = async (billData: BillFormData): Promise<Bill> => {
    try {
      setError(null);
      
      // First, get the account IDs for the accounts we need using the fast COA service
      let expenseAccountId = null;
      let payableAccountId = null;
      let salesTaxAccountId = null;

      try {
        const { loadChartOfAccountsFast } = await import('../services/gl.service');
        const coaData = await loadChartOfAccountsFast();
        
        // Get all active transaction accounts
        const accounts = coaData.accounts.filter((account: any) => 
          !account.is_header_account && account.is_active
        );
        
        console.log('Available accounts:', accounts.map((acc: any) => ({ id: acc.id, number: acc.account_number, name: acc.account_name })));
        
        // Look for Inventory account (for GRN-based entries)
        const inventoryAccount = accounts.find((acc: any) => 
          acc.account_number === '1130' || 
          acc.account_number === '1300' ||
          acc.account_name.toLowerCase().includes('inventory') ||
          acc.account_name.toLowerCase().includes('stock')
        );
        
        // Look for Sales Tax Payable account
        const salesTaxAccount = accounts.find((acc: any) => 
          acc.account_number === '2300' || 
          acc.account_number === '2310' ||
          acc.account_name.toLowerCase().includes('sales tax') ||
          acc.account_name.toLowerCase().includes('tax payable') ||
          acc.account_name.toLowerCase().includes('input tax')
        );
        
        // Look for accounts payable (try multiple patterns)
        const payableAccount = accounts.find((acc: any) => 
          acc.account_number === '2110' || 
          acc.account_number === '2000' ||
          acc.account_number === '2100' ||
          acc.account_name.toLowerCase().includes('payable') ||
          acc.account_name.toLowerCase().includes('accounts payable')
        );

        expenseAccountId = inventoryAccount?.id;
        payableAccountId = payableAccount?.id;
        const salesTaxAccountId = salesTaxAccount?.id;
        
        console.log('Found accounts:', { 
          inventory: inventoryAccount ? { id: inventoryAccount.id, name: inventoryAccount.account_name } : null,
          salesTax: salesTaxAccount ? { id: salesTaxAccount.id, name: salesTaxAccount.account_name } : null,
          payable: payableAccount ? { id: payableAccount.id, name: payableAccount.account_name } : null
        });
      } catch (fastApiError) {
        console.error('Fast COA service failed, falling back to basic API:', fastApiError);
        
        // Fallback to basic API if fast service fails
        const authToken = localStorage.getItem('token');
        const accountsResponse = await fetch('http://localhost:8000/api/gl/accounts/?page_size=1000', {
          headers: {
            'Authorization': `Token ${authToken}`,
            'Content-Type': 'application/json',
          },
        });

        if (accountsResponse.ok) {
          const accountsData = await accountsResponse.json();
          const accounts = accountsData.results || accountsData;
          
          console.log('Available accounts (fallback):', accounts.map((acc: any) => ({ id: acc.id, number: acc.account_number, name: acc.account_name })));
          
          // Look for Inventory account (for GRN-based entries)
          const inventoryAccount = accounts.find((acc: any) => 
            acc.account_number === '1130' || 
            acc.account_number === '1300' ||
            acc.account_name.toLowerCase().includes('inventory') ||
            acc.account_name.toLowerCase().includes('stock')
          );
          
          // Look for Sales Tax Payable account
          const salesTaxAccount = accounts.find((acc: any) => 
            acc.account_number === '2300' || 
            acc.account_number === '2310' ||
            acc.account_name.toLowerCase().includes('sales tax') ||
            acc.account_name.toLowerCase().includes('tax payable') ||
            acc.account_name.toLowerCase().includes('input tax')
          );
          
          // Look for accounts payable (try multiple patterns)
          const payableAccount = accounts.find((acc: any) => 
            acc.account_number === '2110' || 
            acc.account_number === '2000' ||
            acc.account_number === '2100' ||
            acc.account_name.toLowerCase().includes('payable') ||
            acc.account_name.toLowerCase().includes('accounts payable')
          );

          expenseAccountId = inventoryAccount?.id;
          payableAccountId = payableAccount?.id;
          salesTaxAccountId = salesTaxAccount?.id;
          
          console.log('Found accounts (fallback):', { 
            inventory: inventoryAccount ? { id: inventoryAccount.id, name: inventoryAccount.account_name } : null,
            salesTax: salesTaxAccount ? { id: salesTaxAccount.id, name: salesTaxAccount.account_name } : null,
            payable: payableAccount ? { id: payableAccount.id, name: payableAccount.account_name } : null
          });
        }
      }

      // If we can't find the accounts, throw an error instead of using invalid IDs
      if (!expenseAccountId || !payableAccountId) {
        const missingAccounts = [];
        if (!expenseAccountId) missingAccounts.push('Inventory Account');
        if (!payableAccountId) missingAccounts.push('Accounts Payable');
        
        throw new Error(`Required accounts not found: ${missingAccounts.join(', ')}. Please ensure the Chart of Accounts is properly set up with inventory and accounts payable accounts.`);
      }

      // Debug: Log the bill data to understand what values are being passed
      console.log('Bill Data Received:', {
        subtotal: billData.subtotal,
        taxAmount: billData.taxAmount,
        total: billData.total,
        calculatedTotal: (billData.subtotal || 0) + (billData.taxAmount || 0),
        difference: (billData.total || 0) - ((billData.subtotal || 0) + (billData.taxAmount || 0))
      });

      // Validate that total = subtotal + tax (basic accounting check)
      const expectedTotal = (billData.subtotal || 0) + (billData.taxAmount || 0);
      if (Math.abs((billData.total || 0) - expectedTotal) > 0.01) {
        throw new Error(`Total amount (${billData.total}) does not equal subtotal (${billData.subtotal}) + tax (${billData.taxAmount}). Expected total: ${expectedTotal}`);
      }
      
      // Transform bill data to journal entry format
      const journalData = {
        entry_number: billData.billNumber || `JE-${Date.now()}`,
        transaction_date: billData.billDate,
        description: billData.notes || 'Journal Voucher Entry',
        reference_number: billData.billNumber || '',
        status: 'DRAFT',
        entry_type: 'GENERAL',
        currency: billData.currency || 'USD',
        exchange_rate: 1.0,
        journal_lines: [
          // Inventory line (debit)
          {
            account: expenseAccountId,
            description: `Inventory from ${billData.notes || 'GRN'}`,
            debit_amount: billData.subtotal || 0,
            credit_amount: 0,
            line_number: 1
          },
          // Sales Tax line (debit) - only if there's tax amount
          ...(billData.taxAmount && billData.taxAmount > 0 && salesTaxAccountId ? [{
            account: salesTaxAccountId,
            description: `Input Tax on ${billData.notes || 'GRN'}`,
            debit_amount: billData.taxAmount,
            credit_amount: 0,
            line_number: 2
          }] : []),
          // Accounts Payable line (credit)
          {
            account: payableAccountId,
            description: `AP - ${billData.vendorName || 'Vendor'}`,
            debit_amount: 0,
            credit_amount: billData.total || 0,
            line_number: billData.taxAmount && billData.taxAmount > 0 && salesTaxAccountId ? 3 : 2
          }
        ]
      };

      // Debug: Log the journal entry data to verify balance
      const totalDebits = journalData.journal_lines.reduce((sum, line) => sum + line.debit_amount, 0);
      const totalCredits = journalData.journal_lines.reduce((sum, line) => sum + line.credit_amount, 0);
      console.log('Journal Entry Balance Check:', {
        totalDebits,
        totalCredits,
        isBalanced: Math.abs(totalDebits - totalCredits) < 0.01,
        billData: {
          subtotal: billData.subtotal,
          taxAmount: billData.taxAmount,
          total: billData.total
        },
        journalLines: journalData.journal_lines
      });

      // Validate that the entry is balanced before sending
      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        throw new Error(`Journal entry is not balanced: Debits ${totalDebits} ≠ Credits ${totalCredits}. Please check your amounts.`);
      }

      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/gl/journal-entries/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        },
        body: JSON.stringify(journalData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Journal entry creation failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          sentData: journalData
        });
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const newJournalEntry = await response.json();
      
      // Transform journal entry back to bill format for consistency
      const newBill: Bill = {
        id: newJournalEntry.id.toString(),
        billNumber: newJournalEntry.entry_number,
        billDate: newJournalEntry.transaction_date,
        vendorId: billData.vendorId,
        vendorName: billData.vendorName || '',
        dueDate: billData.dueDate,
        paymentTerms: billData.paymentTerms,
        currency: billData.currency || 'USD',
        items: billData.items || [],
        notes: newJournalEntry.description,
        subtotal: billData.subtotal || 0,
        taxAmount: billData.taxAmount || 0,
        total: billData.total || 0,
        status: 'draft' as BillStatus,
        vendor: {
          id: typeof billData.vendorId === 'string' ? parseInt(billData.vendorId) : billData.vendorId,
          name: billData.vendorName || '',
          contactName: '',
          email: '',
          phone: '',
          address: {
            street: '',
            city: '',
            state: '',
            postalCode: '',
            country: ''
          },
          paymentTerms: billData.paymentTerms,
          currency: billData.currency || 'USD',
          status: 'active',
          balance: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        paidAmount: 0,
        balanceDue: billData.total || 0,
        createdAt: newJournalEntry.created_at,
        updatedAt: newJournalEntry.updated_at
      };
      
      // Add to local state
      setBills((prevBills) => [...prevBills, newBill]);
      
      return newBill;
    } catch (err) {
      console.error('Error creating bill:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create bill';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateBill = async (updatedBill: Bill): Promise<void> => {
    try {
      setError(null);
      
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/purchase/bills/${updatedBill.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        },
        body: JSON.stringify(updatedBill)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const updated = await response.json();
      
      // Update local state
      setBills((prevBills) =>
        prevBills.map((bill) =>
          bill.id === updatedBill.id ? updated : bill
        )
      );
    } catch (err) {
      console.error('Error updating bill:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update bill';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteBill = async (billId: string): Promise<void> => {
    try {
      setError(null);
      
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/purchase/bills/${billId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Remove from local state
      setBills((prevBills) => prevBills.filter((bill) => bill.id !== billId));
    } catch (err) {
      console.error('Error deleting bill:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete bill';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const getBillById = (billId: string): Bill | undefined => {
    return bills.find((bill) => bill.id === billId);
  };

  const refreshBills = async (): Promise<void> => {
    await fetchBills();
  };

  const value = {
    bills,
    addBill,
    updateBill,
    deleteBill,
    getBillById,
    isLoading,
    error,
    refreshBills,
  };

  return <BillContext.Provider value={value}>{children}</BillContext.Provider>;
};

export default BillProvider; 