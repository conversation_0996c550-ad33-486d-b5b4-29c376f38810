import React from 'react';
import {
  <PERSON>,
  IconButton,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import DataTable from '../../../shared/components/DataTable';

interface Permission {
  module: string;
  accessLevel: string;
}

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  permissions: Permission[];
}

interface UserListProps {
  users: User[];
  onDelete: (id: number) => void;
  onEdit: (user: User) => void;
}

const UserList: React.FC<UserListProps> = ({ users, onDelete, onEdit }) => {
  const columns = [
    { field: 'firstName', headerName: 'First Name', flex: 1 },
    { field: 'lastName', headerName: 'Last Name', flex: 1 },
    { field: 'email', headerName: 'Email', flex: 1.5 },
    { 
      field: 'permissions',
      headerName: 'Permissions',
      flex: 2,
      renderCell: (params: { row: User }) => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          {(params.row.permissions || []).map((perm: Permission) => (
            <Chip
              key={`${perm.module}-${perm.accessLevel}`}
              label={`${perm.module} (${perm.accessLevel})`}
              size="small"
              color={perm.accessLevel === 'FULL' ? 'success' : 
                     perm.accessLevel === 'MODERATE' ? 'primary' : 'default'}
            />
          ))}
        </Box>
      )
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params: { row: User }) => (
        <Box>
          <Tooltip title="Edit User">
            <IconButton onClick={() => onEdit(params.row)} size="small">
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Delete User">
            <IconButton onClick={() => onDelete(params.row.id)} size="small" color="error">
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  return (
    <DataTable
      rows={users}
      columns={columns}
      pageSize={10}
      checkboxSelection={false}
    />
  );
};

export default UserList;