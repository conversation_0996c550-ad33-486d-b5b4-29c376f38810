import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Alert,
  CircularProgress,
  Typography,
} from '@mui/material';
import { Undo as UndoIcon } from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import GRNReturnForm from '../components/GRNReturnForm';
import { grnReturnService } from '../services/grn-return.service';

const GRNReturnFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialValues, setInitialValues] = useState<any>(null);

  const isEditMode = Boolean(id && id !== 'new');

  useEffect(() => {
    if (isEditMode) {
      loadGRNReturn();
    }
  }, [id, isEditMode]);

  const loadGRNReturn = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const grnReturn = await grnReturnService.getGRNReturn(parseInt(id));
      setInitialValues(grnReturn);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRN Return');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = () => {
    console.log('✅ GRN Return saved - navigating to list');
    // Navigate back to GRN Returns list
    navigate('/dashboard/inventory/grn-returns');
  };

  const handleCancel = () => {
    console.log('❌ GRN Return cancelled - navigating back');
    // Navigate back to GRN Returns list
    navigate('/dashboard/inventory/grn-returns');
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Loading return details...</Typography>
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader
        title={isEditMode ? 'Edit Good Return Note' : 'Create Good Return Note'}
      />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {!isEditMode && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Create a new goods return note
        </Typography>
      )}

      {isEditMode && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Edit return #{id}
        </Typography>
      )}

      <Paper sx={{ p: 0, minHeight: 600 }}>
        <GRNReturnForm
          onClose={handleCancel}
          onSave={handleSave}
          initialValues={initialValues}
        />
      </Paper>
    </PageContainer>
  );
};

export default GRNReturnFormPage; 