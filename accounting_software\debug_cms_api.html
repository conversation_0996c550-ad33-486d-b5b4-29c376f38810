<!DOCTYPE html>
<html>
<head>
    <title>CMS API Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #ddd; 
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            white-space: pre-wrap; 
            border-radius: 4px;
            border: 1px solid #e9ecef;
            max-height: 300px;
            overflow-y: auto;
        }
        .token-input { 
            width: 100%; 
            padding: 8px; 
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        h1 { color: #333; text-align: center; }
        h3 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CMS API Debug Tool</h1>
        <p style="text-align: center; color: #666;">Test and debug Cash Management System API endpoints</p>
        
        <div class="test-section">
            <h3>🔑 Authentication Setup</h3>
            <p>Current token: <span id="current-token">Not set</span></p>
            <input type="text" id="token-input" class="token-input" placeholder="Enter authentication token..." />
            <button onclick="setToken()">Set Token</button>
            <button onclick="setAdminToken()">Use Admin Token</button>
            <button onclick="clearToken()">Clear Token</button>
            <div id="token-result"></div>
        </div>

        <div class="test-section">
            <h3>🏥 Server Health Check</h3>
            <button onclick="checkServer()">Check Django Server</button>
            <div id="server-result"></div>
        </div>

        <div class="test-section">
            <h3>💳 Test CMS Endpoints</h3>
            <button onclick="testEndpoint('/api/cms/accounts/', 'Cash Accounts')">Test Accounts</button>
            <button onclick="testEndpoint('/api/cms/dashboard/treasury/', 'Treasury Dashboard')">Test Treasury</button>
            <button onclick="testEndpoint('/api/cms/transactions/', 'Transactions')">Test Transactions</button>
            <button onclick="testEndpoint('/api/cms/account-types/', 'Account Types')">Test Account Types</button>
            <button onclick="testEndpoint('/api/gl/company-currency-info/', 'Currency Info')">Test Currency</button>
            <div id="endpoint-results"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Detailed API Response</h3>
            <button onclick="getDetailedResponse()">Get Detailed Account Data</button>
            <div id="detailed-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Frontend Integration Test</h3>
            <button onclick="testFrontendIntegration()">Test Frontend API Calls</button>
            <div id="frontend-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        const ADMIN_TOKEN = 'adb3513c-f82f-47fd-96b5-07df3db3ce07';

        function updateTokenDisplay() {
            const token = localStorage.getItem('token');
            document.getElementById('current-token').textContent = 
                token ? `${token.substring(0, 10)}...` : 'Not set';
        }

        function setToken() {
            const token = document.getElementById('token-input').value.trim();
            if (token) {
                localStorage.setItem('token', token);
                updateTokenDisplay();
                showResult('token-result', 'Token set successfully!', 'success');
            } else {
                showResult('token-result', 'Please enter a token', 'error');
            }
        }

        function setAdminToken() {
            localStorage.setItem('token', ADMIN_TOKEN);
            document.getElementById('token-input').value = ADMIN_TOKEN;
            updateTokenDisplay();
            showResult('token-result', 'Admin token set successfully!', 'success');
        }

        function clearToken() {
            localStorage.removeItem('token');
            document.getElementById('token-input').value = '';
            updateTokenDisplay();
            showResult('token-result', 'Token cleared', 'warning');
        }

        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.className = `test-section ${type}`;
            element.innerHTML = `
                <div class="status-indicator status-${type === 'error' ? 'error' : type === 'warning' ? 'pending' : 'success'}"></div>
                ${message}
            `;
        }

        async function checkServer() {
            try {
                showResult('server-result', 'Checking server...', 'warning');
                const response = await fetch(`${API_BASE}/admin/`, { method: 'HEAD' });
                if (response.ok || response.status === 302) {
                    showResult('server-result', '✅ Django server is running!', 'success');
                } else {
                    showResult('server-result', `⚠️ Server responded with status: ${response.status}`, 'warning');
                }
            } catch (error) {
                showResult('server-result', `❌ Server not reachable: ${error.message}`, 'error');
            }
        }

        async function testEndpoint(endpoint, name) {
            const token = localStorage.getItem('token');
            if (!token) {
                showResult('endpoint-results', '❌ No token set. Please set a token first.', 'error');
                return;
            }

            try {
                showResult('endpoint-results', `Testing ${name}...`, 'warning');
                
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const count = data.results ? data.results.length : (Array.isArray(data) ? data.length : 'N/A');
                    showResult('endpoint-results', 
                        `✅ ${name}: Success (${response.status}) - ${count} items`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult('endpoint-results', 
                        `❌ ${name}: Failed (${response.status}) - ${errorText}`, 'error');
                }
            } catch (error) {
                showResult('endpoint-results', `❌ ${name}: Error - ${error.message}`, 'error');
            }
        }

        async function getDetailedResponse() {
            const token = localStorage.getItem('token');
            if (!token) {
                showResult('detailed-result', '❌ No token set. Please set a token first.', 'error');
                return;
            }

            try {
                showResult('detailed-result', 'Fetching detailed account data...', 'warning');
                
                const response = await fetch(`${API_BASE}/api/cms/accounts/`, {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('detailed-result').innerHTML = `
                        <div class="test-section success">
                            <h4>✅ Accounts API Response:</h4>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorText = await response.text();
                    document.getElementById('detailed-result').innerHTML = `
                        <div class="test-section error">
                            <h4>❌ API Error (${response.status}):</h4>
                            <pre>${errorText}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                showResult('detailed-result', `❌ Network Error: ${error.message}`, 'error');
            }
        }

        async function testFrontendIntegration() {
            const token = localStorage.getItem('token');
            if (!token) {
                showResult('frontend-result', '❌ No token set. Please set a token first.', 'error');
                return;
            }

            const endpoints = [
                { url: '/api/cms/accounts/', name: 'Accounts' },
                { url: '/api/cms/dashboard/treasury/', name: 'Treasury' },
                { url: '/api/cms/transactions/?limit=5', name: 'Transactions' }
            ];

            let results = '<h4>🧪 Frontend Integration Test Results:</h4>';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE}${endpoint.url}`, {
                        headers: {
                            'Authorization': `Token ${token}`,
                            'Content-Type': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        results += `<p>✅ ${endpoint.name}: OK (${data.results?.length || 'N/A'} items)</p>`;
                    } else {
                        results += `<p>❌ ${endpoint.name}: Failed (${response.status})</p>`;
                    }
                } catch (error) {
                    results += `<p>❌ ${endpoint.name}: Error - ${error.message}</p>`;
                }
            }

            document.getElementById('frontend-result').innerHTML = `
                <div class="test-section success">
                    ${results}
                    <p><strong>Next Steps:</strong></p>
                    <ul>
                        <li>If all tests pass: Your CMS frontend should work!</li>
                        <li>If tests fail: Check Django server and token</li>
                        <li>Open browser console on CMS pages for detailed errors</li>
                    </ul>
                </div>
            `;
        }

        // Initialize
        updateTokenDisplay();
        
        // Auto-check server on load
        window.addEventListener('load', () => {
            setTimeout(checkServer, 1000);
        });
    </script>
</body>
</html> 