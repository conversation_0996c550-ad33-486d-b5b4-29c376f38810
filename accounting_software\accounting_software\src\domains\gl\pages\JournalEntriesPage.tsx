import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Fab,
  Tooltip,
  Alert,
  Snackbar,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Autocomplete,
  Divider,
  AlertTitle,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Print as PrintIcon,
  Cancel as CancelIcon,
  Save as SaveIcon,
  Clear as ClearIcon,
  Business as BusinessIcon,
  DateRange as DateRangeIcon,
  AttachFile as AttachFileIcon,
  Close as CloseIcon,
  Repeat as RepeatIcon,
  ContentCopy as CopyIcon,

} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';
import { 
  JournalLineTable,
  CurrencyDisplay,
  FormHeaderFooter,
  type JournalLineItem,
  type AccountOption
} from '../../../shared/components';
import { accountsApi, loadChartOfAccountsFast } from '../../../services/gl.service';



// Types
interface Account {
  id: number;
  account_number: string;
  account_name: string;
  account_type: string;
  is_active: boolean;
  is_header_account: boolean;
}

interface JournalLine {
  id?: number;
  line_number: number;
  account: number | null;
  account_name?: string;
  debit_amount: number | string;
  credit_amount: number | string;
  description: string;
  memo?: string;
  customer?: string;
  vendor?: string;
  project?: number | null;
  department?: string;
  location?: string;
}

interface JournalEntry {
  id?: number;
  entry_number: string;
  transaction_date: string;
  description: string;
  reference_number?: string;
  memo?: string;
  status: 'DRAFT' | 'POSTED' | 'PENDING' | 'REVERSED';
  entry_type?: string;
  currency?: string;
  exchange_rate?: number;
  created_by?: string;
  created_at?: string;
  journal_lines: JournalLine[];
  total_debits?: number | string;
  total_credits?: number | string;
  is_balanced?: boolean;
}

const JournalEntriesPage: React.FC = () => {
  // Currency information from company setup
  const { currencyInfo, loading: currencyLoading, error: currencyError } = useCurrencyInfo();
  
  // State Management
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);

  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingEntry, setEditingEntry] = useState<JournalEntry | null>(null);
  const [viewingEntry, setViewingEntry] = useState<JournalEntry | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [entryToDelete, setEntryToDelete] = useState<JournalEntry | null>(null);
  const [resetTableColumns, setResetTableColumns] = useState(false);
  const [showNewJEButton, setShowNewJEButton] = useState(false);
  

  
  // Pagination & Filtering
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFrom, setDateFrom] = useState<Dayjs | null>(null);
  const [dateTo, setDateTo] = useState<Dayjs | null>(null);
  
  // Alerts
  const [alert, setAlert] = useState<{type: 'success' | 'error' | 'info', message: string} | null>(null);

  // API Functions


  // Form validation
  const validationSchema = Yup.object({
    entry_number: Yup.string().required('Entry number is required'),
    transaction_date: Yup.string().required('Transaction date is required'),
    description: Yup.string().required('Description is required'),
    journal_lines: Yup.array().min(2, 'At least 2 journal lines required'),
  });

  // Create empty journal line
  const createEmptyLine = (lineNumber: number): JournalLine => ({
    line_number: lineNumber,
    account: null,
    debit_amount: 0,
    credit_amount: 0,
    description: '',
    memo: '',
    customer: '',
    vendor: '',
    project: null,
    department: '',
    location: ''
  });

  // Transform functions for reusable component
  const transformToJournalLineItems = (journalLines: JournalLine[]): JournalLineItem[] => {
    return journalLines.map((line) => {
      // Use stored account_name if available, otherwise lookup from accounts array
      const accountName = line.account_name || 
                         accounts.find(acc => acc.id === line.account)?.account_name || '';
      
      return {
        id: line.line_number.toString(), // Use line_number as ID like in CashPaymentForm
        account_id: line.account,
        account_name: accountName,
        description: line.description,
        memo: line.memo || '',
        amount: 0, // Not used in journal mode
        debit_amount: typeof line.debit_amount === 'string' ? parseFloat(line.debit_amount) || 0 : line.debit_amount,
        credit_amount: typeof line.credit_amount === 'string' ? parseFloat(line.credit_amount) || 0 : line.credit_amount,
      };
    });
  };

  const transformToAccountOptions = (accountList: Account[]): AccountOption[] => {
    return accountList.map(account => ({
      id: account.id,
      account_number: account.account_number,
      account_name: account.account_name,
      detail_type: account.account_type,
      account_type: account.account_type,
    }));
  };

  // Generate unique journal entry number
  const generateEntryNumber = useCallback(() => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const prefix = `JE-${year}${month}-`;
    
    // Find existing entry numbers for this month
    const existingNumbers = journalEntries
      .filter(entry => entry.entry_number.startsWith(prefix))
      .map(entry => {
        const numberPart = entry.entry_number.replace(prefix, '');
        return parseInt(numberPart) || 0;
      })
      .sort((a, b) => b - a); // Sort descending
    
    // Find the next available number
    let nextNumber = 1;
    if (existingNumbers.length > 0) {
      nextNumber = existingNumbers[0] + 1;
    }
    
    return `${prefix}${String(nextNumber).padStart(3, '0')}`;
  }, [journalEntries]);

  // Initial form values
  const getInitialValues = (): JournalEntry => ({
    entry_number: generateEntryNumber(),
    transaction_date: dayjs().format('YYYY-MM-DD'),
    description: '',
    reference_number: '',
    memo: '',
    status: 'DRAFT',
    entry_type: 'GENERAL',
          currency: currencyInfo?.functional_currency || '',
    exchange_rate: 1.0,
    journal_lines: [createEmptyLine(1), createEmptyLine(2), createEmptyLine(3), createEmptyLine(4)],
    total_debits: 0,
    total_credits: 0,
    is_balanced: false
  });

  // Formik setup - only initialize when currency info is loaded
  const formik = useFormik({
    initialValues: getInitialValues(),
    validationSchema,
    onSubmit: async (values) => {
      await handleSaveEntry(values);
    },
    enableReinitialize: true, // Reinitialize when currency info loads
  });

  // Load data on mount
  useEffect(() => {
    loadJournalEntries();
    loadAccounts();
  }, []);

  // Debug currency info when it loads
  useEffect(() => {
    if (currencyInfo) {
      console.log('Currency Info Loaded:', {
        functional_currency: currencyInfo.functional_currency,
        functional_currency_symbol: currencyInfo.functional_currency_symbol,
        company_name: currencyInfo.company_name
      });
    }
  }, [currencyInfo]);

  // Calculate totals when lines change
  useEffect(() => {
    const lines = formik.values.journal_lines;
    const totalDebits = lines.reduce((sum: number, line: JournalLine) => {
      const debitAmount = typeof line.debit_amount === 'string' ? parseFloat(line.debit_amount) : line.debit_amount;
      return sum + (debitAmount || 0);
    }, 0);
    const totalCredits = lines.reduce((sum: number, line: JournalLine) => {
      const creditAmount = typeof line.credit_amount === 'string' ? parseFloat(line.credit_amount) : line.credit_amount;
      return sum + (creditAmount || 0);
    }, 0);
    const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
    
    // Update totals and balance status
    formik.setFieldValue('total_debits', totalDebits, false);
    formik.setFieldValue('total_credits', totalCredits, false);
    formik.setFieldValue('is_balanced', isBalanced, false);
    
    console.log('Balance calculation:', { totalDebits, totalCredits, isBalanced });
  }, [formik.values.journal_lines]);

  // Recalculate balance when editing entry changes (for draft entries)
  useEffect(() => {
    if (editingEntry) {
      // Force recalculation when editing entry is loaded
      const lines = formik.values.journal_lines;
      if (lines && lines.length > 0) {
        const totalDebits = lines.reduce((sum: number, line: JournalLine) => {
          const debitAmount = typeof line.debit_amount === 'string' ? parseFloat(line.debit_amount) : line.debit_amount;
          return sum + (debitAmount || 0);
        }, 0);
        const totalCredits = lines.reduce((sum: number, line: JournalLine) => {
          const creditAmount = typeof line.credit_amount === 'string' ? parseFloat(line.credit_amount) : line.credit_amount;
          return sum + (creditAmount || 0);
        }, 0);
        const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
        
        formik.setFieldValue('total_debits', totalDebits, false);
        formik.setFieldValue('total_credits', totalCredits, false);
        formik.setFieldValue('is_balanced', isBalanced, false);
        
        console.log('Draft entry balance recalculation:', { 
          editingEntry: editingEntry.entry_number, 
          totalDebits, 
          totalCredits, 
          isBalanced 
        });
      }
    }
  }, [editingEntry, formik.values.journal_lines.length]);

  // Force balance recalculation when dialog opens with editing entry
  useEffect(() => {
    if (dialogOpen && editingEntry && formik.values.journal_lines.length > 0) {
      console.log('Dialog opened with draft entry, forcing balance calculation');
      const lines = formik.values.journal_lines;
      const totalDebits = lines.reduce((sum: number, line: JournalLine) => {
        const debitAmount = typeof line.debit_amount === 'string' ? parseFloat(line.debit_amount) : line.debit_amount;
        return sum + (debitAmount || 0);
      }, 0);
      const totalCredits = lines.reduce((sum: number, line: JournalLine) => {
        const creditAmount = typeof line.credit_amount === 'string' ? parseFloat(line.credit_amount) : line.credit_amount;
        return sum + (creditAmount || 0);
      }, 0);
      const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
      
      formik.setFieldValue('total_debits', totalDebits, false);
      formik.setFieldValue('total_credits', totalCredits, false);
      formik.setFieldValue('is_balanced', isBalanced, false);
      
      console.log('Dialog open balance calculation:', { totalDebits, totalCredits, isBalanced });
    }
  }, [dialogOpen, editingEntry]);

  // API Functions
  const loadJournalEntries = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/api/gl/journal-entries/', {
        headers: { 'Authorization': `Token ${localStorage.getItem('token')}` }
      });
      const data = await response.json();
      setJournalEntries(Array.isArray(data) ? data : data.results || []);
    } catch (error) {
      showAlert('error', 'Failed to load journal entries');
    } finally {
      setLoading(false);
    }
  };

  // Helper to fetch all paginated accounts
  const fetchAllAccounts = async () => {
    let allAccounts: Account[] = [];
    let nextUrl: string | null = '/gl/accounts/';
    const token = localStorage.getItem('token');
    while (nextUrl) {
      const response = await fetch(`http://localhost:8000/api${nextUrl}`, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      if (Array.isArray(data)) {
        // Non-paginated fallback
        allAccounts = data;
        nextUrl = null;
      } else if (data && data.results) {
        allAccounts = allAccounts.concat(data.results);
        // next can be absolute or relative
        if (data.next) {
          // If absolute, strip base URL
          const nextPath = data.next.startsWith('http') ? data.next.replace('http://localhost:8000/api', '') : data.next;
          nextUrl = nextPath;
        } else {
          nextUrl = null;
        }
      } else {
        // Unexpected format
        break;
      }
    }
    return allAccounts;
  };

  const loadAccounts = async () => {
    try {
      // Use the fast COA endpoint to get all accounts efficiently
      const coaData = await loadChartOfAccountsFast();
      
      console.log(`Journal Entry FAST: Loaded ${coaData.accounts.length} total accounts (${coaData.metadata.load_time_ms}ms)`);
      
      // Filter out header accounts since they cannot have transactions posted to them
      const transactionAccounts = coaData.accounts.filter((account: Account) => 
        !account.is_header_account && account.is_active
      );
      
      console.log(`Journal Entry FAST: Filtered to ${transactionAccounts.length} transaction accounts for journal entries`);
      setAccounts(transactionAccounts);
    } catch (error) {
      console.error('Error loading accounts:', error);
    }
  };



  const handleSaveEntry = async (values: JournalEntry, shouldClose: boolean = false) => {
    if (!values.is_balanced) {
      showAlert('error', 'Journal entry must be balanced before saving');
      return false;
    }

    // Additional validation: Check for duplicate entry number (only for new entries)
    if (!editingEntry && values.entry_number) {
      const existingEntry = journalEntries.find(entry => 
        entry.entry_number === values.entry_number && entry.id !== values.id
      );
      if (existingEntry) {
        showAlert('error', `Journal Entry number '${values.entry_number}' already exists. Please use a different entry number.`);
        return false;
      }
    }

    setLoading(true);
    try {
      const url = editingEntry 
        ? `http://localhost:8000/api/gl/journal-entries/${editingEntry.id}/`
        : 'http://localhost:8000/api/gl/journal-entries/';
      
      const method = editingEntry ? 'PUT' : 'POST';
      
      // Prepare data for backend - clean and validate
      const cleanedData = {
        entry_number: values.entry_number,
        transaction_date: values.transaction_date,
        description: values.description,
        reference_number: values.reference_number || '',
        memo: values.memo || '',
        status: values.status,
        entry_type: values.entry_type || 'GENERAL',
        currency: values.currency || currencyInfo?.functional_currency || '',
        exchange_rate: values.exchange_rate || 1.0,
        journal_lines: values.journal_lines
          .filter(line => line.account !== null && (
            (typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount)) > 0 || 
            (typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount)) > 0
          ))
          .map((line, index) => {
            const debitAmount = typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount) || 0;
            const creditAmount = typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount) || 0;
            
            console.log(`Line ${index + 1}: Debit=${debitAmount}, Credit=${creditAmount}, Account=${line.account}`);
            
            return {
              line_number: index + 1,
              account: line.account,
              debit_amount: debitAmount,
              credit_amount: creditAmount,
              description: line.description || values.description || 'Journal Entry Line',
              memo: line.memo || '',
              customer: line.customer || '',
              vendor: line.vendor || '',
              project: line.project || null,
              department: line.department || '',
              location: line.location || ''
            };
          })
      };
      
      // Validate we have valid journal lines
      if (cleanedData.journal_lines.length < 2) {
        showAlert('error', 'At least 2 journal lines are required');
        return false;
      }
      
      const totalDebits = cleanedData.journal_lines.reduce((sum, line) => sum + line.debit_amount, 0);
      const totalCredits = cleanedData.journal_lines.reduce((sum, line) => sum + line.credit_amount, 0);
      
      if (Math.abs(totalDebits - totalCredits) > 0.01) {
        showAlert('error', `Entry not balanced: Debits ${formatCurrency(totalDebits)} ≠ Credits ${formatCurrency(totalCredits)}`);
        return false;
      }
      
      console.log('Sending cleaned journal entry data:', cleanedData);
      
      // Check authentication token
      const token = localStorage.getItem('token');
      if (!token) {
        showAlert('error', 'Authentication token not found. Please login again.');
        return false;
      }
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`
        },
        body: JSON.stringify(cleanedData)
      });

      if (response.ok) {
        showAlert('success', `Journal entry ${editingEntry ? 'updated' : 'created'} successfully`);
        loadJournalEntries();
        
        if (shouldClose) {
          // Close the dialog after successful save
          handleCloseDialog();
        } else {
          // Show "New JE" button for regular save
          setShowNewJEButton(true);
        }
        return true;
      } else {
        const errorData = await response.json();
        console.error('Save error details:', errorData);
        console.error('Response status:', response.status);
        console.error('Response headers:', response.headers);
        console.error('Request data that was sent:', cleanedData);
        
        // Show detailed error message with better formatting
        let errorMessage = 'Save failed';
        if (typeof errorData === 'object') {
          if (errorData.detail) {
            errorMessage = errorData.detail;
          } else if (errorData.journal_lines) {
            // Handle journal lines validation errors
            const lineErrors = errorData.journal_lines.map((lineError: any, index: number) => {
              if (typeof lineError === 'object') {
                const fieldErrors = Object.entries(lineError)
                  .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
                  .join('; ');
                return `Line ${index + 1}: ${fieldErrors}`;
              }
              return `Line ${index + 1}: ${lineError}`;
            }).join(' | ');
            errorMessage = `Journal Lines Errors: ${lineErrors}`;
          } else {
            errorMessage = Object.entries(errorData)
              .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
              .join(' | ');
          }
        }
        console.error('Formatted error message:', errorMessage);
        showAlert('error', errorMessage);
        return false;
      }
    } catch (error) {
      console.error('Request error:', error);
      showAlert('error', `Failed to ${editingEntry ? 'update' : 'create'} journal entry`);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Handler for Save and Close functionality
  const handleSaveAndClose = async () => {
    const success = await handleSaveEntry(formik.values, true);
    // Dialog will be closed automatically if save is successful
  };

  const handleDeleteEntry = async () => {
    if (!entryToDelete) return;

    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/api/gl/journal-entries/${entryToDelete.id}/`, {
        method: 'DELETE',
        headers: { 'Authorization': `Token ${localStorage.getItem('token')}` }
      });

      if (response.ok) {
        showAlert('success', 'Journal entry deleted successfully');
        loadJournalEntries();
      } else {
        throw new Error('Delete failed');
      }
    } catch (error) {
      showAlert('error', 'Failed to delete journal entry');
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setEntryToDelete(null);
    }
  };

  // Dialog handlers
  const handleOpenDialog = (entry?: JournalEntry) => {
    // Reset table column widths to default
    setResetTableColumns(true);
    setTimeout(() => setResetTableColumns(false), 100);
    
    if (entry) {
      setEditingEntry(entry);
      console.log('Loading draft entry:', entry.entry_number, entry);
      
      // Convert string amounts to numbers before setting form values
      const normalizedEntry = {
        ...entry,
        total_debits: typeof entry.total_debits === 'string' ? parseFloat(entry.total_debits) : (entry.total_debits || 0),
        total_credits: typeof entry.total_credits === 'string' ? parseFloat(entry.total_credits) : (entry.total_credits || 0),
        journal_lines: entry.journal_lines.map((line, index) => ({
          ...line,
          line_number: index + 1, // Ensure proper line numbering
          debit_amount: typeof line.debit_amount === 'string' ? parseFloat(line.debit_amount) : (line.debit_amount || 0),
          credit_amount: typeof line.credit_amount === 'string' ? parseFloat(line.credit_amount) : (line.credit_amount || 0)
        }))
      };
      
      console.log('Normalized entry data:', normalizedEntry);
      
      // Set the values and immediately recalculate balance
      formik.setValues(normalizedEntry);
      
      // Force immediate balance recalculation after setting values
      setTimeout(() => {
        const lines = normalizedEntry.journal_lines;
        const totalDebits = lines.reduce((sum: number, line: JournalLine) => {
          const debitAmount = typeof line.debit_amount === 'string' ? parseFloat(line.debit_amount) : (line.debit_amount || 0);
          return sum + debitAmount;
        }, 0);
        const totalCredits = lines.reduce((sum: number, line: JournalLine) => {
          const creditAmount = typeof line.credit_amount === 'string' ? parseFloat(line.credit_amount) : (line.credit_amount || 0);
          return sum + creditAmount;
        }, 0);
        const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;
        
        formik.setFieldValue('total_debits', totalDebits, false);
        formik.setFieldValue('total_credits', totalCredits, false);
        formik.setFieldValue('is_balanced', isBalanced, false);
        
        console.log('Forced balance recalculation for draft:', { 
          totalDebits, 
          totalCredits, 
          isBalanced,
          entryNumber: entry.entry_number 
        });
      }, 100);
      
    } else {
      setEditingEntry(null);
      // Generate fresh values with new entry number each time
      const freshValues = getInitialValues();
      formik.setValues(freshValues);
      console.log('Generated new entry number:', freshValues.entry_number);
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingEntry(null);
    setShowNewJEButton(false);
    formik.resetForm();
  };

  const handleNewJE = () => {
    setEditingEntry(null);
    setShowNewJEButton(false);
    formik.setValues(getInitialValues());
    // Keep dialog open for new entry
  };

  const handleViewEntry = (entry: JournalEntry) => {
    setViewingEntry(entry);
    setViewDialogOpen(true);
  };



  // Journal line handlers for reusable component
  const handleJournalLineChange = (lineId: string, field: string, value: any) => {
    // Debug logging for account selection
    if (field === 'account_id' || field === 'account_selection') {
      console.log(`Account selection change for line ${lineId}:`, {
        field,
        value,
        selectedAccount: field === 'account_id' ? accounts.find(acc => acc.id === value) : value,
      });
    }
    
    // Update using the same pattern as CashPaymentForm
    formik.setFieldValue('journal_lines', 
      formik.values.journal_lines.map(line => {
        if (line.line_number.toString() === lineId) {
          const updatedLine = {
            ...line,
            // Handle new account_selection field from JournalLineTable
            ...(field === 'account_selection' && {
              account: value.account_id,
              account_name: value.account_name,
            }),
            // Direct field assignment like in CashPaymentForm (legacy support)
            ...(field === 'account_id' && {
              account: value,
              account_name: accounts.find(acc => acc.id === value)?.account_name || '',
            }),
            ...(field === 'description' && { description: value }),
            ...(field === 'memo' && { memo: value }),
            ...(field === 'debit_amount' && { 
              debit_amount: typeof value === 'string' ? parseFloat(value) || 0 : value,
              credit_amount: 0 // Auto-clear opposite amount
            }),
            ...(field === 'credit_amount' && { 
              credit_amount: typeof value === 'string' ? parseFloat(value) || 0 : value,
              debit_amount: 0 // Auto-clear opposite amount
            }),
          };

          // Auto-fill description if empty and account is selected
          if ((field === 'account_id' || field === 'account_selection') && value && !updatedLine.description) {
            const selectedAccount = field === 'account_selection' 
              ? accounts.find(acc => acc.id === value.account_id)
              : accounts.find(acc => acc.id === value);
            if (selectedAccount) {
              updatedLine.description = `${selectedAccount.account_name} - ${formik.values.description}`;
            }
          }

          return updatedLine;
        }
        return line;
      })
    );
  };

  const handleAddJournalLine = () => {
    const newLines = [...formik.values.journal_lines, createEmptyLine(formik.values.journal_lines.length + 1)];
    formik.setFieldValue('journal_lines', newLines);
  };

  const handleRemoveJournalLine = (lineId: string) => {
    if (formik.values.journal_lines.length > 2) {
      const newLines = formik.values.journal_lines.filter(line => line.line_number.toString() !== lineId);
      // Renumber the remaining lines
      newLines.forEach((line, i) => {
        line.line_number = i + 1;
      });
      formik.setFieldValue('journal_lines', newLines);
    }
  };

  // Utility functions
  const showAlert = (type: 'success' | 'error' | 'info', message: string) => {
    setAlert({ type, message });
  };

  // Copy Journal Entry
  const handleCopyEntry = () => {
    if (!formik.values.journal_lines.length) {
      showAlert('error', 'No journal entry data to copy');
      return;
    }

    // Generate unique entry number to prevent duplicates
    const originalEntryNumber = formik.values.entry_number;
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = today.toTimeString().slice(0, 5).replace(':', '');
    const newEntryNumber = `COPY-${originalEntryNumber}-${dateStr}-${timeStr}`;

    const copiedEntry = {
      ...getInitialValues(),
      entry_number: newEntryNumber, // Set unique entry number
      description: `Copy of ${formik.values.description}`,
      reference_number: formik.values.reference_number,
      memo: formik.values.memo,
      currency: formik.values.currency,
      journal_lines: formik.values.journal_lines.map((line, index) => ({
        ...line,
        line_number: index + 1,
        id: undefined, // Remove ID for new entry
      })),
    };

    formik.setValues(copiedEntry);
    showAlert('success', `Journal entry copied with new entry number: ${newEntryNumber}. Update details as needed and save.`);
  };

  // Make Recurring Entry
  const handleMakeRecurring = async () => {
    if (!formik.values.is_balanced) {
      showAlert('error', 'Journal entry must be balanced to create recurring template');
      return;
    }

    if (!formik.values.description?.trim()) {
      showAlert('error', 'Description is required to create recurring template');
      return;
    }

    // Check if there are valid journal lines
    const validLines = formik.values.journal_lines.filter(line => 
      line.account !== null && (
        (typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount)) > 0 || 
        (typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount)) > 0
      )
    );

    if (validLines.length < 2) {
      showAlert('error', 'At least 2 valid journal lines are required for recurring template');
      return;
    }

    try {
      setLoading(true);
      
      const recurringData = {
        template_name: `${formik.values.description} - Recurring`,
        description: formik.values.description,
        reference_number: formik.values.reference_number || '',
        memo: formik.values.memo || '',
        currency: formik.values.currency || currencyInfo?.functional_currency || 'INR',
        journal_lines: validLines.map((line, index) => ({
          line_number: index + 1,
          account: line.account,
          debit_amount: typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount) || 0,
          credit_amount: typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount) || 0,
          description: line.description || formik.values.description || 'Recurring Journal Line',
          memo: line.memo || '',
        })),
        frequency: 'MONTHLY', // Default frequency
        start_date: formik.values.transaction_date,
        is_active: true,
        created_by: 'current_user', // Will be set by backend
      };

      console.log('Creating recurring template:', recurringData);

      const token = localStorage.getItem('token');
      if (!token) {
        showAlert('error', 'Authentication token not found. Please login again.');
        return;
      }

      const response = await fetch('http://localhost:8000/api/gl/recurring-journal-entries/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(recurringData),
      });

      if (response.ok) {
        const responseData = await response.json();
        showAlert('success', `Recurring template "${recurringData.template_name}" created successfully! Template ID: ${responseData.id || 'N/A'}`);
      } else {
        const errorData = await response.json();
        console.error('Recurring template creation error:', errorData);
        
        let errorMessage = 'Failed to create recurring template';
        if (typeof errorData === 'object') {
          if (errorData.detail) {
            errorMessage = errorData.detail;
          } else if (errorData.template_name) {
            errorMessage = `Template Name: ${Array.isArray(errorData.template_name) ? errorData.template_name.join(', ') : errorData.template_name}`;
          } else {
            errorMessage = Object.entries(errorData)
              .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
              .join(' | ');
          }
        }
        showAlert('error', errorMessage);
      }
    } catch (error) {
      console.error('Error creating recurring entry:', error);
      showAlert('error', 'Network error: Failed to create recurring template');
    } finally {
      setLoading(false);
    }
  };

  // Print Journal Entry
  const handlePrint = () => {
    if (!formik.values.entry_number || !formik.values.description) {
      showAlert('error', 'Please fill in entry number and description before printing');
      return;
    }

    // Check if there are valid journal lines
    const validLines = formik.values.journal_lines.filter(line => 
      line.account !== null && (
        (typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount)) > 0 || 
        (typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount)) > 0
      )
    );

    if (validLines.length < 2) {
      showAlert('error', 'At least 2 valid journal lines are required for printing');
      return;
    }

    // Create printable content
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      showAlert('error', 'Pop-up blocked. Please allow pop-ups for this site to enable printing.');
      return;
    }

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Journal Entry - ${formik.values.entry_number}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              color: #333;
            }
            .header { 
              text-align: center; 
              border-bottom: 2px solid #1976d2; 
              padding-bottom: 20px; 
              margin-bottom: 30px;
            }
            .company-name { 
              font-size: 24px; 
              font-weight: bold; 
              color: #1976d2; 
              margin-bottom: 5px;
            }
            .document-title { 
              font-size: 20px; 
              font-weight: bold; 
              margin-top: 10px;
            }
            .info-section { 
              display: flex; 
              justify-content: space-between; 
              margin-bottom: 30px;
            }
            .info-left, .info-right { 
              width: 48%; 
            }
            .info-row { 
              margin-bottom: 8px; 
            }
            .label { 
              font-weight: bold; 
              display: inline-block; 
              width: 120px;
            }
            .table { 
              width: 100%; 
              border-collapse: collapse; 
              margin-bottom: 20px;
            }
            .table th, .table td { 
              border: 1px solid #ddd; 
              padding: 12px; 
              text-align: left;
            }
            .table th { 
              background-color: #f5f5f5; 
              font-weight: bold;
            }
            .amount { 
              text-align: right; 
              font-family: monospace;
            }
            .totals { 
              margin-top: 20px; 
              text-align: right;
            }
            .total-row { 
              margin-bottom: 5px; 
              font-size: 16px;
            }
            .balance-status { 
              font-weight: bold; 
              padding: 5px 10px; 
              border-radius: 4px;
              display: inline-block;
            }
            .balanced { 
              background-color: #e8f5e8; 
              color: #2e7d32;
            }
            .unbalanced { 
              background-color: #ffebee; 
              color: #c62828;
            }
            .footer { 
              margin-top: 40px; 
              padding-top: 20px; 
              border-top: 1px solid #ddd; 
              font-size: 12px; 
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">${currencyInfo?.company_name || 'Your Company'}</div>
            <div class="document-title">JOURNAL ENTRY</div>
          </div>

          <div class="info-section">
            <div class="info-left">
              <div class="info-row">
                <span class="label">Entry Number:</span>
                <span>${formik.values.entry_number}</span>
              </div>
              <div class="info-row">
                <span class="label">Date:</span>
                <span>${dayjs(formik.values.transaction_date).format('MMMM DD, YYYY')}</span>
              </div>
              <div class="info-row">
                <span class="label">Reference:</span>
                <span>${formik.values.reference_number || 'N/A'}</span>
              </div>
            </div>
            <div class="info-right">
              <div class="info-row">
                <span class="label">Status:</span>
                <span>${formik.values.status}</span>
              </div>
              <div class="info-row">
                <span class="label">Currency:</span>
                <span>${formik.values.currency || currencyInfo?.functional_currency || 'INR'}</span>
              </div>
              <div class="info-row">
                <span class="label">Created:</span>
                <span>${dayjs().format('MMMM DD, YYYY HH:mm')}</span>
              </div>
            </div>
          </div>

          <div class="info-row" style="margin-bottom: 20px;">
            <span class="label">Description:</span>
            <span>${formik.values.description}</span>
          </div>

          <table class="table">
            <thead>
              <tr>
                <th style="width: 10%;">#</th>
                <th style="width: 25%;">Account</th>
                <th style="width: 30%;">Description</th>
                <th style="width: 15%;">Debit</th>
                <th style="width: 15%;">Credit</th>
                <th style="width: 5%;">Memo</th>
              </tr>
            </thead>
            <tbody>
              ${validLines.map((line, index) => {
                const account = accounts.find(acc => acc.id === line.account);
                const debitAmount = typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount) || 0;
                const creditAmount = typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount) || 0;
                
                return `
                  <tr>
                    <td>${index + 1}</td>
                    <td>${account ? `${account.account_number} - ${account.account_name}` : 'Unknown Account'}</td>
                    <td>${line.description || 'N/A'}</td>
                    <td class="amount">${debitAmount > 0 ? formatCurrency(debitAmount) : '-'}</td>
                    <td class="amount">${creditAmount > 0 ? formatCurrency(creditAmount) : '-'}</td>
                    <td>${line.memo || '-'}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>

          <div class="totals">
            <div class="total-row">
              <strong>Total Debits: ${formatCurrency(formik.values.total_debits || 0)}</strong>
            </div>
            <div class="total-row">
              <strong>Total Credits: ${formatCurrency(formik.values.total_credits || 0)}</strong>
            </div>
            <div class="total-row">
              <span class="balance-status ${formik.values.is_balanced ? 'balanced' : 'unbalanced'}">
                ${formik.values.is_balanced ? '✓ BALANCED' : '✗ NOT BALANCED'}
              </span>
            </div>
          </div>

          ${formik.values.memo ? `
            <div style="margin-top: 30px;">
              <div class="label">Memo:</div>
              <div style="margin-top: 5px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                ${formik.values.memo}
              </div>
            </div>
          ` : ''}

          <div class="footer">
            <div>Printed on: ${dayjs().format('MMMM DD, YYYY HH:mm:ss')}</div>
            <div>Generated by: ${currencyInfo?.company_name || 'ERP Accounting System'}</div>
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 500);
            };
          </script>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    
    showAlert('success', 'Print dialog opened successfully');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'POSTED': return 'success';
      case 'DRAFT': return 'warning';
      case 'PENDING': return 'info';
      case 'REVERSED': return 'error';
      default: return 'default';
    }
  };

  const formatCurrency = (amount: number | string, currencyCode?: string) => {
    const currency = currencyCode || currencyInfo?.functional_currency;
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
    
    // If no currency is available, return plain number format
    if (!currency) {
      return numericAmount.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
    
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(numericAmount);
  };

  // Filter entries
  const filteredEntries = journalEntries.filter(entry => {
    const matchesSearch = entry.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         entry.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || entry.status === statusFilter;
    const matchesDateRange = (!dateFrom || dayjs(entry.transaction_date).isAfter(dateFrom.subtract(1, 'day'))) &&
                            (!dateTo || dayjs(entry.transaction_date).isBefore(dateTo.add(1, 'day')));
    
    return matchesSearch && matchesStatus && matchesDateRange;
  });

  const paginatedEntries = filteredEntries.slice((page - 1) * rowsPerPage, page * rowsPerPage);

  // Get total amount for summary cards - safely handle string/number amounts
  const getTotalAmount = () => {
    return journalEntries.reduce((sum, entry) => {
      const totalDebits = typeof entry.total_debits === 'number' ? entry.total_debits : parseFloat(entry.total_debits || '0');
      return sum + totalDebits;
    }, 0);
  };

  // Helper function to get numeric value from formik total_debits for FormHeaderFooter
  const getFormikTotalAmount = (): number => {
    const totalDebits = formik.values.total_debits;
    if (typeof totalDebits === 'string') {
      return parseFloat(totalDebits) || 0;
    }
    return totalDebits || 0;
  };

  // Show loading state while currency info is being fetched
  if (currencyLoading) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
          <Typography variant="h6">Loading currency information...</Typography>
        </Box>
      </LocalizationProvider>
    );
  }

  // Show error state if currency info failed to load
  if (currencyError) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            Failed to load currency information: {currencyError}
          </Alert>
        </Box>
      </LocalizationProvider>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', mb: 1 }}>
              Journal Entries
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
              Manage your general journal entries and maintain accurate accounting records
            </Typography>
            <Typography variant="body2" color="text.secondary">
              📊 {currencyInfo?.company_name} • Functional Currency: {currencyInfo?.functional_currency} ({currencyInfo?.functional_currency_symbol})
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<ExportIcon />}
              sx={{ mr: 1 }}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<PrintIcon />}
              sx={{ mr: 1 }}
            >
              Print
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              sx={{ 
                px: 3,
                background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
                }
              }}
            >
              New Journal Entry
            </Button>
          </Box>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 3, boxShadow: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search by entry number or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Status</MenuItem>
                    <MenuItem value="DRAFT">Draft</MenuItem>
                    <MenuItem value="POSTED">Posted</MenuItem>
                    <MenuItem value="PENDING">Pending</MenuItem>
                    <MenuItem value="REVERSED">Reversed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="From Date"
                  value={dateFrom}
                  onChange={setDateFrom}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="To Date"
                  value={dateTo}
                  onChange={setDateTo}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
              <CardContent>
                <Typography variant="h6">Total Entries</Typography>
                <Typography variant="h4">{journalEntries.length}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', color: 'white' }}>
              <CardContent>
                <Typography variant="h6">Draft Entries</Typography>
                <Typography variant="h4">{journalEntries.filter(e => e.status === 'DRAFT').length}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', color: 'white' }}>
              <CardContent>
                <Typography variant="h6">Posted Entries</Typography>
                <Typography variant="h4">{journalEntries.filter(e => e.status === 'POSTED').length}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', color: 'white' }}>
              <CardContent>
                <Typography variant="h6">Total Amount</Typography>
                <Typography variant="h4">
                  {formatCurrency(getTotalAmount())}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Entries Table */}
        <Card sx={{ boxShadow: 4 }}>
          <CardContent sx={{ p: 0 }}>
            <TableContainer>
              <Table>
                <TableHead sx={{ bgcolor: 'grey.100' }}>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 600 }}>Entry Number</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Date</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Reference</TableCell>
                    <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Amount</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Balance</TableCell>
                    <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {paginatedEntries.map((entry) => (
                    <TableRow key={entry.id} hover>
                      <TableCell sx={{ fontWeight: 500, color: 'primary.main' }}>
                        {entry.entry_number}
                      </TableCell>
                      <TableCell>{dayjs(entry.transaction_date).format('MMM DD, YYYY')}</TableCell>
                      <TableCell>{entry.description}</TableCell>
                      <TableCell>{entry.reference_number || '-'}</TableCell>
                      <TableCell sx={{ textAlign: 'right', fontWeight: 500 }}>
                        {formatCurrency(entry.total_debits || 0)}
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={entry.status} 
                          color={getStatusColor(entry.status)}
                          size="small"
                          sx={{ fontWeight: 500 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={entry.is_balanced ? 'Balanced' : 'Unbalanced'} 
                          color={entry.is_balanced ? 'success' : 'error'}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                          <Tooltip title="View Entry">
                            <IconButton 
                              size="small" 
                              onClick={() => handleViewEntry(entry)}
                              sx={{ color: 'info.main' }}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>

                          <Tooltip title="Edit Entry">
                            <IconButton 
                              size="small" 
                              onClick={() => handleOpenDialog(entry)}
                              sx={{ color: 'warning.main' }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Entry">
                            <IconButton 
                              size="small" 
                              onClick={() => {
                                setEntryToDelete(entry);
                                setDeleteDialogOpen(true);
                              }}
                              sx={{ color: 'error.main' }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                  {paginatedEntries.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="h6" color="text.secondary">
                          No journal entries found
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          Create your first journal entry to get started
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            
            {filteredEntries.length > rowsPerPage && (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <Pagination
                  count={Math.ceil(filteredEntries.length / rowsPerPage)}
                  page={page}
                  onChange={(_, newPage) => setPage(newPage)}
                  color="primary"
                />
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Create/Edit Full Page Form */}
        {dialogOpen && (
          <FormHeaderFooter
            title={editingEntry ? 'EDIT JOURNAL ENTRY' : 'CREATE JOURNAL ENTRY'}
            subtitle={editingEntry ? 'Modify journal entry details' : 'Record journal entries with proper double-entry bookkeeping'}
            titleColor="primary"
            showAmount={true}
            amount={getFormikTotalAmount()}
            amountLabel="Total Amount"
            currencyCode={currencyInfo?.functional_currency}
            amountColor={formik.values.is_balanced ? 'primary' : 'error.main'}
            onClose={handleCloseDialog}
            onCancel={handleCloseDialog}
            onPrint={handlePrint}
            onCopy={handleCopyEntry}
            onMakeRecurring={handleMakeRecurring}
            onSave={() => formik.handleSubmit()}
            onSaveAndClose={handleSaveAndClose}
            isEditMode={!!editingEntry}
            loading={loading}
            saveLabel={editingEntry ? 'Update Entry' : 'Save Entry'}
            saveAndCloseLabel={editingEntry ? 'Update and Close' : 'Save and Close'}
          >
            <form onSubmit={formik.handleSubmit}>

              <Grid container spacing={3} sx={{ mt: 1 }}>
                {/* Header Information */}
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Entry Number"
                    name="entry_number"
                    value={formik.values.entry_number}
                    onChange={formik.handleChange}
                    error={formik.touched.entry_number && Boolean(formik.errors.entry_number)}
                    helperText={formik.touched.entry_number && formik.errors.entry_number}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <DatePicker
                    label="Transaction Date"
                    value={dayjs(formik.values.transaction_date)}
                    maxDate={dayjs()}
                    onChange={(newValue) => {
                      if (newValue) {
                        // Ensure the date is not in the future
                        const selectedDate = newValue.isAfter(dayjs()) ? dayjs() : newValue;
                        formik.setFieldValue('transaction_date', selectedDate.format('YYYY-MM-DD'));
                      }
                    }}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: formik.touched.transaction_date && Boolean(formik.errors.transaction_date),
                        helperText: formik.touched.transaction_date && formik.errors.transaction_date,
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Reference Number"
                    name="reference_number"
                    value={formik.values.reference_number || ''}
                    onChange={formik.handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      name="status"
                      value={formik.values.status}
                      onChange={formik.handleChange}
                      label="Status"
                    >
                      <MenuItem value="DRAFT">Draft</MenuItem>
                      <MenuItem value="POSTED">Posted</MenuItem>
                      <MenuItem value="PENDING">Pending</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    name="description"
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    error={formik.touched.description && Boolean(formik.errors.description)}
                    helperText={formik.touched.description && formik.errors.description}
                    multiline
                    rows={2}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              {/* Journal Lines */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Journal Lines
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={handleAddJournalLine}
                    size="small"
                  >
                    Add Line
                  </Button>
                </Box>

                <JournalLineTable
                  tableMode="custom"
                  showAccountColumn={true}
                  showDescriptionColumn={true}
                  showMemoColumn={true}
                  showAmountColumn={false}
                  showDebitCreditColumns={true}
                  showSalesTaxColumn={false}
                  showActionsColumn={true}
                  lines={transformToJournalLineItems(formik.values.journal_lines)}
                  accounts={transformToAccountOptions(accounts)}
                  onLineChange={handleJournalLineChange}
                  onAddLine={handleAddJournalLine}
                  onRemoveLine={handleRemoveJournalLine}
                  currencySymbol={currencyInfo?.functional_currency_symbol || ''}
                  accountPlaceholder="Select account..."
                  descriptionPlaceholder="Enter description for this line"
                  memoPlaceholder="Memo..."
                  minLines={4}
                  resetColumnWidths={resetTableColumns}
                  tableHeight="300px"
                />

                {/* Totals */}
                <Box sx={{ 
                  mt: 2, 
                  p: 2, 
                  bgcolor: formik.values.is_balanced ? 'success.light' : 'error.light',
                  borderRadius: 1,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Totals
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 4 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Debit: <CurrencyDisplay 
                        value={formik.values.total_debits || 0}
                        currencyCode={currencyInfo?.functional_currency}
                        showBlankForZero={false}
                        component="span"
                      />
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Credit: <CurrencyDisplay 
                        value={formik.values.total_credits || 0}
                        currencyCode={currencyInfo?.functional_currency}
                        showBlankForZero={false}
                        component="span"
                      />
                    </Typography>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontWeight: 600,
                        color: formik.values.is_balanced ? 'success.dark' : 'error.dark'
                      }}
                    >
                      {formik.values.is_balanced ? '✓ Balanced' : '✗ Not Balanced'}
                    </Typography>
                  </Box>
                </Box>

                {/* Attachments */}
                <Box sx={{ mt: 3 }}>
                  <Button
                    startIcon={<AttachFileIcon />}
                    variant="outlined"
                    component="label"
                  >
                    Attach Files
                    <input type="file" hidden multiple />
                  </Button>
                </Box>
              </Box>

            </form>
          </FormHeaderFooter>
        )}

        {/* View Dialog */}
        <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="lg" fullWidth>
          <DialogTitle sx={{ bgcolor: 'info.main', color: 'white' }}>
            View Journal Entry - {viewingEntry?.entry_number}
          </DialogTitle>
          <DialogContent sx={{ p: 3 }}>
            {viewingEntry && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">Entry Number</Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>{viewingEntry.entry_number}</Typography>
                  
                  <Typography variant="subtitle2" color="text.secondary">Transaction Date</Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {dayjs(viewingEntry.transaction_date).format('MMMM DD, YYYY')}
                  </Typography>
                  
                  <Typography variant="subtitle2" color="text.secondary">Reference</Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>{viewingEntry.reference_number || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                  <Chip 
                    label={viewingEntry.status} 
                    color={getStatusColor(viewingEntry.status)}
                    sx={{ mb: 2 }}
                  />
                  
                  <Typography variant="subtitle2" color="text.secondary">Total Amount</Typography>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    {formatCurrency(viewingEntry.total_debits || 0)}
                  </Typography>
                  
                  <Typography variant="subtitle2" color="text.secondary">Balance Status</Typography>
                  <Chip 
                    label={viewingEntry.is_balanced ? 'Balanced' : 'Unbalanced'} 
                    color={viewingEntry.is_balanced ? 'success' : 'error'}
                    sx={{ mb: 2 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                  <Typography variant="body1" sx={{ mb: 3 }}>{viewingEntry.description}</Typography>
                  
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>Journal Lines</Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                      <TableHead sx={{ bgcolor: 'grey.100' }}>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600 }}>Account</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Memo</TableCell>
                          <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Debit</TableCell>
                          <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Credit</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {viewingEntry.journal_lines.map((line, index) => (
                          <TableRow key={index}>
                            <TableCell>{line.account_name || 'N/A'}</TableCell>
                            <TableCell>{line.description}</TableCell>
                            <TableCell>{line.memo || '-'}</TableCell>
                            <TableCell sx={{ textAlign: 'right' }}>
                              {(typeof line.debit_amount === 'number' ? line.debit_amount : parseFloat(line.debit_amount) || 0) > 0 ? 
                                formatCurrency(line.debit_amount) : '-'}
                            </TableCell>
                            <TableCell sx={{ textAlign: 'right' }}>
                              {(typeof line.credit_amount === 'number' ? line.credit_amount : parseFloat(line.credit_amount) || 0) > 0 ? 
                                formatCurrency(line.credit_amount) : '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions sx={{ justifyContent: 'space-between' }}>
            <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                onClick={() => {
                  if (viewingEntry) {
                    // Copy the viewed entry to create a new one
                    const copiedEntry = {
                      ...getInitialValues(),
                      description: `Copy of ${viewingEntry.description}`,
                      journal_lines: viewingEntry.journal_lines.map((line, index) => ({
                        ...line,
                        line_number: index + 1,
                        id: undefined,
                      })),
                    };
                    formik.setValues(copiedEntry);
                    setViewDialogOpen(false);
                    setDialogOpen(true);
                    showAlert('success', 'Journal entry copied! Update details and save as new entry.');
                  }
                }}
                startIcon={<CopyIcon />}
                variant="outlined"
                color="info"
              >
                Copy Entry
              </Button>
            </Box>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete journal entry "{entryToDelete?.entry_number}"?
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDeleteEntry} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={Boolean(alert)}
          autoHideDuration={6000}
          onClose={() => setAlert(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert 
            onClose={() => setAlert(null)} 
            severity={alert?.type}
            sx={{ width: '100%' }}
          >
            {alert?.message}
          </Alert>
        </Snackbar>



        {/* Floating Action Button for Mobile */}
        <Fab
          color="primary"
          sx={{ 
            position: 'fixed', 
            bottom: 16, 
            right: 16,
            display: { xs: 'flex', md: 'none' }
          }}
          onClick={() => handleOpenDialog()}
        >
          <AddIcon />
        </Fab>
      </Box>
    </LocalizationProvider>
  );
};

export default JournalEntriesPage; 