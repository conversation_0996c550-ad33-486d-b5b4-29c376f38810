import { useState, useEffect } from 'react';

export interface CurrencyInfo {
  functional_currency: string;
  reporting_currency: string;
  functional_currency_symbol: string;
  reporting_currency_symbol: string;
  available_currencies: Array<{
    code: string;
    name: string;
    symbol: string;
  }>;
  company_name: string;
}

export const useCurrencyInfo = () => {
  const [currencyInfo, setCurrencyInfo] = useState<CurrencyInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCurrencyInfo = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('http://localhost:8000/api/gl/company-currency-info/', {
          headers: {
            'Authorization': `Token ${localStorage.getItem('token')}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setCurrencyInfo(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch currency info');
        console.error('Error fetching currency info:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrencyInfo();
  }, []);

  return { currencyInfo, loading, error };
};

export default useCurrencyInfo; 