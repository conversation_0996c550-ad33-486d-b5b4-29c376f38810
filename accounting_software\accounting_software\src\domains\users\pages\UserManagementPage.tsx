import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Typo<PERSON>, <PERSON>ton, Box, CircularProgress } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { useNavigate } from 'react-router-dom';
import UserList from '../components/UserList';
import FormDialog from '../../../shared/components/FormDialog';
import AddUserForm from '../components/forms/AddUserForm';
import { useSnackbar } from 'notistack';
import { useAuth } from '../../../contexts/AuthContext';

// Interface for user data received from the backend API
interface BackendUser {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string | null;
  permissions: Array<{
    module: string;
    accessLevel: string;
  }>;
}

// Interface for the data structure expected by UserList component (Matches User interface in UserList.tsx)
// This interface matches the User interface inside UserList.tsx based on reading that file.
interface UserListExpectedUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  permissions: Array<{
    module: string;
    accessLevel: string;
  }>;
  // Note: This interface does NOT include 'username' to match UserList's internal User interface.
}

// Interface for the data structure expected by the AddUserForm component (initialValues and onSubmit)
// Note: AddUserForm's onSubmit currently sends an object with first_name and last_name,
// so we will adjust the type here to reflect that for now.
interface UserFormData {
  firstName: string; // Although the form collects this,
  lastName: string;  // the onSubmit handler sends first_name/last_name
  username: string;
  email: string;
  password?: string;
  confirmPassword?: string;
  role?: string;
  permissions?: Array<{
    module: string;
    accessLevel: string;
  }>;
}

// Let's define an interface for the data shape received from AddUserForm's onSubmit
interface AddUserFormDataPassed {
  username: string;
  email: string;
  password?: string;
  first_name: string;
  last_name: string;
  role?: string;
  permissions?: Array<{
    module: string;
    accessLevel: string;
  }>;
}

const UserManagementPage: React.FC = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<BackendUser | null>(null);
  const [users, setUsers] = useState<BackendUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { isAuthenticated, isLoading: authLoading } = useAuth();

  useEffect(() => {
    if (!authLoading) {  // Only check auth after loading is complete
      if (!isAuthenticated) {
        navigate('/login');
      } else {
        fetchUsers();
      }
    }
  }, [isAuthenticated, authLoading]);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch('http://localhost:8000/api/account/users/', {
        headers: {
          'Authorization': `Token ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
           localStorage.removeItem('token');
           enqueueSnackbar('Session expired. Please log in again.', { variant: 'warning' });
           navigate('/login');
           return;
        }
        if (response.status === 403) {
          enqueueSnackbar('You do not have permission to view users.', { variant: 'error' });
          navigate('/dashboard');
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to fetch users');
      }

      const data = await response.json();
      // Store backend users directly in state
      setUsers(data.results || data);

    } catch (error) {
      console.error('Error fetching users:', error);
      setError((error as Error).message || 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  // userData is received as AddUserFormDataPassed from the form
  const handleAddUser = async (userData: AddUserFormDataPassed) => {
    console.log("handleAddUser called with data:", userData);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error("Token not found in localStorage");
        enqueueSnackbar("Authentication token missing. Please log in again.", { variant: 'error' });
        navigate('/login');
        return;
      }

      const backendData = {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        first_name: userData.first_name,
        last_name: userData.last_name,
        role: userData.role,
        permissions: userData.permissions
      };
      console.log("Sending backend data:", backendData);

      const response = await fetch('http://localhost:8000/api/account/users/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`
        },
        body: JSON.stringify(backendData)
      });

      const responseData = await response.json().catch(() => ({}));
      console.log("Backend response status:", response.status, "data:", responseData);

      if (!response.ok) {
        throw new Error(responseData.detail || 'Failed to create user');
      }

      enqueueSnackbar('User created successfully', { variant: 'success' });
      setIsAddDialogOpen(false);
      fetchUsers();
    } catch (error) {
      console.error('Error creating user:', error);
      enqueueSnackbar((error as Error).message || 'Failed to create user', { variant: 'error' });
    }
  };

  // userData is received as UserFormData from the form
  const handleEditUser = async (userData: UserFormData) => {
    console.log("handleEditUser called with data:", userData);
    if (!selectedUser) {
      console.error("handleEditUser called but selectedUser is null");
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        console.error("Token not found in localStorage");
        enqueueSnackbar("Authentication token missing. Please log in again.", { variant: 'error' });
        navigate('/login');
        return;
      }

      const backendData = {
        username: userData.username,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        role: userData.role,
        permissions: userData.permissions,
        ...(userData.password && { password: userData.password }),
      };
      console.log("Sending backend data for update:", backendData);

      const response = await fetch(`http://localhost:8000/api/account/users/${selectedUser.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`
        },
        body: JSON.stringify(backendData)
      });

      const responseData = await response.json().catch(() => ({}));
      console.log("Backend response status (edit):", response.status, "data:", responseData);

      if (!response.ok) {
        throw new Error(responseData.detail || 'Failed to update user');
      }

      enqueueSnackbar('User updated successfully', { variant: 'success' });
      setIsEditDialogOpen(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      enqueueSnackbar((error as Error).message || 'Failed to update user', { variant: 'error' });
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
         console.error("Token not found in localStorage (delete)"); // Debug log
         enqueueSnackbar("Authentication token missing. Please log in again.", { variant: 'error' });
         navigate('/login');
         return;
      }

      const response = await fetch(`http://localhost:8000/api/account/users/${userId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Token ${token}`
        }
      });

      if (!response.ok) {
         const errorData = await response.json().catch(() => ({})); // Attempt to parse JSON, default to empty object if fails
         console.error("Delete failed, status:", response.status, "data:", errorData); // Debug log
         throw new Error(errorData.detail || 'Failed to delete user');
      }

      enqueueSnackbar('User deleted successfully', { variant: 'success' });
      fetchUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      enqueueSnackbar((error as Error).message || 'Failed to delete user', { variant: 'error' });
    }
  };

  // Handle click for editing, expects UserListExpectedUser
  const handleEditClick = (user: UserListExpectedUser) => {
    console.log("handleEditClick called with user:", user);
    const backendUser = users.find(u => u.id === user.id);
    if (!backendUser) {
      console.error("Could not find backend user for id:", user.id);
      enqueueSnackbar("Error: Could not find user data for editing.", { variant: 'error' });
      return;
    }

    const initialValuesForForm: Partial<UserFormData> = {
      username: backendUser.username,
      email: backendUser.email,
      firstName: backendUser.first_name,
      lastName: backendUser.last_name,
      role: backendUser.role || undefined,
      permissions: backendUser.permissions || [],
    };
    console.log("Mapped initialValues for form:", initialValuesForForm);

    setSelectedUser(backendUser);
    setIsEditDialogOpen(true);
  };

  return (
    <Box sx={{ 
      flexGrow: 1,
      pt: 8,
      px: 3,
    }}>
      <Container maxWidth="lg">
        <Box sx={{ mb: 4 }}>
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            mb: 3 
          }}>
            <Typography variant="h4">User Management</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setIsAddDialogOpen(true)}
            >
              Add User
            </Button>
          </Box>

          {/* Display loading, error, or user list */}
          {authLoading || loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}><CircularProgress /></Box>
          ) : error ? (
            <Box sx={{ color: 'red', textAlign: 'center', mt: 4 }}>{error}</Box>
          ) : (
            <UserList 
              users={users.map(user => ({
                id: user.id,
                firstName: user.first_name,
                lastName: user.last_name,
                email: user.email,
                permissions: user.permissions || [],
              }))}
              onDelete={handleDeleteUser}
              onEdit={handleEditClick} 
            />
          )}

          <FormDialog
            open={isAddDialogOpen}
            onClose={() => { console.log("Add Dialog closed"); setIsAddDialogOpen(false); }} // Debug log
            title="Add New User"
            maxWidth="md"
            fullWidth
          >
            {/* AddUserForm expects AddUserFormDataPassed */}
            <AddUserForm
              onSubmit={handleAddUser as (values: any) => void} // Cast to any for now due to interface mismatch
              onCancel={() => { console.log("Add User form cancelled"); setIsAddDialogOpen(false); }} // Debug log
            />
          </FormDialog>

          <FormDialog
            open={isEditDialogOpen}
            onClose={() => {
              console.log("Edit Dialog closed"); // Debug log
              setIsEditDialogOpen(false);
              setSelectedUser(null);
            }}
            title="Edit User"
            maxWidth="md"
            fullWidth
          >
            {/* Pass mapped selected user data (initialValuesForForm) to AddUserForm for initialValues */}
            {selectedUser && (
               <AddUserForm
                 onSubmit={handleEditUser}
                 onCancel={() => {
                   console.log("Edit User form cancelled"); // Debug log
                   setIsEditDialogOpen(false);
                   setSelectedUser(null);
                 }}
                 initialValues={{
                    username: selectedUser.username,
                    email: selectedUser.email,
                    firstName: selectedUser.first_name,
                    lastName: selectedUser.last_name,
                    role: selectedUser.role || undefined,
                    permissions: selectedUser.permissions || [],
                 } as Partial<UserFormData>}
               />
            )}
          </FormDialog>
        </Box>
      </Container>
    </Box>
  );
};

export default UserManagementPage;