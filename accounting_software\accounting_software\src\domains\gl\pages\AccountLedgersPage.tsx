import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Alert,
  Snackbar,
  Pagination,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Autocomplete,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Stack,
  Switch,
  FormControlLabel,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Print as PrintIcon,
  ExpandMore as ExpandMoreIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Receipt as ReceiptIcon,
  DateRange as DateRangeIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';
import { loadChartOfAccountsFast } from '../../../services/gl.service';

// Types
interface Account {
  id: number;
  account_number: string;
  account_name: string;
  account_type: string;
  account_type_name: string;
  detail_type_name: string;
  current_balance: number;
  is_active: boolean;
  is_header_account: boolean;
  normal_balance: 'DEBIT' | 'CREDIT';
}

interface LedgerEntry {
  id: number;
  transaction_date: string;
  entry_number: string;
  description: string;
  reference_number?: string;
  debit_amount: number;
  credit_amount: number;
  running_balance: number;
  journal_entry_id: number;
  journal_entry_status: string;
  contra_account?: string;
  memo?: string;
}

interface AccountLedgerData {
  account: Account;
  entries: LedgerEntry[];
  opening_balance: number;
  closing_balance: number;
  total_debits: number;
  total_credits: number;
  period_from: string;
  period_to: string;
}

interface JournalEntryDetails {
  id: number;
  entry_number: string;
  transaction_date: string;
  description: string;
  reference_number?: string;
  memo?: string;
  status: string;
  entry_type: string;
  currency: string;
  total_debits: number;
  total_credits: number;
  is_balanced: boolean;
  journal_lines: Array<{
    id: number;
    line_number: number;
    account_id: number;
    account_name: string;
    account_number: string;
    debit_amount: number;
    credit_amount: number;
    description: string;
    memo?: string;
  }>;
}

const AccountLedgersPage: React.FC = () => {
  // Currency information
  const { currencyInfo, loading: currencyLoading, error: currencyError } = useCurrencyInfo();
  
  // State Management
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [ledgerData, setLedgerData] = useState<AccountLedgerData | null>(null);
  const [loading, setLoading] = useState(false);
  const [accountsLoading, setAccountsLoading] = useState(false);
  
  // Journal Entry View Dialog
  const [viewJournalDialog, setViewJournalDialog] = useState(false);
  const [viewingJournalEntry, setViewingJournalEntry] = useState<JournalEntryDetails | null>(null);
  const [journalEntryLoading, setJournalEntryLoading] = useState(false);
  
  // Filters
  const [dateFrom, setDateFrom] = useState<Dayjs>(dayjs().startOf('month'));
  const [dateTo, setDateTo] = useState<Dayjs>(dayjs());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [includeZeroBalance, setIncludeZeroBalance] = useState(false);
  
  // Pagination
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(25);
  
  // Alerts
  const [alert, setAlert] = useState<{type: 'success' | 'error' | 'info', message: string} | null>(null);

  // Load accounts on mount
  useEffect(() => {
    loadAccounts();
  }, []);

  // Load ledger data when account or date range changes
  useEffect(() => {
    if (selectedAccount) {
      loadLedgerData();
    }
  }, [selectedAccount, dateFrom, dateTo, statusFilter]);

  const loadAccounts = async () => {
    setAccountsLoading(true);
    try {
      const coaData = await loadChartOfAccountsFast();
      
      // Filter to only transaction accounts (no header accounts) and sort by account number
      const transactionAccounts = coaData.accounts
        .filter((account: Account) => !account.is_header_account && account.is_active)
        .sort((a: Account, b: Account) => a.account_number.localeCompare(b.account_number));
      
      console.log(`Account Ledgers: Loaded ${transactionAccounts.length} transaction accounts`);
      setAccounts(transactionAccounts);
    } catch (error) {
      console.error('Error loading accounts:', error);
      showAlert('error', 'Failed to load accounts');
    } finally {
      setAccountsLoading(false);
    }
  };

  const loadLedgerData = async () => {
    if (!selectedAccount) return;
    
    setLoading(true);
    try {
      const params = new URLSearchParams({
        account_id: selectedAccount.id.toString(),
        date_from: dateFrom.format('YYYY-MM-DD'),
        date_to: dateTo.format('YYYY-MM-DD'),
      });
      
      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/gl/accounts/${selectedAccount.id}/ledger/?${params}`, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });
      if (!response.ok) {
        throw new Error('Failed to load ledger data');
      }
      
      const data = await response.json();
      setLedgerData(data);
      setPage(1); // Reset to first page when loading new data
    } catch (error) {
      console.error('Error loading ledger data:', error);
      showAlert('error', 'Failed to load account ledger data');
      setLedgerData(null);
    } finally {
      setLoading(false);
    }
  };

  const loadJournalEntryDetails = async (journalEntryId: number) => {
    setJournalEntryLoading(true);
    try {
      const token = localStorage.getItem('token');
    const response = await fetch(`http://localhost:8000/api/gl/journal-entries/${journalEntryId}/`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Token ${token}` })
      }
    });
      if (!response.ok) {
        throw new Error('Failed to load journal entry details');
      }
      
      const data = await response.json();
      setViewingJournalEntry(data);
      setViewJournalDialog(true);
    } catch (error) {
      console.error('Error loading journal entry details:', error);
      showAlert('error', 'Failed to load journal entry details');
    } finally {
      setJournalEntryLoading(false);
    }
  };

  const handleViewJournalEntry = (journalEntryId: number) => {
    loadJournalEntryDetails(journalEntryId);
  };

  const handleCloseJournalDialog = () => {
    setViewJournalDialog(false);
    setViewingJournalEntry(null);
  };

  // Utility functions
  const showAlert = (type: 'success' | 'error' | 'info', message: string) => {
    setAlert({ type, message });
  };

  const formatCurrency = (amount: number | string, currencyCode?: string) => {
    const currency = currencyCode || currencyInfo?.functional_currency || 'USD';
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(numericAmount);
  };

  const getBalanceColor = (balance: number, normalBalance: 'DEBIT' | 'CREDIT') => {
    if (balance === 0) return 'text.secondary';
    
    // For debit normal balance accounts (Assets, Expenses)
    if (normalBalance === 'DEBIT') {
      return balance > 0 ? 'success.main' : 'error.main';
    }
    // For credit normal balance accounts (Liabilities, Equity, Revenue)
    else {
      return balance > 0 ? 'success.main' : 'error.main';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'POSTED': return 'success';
      case 'DRAFT': return 'warning';
      case 'PENDING': return 'info';
      case 'REVERSED': return 'error';
      default: return 'default';
    }
  };

  // Filter entries based on search term
  const filteredEntries = ledgerData?.entries.filter(entry => {
    if (!includeZeroBalance && entry.debit_amount === 0 && entry.credit_amount === 0) {
      return false;
    }
    
    return (
      entry.entry_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (entry.reference_number && entry.reference_number.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (entry.contra_account && entry.contra_account.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }) || [];

  const paginatedEntries = filteredEntries.slice((page - 1) * rowsPerPage, page * rowsPerPage);

  // Export functions
  const handleExportCSV = () => {
    if (!ledgerData || !selectedAccount) return;
    
    const csvContent = [
      ['Date', 'Entry Number', 'Description', 'Reference', 'Debit', 'Credit', 'Balance', 'Status'].join(','),
      ...filteredEntries.map(entry => [
        dayjs(entry.transaction_date).format('YYYY-MM-DD'),
        entry.entry_number,
        `"${entry.description}"`,
        entry.reference_number || '',
        entry.debit_amount.toFixed(2),
        entry.credit_amount.toFixed(2),
        entry.running_balance.toFixed(2),
        entry.journal_entry_status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ledger-${selectedAccount.account_number}-${dateFrom.format('YYYY-MM-DD')}-to-${dateTo.format('YYYY-MM-DD')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    window.print();
  };

  if (currencyLoading || accountsLoading) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
          <Stack alignItems="center" spacing={2}>
            <CircularProgress size={60} />
            <Typography variant="h6" color="textSecondary">
              {currencyLoading ? 'Loading currency information...' : 'Loading accounts...'}
            </Typography>
          </Stack>
        </Box>
      </LocalizationProvider>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
            Account Ledgers
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View detailed transaction history for individual accounts
          </Typography>
        </Box>

        {/* Account Selection & Filters */}
        <Card sx={{ mb: 3, boxShadow: 2 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              {/* Account Selection */}
              <Grid item xs={12} md={4}>
                <Autocomplete
                  value={selectedAccount}
                  onChange={(_, newValue) => setSelectedAccount(newValue)}
                  options={accounts}
                  getOptionLabel={(option) => `${option.account_number} - ${option.account_name}`}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Account"
                      placeholder="Choose an account to view ledger"
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <AccountBalanceIcon sx={{ mr: 1, color: 'action.active' }} />,
                      }}
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {option.account_number} - {option.account_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.account_type_name} • Balance: {formatCurrency(option.current_balance)}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                />
              </Grid>

              {/* Date Range */}
              <Grid item xs={12} md={2}>
                <DatePicker
                  label="From Date"
                  value={dateFrom}
                  onChange={(newValue) => newValue && setDateFrom(newValue)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} md={2}>
                <DatePicker
                  label="To Date"
                  value={dateTo}
                  onChange={(newValue) => newValue && setDateTo(newValue)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>

              {/* Status Filter */}
              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="all">All Status</MenuItem>
                    <MenuItem value="POSTED">Posted</MenuItem>
                    <MenuItem value="DRAFT">Draft</MenuItem>
                    <MenuItem value="PENDING">Pending</MenuItem>
                    <MenuItem value="REVERSED">Reversed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Action Buttons */}
              <Grid item xs={12} md={2}>
                <Stack direction="row" spacing={1}>
                  <Tooltip title="Export to CSV">
                    <IconButton 
                      onClick={handleExportCSV} 
                      disabled={!ledgerData}
                      color="primary"
                    >
                      <ExportIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Print">
                    <IconButton 
                      onClick={handlePrint} 
                      disabled={!ledgerData}
                      color="primary"
                    >
                      <PrintIcon />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Account Summary */}
        {selectedAccount && ledgerData && (
          <Card sx={{ mb: 3, boxShadow: 2 }}>
            <CardContent>
              <Grid container spacing={3}>
                {/* Account Info */}
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <AccountBalanceIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {selectedAccount.account_number}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {selectedAccount.account_name}
                    </Typography>
                    <Chip 
                      label={selectedAccount.account_type_name} 
                      color="primary" 
                      size="small" 
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>

                {/* Financial Summary */}
                <Grid item xs={12} md={9}>
                  <Grid container spacing={2}>
                    <Grid item xs={6} sm={3}>
                      <Card variant="outlined" sx={{ p: 2, backgroundColor: 'info.light', color: 'info.contrastText' }}>
                        <Typography variant="caption" sx={{ fontWeight: 600 }}>Opening Balance</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(ledgerData.opening_balance)}
                        </Typography>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={6} sm={3}>
                      <Card variant="outlined" sx={{ p: 2, backgroundColor: 'success.light', color: 'success.contrastText' }}>
                        <Typography variant="caption" sx={{ fontWeight: 600 }}>Total Debits</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(ledgerData.total_debits)}
                        </Typography>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={6} sm={3}>
                      <Card variant="outlined" sx={{ p: 2, backgroundColor: 'error.light', color: 'error.contrastText' }}>
                        <Typography variant="caption" sx={{ fontWeight: 600 }}>Total Credits</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(ledgerData.total_credits)}
                        </Typography>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={6} sm={3}>
                      <Card 
                        variant="outlined" 
                        sx={{ 
                          p: 2, 
                          backgroundColor: ledgerData.closing_balance >= 0 ? 'success.light' : 'warning.light',
                          color: ledgerData.closing_balance >= 0 ? 'success.contrastText' : 'warning.contrastText'
                        }}
                      >
                        <Typography variant="caption" sx={{ fontWeight: 600 }}>Closing Balance</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 700 }}>
                          {formatCurrency(ledgerData.closing_balance)}
                        </Typography>
                      </Card>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Search and Options */}
        {selectedAccount && ledgerData && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Search entries by number, description, reference, or contra account..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={includeZeroBalance}
                        onChange={(e) => setIncludeZeroBalance(e.target.checked)}
                      />
                    }
                    label="Include Zero Amount Entries"
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'right' }}>
                    Showing {filteredEntries.length} of {ledgerData.entries.length} entries
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Ledger Entries Table */}
        {selectedAccount && ledgerData && (
          <Card sx={{ boxShadow: 4 }}>
            <CardContent sx={{ p: 0 }}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <TableContainer sx={{ maxHeight: '70vh' }}>
                    <Table stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Date</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Entry #</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Description</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Reference</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Contra Account</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Debit</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Credit</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Balance</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Status</TableCell>
                          <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {paginatedEntries.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={10} sx={{ textAlign: 'center', py: 4 }}>
                              <Stack alignItems="center" spacing={2}>
                                <ReceiptIcon sx={{ fontSize: 64, color: 'text.disabled' }} />
                                <Typography variant="h6" color="text.secondary">
                                  No transactions found
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Try adjusting your date range or search criteria
                                </Typography>
                              </Stack>
                            </TableCell>
                          </TableRow>
                        ) : (
                          paginatedEntries.map((entry) => (
                            <TableRow key={entry.id} hover>
                              <TableCell>
                                {dayjs(entry.transaction_date).format('MMM DD, YYYY')}
                              </TableCell>
                              <TableCell sx={{ fontWeight: 500, color: 'primary.main' }}>
                                {entry.entry_number}
                              </TableCell>
                              <TableCell>
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                    {entry.description}
                                  </Typography>
                                  {entry.memo && (
                                    <Typography variant="caption" color="text.secondary">
                                      {entry.memo}
                                    </Typography>
                                  )}
                                </Box>
                              </TableCell>
                              <TableCell>{entry.reference_number || '-'}</TableCell>
                              <TableCell>
                                <Typography variant="body2" color="text.secondary">
                                  {entry.contra_account || '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                {entry.debit_amount > 0 ? (
                                  <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
                                    {formatCurrency(entry.debit_amount)}
                                  </Typography>
                                ) : '-'}
                              </TableCell>
                              <TableCell align="right">
                                {entry.credit_amount > 0 ? (
                                  <Typography variant="body2" sx={{ fontWeight: 500, color: 'error.main' }}>
                                    {formatCurrency(entry.credit_amount)}
                                  </Typography>
                                ) : '-'}
                              </TableCell>
                              <TableCell align="right">
                                <Typography 
                                  variant="body2" 
                                  sx={{ 
                                    fontWeight: 600,
                                    color: getBalanceColor(entry.running_balance, selectedAccount.normal_balance)
                                  }}
                                >
                                  {formatCurrency(entry.running_balance)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip 
                                  label={entry.journal_entry_status} 
                                  color={getStatusColor(entry.journal_entry_status)}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell>
                                <Tooltip title="View Journal Entry">
                                  <IconButton 
                                    size="small"
                                    onClick={() => handleViewJournalEntry(entry.journal_entry_id)}
                                    disabled={journalEntryLoading}
                                  >
                                    {journalEntryLoading ? <CircularProgress size={16} /> : <ViewIcon />}
                                  </IconButton>
                                </Tooltip>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Pagination */}
                  {filteredEntries.length > rowsPerPage && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                      <Pagination
                        count={Math.ceil(filteredEntries.length / rowsPerPage)}
                        page={page}
                        onChange={(_, newPage) => setPage(newPage)}
                        color="primary"
                        size="large"
                      />
                    </Box>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {!selectedAccount && (
          <Card sx={{ textAlign: 'center', py: 8 }}>
            <CardContent>
              <AccountBalanceIcon sx={{ fontSize: 80, color: 'text.disabled', mb: 2 }} />
              <Typography variant="h5" color="text.secondary" sx={{ mb: 1 }}>
                Select an Account
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Choose an account from the dropdown above to view its detailed transaction history
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Journal Entry View Dialog */}
        <Dialog 
          open={viewJournalDialog} 
          onClose={handleCloseJournalDialog} 
          maxWidth="lg" 
          fullWidth
          PaperProps={{
            sx: { minHeight: '70vh' }
          }}
        >
          <DialogTitle sx={{ 
            bgcolor: 'primary.main', 
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <ReceiptIcon />
            Journal Entry Details
            {viewingJournalEntry && (
              <Chip 
                label={viewingJournalEntry.entry_number} 
                sx={{ 
                  ml: 2, 
                  bgcolor: 'white', 
                  color: 'primary.main',
                  fontWeight: 600 
                }} 
              />
            )}
          </DialogTitle>
          
          <DialogContent sx={{ p: 3 }}>
            {journalEntryLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : viewingJournalEntry ? (
              <Grid container spacing={3}>
                {/* Header Information */}
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">Entry Number</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                          {viewingJournalEntry.entry_number}
                        </Typography>
                        
                        <Typography variant="subtitle2" color="text.secondary">Transaction Date</Typography>
                        <Typography variant="body1" sx={{ mb: 2 }}>
                          {dayjs(viewingJournalEntry.transaction_date).format('MMM DD, YYYY')}
                        </Typography>
                        
                        <Typography variant="subtitle2" color="text.secondary">Entry Type</Typography>
                        <Typography variant="body1" sx={{ mb: 2 }}>
                          {viewingJournalEntry.entry_type}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                        <Chip 
                          label={viewingJournalEntry.status} 
                          color={getStatusColor(viewingJournalEntry.status)}
                          sx={{ mb: 2 }}
                        />
                        
                        <Typography variant="subtitle2" color="text.secondary">Total Amount</Typography>
                        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                          {formatCurrency(viewingJournalEntry.total_debits)}
                        </Typography>
                        
                        <Typography variant="subtitle2" color="text.secondary">Balance Status</Typography>
                        <Chip 
                          label={viewingJournalEntry.is_balanced ? 'Balanced' : 'Unbalanced'} 
                          color={viewingJournalEntry.is_balanced ? 'success' : 'error'}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                        <Typography variant="body1" sx={{ mb: 2 }}>{viewingJournalEntry.description}</Typography>
                        
                        {viewingJournalEntry.reference_number && (
                          <>
                            <Typography variant="subtitle2" color="text.secondary">Reference Number</Typography>
                            <Typography variant="body1" sx={{ mb: 2 }}>{viewingJournalEntry.reference_number}</Typography>
                          </>
                        )}
                        
                        {viewingJournalEntry.memo && (
                          <>
                            <Typography variant="subtitle2" color="text.secondary">Memo</Typography>
                            <Typography variant="body1">{viewingJournalEntry.memo}</Typography>
                          </>
                        )}
                      </Grid>
                    </Grid>
                  </Card>
                </Grid>

                {/* Journal Lines */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>Journal Lines</Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead sx={{ bgcolor: 'grey.100' }}>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600 }}>Line #</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Account</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Memo</TableCell>
                          <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Debit</TableCell>
                          <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Credit</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {viewingJournalEntry.journal_lines.map((line) => (
                          <TableRow key={line.id}>
                            <TableCell sx={{ fontWeight: 500 }}>{line.line_number}</TableCell>
                            <TableCell>
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {line.account_number} - {line.account_name}
                                </Typography>
                              </Box>
                            </TableCell>
                            <TableCell>{line.description}</TableCell>
                            <TableCell>{line.memo || '-'}</TableCell>
                            <TableCell sx={{ textAlign: 'right' }}>
                              {line.debit_amount > 0 ? (
                                <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
                                  {formatCurrency(line.debit_amount)}
                                </Typography>
                              ) : '-'}
                            </TableCell>
                            <TableCell sx={{ textAlign: 'right' }}>
                              {line.credit_amount > 0 ? (
                                <Typography variant="body2" sx={{ fontWeight: 500, color: 'error.main' }}>
                                  {formatCurrency(line.credit_amount)}
                                </Typography>
                              ) : '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                        
                        {/* Totals Row */}
                        <TableRow sx={{ bgcolor: 'grey.50', borderTop: 2, borderColor: 'divider' }}>
                          <TableCell colSpan={4} sx={{ fontWeight: 600, textAlign: 'right' }}>
                            TOTALS:
                          </TableCell>
                          <TableCell sx={{ textAlign: 'right', fontWeight: 600, color: 'success.main' }}>
                            {formatCurrency(viewingJournalEntry.total_debits)}
                          </TableCell>
                          <TableCell sx={{ textAlign: 'right', fontWeight: 600, color: 'error.main' }}>
                            {formatCurrency(viewingJournalEntry.total_credits)}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No journal entry data available
              </Typography>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={handleCloseJournalDialog} variant="outlined">
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={Boolean(alert)}
          autoHideDuration={6000}
          onClose={() => setAlert(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={() => setAlert(null)} severity={alert?.type || 'info'} sx={{ width: '100%' }}>
            {alert?.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default AccountLedgersPage;