import React from 'react';
import {
  Box,
  Typography,
  Icon<PERSON>utton,
  Button,
} from '@mui/material';
import {
  Close as CloseIcon,
  Cancel as CancelIcon,
  Print as PrintIcon,
  Repeat as RepeatIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  ContentCopy as ContentCopyIcon,
} from '@mui/icons-material';
import CurrencyDisplay from './CurrencyDisplay';

interface FormHeaderFooterProps {
  // Header props
  title: string;
  subtitle: string;
  titleColor?: 'primary' | 'success.main' | 'error.main' | 'warning.main' | 'info.main';
  
  // Amount display (optional)
  showAmount?: boolean;
  amount?: number;
  amountLabel?: string;
  currencyCode?: string;
  amountColor?: 'primary' | 'success.main' | 'error.main' | 'warning.main' | 'info.main';
  
  // Header actions
  onClose: () => void;
  
  // Footer actions
  onCancel: () => void;
  onPrint?: () => void;
  onCopy?: () => void;
  onMakeRecurring?: () => void;
  onSave?: () => void;
  onSaveAndClose?: () => void;
  
  // State props
  isViewMode?: boolean;
  isEditMode?: boolean;
  loading?: boolean;
  
  // Button labels (optional customization)
  saveLabel?: string;
  saveAndCloseLabel?: string;
  
  // Content
  children: React.ReactNode;
}

export const FormHeaderFooter: React.FC<FormHeaderFooterProps> = ({
  title,
  subtitle,
  titleColor = 'primary',
  showAmount = false,
  amount = 0,
  amountLabel = 'Amount',
  currencyCode,
  amountColor = 'primary',
  onClose,
  onCancel,
  onPrint,
  onCopy,
  onMakeRecurring,
  onSave,
  onSaveAndClose,
  isViewMode = false,
  isEditMode = false,
  loading = false,
  saveLabel,
  saveAndCloseLabel,
  children,
}) => {
  return (
    <Box
      sx={{
        position: 'fixed',
        top: '64px', // Account for navigation bar height
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: 'background.default',
        zIndex: 1200,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" color={titleColor}>
            {title}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            {subtitle}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
          {/* Amount Display (if enabled) */}
          {showAmount && (
            <Box sx={{ textAlign: 'right', minWidth: '200px' }}>
              <CurrencyDisplay 
                value={amount}
                currencyCode={currencyCode}
                variant="h4"
                color={amountColor}
                sx={{ fontWeight: 'bold' }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                {amountLabel}
              </Typography>
            </Box>
          )}
          
          <IconButton 
            onClick={onClose} 
            sx={{ 
              color: 'text.primary',
              '&:hover': {
                backgroundColor: 'action.hover',
                color: 'error.main'
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        {children}
      </Box>

      {/* Footer Actions */}
      <Box
        sx={{
          bgcolor: 'background.paper',
          borderTop: 1,
          borderColor: 'divider',
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Button
          startIcon={<CancelIcon />}
          onClick={onCancel}
          color="inherit"
        >
          Cancel
        </Button>

        <Box sx={{ display: 'flex', gap: 2 }}>
          {onPrint && (
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              color="inherit"
              onClick={onPrint}
            >
              Print
            </Button>
          )}
          
          {onCopy && (
            <Button
              startIcon={<ContentCopyIcon />}
              variant="outlined"
              color="inherit"
              onClick={onCopy}
            >
              Copy
            </Button>
          )}
          
          {!isViewMode && onMakeRecurring && (
            <Button
              startIcon={<RepeatIcon />}
              variant="outlined"
              color="inherit"
              onClick={onMakeRecurring}
            >
              Make Recurring
            </Button>
          )}
          
          {!isViewMode && onSave && (
            <Button
              startIcon={<SaveIcon />}
              variant="outlined"
              onClick={onSave}
              disabled={loading}
            >
              {saveLabel || (isEditMode ? 'Update' : 'Save')}
            </Button>
          )}
          
          {!isViewMode && onSaveAndClose && (
            <Button
              startIcon={<SaveAltIcon />}
              variant="contained"
              onClick={onSaveAndClose}
              disabled={loading}
            >
              {saveAndCloseLabel || (isEditMode ? 'Update and Close' : 'Save and Close')}
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default FormHeaderFooter; 