import React, { useState, useEffect, useCallback } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  Autocomplete,
  FormHelperText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Chip,
  InputAdornment,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  Category as CategoryIcon,
  Inventory as InventoryIcon,
  AttachMoney as MoneyIcon,
  Description as DescriptionIcon,
  LocalOffer as TagIcon,
  Business as BusinessIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { useProducts } from '../../../contexts/ProductContext';
import { useChartOfAccounts } from '../../../contexts/ChartOfAccountsContext';
import { useProductCategories } from '../../../contexts/ProductCategoriesContext';
import { 
  ProductFormData, 
  PRODUCT_CATEGORIES, 
  CURRENCIES, 
  ProductType,
  BundleItem,
  Product
} from '../../../shared/types/product.types';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface ProductFormProps {
  onSuccess?: () => void;
  product?: Product | null;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-tabpanel-${index}`}
      aria-labelledby={`product-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ProductForm: React.FC<ProductFormProps> = ({ onSuccess, product }) => {
  const { addProduct, updateProduct, products, loading } = useProducts();
  const { categories } = useProductCategories();
  
  // Only use COA for new products, not for editing existing ones
  let accounts: any[] = [];
  try {
    if (!product) {
      const { accounts: coaAccounts } = useChartOfAccounts();
      accounts = coaAccounts;
    }
  } catch (error) {
    console.warn('ChartOfAccounts not available, skipping account fields');
    accounts = [];
  }
  const [tabValue, setTabValue] = useState(0);
  const [selectedProducts, setSelectedProducts] = useState<BundleItem[]>([]);

  // Set initial tab value based on product type if editing
  useEffect(() => {
    if (product) {
      const typeIndex = product.type === 'product' ? 0 : product.type === 'service' ? 1 : 2;
      setTabValue(typeIndex);
      
      // If it's a bundle, set the selected products
      if (product.type === 'bundle' && product.bundleItems) {
        setSelectedProducts(product.bundleItems);
      }
    }
  }, [product]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    
    // Update form type based on tab selection
    const productType: ProductType = newValue === 0 ? 'product' : newValue === 1 ? 'service' : 'bundle';
    formik.setFieldValue('type', productType);
    
    // Reset type-specific fields when changing types
    if (productType === 'service') {
      formik.setFieldValue('trackInventory', false);
      formik.setFieldValue('inventoryQuantity', 0);
      formik.setFieldValue('lowStockAlert', false);
      formik.setFieldValue('inventoryAccountId', '');
    }
  };

  const validationSchema = Yup.object({
    name: Yup.string().required('Product name is required'),
    sku: Yup.string().required('SKU is required'),
    salesPrice: Yup.number().required('Sales price is required').min(0),
    costPrice: Yup.number().required('Cost price is required').min(0),
    // COA fields only required for new products, not for editing
    incomeAccountId: product ? Yup.string() : Yup.string().required('Income account is required'),
    expenseAccountId: product ? Yup.string() : Yup.string().required('Expense account is required'),
    inventoryAccountId: Yup.string().when(['type'], {
      is: (type: ProductType) => type === 'product' && !product,
      then: (schema) => schema.required('Inventory account is required for physical products'),
    }),
  });

  // Set initial values based on whether we're editing or creating
  const initialValues: ProductFormData = product ? {
    name: product.name,
    sku: product.sku,
    description: product.description || '',
    type: product.type,
    category: product.category,
    salesPrice: product.salesPrice,
    costPrice: product.costPrice,
    currency: product.currency,
    trackInventory: product.trackInventory || false,
    inventoryQuantity: product.inventoryQuantity || 0,
    lowStockAlert: product.lowStockAlert || false,
    lowStockThreshold: product.lowStockThreshold || 5,
    status: product.status,
    incomeAccountId: product.incomeAccountId,
    expenseAccountId: product.expenseAccountId,
    inventoryAccountId: product.inventoryAccountId || '',
    bundleItems: product.bundleItems || [],
    displayBundleItems: product.displayBundleItems || true
  } : {
    name: '',
    sku: '',
    description: '',
    type: 'product' as ProductType,
    category: '',
    salesPrice: 0,
    costPrice: 0,
    currency: 'USD',
    trackInventory: false,
    inventoryQuantity: 0,
    lowStockAlert: false,
    lowStockThreshold: 5,
    status: 'active' as const,
    incomeAccountId: '',
    expenseAccountId: '',
    inventoryAccountId: '',
    bundleItems: [] as BundleItem[],
    displayBundleItems: true
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, setStatus }) => {
      try {
        setStatus(null);
        
        // For bundle products, add the selected products
        if (values.type === 'bundle') {
          values.bundleItems = selectedProducts;
        }
        
        // For services, ensure inventory-related fields are not set
        if (values.type === 'service') {
          values.trackInventory = false;
          values.inventoryQuantity = 0;
          values.lowStockAlert = false;
          values.inventoryAccountId = '';
        }
        
        console.log('🚀 Submitting product form with values:', values);
        
        if (product) {
          // Update existing product
          await updateProduct({
            ...product,
            ...values,
            updatedAt: new Date().toISOString()
          });
          console.log('✅ Product updated successfully');
        } else {
          // Add new product
          await addProduct(values);
          console.log('✅ Product created successfully');
        }
        
        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }
        
      } catch (error: any) {
        console.error('❌ Error submitting product form:', error);
        setStatus(error.message || 'Failed to save product');
      } finally {
        setSubmitting(false);
      }
    },
  });

  const handleAddBundleItem = () => {
    setSelectedProducts([...selectedProducts, { productId: '', quantity: 1 }]);
  };

  const handleRemoveBundleItem = (index: number) => {
    const updatedItems = [...selectedProducts];
    updatedItems.splice(index, 1);
    setSelectedProducts(updatedItems);
  };

  const handleBundleItemChange = (index: number, field: keyof BundleItem, value: string | number) => {
    const updatedItems = [...selectedProducts];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // If product ID changed, find the product name for display
    if (field === 'productId' && typeof value === 'string') {
      const selectedProduct = products.find(p => p.id === value);
      if (selectedProduct) {
        updatedItems[index].name = selectedProduct.name;
      }
    }
    
    setSelectedProducts(updatedItems);
  };

  return (
    <Card>
      <CardHeader 
        title={product ? "Edit Product/Service" : "Create Product/Service"} 
        subheader={product ? "Update product information and pricing" : "Add a new product, service, or bundle to your inventory"}
      />
      <Divider />
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="product type tabs">
          <Tab label="Physical Product" id="product-tab-0" />
          <Tab label="Service" id="product-tab-1" />
          <Tab label="Bundle" id="product-tab-2" />
        </Tabs>
      </Box>
      
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            {/* Common fields for all product types */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Name"
                {...formik.getFieldProps('name')}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="SKU"
                {...formik.getFieldProps('sku')}
                error={formik.touched.sku && Boolean(formik.errors.sku)}
                helperText={formik.touched.sku && formik.errors.sku}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                {...formik.getFieldProps('description')}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={PRODUCT_CATEGORIES}
                getOptionLabel={(option) => option.label}
                value={PRODUCT_CATEGORIES.find(cat => cat.value === formik.values.category) || null}
                onChange={(_, newValue) => {
                  formik.setFieldValue('category', newValue ? newValue.value : '');
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Category"
                    error={formik.touched.category && Boolean(formik.errors.category)}
                    helperText={formik.touched.category && formik.errors.category}
                    fullWidth
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Autocomplete
                options={[
                  { value: 'active', label: 'Active' },
                  { value: 'inactive', label: 'Inactive' }
                ]}
                getOptionLabel={(option) => option.label}
                value={
                  formik.values.status === 'active' 
                    ? { value: 'active', label: 'Active' } 
                    : { value: 'inactive', label: 'Inactive' }
                }
                onChange={(_, newValue) => {
                  formik.setFieldValue('status', newValue ? newValue.value : 'active');
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Status"
                    fullWidth
                  />
                )}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormattedCurrencyInput
                fullWidth
                label="Sales Price"
                name="salesPrice"
                value={formik.values.salesPrice}
                onChange={(e) => formik.setFieldValue('salesPrice', parseFloat(e.target.value) || 0)}
                currencySymbol="$"
                error={formik.touched.salesPrice && Boolean(formik.errors.salesPrice)}
                helperText={formik.touched.salesPrice && formik.errors.salesPrice}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormattedCurrencyInput
                fullWidth
                label="Cost Price"
                name="costPrice"
                value={formik.values.costPrice}
                onChange={(e) => formik.setFieldValue('costPrice', parseFloat(e.target.value) || 0)}
                currencySymbol="$"
                error={formik.touched.costPrice && Boolean(formik.errors.costPrice)}
                helperText={formik.touched.costPrice && formik.errors.costPrice}
              />
            </Grid>
            
            {/* COA fields only for new products */}
            {!product && (
              <>
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={accounts.filter(account => account.type === 'income')}
                    getOptionLabel={(option) => option.name}
                    value={accounts.find(acc => acc.id === formik.values.incomeAccountId) || null}
                    onChange={(_, newValue) => {
                      formik.setFieldValue('incomeAccountId', newValue ? newValue.id : '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Income Account"
                        error={formik.touched.incomeAccountId && Boolean(formik.errors.incomeAccountId)}
                        helperText={formik.touched.incomeAccountId && formik.errors.incomeAccountId}
                        fullWidth
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={accounts.filter(account => account.type === 'expense')}
                    getOptionLabel={(option) => option.name}
                    value={accounts.find(acc => acc.id === formik.values.expenseAccountId) || null}
                    onChange={(_, newValue) => {
                      formik.setFieldValue('expenseAccountId', newValue ? newValue.id : '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Expense Account"
                        error={formik.touched.expenseAccountId && Boolean(formik.errors.expenseAccountId)}
                        helperText={formik.touched.expenseAccountId && formik.errors.expenseAccountId}
                        fullWidth
                      />
                    )}
                  />
                </Grid>
              </>
            )}
            
            {/* Type-specific fields */}
            <TabPanel value={tabValue} index={0}>
              {/* Physical Product specific fields */}
              <Grid container spacing={3}>
                {/* Inventory account only for new products */}
                {!product && (
                  <Grid item xs={12} md={6}>
                    <Autocomplete
                      options={accounts.filter(account => account.type === 'asset')}
                      getOptionLabel={(option) => option.name}
                      value={accounts.find(acc => acc.id === formik.values.inventoryAccountId) || null}
                      onChange={(_, newValue) => {
                        formik.setFieldValue('inventoryAccountId', newValue ? newValue.id : '');
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Inventory Account"
                          error={formik.touched.inventoryAccountId && Boolean(formik.errors.inventoryAccountId)}
                          helperText={formik.touched.inventoryAccountId && formik.errors.inventoryAccountId}
                          fullWidth
                        />
                      )}
                    />
                  </Grid>
                )}
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formik.values.trackInventory}
                        onChange={(e) => {
                          formik.setFieldValue('trackInventory', e.target.checked);
                          if (!e.target.checked) {
                            formik.setFieldValue('lowStockAlert', false);
                          }
                        }}
                      />
                    }
                    label="Track Inventory"
                  />
                </Grid>
                
                {formik.values.trackInventory && (
                  <>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Inventory Quantity"
                        type="number"
                        inputProps={{ min: 0 }}
                        {...formik.getFieldProps('inventoryQuantity')}
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.lowStockAlert}
                            onChange={(e) => formik.setFieldValue('lowStockAlert', e.target.checked)}
                          />
                        }
                        label="Enable Low Stock Alert"
                      />
                    </Grid>
                    
                    {formik.values.lowStockAlert && (
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Low Stock Threshold"
                          type="number"
                          inputProps={{ min: 1 }}
                          {...formik.getFieldProps('lowStockThreshold')}
                        />
                      </Grid>
                    )}
                  </>
                )}
              </Grid>
            </TabPanel>
            
            <TabPanel value={tabValue} index={1}>
              {/* Service specific fields - no additional fields needed */}
              <Typography variant="body2" color="textSecondary">
                Services don't have inventory tracking or physical attributes.
              </Typography>
            </TabPanel>
            
            <TabPanel value={tabValue} index={2}>
              {/* Bundle specific fields */}
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1">Bundle Items</Typography>
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Add products or services to this bundle
                  </Typography>
                  
                  <TableContainer component={Paper} sx={{ mt: 2 }}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Product/Service</TableCell>
                          <TableCell>Quantity</TableCell>
                          <TableCell width="10%">Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedProducts.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={3} align="center">
                              No items added to this bundle
                            </TableCell>
                          </TableRow>
                        ) : (
                          selectedProducts.map((item, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Autocomplete
                                  options={products.filter(p => p.type !== 'bundle')}
                                  getOptionLabel={(option) => `${option.name} (${option.type})`}
                                  value={products.find(p => p.id === item.productId) || null}
                                  onChange={(_, newValue) => {
                                    handleBundleItemChange(index, 'productId', newValue ? newValue.id : '');
                                  }}
                                  renderInput={(params) => (
                                    <TextField
                                      {...params}
                                      placeholder="Select a product or service"
                                      fullWidth
                                      size="small"
                                    />
                                  )}
                                  groupBy={(option) => option.type.charAt(0).toUpperCase() + option.type.slice(1)}
                                  renderGroup={(params) => (
                                    <div key={params.key}>
                                      <Typography
                                        variant="caption"
                                        sx={{
                                          fontWeight: 'bold',
                                          p: 1,
                                          display: 'block',
                                          color: 'text.secondary',
                                        }}
                                      >
                                        {params.group}
                                      </Typography>
                                      {params.children}
                                    </div>
                                  )}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell>
                                <TextField
                                  type="number"
                                  inputProps={{ min: 1 }}
                                  value={item.quantity}
                                  onChange={(e) => handleBundleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell>
                                <IconButton onClick={() => handleRemoveBundleItem(index)} size="small">
                                  <DeleteIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  
                  <Button
                    startIcon={<AddIcon />}
                    onClick={handleAddBundleItem}
                    sx={{ mt: 2 }}
                  >
                    Add Item
                  </Button>
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formik.values.displayBundleItems}
                        onChange={(e) => formik.setFieldValue('displayBundleItems', e.target.checked)}
                      />
                    }
                    label="Display Bundle Items on Invoices"
                  />
                </Grid>
              </Grid>
            </TabPanel>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              
              {/* Error Display */}
              {formik.status && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {formik.status}
                </Alert>
              )}
              
              {/* Loading Display */}
              {loading && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <CircularProgress size={16} />
                    <Typography variant="body2">
                      {product ? 'Updating product...' : 'Creating product...'}
                    </Typography>
                  </Box>
                </Alert>
              )}
              
              <Button 
                type="submit" 
                variant="contained" 
                color="primary" 
                size="large"
                disabled={formik.isSubmitting || loading}
                startIcon={formik.isSubmitting ? <CircularProgress size={16} /> : null}
              >
                {formik.isSubmitting ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}
              </Button>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProductForm; 