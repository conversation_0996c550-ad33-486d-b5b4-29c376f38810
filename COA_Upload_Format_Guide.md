# Chart of Accounts (COA) Upload Format Guide

## Overview
This guide provides the exact format required to upload Chart of Accounts data into your ERP system. The system follows International Financial Reporting Standards (IFRS) with QuickBooks-style detail types for enhanced classification.

## System Architecture

### Account Types (5 Main Categories)
1. **ASSET** - Assets (Normal Balance: DEBIT)
2. **LIABILITY** - Liabilities (Normal Balance: CREDIT) 
3. **EQUITY** - Equity (Normal Balance: CREDIT)
4. **REVENUE** - Revenue (Normal Balance: CREDIT)
5. **EXPENSE** - Expenses (Normal Balance: DEBIT)

### Account Numbering Convention
- **1xxx** - Assets (1000-1999)
- **2xxx** - Liabilities (2000-2999)
- **3xxx** - Equity (3000-3999)
- **4xxx** - Revenue (4000-4999)
- **5xxx-9xxx** - Expenses (5000-9999)

## CSV Upload Format

### Required Columns (Minimum)
```csv
account_number,account_name,account_type_code,detail_type_code,description,opening_balance,currency,is_active
```

### Complete Format (All Available Fields)
```csv
account_number,account_name,account_type_code,detail_type_code,parent_account_number,description,opening_balance,opening_balance_date,currency,is_active,is_header_account,tax_line,bank_account_number,bank_routing_number
```

## Field Specifications

| Field Name | Type | Required | Description | Example |
|------------|------|----------|-------------|---------|
| `account_number` | String(20) | **Yes** | Unique account identifier | "1000", "4100" |
| `account_name` | String(200) | **Yes** | Descriptive name | "Cash in Hand", "Sales Revenue" |
| `account_type_code` | String | **Yes** | ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE | "ASSET" |
| `detail_type_code` | String | **Yes** | Specific classification (see below) | "CASH_BANK" |
| `parent_account_number` | String(20) | No | Parent account for hierarchy | "1000" |
| `description` | Text | No | Detailed description | "Petty cash and cash on hand" |
| `opening_balance` | Decimal(15,2) | No | Starting balance | "5000.00" |
| `opening_balance_date` | Date | No | Date of opening balance | "2024-01-01" |
| `currency` | String(3) | No | ISO currency code | "INR", "USD" |
| `is_active` | Boolean | No | Account status | "TRUE", "FALSE" |
| `is_header_account` | Boolean | No | Summary account flag | "TRUE", "FALSE" |
| `tax_line` | String(100) | No | Tax reporting line | "Line 1a - Income" |
| `bank_account_number` | String(50) | No | Bank account number | "*********" |
| `bank_routing_number` | String(20) | No | Bank routing number | "*********" |

## Available Detail Types

### Asset Detail Types
- `CASH_BANK` - Cash and Bank
- `AR_TRADE` - Accounts Receivable
- `INVENTORY` - Inventory
- `PREPAID` - Prepaid Expenses
- `FIXED_ASSET` - Fixed Assets

### Liability Detail Types
- `AP_TRADE` - Accounts Payable
- `ACCRUED` - Accrued Liabilities
- `TAX_PAYABLE` - Tax Payable
- `LOAN` - Loans and Debt

### Equity Detail Types
- `CAPITAL` - Owner Capital
- `RETAINED` - Retained Earnings

### Revenue Detail Types
- `SALES` - Sales Revenue
- `SERVICE` - Service Revenue
- `OTHER_REV` - Other Revenue

### Expense Detail Types
- `COGS` - Cost of Goods Sold
- `ADVERTISING` - Advertising
- `OFFICE_SUPPLIES` - Office Supplies
- `TRAVEL` - Travel & Entertainment
- `UTILITIES` - Utilities
- `RENT` - Rent Expense
- `SALARIES` - Salaries & Wages

## Sample CSV Template

```csv
account_number,account_name,account_type_code,detail_type_code,parent_account_number,description,opening_balance,opening_balance_date,currency,is_active,is_header_account
1000,Cash in Hand,ASSET,CASH_BANK,,Petty cash and cash on hand,5000.00,2024-01-01,INR,TRUE,FALSE
1010,Business Checking Account,ASSET,CASH_BANK,,Primary business bank account,25000.00,2024-01-01,INR,TRUE,FALSE
1100,Accounts Receivable,ASSET,AR_TRADE,,Money owed by customers,8500.00,2024-01-01,INR,TRUE,FALSE
1200,Inventory,ASSET,INVENTORY,,Stock and inventory items,15000.00,2024-01-01,INR,TRUE,FALSE
1300,Prepaid Insurance,ASSET,PREPAID,,Insurance paid in advance,2400.00,2024-01-01,INR,TRUE,FALSE
1400,Equipment,ASSET,FIXED_ASSET,,Office and business equipment,50000.00,2024-01-01,INR,TRUE,FALSE
2000,Accounts Payable,LIABILITY,AP_TRADE,,Money owed to suppliers,4200.00,2024-01-01,INR,TRUE,FALSE
2100,Accrued Expenses,LIABILITY,ACCRUED,,Expenses incurred but not yet paid,1500.00,2024-01-01,INR,TRUE,FALSE
2200,GST Payable,LIABILITY,TAX_PAYABLE,,Goods and Services Tax owed,3200.00,2024-01-01,INR,TRUE,FALSE
2300,Bank Loan,LIABILITY,LOAN,,Long-term bank loan,75000.00,2024-01-01,INR,TRUE,FALSE
3000,Owner Capital,EQUITY,CAPITAL,,Owner investment in business,100000.00,2024-01-01,INR,TRUE,FALSE
3100,Retained Earnings,EQUITY,RETAINED,,Accumulated profits,25000.00,2024-01-01,INR,TRUE,FALSE
4000,Sales Revenue,REVENUE,SALES,,Revenue from product sales,0.00,2024-01-01,INR,TRUE,FALSE
4100,Service Revenue,REVENUE,SERVICE,,Revenue from services,0.00,2024-01-01,INR,TRUE,FALSE
4200,Interest Income,REVENUE,OTHER_REV,,Interest earned on investments,0.00,2024-01-01,INR,TRUE,FALSE
5000,Cost of Goods Sold,EXPENSE,COGS,,Direct cost of products sold,0.00,2024-01-01,INR,TRUE,FALSE
6000,Advertising Expense,EXPENSE,ADVERTISING,,Marketing and advertising costs,0.00,2024-01-01,INR,TRUE,FALSE
6100,Office Supplies,EXPENSE,OFFICE_SUPPLIES,,Office supplies and materials,0.00,2024-01-01,INR,TRUE,FALSE
6200,Travel Expense,EXPENSE,TRAVEL,,Business travel costs,0.00,2024-01-01,INR,TRUE,FALSE
6300,Utilities,EXPENSE,UTILITIES,,Electricity water gas internet,0.00,2024-01-01,INR,TRUE,FALSE
6400,Rent Expense,EXPENSE,RENT,,Monthly office rent,0.00,2024-01-01,INR,TRUE,FALSE
6500,Salary Expense,EXPENSE,SALARIES,,Employee salaries and wages,0.00,2024-01-01,INR,TRUE,FALSE
```

## Hierarchical Structure Example

For parent-child relationships:

```csv
account_number,account_name,account_type_code,detail_type_code,parent_account_number,description,is_header_account
1000,Current Assets,ASSET,CASH_BANK,,Main current assets category,TRUE
1010,Cash Accounts,ASSET,CASH_BANK,1000,All cash accounts,TRUE
1011,Petty Cash,ASSET,CASH_BANK,1010,Office petty cash,FALSE
1012,Main Bank Account,ASSET,CASH_BANK,1010,Primary checking account,FALSE
1013,Savings Account,ASSET,CASH_BANK,1010,Business savings,FALSE
```

## Validation Rules

1. **Account Numbers**: Must be unique across the system
2. **Account Type Codes**: Must match exactly (case-sensitive)
3. **Detail Type Codes**: Must exist for the specified account type
4. **Parent Accounts**: Must exist before child accounts in the file
5. **Currency**: Defaults to "INR" if not specified
6. **Boolean Fields**: Use TRUE/FALSE or 1/0
7. **Decimal Format**: Use dot (.) as decimal separator
8. **Date Format**: Use YYYY-MM-DD format

## Import Process

1. **Prepare CSV File**: Follow the exact format above
2. **Validate Data**: Ensure all required fields are present
3. **Check Dependencies**: Account types and detail types must exist
4. **Upload via Admin**: Use Django admin interface for import
5. **Review Results**: Check for any import errors or warnings

## Best Practices

1. **Start Simple**: Begin with basic accounts, add complexity later
2. **Consistent Numbering**: Follow the numbering convention strictly
3. **Meaningful Names**: Use clear, descriptive account names
4. **Proper Classification**: Choose appropriate detail types
5. **Balance Validation**: Ensure opening balances are correct
6. **Backup First**: Always backup existing data before import

## Common Errors to Avoid

1. **Duplicate Account Numbers**: Each must be unique
2. **Invalid Account Type**: Must be exactly ASSET, LIABILITY, EQUITY, REVENUE, or EXPENSE
3. **Missing Detail Type**: Every account must have a valid detail type
4. **Circular References**: Parent accounts cannot reference themselves
5. **Invalid Currency Codes**: Use standard ISO 4217 codes
6. **Incorrect Date Format**: Must be YYYY-MM-DD

## Support

For technical support with COA uploads:
- Check the Django Admin logs for detailed error messages
- Verify account types and detail types exist in the system
- Ensure proper permissions for the uploading user
- Contact system administrator for complex hierarchical structures 