import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Undo as UndoIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  PostAdd as PostIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
  ThumbUp as ApproveIcon,
  LocalShipping as ShippingIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { grnReturnService, GRNReturn } from '../services/grn-return.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const GoodsReturnPage: React.FC = () => {
  const navigate = useNavigate();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();
  const [returns, setReturns] = useState<GRNReturn[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedReturn, setSelectedReturn] = useState<GRNReturn | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [markReturnedDialogOpen, setMarkReturnedDialogOpen] = useState(false);
  const [postDialogOpen, setPostDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  
  // Pagination and filtering
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [returnReasonFilter, setReturnReasonFilter] = useState('');

  // IFRS-compliant currency formatting function
  const formatCurrencyAmount = (amount: number): string => {
    if (!currencyInfo || currencyLoading) {
      console.log('💰 Currency info not loaded yet, using fallback');
      return `₹${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    
    console.log('💰 Using currency info:', currencyInfo);
    const symbol = currencyInfo.functional_currency_symbol || '$';
    return `${symbol}${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}`;
  };

  // Helper function to get product names from return items
  const getProductNames = (returnNote: GRNReturn): { display: string; full: string[] } => {
    if (!returnNote.items || returnNote.items.length === 0) {
      return { display: 'No products', full: [] };
    }

    const productNames = returnNote.items
      .map(item => item.product_name || `Product ${item.product}`)
      .filter(Boolean);

    if (productNames.length <= 3) {
      return { 
        display: productNames.join(', '), 
        full: productNames 
      };
    }

    const displayed = productNames.slice(0, 3);
    const remaining = productNames.length - 3;
    return {
      display: `${displayed.join(', ')} +${remaining} more`,
      full: productNames
    };
  };

  useEffect(() => {
    loadReturns();
  }, [page, pageSize, searchTerm, statusFilter, returnReasonFilter]);

  const loadReturns = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔍 Loading GRN Returns with search term:', searchTerm);
      
      const response = await grnReturnService.getGRNReturns({
        page: page + 1, // API uses 1-based pagination
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        return_reason: returnReasonFilter || undefined,
      });
      
      console.log('📋 Loaded GRN Returns:', response.results);
      setReturns(response.results || []);
      setTotalCount(response.count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRN Returns');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, searchTerm, statusFilter, returnReasonFilter]);

  const handleCreateReturn = () => {
    navigate('/dashboard/inventory/grn-returns/new');
  };

  const handleViewReturn = (returnNote: GRNReturn) => {
    navigate(`/dashboard/inventory/grn-returns/${returnNote.grn_return_id}`);
  };

  const handleEditReturn = (returnNote: GRNReturn) => {
    navigate(`/dashboard/inventory/grn-returns/${returnNote.grn_return_id}/edit`);
  };

  const handleDeleteReturn = async () => {
    if (!selectedReturn) return;
    
    try {
      await grnReturnService.deleteGRNReturn(selectedReturn.grn_return_id!);
      setDeleteDialogOpen(false);
      setSelectedReturn(null);
      loadReturns();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete GRN Return');
    }
  };

  const handleApproveReturn = async () => {
    if (!selectedReturn?.grn_return_id) return;
    
    try {
      await grnReturnService.approveGRNReturn(selectedReturn.grn_return_id);
      setApproveDialogOpen(false);
      setSelectedReturn(null);
      loadReturns();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to approve GRN Return');
    }
  };

  const handleMarkAsReturned = async () => {
    if (!selectedReturn?.grn_return_id) return;
    
    try {
      await grnReturnService.markAsReturned(selectedReturn.grn_return_id);
      setMarkReturnedDialogOpen(false);
      setSelectedReturn(null);
      loadReturns();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark GRN Return as returned');
    }
  };

  const handlePostToInventory = async () => {
    if (!selectedReturn?.grn_return_id) return;
    
    try {
      await grnReturnService.postToInventory(selectedReturn.grn_return_id);
      setPostDialogOpen(false);
      setSelectedReturn(null);
      loadReturns();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post GRN Return to inventory');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      DRAFT: { color: 'default' as const, label: 'Draft' },
      APPROVED: { color: 'warning' as const, label: 'Approved' },
      RETURNED: { color: 'info' as const, label: 'Returned' },
      POSTED: { color: 'success' as const, label: 'Posted' },
      CANCELLED: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const getReturnReasonChip = (reason: string) => {
    const reasonConfig = {
      DEFECTIVE: { color: 'error' as const, label: 'Defective' },
      WRONG_ITEM: { color: 'warning' as const, label: 'Wrong Item' },
      EXCESS_QTY: { color: 'info' as const, label: 'Excess Qty' },
      QUALITY_ISSUE: { color: 'error' as const, label: 'Quality Issue' },
      NOT_ORDERED: { color: 'warning' as const, label: 'Not Ordered' },
      EXPIRED: { color: 'error' as const, label: 'Expired' },
      OTHER: { color: 'default' as const, label: 'Other' },
    };
    
    const config = reasonConfig[reason as keyof typeof reasonConfig] || reasonConfig.OTHER;
    return <Chip label={config.label} color={config.color} size="small" variant="outlined" />;
  };

  const columns = [
    {
      field: 'grn_return_number',
      headerName: 'Return #',
      width: 100,
      renderCell: (params: any) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <UndoIcon color="primary" fontSize="small" />
          <Typography variant="body2" fontWeight="medium">
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'original_grn_number',
      headerName: 'GRN',
      width: 80,
      renderCell: (params: any) => (
        <Typography variant="body2" color="text.secondary">
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'vendor_name',
      headerName: 'Vendor',
      width: 110,
      renderCell: (params: any) => (
        <Typography 
          variant="body2"
          sx={{ 
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'return_date',
      headerName: 'Date',
      width: 80,
      renderCell: (params: any) => (
        <Typography variant="body2">
          {formatDate(params.value)}
        </Typography>
      ),
    },
    {
      field: 'return_reason',
      headerName: 'Reason',
      width: 90,
      renderCell: (params: any) => getReturnReasonChip(params.value),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 70,
      renderCell: (params: any) => getStatusChip(params.value),
    },
    {
      field: 'total_quantity',
      headerName: 'Qty',
      width: 50,
      align: 'right' as const,
      renderCell: (params: any) => (
        <Typography variant="body2" fontWeight="medium">
          {Number(params.value || 0).toFixed(1)}
        </Typography>
      ),
    },
    {
      field: 'warehouse_name',
      headerName: 'Warehouse',
      width: 100,
      renderCell: (params: any) => (
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{ 
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
        >
          {params.value}
        </Typography>
      ),
    },
    {
      field: 'products',
      headerName: 'Products',
      width: 140,
      renderCell: (params: any) => {
        const { display, full } = getProductNames(params.row);
        return (
          <Tooltip title={full.join(', ')} arrow>
            <Typography 
              variant="body2" 
              sx={{ 
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {display}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 110,
      sortable: false,
      renderCell: (params: any) => (
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Tooltip title="View Details">
            <IconButton
              size="small"
              onClick={() => handleViewReturn(params.row)}
              color="primary"
            >
              <ViewIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <IconButton
            size="small"
            onClick={(event) => {
              event.stopPropagation();
              setSelectedReturn(params.row);
              setAnchorEl(event.currentTarget);
            }}
          >
            <MoreVertIcon fontSize="small" />
          </IconButton>
        </Box>
      ),
    },
  ];

  const menuItems = [
    {
      label: 'View Details',
      icon: <ViewIcon fontSize="small" />,
      onClick: () => {
        if (selectedReturn) handleViewReturn(selectedReturn);
        setAnchorEl(null);
      },
    },
    {
      label: 'Edit',
      icon: <EditIcon fontSize="small" />,
      onClick: () => {
        if (selectedReturn) handleEditReturn(selectedReturn);
        setAnchorEl(null);
      },
      disabled: selectedReturn?.status !== 'DRAFT',
    },
    {
      label: 'Approve',
      icon: <ApproveIcon fontSize="small" />,
      onClick: () => {
        setApproveDialogOpen(true);
        setAnchorEl(null);
      },
      disabled: selectedReturn?.status !== 'DRAFT',
    },
    {
      label: 'Mark as Returned',
      icon: <ShippingIcon fontSize="small" />,
      onClick: () => {
        setMarkReturnedDialogOpen(true);
        setAnchorEl(null);
      },
      disabled: selectedReturn?.status !== 'APPROVED',
    },
    {
      label: 'Post to Inventory',
      icon: <PostIcon fontSize="small" />,
      onClick: () => {
        setPostDialogOpen(true);
        setAnchorEl(null);
      },
      disabled: selectedReturn?.status !== 'RETURNED',
    },
    {
      label: 'Delete',
      icon: <DeleteIcon fontSize="small" />,
      onClick: () => {
        setDeleteDialogOpen(true);
        setAnchorEl(null);
      },
      disabled: selectedReturn?.status === 'POSTED',
      color: 'error',
    },
  ];

  const filters = [
    {
      field: 'status',
      label: 'Status',
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { value: '', label: 'All Statuses' },
        { value: 'DRAFT', label: 'Draft' },
        { value: 'APPROVED', label: 'Approved' },
        { value: 'RETURNED', label: 'Returned' },
        { value: 'POSTED', label: 'Posted' },
        { value: 'CANCELLED', label: 'Cancelled' },
      ],
    },
    {
      field: 'return_reason',
      label: 'Return Reason',
      value: returnReasonFilter,
      onChange: setReturnReasonFilter,
      options: [
        { value: '', label: 'All Reasons' },
        { value: 'DEFECTIVE', label: 'Defective' },
        { value: 'WRONG_ITEM', label: 'Wrong Item' },
        { value: 'EXCESS_QTY', label: 'Excess Quantity' },
        { value: 'QUALITY_ISSUE', label: 'Quality Issue' },
        { value: 'NOT_ORDERED', label: 'Not Ordered' },
        { value: 'EXPIRED', label: 'Expired' },
        { value: 'OTHER', label: 'Other' },
      ],
    },
  ];

  return (
    <PageContainer
      title="Good Return Notes"
      actions={
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadReturns}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateReturn}
          >
            Create Return
          </Button>
        </Box>
      }
    >
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Search and Filters */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
        <TextField
          placeholder="Search return number, vendor, GRN..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ minWidth: 300, flexGrow: 1 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
        />
        
        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Status</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            label="Status"
          >
            <MenuItem value="">All Statuses</MenuItem>
            <MenuItem value="DRAFT">Draft</MenuItem>
            <MenuItem value="APPROVED">Approved</MenuItem>
            <MenuItem value="RETURNED">Returned</MenuItem>
            <MenuItem value="POSTED">Posted</MenuItem>
            <MenuItem value="CANCELLED">Cancelled</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{ minWidth: 150 }}>
          <InputLabel>Reason</InputLabel>
          <Select
            value={returnReasonFilter}
            onChange={(e) => setReturnReasonFilter(e.target.value)}
            label="Reason"
          >
            <MenuItem value="">All Reasons</MenuItem>
            <MenuItem value="DEFECTIVE">Defective</MenuItem>
            <MenuItem value="WRONG_ITEM">Wrong Item</MenuItem>
            <MenuItem value="EXCESS_QTY">Excess Qty</MenuItem>
            <MenuItem value="QUALITY_ISSUE">Quality Issue</MenuItem>
            <MenuItem value="NOT_ORDERED">Not Ordered</MenuItem>
            <MenuItem value="EXPIRED">Expired</MenuItem>
            <MenuItem value="OTHER">Other</MenuItem>
          </Select>
        </FormControl>

        {(searchTerm || statusFilter || returnReasonFilter) && (
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              setSearchTerm('');
              setStatusFilter('');
              setReturnReasonFilter('');
            }}
          >
            Clear Filters
          </Button>
        )}
      </Box>

      <Box sx={{ 
        height: 600, 
        width: '100%',
        overflow: 'auto', // Allow horizontal scrolling
        '& .MuiDataGrid-root': {
          width: '100%',
          minWidth: '830px', // Ensure minimum width for all columns
        },
        '& .MuiDataGrid-columnHeaders': {
          backgroundColor: 'background.paper',
          borderBottom: '2px solid',
          borderColor: 'divider',
        },
        '& .MuiDataGrid-cell': {
          borderColor: 'divider',
        },
        // Override DataTable's StyledTableContainer overflow hidden
        '& .MuiTableContainer-root': {
          overflow: 'auto !important', // Allow horizontal scrolling
        },
        '& .MuiTable-root': {
          minWidth: '830px', // Ensure table has minimum width
        },
      }}>
        <DataTable
          rows={returns}
          columns={columns}
          loading={loading}
          page={page}
          pageSize={pageSize}
          totalCount={totalCount}
          onPageChange={setPage}
          onPageSizeChange={setPageSize}
          getRowId={(row) => row.grn_return_id}
        />
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {menuItems.map((item, index) => (
          <MenuItem
            key={index}
            onClick={item.onClick}
            disabled={item.disabled}
            sx={{ color: item.color }}
          >
            <ListItemIcon sx={{ color: 'inherit' }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText>{item.label}</ListItemText>
          </MenuItem>
        ))}
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Delete Return Note</DialogTitle>
        <DialogContent>
          Are you sure you want to delete return note {selectedReturn?.grn_return_number}?
          This action cannot be undone.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteReturn} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Approve Confirmation Dialog */}
      <Dialog open={approveDialogOpen} onClose={() => setApproveDialogOpen(false)}>
        <DialogTitle>Approve Return Note</DialogTitle>
        <DialogContent>
          Are you sure you want to approve return note {selectedReturn?.grn_return_number}?
          Once approved, the return can be processed for shipment back to the vendor.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApproveDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleApproveReturn} color="primary" variant="contained">
            Approve
          </Button>
        </DialogActions>
      </Dialog>

      {/* Mark as Returned Confirmation Dialog */}
      <Dialog open={markReturnedDialogOpen} onClose={() => setMarkReturnedDialogOpen(false)}>
        <DialogTitle>Mark as Returned</DialogTitle>
        <DialogContent>
          Are you sure you want to mark return note {selectedReturn?.grn_return_number} as returned to vendor?
          This indicates that the goods have been physically shipped back to the vendor.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMarkReturnedDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleMarkAsReturned} color="primary" variant="contained">
            Mark as Returned
          </Button>
        </DialogActions>
      </Dialog>

      {/* Post to Inventory Confirmation Dialog */}
      <Dialog open={postDialogOpen} onClose={() => setPostDialogOpen(false)}>
        <DialogTitle>Post to Inventory</DialogTitle>
        <DialogContent>
          Are you sure you want to post return note {selectedReturn?.grn_return_number} to inventory?
          This will:
          <ul>
            <li>Reduce inventory quantities for returned items</li>
            <li>Create stock transactions for audit trail</li>
            <li>Generate journal entries for accounting</li>
          </ul>
          This action cannot be undone.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPostDialogOpen(false)}>Cancel</Button>
          <Button onClick={handlePostToInventory} color="primary" variant="contained">
            Post to Inventory
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default GoodsReturnPage; 