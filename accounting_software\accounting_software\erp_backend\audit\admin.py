"""
Django Admin Interface for Audit Trail Management

Provides comprehensive admin interface for:
- Viewing audit logs with advanced filtering
- User session management
- Compliance report generation
- Security event monitoring

Required for audit review and compliance verification.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
import json

from .models import AuditLog, UserSession

@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for Audit Log entries.
    
    Provides comprehensive view of all system activities with:
    - Advanced filtering and search
    - Risk level highlighting
    - Change tracking visualization
    - Export capabilities
    """
    
    list_display = [
        'timestamp', 'user_display', 'action_display', 'description_short',
        'module', 'risk_level_display', 'is_financial_display', 'ip_address'
    ]
    
    list_filter = [
        'action', 'module', 'risk_level', 'is_financial', 'timestamp',
        'user', 'requires_approval', 'is_approved'
    ]
    
    search_fields = [
        'description', 'user__username', 'user__email', 'ip_address',
        'metadata', 'audit_id'
    ]
    
    readonly_fields = [
        'audit_id', 'timestamp', 'user', 'action', 'description',
        'content_type', 'object_id', 'old_values', 'new_values',
        'ip_address', 'user_agent', 'session_key', 'module',
        'risk_level', 'metadata', 'is_financial'
    ]
    
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('audit_id', 'timestamp', 'user', 'action', 'description'),
        }),
        ('Affected Object', {
            'fields': ('content_type', 'object_id', 'affected_object_link'),
            'classes': ['collapse']
        }),
        ('Data Changes', {
            'fields': ('changes_display', 'old_values_display', 'new_values_display'),
            'classes': ['collapse']
        }),
        ('Technical Details', {
            'fields': ('ip_address', 'user_agent', 'session_key'),
            'classes': ['collapse']
        }),
        ('Business Context', {
            'fields': ('module', 'risk_level', 'is_financial', 'metadata_display'),
        }),
        ('Approval Workflow', {
            'fields': ('requires_approval', 'is_approved', 'approved_by', 'approved_at'),
            'classes': ['collapse']
        }),
    )
    
    def user_display(self, obj):
        """Display user with link to user admin."""
        if obj.user:
            url = reverse('admin:auth_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return "System/Anonymous"
    user_display.short_description = 'User'
    
    def action_display(self, obj):
        """Display action with color coding."""
        colors = {
            'CREATE': '#4caf50',      # Green
            'UPDATE': '#2196f3',      # Blue  
            'DELETE': '#f44336',      # Red
            'POST_JOURNAL': '#ff9800', # Orange
            'LOGIN': '#9c27b0',       # Purple
            'LOGIN_FAILED': '#f44336', # Red
        }
        color = colors.get(obj.action, '#757575')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_action_display()
        )
    action_display.short_description = 'Action'
    
    def description_short(self, obj):
        """Display truncated description."""
        if len(obj.description) > 80:
            return obj.description[:80] + "..."
        return obj.description
    description_short.short_description = 'Description'
    
    def risk_level_display(self, obj):
        """Display risk level with color coding."""
        colors = {
            'LOW': '#4caf50',      # Green
            'MEDIUM': '#ff9800',   # Orange
            'HIGH': '#f44336',     # Red
            'CRITICAL': '#9c27b0', # Purple
        }
        color = colors.get(obj.risk_level, '#757575')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_risk_level_display()
        )
    risk_level_display.short_description = 'Risk Level'
    
    def is_financial_display(self, obj):
        """Display financial flag with icon."""
        if obj.is_financial:
            return format_html('<span style="color: #ff9800;">💰 Financial</span>')
        return format_html('<span style="color: #757575;">📋 General</span>')
    is_financial_display.short_description = 'Data Type'
    
    def affected_object_link(self, obj):
        """Link to the affected object's admin page."""
        if obj.content_type and obj.object_id:
            try:
                model_class = obj.content_type.model_class()
                app_label = obj.content_type.app_label
                model_name = obj.content_type.model
                
                url = reverse(f'admin:{app_label}_{model_name}_change', args=[obj.object_id])
                return format_html('<a href="{}" target="_blank">View Object</a>', url)
            except:
                return "Object not found"
        return "N/A"
    affected_object_link.short_description = 'View Affected Object'
    
    def changes_display(self, obj):
        """Display summary of changes."""
        return obj.get_changes_summary()
    changes_display.short_description = 'Changes Summary'
    
    def old_values_display(self, obj):
        """Display old values in formatted JSON."""
        if obj.old_values:
            return format_html('<pre>{}</pre>', json.dumps(obj.old_values, indent=2))
        return "N/A"
    old_values_display.short_description = 'Old Values'
    
    def new_values_display(self, obj):
        """Display new values in formatted JSON."""
        if obj.new_values:
            return format_html('<pre>{}</pre>', json.dumps(obj.new_values, indent=2))
        return "N/A"
    new_values_display.short_description = 'New Values'
    
    def metadata_display(self, obj):
        """Display metadata in formatted JSON."""
        if obj.metadata:
            return format_html('<pre>{}</pre>', json.dumps(obj.metadata, indent=2))
        return "N/A"
    metadata_display.short_description = 'Metadata'
    
    def has_add_permission(self, request):
        """Audit logs cannot be manually created."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Audit logs cannot be modified."""
        return False
    
    def has_delete_permission(self, request, obj=None):
        """Only superusers can delete audit logs (for data retention)."""
        return request.user.is_superuser
    
    actions = ['mark_as_reviewed', 'export_audit_logs']
    
    def mark_as_reviewed(self, request, queryset):
        """Mark selected audit logs as reviewed."""
        count = queryset.update(metadata__reviewed_by=request.user.username)
        self.message_user(request, f'{count} audit logs marked as reviewed.')
    mark_as_reviewed.short_description = "Mark selected logs as reviewed"
    
    def export_audit_logs(self, request, queryset):
        """Export selected audit logs."""
        # This would integrate with your export functionality
        self.message_user(request, f'Export functionality would export {queryset.count()} logs.')
    export_audit_logs.short_description = "Export selected logs"

@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """
    Admin interface for User Session tracking.
    
    Provides visibility into user sessions for:
    - Security monitoring
    - Concurrent session detection
    - Session duration analysis
    """
    
    list_display = [
        'user', 'login_time', 'logout_time', 'session_duration_display',
        'ip_address', 'is_active', 'logout_reason'
    ]
    
    list_filter = [
        'is_active', 'logout_reason', 'login_time', 'user'
    ]
    
    search_fields = [
        'user__username', 'user__email', 'ip_address', 'session_key'
    ]
    
    readonly_fields = [
        'user', 'session_key', 'login_time', 'logout_time',
        'last_activity', 'ip_address', 'user_agent', 'session_duration_display'
    ]
    
    date_hierarchy = 'login_time'
    ordering = ['-login_time']
    
    def session_duration_display(self, obj):
        """Display session duration in human-readable format."""
        duration = obj.get_session_duration()
        
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    session_duration_display.short_description = 'Duration'
    
    def has_add_permission(self, request):
        """Sessions cannot be manually created."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Sessions cannot be modified."""
        return False

# ComplianceReport admin will be added in a future version

# Custom admin site for audit trail
class AuditTrailAdminSite(admin.AdminSite):
    """Custom admin site for audit trail management."""
    site_header = "Audit Trail Administration"
    site_title = "Audit Trail Admin"
    index_title = "Audit Trail Management"
    
    def index(self, request, extra_context=None):
        """Custom admin index with audit statistics."""
        extra_context = extra_context or {}
        
        # Get audit statistics
        now = timezone.now()
        today = now.date()
        week_ago = today - timedelta(days=7)
        
        stats = {
            'total_logs': AuditLog.objects.count(),
            'today_logs': AuditLog.objects.filter(timestamp__date=today).count(),
            'week_logs': AuditLog.objects.filter(timestamp__date__gte=week_ago).count(),
            'financial_logs': AuditLog.objects.filter(is_financial=True).count(),
            'high_risk_logs': AuditLog.objects.filter(risk_level__in=['HIGH', 'CRITICAL']).count(),
            'active_sessions': UserSession.objects.filter(is_active=True).count(),
            'failed_logins_today': AuditLog.objects.filter(
                action='LOGIN_FAILED',
                timestamp__date=today
            ).count(),
        }
        
        extra_context['audit_stats'] = stats
        
        return super().index(request, extra_context) 