from django.contrib import admin
from .models import (
    UnitsOfMeasure, Warehouse, Inventory, StockTransaction,
    GoodsReceiptNote, GoodsReceiptNoteItem, VendorInvoice,
    GoodsReturnNote, GoodsReturnNoteItem, InventoryTransfer,
    InventoryTransferItem
)


@admin.register(UnitsOfMeasure)
class UnitsOfMeasureAdmin(admin.ModelAdmin):
    list_display = ['unit_name', 'abbreviation', 'unit_type', 'is_active']
    list_filter = ['unit_type', 'is_active']
    search_fields = ['unit_name', 'abbreviation']


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = ['name', 'warehouse_code', 'warehouse_type', 'location', 'is_active']
    list_filter = ['warehouse_type', 'is_active']
    search_fields = ['name', 'warehouse_code', 'location']


@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ['product', 'warehouse', 'quantity_on_hand', 'quantity_reserved', 'reorder_point']
    list_filter = ['warehouse', 'product__category']
    search_fields = ['product__name', 'product__sku', 'warehouse__name']


@admin.register(StockTransaction)
class StockTransactionAdmin(admin.ModelAdmin):
    list_display = ['product', 'warehouse', 'transaction_type', 'quantity', 'txn_date']
    list_filter = ['transaction_type', 'warehouse', 'txn_date']
    search_fields = ['product__name', 'product__sku']
    readonly_fields = ['total_cost']


@admin.register(GoodsReceiptNote)
class GoodsReceiptNoteAdmin(admin.ModelAdmin):
    list_display = ['grn_number', 'purchase_order', 'warehouse', 'receipt_date', 'status']
    list_filter = ['status', 'warehouse', 'receipt_date']
    search_fields = ['grn_number', 'purchase_order__po_number']


@admin.register(GoodsReceiptNoteItem)
class GoodsReceiptNoteItemAdmin(admin.ModelAdmin):
    list_display = ['grn', 'product', 'quantity_ordered', 'quantity_received', 'condition']
    list_filter = ['condition', 'grn__status']
    search_fields = ['product__name', 'grn__grn_number']


@admin.register(VendorInvoice)
class VendorInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'purchase_order', 'invoice_date', 'total_amount', 'status']
    list_filter = ['status', 'invoice_date']
    search_fields = ['invoice_number', 'purchase_order__po_number']


class GoodsReturnNoteItemInline(admin.TabularInline):
    model = GoodsReturnNoteItem
    extra = 0
    fields = ['product', 'original_grn_item', 'quantity_received', 'quantity_returned', 
              'return_reason', 'condition_notes', 'photos_attached']
    readonly_fields = ['return_value']


@admin.register(GoodsReturnNote)
class GoodsReturnNoteAdmin(admin.ModelAdmin):
    list_display = [
        'grn_return_number', 'vendor', 'original_grn', 'warehouse', 
        'return_date', 'return_reason', 'status', 'total_value'
    ]
    list_filter = ['status', 'return_reason', 'warehouse', 'return_date', 'vendor']
    search_fields = [
        'grn_return_number', 'vendor__name', 'vendor__display_name',
        'original_grn__grn_number', 'notes'
    ]
    readonly_fields = [
        'grn_return_number', 'created_at', 'updated_at', 'approved_at', 
        'posted_at', 'total_quantity', 'total_value'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('grn_return_number', 'original_grn', 'vendor', 'warehouse')
        }),
        ('Return Details', {
            'fields': ('return_date', 'return_reason', 'status', 'notes')
        }),
        ('Totals', {
            'fields': ('total_quantity', 'total_value', 'expected_credit_amount', 
                      'actual_credit_amount', 'credit_received_date')
        }),
        ('Workflow', {
            'fields': ('returned_by', 'approved_by', 'approved_at', 'posted_by', 'posted_at')
        }),
        ('Audit Trail', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    inlines = [GoodsReturnNoteItemInline]
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new record
            obj.returned_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(GoodsReturnNoteItem)
class GoodsReturnNoteItemAdmin(admin.ModelAdmin):
    list_display = [
        'grn_return', 'product', 'quantity_received', 'quantity_returned', 
        'return_reason', 'return_value', 'photos_attached'
    ]
    list_filter = ['return_reason', 'photos_attached', 'grn_return__status']
    search_fields = [
        'product__name', 'product__sku', 'grn_return__grn_return_number',
        'original_grn_item__grn__grn_number', 'condition_notes'
    ]
    readonly_fields = ['return_value']
    
    fieldsets = (
        ('Item Information', {
            'fields': ('grn_return', 'product', 'original_grn_item')
        }),
        ('Quantities', {
            'fields': ('quantity_received', 'quantity_returned', 'unit_cost', 'return_value')
        }),
        ('Return Details', {
            'fields': ('return_reason', 'batch_number', 'condition_notes', 'photos_attached')
        }),
        ('Display Order', {
            'fields': ('line_order',),
            'classes': ('collapse',)
        })
    )


@admin.register(InventoryTransfer)
class InventoryTransferAdmin(admin.ModelAdmin):
    list_display = ['transfer_number', 'from_warehouse', 'to_warehouse', 'transfer_date', 'status', 'total_items']
    list_filter = ['status', 'from_warehouse', 'to_warehouse', 'transfer_date']
    search_fields = ['transfer_number', 'notes']
    readonly_fields = ['transfer_number', 'total_quantity', 'total_items', 'created_at', 'updated_at']


@admin.register(InventoryTransferItem)
class InventoryTransferItemAdmin(admin.ModelAdmin):
    list_display = ['transfer', 'product', 'quantity', 'batch_number']
    list_filter = ['transfer__status', 'product']
    search_fields = ['transfer__transfer_number', 'product__name', 'product__sku'] 