import React, { useState } from 'react';
import { 
  Typo<PERSON>,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  Box,
  Chip,
  IconButton,
  Grid,
  InputAdornment
} from '@mui/material';
import { 
  Search as SearchIcon, 
  Add as AddIcon, 
  Check as CheckIcon, 
  Close as CloseIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';

// Sample time off requests
const initialRequests = [
  { id: 1, employee: "<PERSON>", type: "Vacation", startDate: "2024-04-15", endDate: "2024-04-20", days: 5, status: "Pending" },
  { id: 2, employee: "<PERSON>", type: "Sick Leave", startDate: "2024-04-08", endDate: "2024-04-09", days: 2, status: "Approved" },
  { id: 3, employee: "<PERSON>", type: "Personal", startDate: "2024-05-01", endDate: "2024-05-01", days: 1, status: "Approved" },
  { id: 4, employee: "<PERSON>", type: "Vacation", startDate: "2024-06-10", endDate: "2024-06-17", days: 7, status: "Pending" },
  { id: 5, employee: "<PERSON>", type: "Sick Leave", startDate: "2024-04-03", endDate: "2024-04-04", days: 2, status: "Rejected" },
];

const TimeOffPage: React.FC = () => {
  const [requests] = useState(initialRequests);
  const [searchTerm, setSearchTerm] = useState('');
  
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  const filteredRequests = requests.filter(request => 
    request.employee.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'Approved': return 'success';
      case 'Rejected': return 'error';
      case 'Pending': return 'warning';
      default: return 'default';
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Time Off</Typography>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
        >
          Request Time Off
        </Button>
      </Box>

      <Box sx={{ display: 'flex', mb: 3, gap: 2 }}>
        <TextField
          placeholder="Search requests..."
          variant="outlined"
          fullWidth
          size="small"
          value={searchTerm}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <Button 
          variant="outlined" 
          startIcon={<FilterListIcon />}
        >
          Filter
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, bgcolor: 'success.light', color: 'success.contrastText' }}>
            <Typography variant="subtitle2" fontWeight="medium">Available PTO</Typography>
            <Typography variant="h4">15 days</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
            <Typography variant="subtitle2" fontWeight="medium">Used PTO</Typography>
            <Typography variant="h4">8 days</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, bgcolor: 'secondary.light', color: 'secondary.contrastText' }}>
            <Typography variant="subtitle2" fontWeight="medium">Pending Requests</Typography>
            <Typography variant="h4">2</Typography>
          </Paper>
        </Grid>
      </Grid>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Employee</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Start Date</TableCell>
              <TableCell>End Date</TableCell>
              <TableCell>Days</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredRequests.map((request) => (
              <TableRow key={request.id}>
                <TableCell component="th" scope="row">
                  {request.employee}
                </TableCell>
                <TableCell>{request.type}</TableCell>
                <TableCell>{new Date(request.startDate).toLocaleDateString()}</TableCell>
                <TableCell>{new Date(request.endDate).toLocaleDateString()}</TableCell>
                <TableCell>{request.days}</TableCell>
                <TableCell>
                  <Chip 
                    label={request.status} 
                    color={getStatusColor(request.status) as "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning"}
                    size="small"
                  />
                </TableCell>
                <TableCell align="right">
                  {request.status === "Pending" && (
                    <>
                      <IconButton size="small" color="success">
                        <CheckIcon fontSize="small" />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default TimeOffPage;