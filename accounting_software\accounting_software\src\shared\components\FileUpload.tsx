import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  LinearProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  styled,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  InsertDriveFile as FileIcon,
  Image as ImageIcon,
  PictureAsPdf as PdfIcon,
  Description as DocIcon,
} from '@mui/icons-material';
import { alpha as muiAlpha } from '@mui/material/styles';

// Define custom alpha function for string colors
const alpha = (color: string, opacity: number): string => {
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return color;
};

interface DropZoneProps {
  theme: any;
  isdragactive: string;
}

const DropZone = styled(Paper)<{ isdragactive: string }>(({ theme, isdragactive }) => ({
  border: `2px dashed ${isdragactive === 'true' ? theme.palette.primary.main : theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  backgroundColor: isdragactive === 'true' ? muiAlpha(theme.palette.primary.main, 0.05) : theme.palette.background.default,
  padding: theme.spacing(3),
  textAlign: 'center',
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    borderColor: theme.palette.primary.main,
    backgroundColor: muiAlpha(theme.palette.primary.main, 0.05),
  },
}));

const FilePreview = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  marginTop: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.default,
}));

const FileItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(1),
  backgroundColor: theme.palette.background.paper,
  '&:last-child': {
    marginBottom: 0,
  },
}));

const ProgressBar = styled(LinearProgress)(({ theme }) => ({
  marginTop: theme.spacing(1),
  height: 8,
  borderRadius: 4,
}));

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to get file icon based on type
const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) return <ImageIcon color="primary" />;
  if (fileType === 'application/pdf') return <PdfIcon color="error" />;
  if (fileType.includes('document') || fileType.includes('sheet')) return <DocIcon color="success" />;
  return <FileIcon color="action" />;
};

interface FileUploadProps {
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedFileTypes?: string[];
  onFilesChange: (files: File[]) => void;
  initialFiles?: File[];
  helperText?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  maxFiles = 5,
  maxSize = 20 * 1024 * 1024, // 20MB default
  acceptedFileTypes = [],
  onFilesChange,
  initialFiles = [],
  helperText,
}) => {
  const [files, setFiles] = useState<File[]>(initialFiles);
  const [isDragActive, setIsDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Simulate upload progress for demo purposes
    const progressIntervals: Array<ReturnType<typeof setInterval>> = [];
    
    try {
      files.forEach((file) => {
        if (uploadProgress[file.name] === undefined || uploadProgress[file.name] < 100) {
          const interval = setInterval(() => {
            setUploadProgress((prev) => {
              try {
                const currentProgress = prev[file.name] || 0;
                if (currentProgress >= 100) {
                  clearInterval(interval);
                  return prev;
                }
                return {
                  ...prev,
                  [file.name]: Math.min(currentProgress + 10, 100),
                };
              } catch (error) {
                console.error('Error updating upload progress:', error);
                return prev;
              }
            });
          }, 300);
          progressIntervals.push(interval);
        }
      });
    } catch (error) {
      console.error('Error setting up progress intervals:', error);
    }

    return () => {
      try {
        progressIntervals.forEach(clearInterval);
      } catch (error) {
        console.error('Error clearing intervals:', error);
      }
    };
  }, [files, uploadProgress]);

  useEffect(() => {
    try {
      onFilesChange(files);
    } catch (error) {
      console.error('Error in onFilesChange callback:', error);
      setError('An error occurred while updating files. Please try again.');
    }
  }, [files, onFilesChange]);

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const validateFile = (file: File): boolean => {
    // Check file size
    if (file.size > maxSize) {
      setError(`File "${file.name}" exceeds the maximum size of ${formatFileSize(maxSize)}`);
      return false;
    }

    // Check file type if acceptedFileTypes is provided
    if (acceptedFileTypes.length > 0 && !acceptedFileTypes.includes(file.type)) {
      setError(`File "${file.name}" is not an accepted file type`);
      return false;
    }

    return true;
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    setError(null);

    try {
      const droppedFiles = Array.from(e.dataTransfer.files);
      
      // Check if adding these files would exceed maxFiles
      if (files.length + droppedFiles.length > maxFiles) {
        setError(`You can only upload a maximum of ${maxFiles} files`);
        return;
      }

      // Validate each file
      const validFiles = droppedFiles.filter(validateFile);
      
      if (validFiles.length > 0) {
        setFiles((prevFiles) => [...prevFiles, ...validFiles]);
      }
    } catch (error) {
      console.error('Error handling dropped files:', error);
      setError('An error occurred while processing the files. Please try again.');
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    
    try {
      if (e.target.files) {
        const selectedFiles = Array.from(e.target.files);
        
        // Check if adding these files would exceed maxFiles
        if (files.length + selectedFiles.length > maxFiles) {
          setError(`You can only upload a maximum of ${maxFiles} files`);
          return;
        }

        // Validate each file
        const validFiles = selectedFiles.filter(validateFile);
        
        if (validFiles.length > 0) {
          setFiles((prevFiles) => [...prevFiles, ...validFiles]);
        }
      }
    } catch (error) {
      console.error('Error handling selected files:', error);
      setError('An error occurred while processing the files. Please try again.');
    }
  };

  const handleRemoveFile = (index: number) => {
    try {
      setFiles((prevFiles) => {
        const newFiles = [...prevFiles];
        newFiles.splice(index, 1);
        return newFiles;
      });
      
      // Reset error if files are removed
      if (error && error.includes('maximum')) {
        setError(null);
      }
    } catch (error) {
      console.error('Error removing file:', error);
      setError('An error occurred while removing the file. Please try again.');
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Box>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
        multiple={maxFiles > 1}
        accept={acceptedFileTypes.join(',')}
      />
      
      <DropZone
        isdragactive={isDragActive ? 'true' : 'false'}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Drag & Drop Files Here
        </Typography>
        <Typography variant="body2" color="textSecondary" gutterBottom>
          or
        </Typography>
        <Button
          variant="contained"
          component="span"
          startIcon={<CloudUploadIcon />}
          sx={{ mt: 1, borderRadius: 2 }}
        >
          Browse Files
        </Button>
        {helperText && (
          <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 1 }}>
            {helperText}
          </Typography>
        )}
      </DropZone>

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {files.length > 0 && (
        <FilePreview>
          <Typography variant="subtitle2" gutterBottom>
            Uploaded Files ({files.length}/{maxFiles})
          </Typography>
          <List>
            {files.map((file, index) => (
              <FileItem key={`${file.name}-${index}`}>
                {getFileIcon(file.type)}
                <ListItemText
                  primary={file.name}
                  secondary={formatFileSize(file.size)}
                  sx={{ ml: 2 }}
                />
                <ListItemSecondaryAction>
                  {uploadProgress[file.name] !== undefined && uploadProgress[file.name] < 100 ? (
                    <Box sx={{ width: 100 }}>
                      <ProgressBar variant="determinate" value={uploadProgress[file.name]} />
                    </Box>
                  ) : (
                    <Chip
                      label="Uploaded"
                      size="small"
                      color="success"
                      sx={{ mr: 1 }}
                    />
                  )}
                  <IconButton edge="end" onClick={() => handleRemoveFile(index)}>
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </FileItem>
            ))}
          </List>
        </FilePreview>
      )}
    </Box>
  );
};

export default FileUpload; 