import React from 'react';
import { Typography, TypographyProps } from '@mui/material';
import { formatCurrencyDisplay } from '../utils/formatters';

export interface CurrencyDisplayProps extends Omit<TypographyProps, 'children'> {
  value: number | string;
  currencyCode?: string;
  showSymbol?: boolean;
  decimalPlaces?: number;
  locale?: string;
  showBlankForZero?: boolean;
  colorize?: boolean; // Automatically color positive/negative values
  align?: 'left' | 'center' | 'right';
  prefix?: string; // Optional prefix text
  suffix?: string; // Optional suffix text
}

/**
 * CurrencyDisplay Component
 * 
 * A reusable component for displaying currency values with QuickBooks-style formatting:
 * - Shows blank for zero values
 * - Proper formatting with thousands separators for non-zero values
 * - Automatic right-alignment for better readability
 * - Optional color coding for positive/negative values
 * - Consistent styling across the application
 */
const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({
  value,
  currencyCode = 'USD',
  showSymbol = true,
  decimalPlaces = 2,
  locale,
  showBlankForZero = true,
  colorize = false,
  align = 'right',
  prefix,
  suffix,
  sx,
  ...typographyProps
}) => {
  // Convert value to number
  const numericValue = typeof value === 'string' ? parseFloat(value) || 0 : value || 0;
  
  // Format the currency value
  const formattedValue = formatCurrencyDisplay(numericValue, currencyCode, {
    showSymbol,
    decimalPlaces,
    locale,
  });
  
  // Handle blank display for zero values
  if (showBlankForZero && numericValue === 0) {
    return (
      <Typography
        {...typographyProps}
        sx={{
          textAlign: align,
          fontFamily: 'monospace',
          ...sx,
        }}
      >
        {prefix}{suffix}
      </Typography>
    );
  }
  
  // Determine color based on value
  let color = typographyProps.color;
  if (colorize && !color) {
    if (numericValue > 0) {
      color = 'success.main';
    } else if (numericValue < 0) {
      color = 'error.main';
    } else {
      color = 'text.primary';
    }
  }
  
  // Build display content
  const displayContent = `${prefix || ''}${formattedValue}${suffix || ''}`;
  
  return (
    <Typography
      {...typographyProps}
      color={color}
      sx={{
        textAlign: align,
        fontFamily: 'monospace',
        fontWeight: numericValue !== 0 ? 'medium' : 'normal',
        ...sx,
      }}
    >
      {displayContent}
    </Typography>
  );
};

export default CurrencyDisplay; 