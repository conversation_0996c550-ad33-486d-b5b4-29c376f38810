DEBUG 2025-06-17 18:32:26,770 utils 2780 6928 (0.000) SELECT COUNT(*) AS "__count" FROM "sales_payment_terms"; args=(); alias=default
DEBUG 2025-06-17 18:32:26,785 utils 2780 6928 (0.000) SELECT "sales_payment_terms"."id", "sales_payment_terms"."name", "sales_payment_terms"."code", "sales_payment_terms"."days", "sales_payment_terms"."description", "sales_payment_terms"."is_default", "sales_payment_terms"."is_active", "sales_payment_terms"."created_at", "sales_payment_terms"."updated_at", "sales_payment_terms"."created_by_id" FROM "sales_payment_terms" ORDER BY "sales_payment_terms"."days" ASC, "sales_payment_terms"."name" ASC; args=(); alias=default
