# Chart of Accounts Integration - Purchase Product Form

## Overview
The Purchase Product Form now includes full Chart of Accounts (COA) integration, allowing users to select appropriate GL accounts for inventory and expense tracking.

## Features Implemented

### 1. **Purchase/Expense Account Selection**
- **Purpose**: Records purchase transactions and cost of goods sold
- **Account Types**: Filters for Expenses, Cost of Goods Sold, and accounts containing "purchase" or "expense"
- **Required Field**: Must be selected for all products
- **Display**: Shows account number, name, type, and detail type

### 2. **Inventory Asset Account Selection**
- **Purpose**: Tracks the asset value of inventory stock
- **Account Types**: Filters for Asset accounts containing "inventory" or "stock"
- **Conditional**: Only enabled when "Track Inventory" is checked
- **Display**: Shows account number, name, type, and current balance

### 3. **Real-time Account Loading**
- Fetches accounts from the GL module using `loadChartOfAccountsFast()` API
- Loading indicators during account fetch
- Error handling for failed account loading

### 4. **Enhanced User Experience**
- **Autocomplete Dropdowns**: Easy search and selection of accounts
- **Rich Display**: Account number, name, type, and additional details
- **Contextual Help**: Info alerts explaining account purposes
- **Conditional Logic**: Inventory account only available when tracking inventory

## Technical Implementation

### API Integration
```typescript
// Loads Chart of Accounts from GL module
const accountsData = await loadChartOfAccountsFast();
setAccounts(accountsData.accounts || []);
```

### Account Filtering
```typescript
// Purchase/Expense Account Filter
accounts.filter(account => 
  account.account_type_name === 'Expenses' || 
  account.account_type_name === 'Cost of Goods Sold' ||
  account.account_name.toLowerCase().includes('purchase') ||
  account.account_name.toLowerCase().includes('expense')
)

// Inventory Asset Account Filter
accounts.filter(account => 
  account.account_type_name === 'Assets' && (
    account.account_name.toLowerCase().includes('inventory') ||
    account.account_name.toLowerCase().includes('stock') ||
    account.detail_type_name?.toLowerCase().includes('inventory')
  )
)
```

### Form Integration
- Uses Formik for form state management
- Autocomplete components with Material-UI
- Real-time validation and error handling
- Proper field mapping to backend models

## Account Structure Expected

### Typical Expense Accounts
- `5001 - Cost of Goods Sold`
- `5100 - Purchase Expenses`
- `5200 - Inventory Purchases`

### Typical Inventory Accounts
- `1300 - Inventory - Raw Materials`
- `1310 - Inventory - Finished Goods`
- `1320 - Inventory - Work in Progress`

## Benefits

1. **Accurate Financial Reporting**: Proper GL account mapping ensures accurate financial statements
2. **Automated Journal Entries**: Purchase transactions automatically post to correct accounts
3. **Inventory Valuation**: Real-time inventory asset tracking
4. **Compliance**: Follows standard accounting practices and GAAP principles
5. **Integration**: Seamless connection between Purchase and GL modules

## Usage Instructions

1. **Create Product**: Navigate to Purchase → Products → New Product
2. **Fill Basic Info**: Complete product name, SKU, pricing, etc.
3. **Accounting Configuration**: 
   - Select appropriate Purchase/Expense Account (required)
   - If tracking inventory, select Inventory Asset Account
4. **Save**: Product is created with proper GL account mappings

## Error Handling

- **Account Loading Errors**: Displays error message if COA fails to load
- **Validation**: Required field validation for expense account
- **Fallback**: Graceful degradation if accounts are unavailable

## Future Enhancements

1. **Account Creation**: Quick account creation from product form
2. **Default Mappings**: Category-based default account suggestions
3. **Multi-Currency**: Currency-specific account filtering
4. **Audit Trail**: Track account changes and assignments 