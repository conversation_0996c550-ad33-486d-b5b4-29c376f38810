from django.contrib import admin
from .models import SalesTax

@admin.register(SalesTax)
class SalesTaxAdmin(admin.ModelAdmin):
    list_display = ('tax_type', 'description', 'rate', 'remarks', 'created_by', 'created_at')
    list_filter = ('tax_type', 'created_by')
    search_fields = ('description', 'remarks')
    readonly_fields = ('created_by', 'created_at', 'updated_at')

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser
    def has_add_permission(self, request):
        return request.user.is_superuser
    def has_delete_permission(self, request, obj=None):
        return request.user.is_superuser
