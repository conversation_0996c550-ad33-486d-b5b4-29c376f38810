# Generated by Django 4.2.21 on 2025-06-24 21:19

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Full name or company name', max_length=255)),
                ('contact_type', models.CharField(choices=[('customer', 'Customer'), ('vendor', 'Vendor'), ('employee', 'Employee'), ('other', 'Other')], max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone', models.CharField(blank=True, max_length=50, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'contacts',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PriceListEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price_type', models.CharField(choices=[('sale', 'Sale'), ('cost', 'Cost')], max_length=10)),
                ('price', models.DecimalField(decimal_places=2, max_digits=18)),
                ('currency', models.CharField(default='USD', max_length=10)),
                ('valid_from', models.DateField()),
                ('valid_to', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('price_source', models.CharField(choices=[('manual', 'Manual'), ('auto', 'Auto'), ('rule', 'Rule')], default='manual', max_length=50)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'price_list_entries',
            },
        ),
        migrations.CreateModel(
            name='PricingRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rule_type', models.CharField(choices=[('quantity_discount', 'Quantity Discount'), ('promo_price', 'Promotional Price'), ('contract_price', 'Contract Price')], max_length=30)),
                ('min_quantity', models.DecimalField(decimal_places=2, default=1, max_digits=10)),
                ('discount_percent', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
            ],
            options={
                'db_table': 'pricing_rules',
            },
        ),
        migrations.CreateModel(
            name='ProductService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('sku', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('type', models.CharField(choices=[('product', 'Product'), ('service', 'Service')], max_length=20)),
                ('unit_of_measure', models.CharField(blank=True, max_length=50, null=True)),
                ('taxable', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'products_services',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('invoice', 'Invoice'), ('bill', 'Bill'), ('payment', 'Payment'), ('receipt', 'Receipt')], max_length=20)),
                ('transaction_date', models.DateField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('total_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=18, null=True)),
                ('reference_no', models.CharField(blank=True, max_length=100, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('contact', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='contacts.contact')),
            ],
            options={
                'db_table': 'transactions',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('contact', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='contacts.contact')),
            ],
            options={
                'db_table': 'customers',
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('contact', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='contacts.contact')),
                ('position', models.CharField(blank=True, max_length=100, null=True)),
                ('department', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'db_table': 'employees',
            },
        ),
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('contact', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='contacts.contact')),
            ],
            options={
                'db_table': 'vendors',
            },
        ),
        migrations.CreateModel(
            name='TransactionLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('quantity', models.DecimalField(decimal_places=2, default=1, max_digits=12)),
                ('unit_price', models.DecimalField(blank=True, decimal_places=2, max_digits=18, null=True)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('total', models.DecimalField(blank=True, decimal_places=2, max_digits=18, null=True)),
                ('gl_account_id', models.IntegerField(blank=True, null=True)),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transaction_lines', to='contacts.productservice')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='contacts.transaction')),
            ],
            options={
                'db_table': 'transaction_lines',
                'ordering': ['id'],
            },
        ),
        migrations.AddIndex(
            model_name='productservice',
            index=models.Index(fields=['type'], name='products_se_type_8bab6a_idx'),
        ),
        migrations.AddIndex(
            model_name='productservice',
            index=models.Index(fields=['sku'], name='products_se_sku_a5586c_idx'),
        ),
        migrations.AddIndex(
            model_name='productservice',
            index=models.Index(fields=['name'], name='products_se_name_ddfdfa_idx'),
        ),
        migrations.AddField(
            model_name='pricingrule',
            name='customer',
            field=models.ForeignKey(blank=True, limit_choices_to={'contact_type': 'customer'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pricing_rules', to='contacts.contact'),
        ),
        migrations.AddField(
            model_name='pricingrule',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pricing_rules', to='contacts.productservice'),
        ),
        migrations.AddField(
            model_name='pricelistentry',
            name='customer',
            field=models.ForeignKey(blank=True, limit_choices_to={'contact_type': 'customer'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_prices', to='contacts.contact'),
        ),
        migrations.AddField(
            model_name='pricelistentry',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_entries', to='contacts.productservice'),
        ),
        migrations.AddField(
            model_name='pricelistentry',
            name='vendor',
            field=models.ForeignKey(blank=True, limit_choices_to={'contact_type': 'vendor'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='vendor_prices', to='contacts.contact'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['contact_type'], name='contacts_contact_8d5bcc_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['name'], name='contacts_name_084782_idx'),
        ),
        migrations.AddIndex(
            model_name='contact',
            index=models.Index(fields=['email'], name='contacts_email_2eb381_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_type', 'status'], name='transaction_transac_9f5934_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['contact', 'transaction_date'], name='transaction_contact_584323_idx'),
        ),
        migrations.AddIndex(
            model_name='transaction',
            index=models.Index(fields=['transaction_date'], name='transaction_transac_0d716a_idx'),
        ),
        migrations.AddIndex(
            model_name='pricingrule',
            index=models.Index(fields=['product', 'start_date'], name='pricing_rul_product_243426_idx'),
        ),
        migrations.AddIndex(
            model_name='pricingrule',
            index=models.Index(fields=['customer', 'start_date'], name='pricing_rul_custome_a69d9c_idx'),
        ),
        migrations.AddIndex(
            model_name='pricingrule',
            index=models.Index(fields=['rule_type', 'start_date'], name='pricing_rul_rule_ty_9d4210_idx'),
        ),
        migrations.AddIndex(
            model_name='pricelistentry',
            index=models.Index(fields=['product', 'price_type', 'valid_from'], name='price_list__product_5155b4_idx'),
        ),
        migrations.AddIndex(
            model_name='pricelistentry',
            index=models.Index(fields=['customer', 'price_type'], name='price_list__custome_44d902_idx'),
        ),
        migrations.AddIndex(
            model_name='pricelistentry',
            index=models.Index(fields=['vendor', 'price_type'], name='price_list__vendor__ec11cb_idx'),
        ),
        migrations.AddIndex(
            model_name='pricelistentry',
            index=models.Index(fields=['is_active', 'valid_from', 'valid_to'], name='price_list__is_acti_be4919_idx'),
        ),
    ]
