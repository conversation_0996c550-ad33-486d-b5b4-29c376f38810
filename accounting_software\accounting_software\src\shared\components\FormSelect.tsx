import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectProps,
  Box,
  Typography,
} from '@mui/material';

export interface SelectOption {
  value: string | number;
  label: string;
  description?: string;
  disabled?: boolean;
  [key: string]: any;
}

export interface FormSelectProps extends Omit<SelectProps, 'variant'> {
  label: string;
  options: SelectOption[];
  fullWidth?: boolean;
  required?: boolean;
  helperText?: string;
  error?: boolean;
  variant?: 'outlined' | 'filled' | 'standard';
  size?: 'small' | 'medium';
  showDescription?: boolean;
  emptyOption?: string;
  emptyOptionValue?: string | number;
}

const FormSelect: React.FC<FormSelectProps> = ({
  label,
  options,
  fullWidth = true,
  required = false,
  helperText,
  error = false,
  variant = 'outlined',
  size = 'medium',
  showDescription = false,
  emptyOption,
  emptyOptionValue = '',
  value,
  ...props
}) => {
  const controlId = `form-select-${label.toLowerCase().replace(/\s+/g, '-')}`;
  
  return (
    <FormControl 
      fullWidth={fullWidth} 
      required={required} 
      error={error}
      variant={variant}
      size={size}
    >
      <InputLabel id={controlId}>{label}</InputLabel>
      <Select
        labelId={controlId}
        label={label}
        value={value}
        {...props}
      >
        {emptyOption && (
          <MenuItem value={emptyOptionValue}>
            <em>{emptyOption}</em>
          </MenuItem>
        )}
        
        {options.map((option) => (
          <MenuItem 
            key={option.value} 
            value={option.value}
            disabled={option.disabled}
          >
            {showDescription && option.description ? (
              <Box>
                <Typography variant="body2">
                  {option.label}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {option.description}
                </Typography>
              </Box>
            ) : (
              option.label
            )}
          </MenuItem>
        ))}
      </Select>
      
      {helperText && (
        <FormHelperText>{helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default FormSelect; 