import React, { useState } from 'react';
import {
  AppBar,
  Too<PERSON>bar,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  styled,
  Badge,
  Tooltip,
  InputBase,
  Typography,
} from '@mui/material';
import {
  AccountCircle,
  Logout,
  Settings,
  NotificationsOutlined,
  HelpOutlineOutlined,
  Search,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  zIndex: theme.zIndex.drawer + 1,
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  boxShadow: '0px 1px 3px rgba(0,0,0,0.1)',
  position: 'relative',
  height: 64,
}));

const StyledToolbar = styled(Toolbar)({
  display: 'flex',
  justifyContent: 'space-between',
  gap: '8px',
});

const SearchInput = styled(InputBase)(({ theme }) => ({
  flex: 1,
  maxWidth: '600px',
  padding: theme.spacing(1),
  paddingLeft: theme.spacing(2),
  backgroundColor: theme.palette.grey[100],
  borderRadius: theme.shape.borderRadius,
  '&:hover': {
    backgroundColor: theme.palette.grey[200],
  },
}));

const RightSection = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
});

const Topbar: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const { user, isAuthenticated, isLoading, logout } = useAuth();

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
    navigate('/login');
  };

  const handleProfile = () => {
    handleClose();
    console.log('Navigate to profile settings');
  };

  const getAvatarInitials = (user: any) => {
    if (user && user.first_name && user.last_name) {
      return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`.toUpperCase();
    } else if (user && user.username) {
      return user.username.charAt(0).toUpperCase();
    } else {
      return 'U';
    }
  };

  const getUserDisplayName = (user: any) => {
    if (user && user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    } else if (user && user.username) {
      return user.username;
    } else {
      return 'Guest';
    }
  };

  return (
    <StyledAppBar position="fixed">
      <StyledToolbar>
        <SearchInput
          placeholder="Search transactions, invoices, customers..."
          startAdornment={<Search sx={{ mr: 1, color: 'text.secondary' }} />}
        />
        <RightSection>
          <Tooltip title="Help">
            <IconButton color="default">
              <HelpOutlineOutlined />
            </IconButton>
          </Tooltip>
          <Tooltip title="Notifications">
            <IconButton color="default">
              <Badge badgeContent={4} color="error">
                <NotificationsOutlined />
              </Badge>
            </IconButton>
          </Tooltip>
          {!isLoading && isAuthenticated && user ? (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Typography variant="body1" sx={{ marginRight: 1 }}>
                {getUserDisplayName(user)}
              </Typography>
              <Tooltip title="Account settings">
                <IconButton
                  size="large"
                  aria-label="account of current user"
                  aria-controls="menu-appbar"
                  aria-haspopup="true"
                  onClick={handleMenu}
                  color="inherit"
                >
                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                    {getAvatarInitials(user)}
                  </Avatar>
                </IconButton>
              </Tooltip>
              <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
              >
                <MenuItem onClick={handleProfile}>
                  <Settings fontSize="small" sx={{ mr: 1 }} />
                  Profile Settings
                </MenuItem>
                <MenuItem onClick={handleLogout}>
                  <Logout fontSize="small" sx={{ mr: 1 }} />
                  Logout
                </MenuItem>
              </Menu>
            </Box>
          ) : (
            <Box>
               <Tooltip title="Account">
                 <IconButton color="default" onClick={() => navigate('/login')}>
                    <AccountCircle />
                 </IconButton>
               </Tooltip>
            </Box>
          )}
        </RightSection>
      </StyledToolbar>
    </StyledAppBar>
  );
};

export default Topbar; 