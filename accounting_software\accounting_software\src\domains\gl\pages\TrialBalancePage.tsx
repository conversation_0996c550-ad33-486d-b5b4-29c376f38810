import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  Alert,
  AlertTitle,
  Chip,
  Divider,
  Grid,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  Stack,
  Avatar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  ToggleButton,
  ToggleButtonGroup,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Autocomplete,
  Pagination,
  Snackbar,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Balance as BalanceIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  CalendarToday as CalendarTodayIcon,
  CurrencyExchange as CurrencyExchangeIcon,
  Visibility as ViewIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';

// Import account ledger types (reusing from AccountLedgersPage)
interface LedgerEntry {
  id: number;
  transaction_date: string;
  entry_number: string;
  description: string;
  reference_number?: string;
  debit_amount: number;
  credit_amount: number;
  running_balance: number;
  journal_entry_id: number;
  journal_entry_status: string;
  contra_account?: string;
  memo?: string;
}

interface AccountLedgerData {
  account: {
    id: number;
    account_number: string;
    account_name: string;
    account_type: string;
    account_type_name: string;
    detail_type_name: string;
    current_balance: number;
    is_active: boolean;
    is_header_account: boolean;
    normal_balance: 'DEBIT' | 'CREDIT';
  };
  entries: LedgerEntry[];
  opening_balance: number;
  closing_balance: number;
  total_debits: number;
  total_credits: number;
  period_from: string;
  period_to: string;
}

interface JournalEntryDetails {
  id: number;
  entry_number: string;
  transaction_date: string;
  description: string;
  reference_number?: string;
  memo?: string;
  status: string;
  entry_type: string;
  currency: string;
  total_debits: number;
  total_credits: number;
  is_balanced: boolean;
  journal_lines: Array<{
    id: number;
    line_number: number;
    account_id: number;
    account_name: string;
    account_number: string;
    debit_amount: number;
    credit_amount: number;
    description: string;
    memo?: string;
  }>;
}

// Period type definitions
type PeriodType = 
  | 'ALL_DATES'
  | 'CUSTOM'
  | 'TODAY'
  | 'THIS_WEEK'
  | 'THIS_WEEK_TO_DATE'
  | 'THIS_MONTH'
  | 'THIS_MONTH_TO_DATE'
  | 'THIS_CALENDAR_QUARTER'
  | 'THIS_CALENDAR_QUARTER_TO_DATE'
  | 'THIS_FISCAL_QUARTER'
  | 'THIS_FISCAL_QUARTER_TO_DATE'
  | 'THIS_CALENDAR_YEAR'
  | 'THIS_CALENDAR_YEAR_TO_DATE'
  | 'THIS_FISCAL_YEAR'
  | 'THIS_FISCAL_YEAR_TO_DATE'
  | 'LAST_WEEK'
  | 'LAST_MONTH'
  | 'LAST_CALENDAR_QUARTER'
  | 'LAST_FISCAL_QUARTER'
  | 'LAST_CALENDAR_YEAR'
  | 'LAST_FISCAL_YEAR';

interface PeriodOption {
  value: PeriodType;
  label: string;
  description?: string;
}

const PERIOD_OPTIONS: PeriodOption[] = [
  { value: 'ALL_DATES', label: 'All Dates', description: 'From the beginning of time' },
  { value: 'CUSTOM', label: 'Custom', description: 'Select specific date range' },
  { value: 'TODAY', label: 'Today', description: 'Current date only' },
  { value: 'THIS_WEEK', label: 'This Week', description: 'Monday to Sunday of current week' },
  { value: 'THIS_WEEK_TO_DATE', label: 'This Week-to-date', description: 'Monday to today' },
  { value: 'THIS_MONTH', label: 'This Month', description: 'First to last day of current month' },
  { value: 'THIS_MONTH_TO_DATE', label: 'This Month-to-date', description: 'First day of month to today' },
  { value: 'THIS_CALENDAR_QUARTER', label: 'This Calendar Quarter', description: 'Current calendar quarter' },
  { value: 'THIS_CALENDAR_QUARTER_TO_DATE', label: 'This Calendar Quarter-to-date', description: 'Start of quarter to today' },
  { value: 'THIS_FISCAL_QUARTER', label: 'This Fiscal Quarter', description: 'Current fiscal quarter' },
  { value: 'THIS_FISCAL_QUARTER_TO_DATE', label: 'This Fiscal Quarter-to-date', description: 'Start of fiscal quarter to today' },
  { value: 'THIS_CALENDAR_YEAR', label: 'This Calendar Year', description: 'January 1 to December 31' },
  { value: 'THIS_CALENDAR_YEAR_TO_DATE', label: 'This Calendar Year-to-date', description: 'January 1 to today' },
  { value: 'THIS_FISCAL_YEAR', label: 'This Fiscal Year', description: 'Current fiscal year' },
  { value: 'THIS_FISCAL_YEAR_TO_DATE', label: 'This Fiscal Year-to-date', description: 'Start of fiscal year to today' },
  { value: 'LAST_WEEK', label: 'Last Week', description: 'Previous complete week' },
  { value: 'LAST_MONTH', label: 'Last Month', description: 'Previous complete month' },
  { value: 'LAST_CALENDAR_QUARTER', label: 'Last Calendar Quarter', description: 'Previous calendar quarter' },
  { value: 'LAST_FISCAL_QUARTER', label: 'Last Fiscal Quarter', description: 'Previous fiscal quarter' },
  { value: 'LAST_CALENDAR_YEAR', label: 'Last Calendar Year', description: 'Previous calendar year' },
  { value: 'LAST_FISCAL_YEAR', label: 'Last Fiscal Year', description: 'Previous fiscal year' },
];

interface TrialBalanceAccount {
  account_id: number;
  account_number: string;
  account_name: string;
  account_type: string;
  account_type_code: string;
  detail_type: string;
  normal_balance: 'DEBIT' | 'CREDIT';
  opening_balance: number | string;
  total_debits: number | string;
  total_credits: number | string;
  current_balance: number | string;
  debit_balance: number | string;
  credit_balance: number | string;
}

interface AccountTypeSummary {
  account_type: string;
  account_type_code: string;
  normal_balance: 'DEBIT' | 'CREDIT';
  total_debit_balance: number | string;
  total_credit_balance: number | string;
  account_count: number;
}

interface TrialBalanceData {
  as_of_date: string;
  company_name: string;
  report_title: string;
  accounts: TrialBalanceAccount[];
  account_type_summary: AccountTypeSummary[];
  total_debits: number | string;
  total_credits: number | string;
  is_balanced: boolean;
  variance: number | string;
  total_accounts: number;
  include_zero_balances: boolean;
  generated_at: string;
}

const TrialBalancePage: React.FC = () => {
  const [trialBalanceData, setTrialBalanceData] = useState<TrialBalanceData | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodType>('THIS_MONTH_TO_DATE');
  const [customDateFrom, setCustomDateFrom] = useState<Dayjs>(dayjs().startOf('month'));
  const [customDateTo, setCustomDateTo] = useState<Dayjs>(dayjs());
  const [includeZeroBalances, setIncludeZeroBalances] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAccountType, setSelectedAccountType] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [displayCurrency, setDisplayCurrency] = useState<'functional' | 'reporting'>('functional');

  // Account Ledger Dialog State
  const [ledgerDialogOpen, setLedgerDialogOpen] = useState(false);
  const [ledgerData, setLedgerData] = useState<AccountLedgerData | null>(null);
  const [ledgerLoading, setLedgerLoading] = useState(false);
  const [selectedAccountForLedger, setSelectedAccountForLedger] = useState<TrialBalanceAccount | null>(null);
  
  // Journal Entry Dialog State  
  const [viewJournalDialog, setViewJournalDialog] = useState(false);
  const [viewingJournalEntry, setViewingJournalEntry] = useState<JournalEntryDetails | null>(null);
  const [journalEntryLoading, setJournalEntryLoading] = useState(false);
  
  // Ledger search and pagination
  const [ledgerSearchTerm, setLedgerSearchTerm] = useState('');
  const [ledgerPage, setLedgerPage] = useState(1);
  const [ledgerRowsPerPage] = useState(25);
  
  // Alert for drill-down actions
  const [alert, setAlert] = useState<{type: 'success' | 'error' | 'info', message: string} | null>(null);

  // Get currency information
  const { currencyInfo, loading: currencyLoading, error: currencyError } = useCurrencyInfo();

  // Calculate date range based on selected period
  const getDateRange = (period: PeriodType): { fromDate: Dayjs | null; toDate: Dayjs } => {
    const today = dayjs();
    const startOfWeek = today.startOf('week').add(1, 'day'); // Monday
    const startOfMonth = today.startOf('month');
    const startOfYear = today.startOf('year');
    
    // Fiscal year starts in April (common for many countries like India)
    const fiscalYearStart = today.month() >= 3 ? today.year() : today.year() - 1;
    const startOfFiscalYear = dayjs(`${fiscalYearStart}-04-01`);
    
    // Calendar quarter calculation
    const quarter = Math.floor(today.month() / 3);
    const startOfCalendarQuarter = today.startOf('year').add(quarter * 3, 'month');
    
    // Fiscal quarter calculation (April-June, July-Sep, Oct-Dec, Jan-Mar)
    let fiscalQuarter = today.month() >= 3 ? Math.floor((today.month() - 3) / 3) : 3;
    const startOfFiscalQuarter = startOfFiscalYear.add(fiscalQuarter * 3, 'month');

    switch (period) {
      case 'ALL_DATES':
        return { fromDate: null, toDate: today };
      
      case 'CUSTOM':
        return { fromDate: customDateFrom, toDate: customDateTo };
      
      case 'TODAY':
        return { fromDate: today, toDate: today };
      
      case 'THIS_WEEK':
        return { fromDate: startOfWeek, toDate: startOfWeek.add(6, 'day') };
      
      case 'THIS_WEEK_TO_DATE':
        return { fromDate: startOfWeek, toDate: today };
      
      case 'THIS_MONTH':
        return { fromDate: startOfMonth, toDate: startOfMonth.endOf('month') };
      
      case 'THIS_MONTH_TO_DATE':
        return { fromDate: startOfMonth, toDate: today };
      
      case 'THIS_CALENDAR_QUARTER':
        return { fromDate: startOfCalendarQuarter, toDate: startOfCalendarQuarter.add(2, 'month').endOf('month') };
      
      case 'THIS_CALENDAR_QUARTER_TO_DATE':
        return { fromDate: startOfCalendarQuarter, toDate: today };
      
      case 'THIS_FISCAL_QUARTER':
        return { fromDate: startOfFiscalQuarter, toDate: startOfFiscalQuarter.add(2, 'month').endOf('month') };
      
      case 'THIS_FISCAL_QUARTER_TO_DATE':
        return { fromDate: startOfFiscalQuarter, toDate: today };
      
      case 'THIS_CALENDAR_YEAR':
        return { fromDate: startOfYear, toDate: startOfYear.endOf('year') };
      
      case 'THIS_CALENDAR_YEAR_TO_DATE':
        return { fromDate: startOfYear, toDate: today };
      
      case 'THIS_FISCAL_YEAR':
        return { fromDate: startOfFiscalYear, toDate: startOfFiscalYear.add(1, 'year').subtract(1, 'day') };
      
      case 'THIS_FISCAL_YEAR_TO_DATE':
        return { fromDate: startOfFiscalYear, toDate: today };
      
      case 'LAST_WEEK':
        const lastWeekStart = startOfWeek.subtract(1, 'week');
        return { fromDate: lastWeekStart, toDate: lastWeekStart.add(6, 'day') };
      
      case 'LAST_MONTH':
        const lastMonth = startOfMonth.subtract(1, 'month');
        return { fromDate: lastMonth, toDate: lastMonth.endOf('month') };
      
      case 'LAST_CALENDAR_QUARTER':
        const lastCalendarQuarter = startOfCalendarQuarter.subtract(3, 'month');
        return { fromDate: lastCalendarQuarter, toDate: lastCalendarQuarter.add(2, 'month').endOf('month') };
      
      case 'LAST_FISCAL_QUARTER':
        const lastFiscalQuarter = startOfFiscalQuarter.subtract(3, 'month');
        return { fromDate: lastFiscalQuarter, toDate: lastFiscalQuarter.add(2, 'month').endOf('month') };
      
      case 'LAST_CALENDAR_YEAR':
        const lastCalendarYear = startOfYear.subtract(1, 'year');
        return { fromDate: lastCalendarYear, toDate: lastCalendarYear.endOf('year') };
      
      case 'LAST_FISCAL_YEAR':
        const lastFiscalYear = startOfFiscalYear.subtract(1, 'year');
        return { fromDate: lastFiscalYear, toDate: lastFiscalYear.add(1, 'year').subtract(1, 'day') };
      
      default:
        return { fromDate: startOfMonth, toDate: today };
    }
  };

  // Get current date range for display
  const currentDateRange = getDateRange(selectedPeriod);
  const asOfDate = currentDateRange.toDate;

  // Load trial balance data
  const loadTrialBalance = async () => {
    setLoading(true);
    setError('');
    try {
      const url = new URL('http://localhost:8000/api/gl/accounts/trial_balance/');
      url.searchParams.append('as_of_date', asOfDate.format('YYYY-MM-DD'));
      url.searchParams.append('include_zero_balances', includeZeroBalances.toString());

      const response = await fetch(url.toString(), {
        headers: { 'Authorization': `Token ${localStorage.getItem('token')}` }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setTrialBalanceData(data);
    } catch (error) {
      console.error('Error loading trial balance:', error);
      setError(error instanceof Error ? error.message : 'Failed to load trial balance');
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters change
  useEffect(() => {
    loadTrialBalance();
  }, [selectedPeriod, customDateFrom, customDateTo, includeZeroBalances]);

  // Handle period change
  const handlePeriodChange = (event: SelectChangeEvent<PeriodType>) => {
    const newPeriod = event.target.value as PeriodType;
    setSelectedPeriod(newPeriod);
  };

  // Handle custom date changes
  const handleCustomDateFromChange = (newValue: Dayjs | null) => {
    if (newValue) {
      setCustomDateFrom(newValue);
    }
  };

  const handleCustomDateToChange = (newValue: Dayjs | null) => {
    if (newValue) {
      setCustomDateTo(newValue);
    }
  };

  // Get formatted date range string for display
  const getDateRangeDisplay = (): string => {
    const { fromDate, toDate } = currentDateRange;
    
    if (!fromDate) {
      return `All dates through ${toDate.format('MMMM D, YYYY')}`;
    }
    
    if (fromDate.isSame(toDate, 'day')) {
      return toDate.format('MMMM D, YYYY');
    }
    
    return `${fromDate.format('MMMM D, YYYY')} to ${toDate.format('MMMM D, YYYY')}`;
  };

  // Helper function to convert number|string to number
  const toNumber = (value: number | string): number => {
    return typeof value === 'number' ? value : parseFloat(value.toString()) || 0;
  };

  // Format currency
  const formatCurrency = (amount: number | string, currencyOverride?: string) => {
    if (!currencyInfo) return '0.00';
    
    const currency = currencyOverride || 
      (displayCurrency === 'functional' ? currencyInfo.functional_currency : currencyInfo.reporting_currency);
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    const numericAmount = toNumber(amount);
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(Math.abs(numericAmount));
  };

  // Get current currency symbol
  const getCurrentCurrencySymbol = () => {
    if (!currencyInfo) return '$';
    return displayCurrency === 'functional' 
      ? currencyInfo.functional_currency_symbol 
      : currencyInfo.reporting_currency_symbol;
  };

  // Get current currency code
  const getCurrentCurrency = () => {
    if (!currencyInfo) return 'USD';
    return displayCurrency === 'functional' 
      ? currencyInfo.functional_currency 
      : currencyInfo.reporting_currency;
  };

  // Filter accounts based on search and account type
  const filteredAccounts = trialBalanceData?.accounts.filter(account => {
    const matchesSearch = 
      account.account_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !selectedAccountType || account.account_type === selectedAccountType;
    return matchesSearch && matchesType;
  }) || [];

  // Export functions
  const handleExportCSV = () => {
    if (!trialBalanceData) return;
    
    const csvContent = [
      ['Account Number', 'Account Name', 'Account Type', 'Debit Balance', 'Credit Balance'].join(','),
      ...filteredAccounts.map(account => [
        account.account_number,
        `"${account.account_name}"`,
        account.account_type,
        toNumber(account.debit_balance).toFixed(2),
        toNumber(account.credit_balance).toFixed(2)
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trial-balance-${selectedPeriod.toLowerCase().replace(/_/g, '-')}-${asOfDate.format('YYYY-MM-DD')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    window.print();
  };

  // Drill-down functions
  const showAlert = (type: 'success' | 'error' | 'info', message: string) => {
    setAlert({ type, message });
  };

  const handleBalanceClick = async (account: TrialBalanceAccount) => {
    setSelectedAccountForLedger(account);
    setLedgerLoading(true);
    setLedgerDialogOpen(true);
    setLedgerPage(1);
    setLedgerSearchTerm('');
    
    try {
      const { fromDate, toDate } = getDateRange(selectedPeriod);
      const params = new URLSearchParams({
        account_id: account.account_id.toString(),
        date_to: toDate.format('YYYY-MM-DD'),
      });
      
      if (fromDate) {
        params.append('date_from', fromDate.format('YYYY-MM-DD'));
      }

      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/gl/accounts/${account.account_id}/ledger/?${params}`, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });
      if (!response.ok) {
        throw new Error('Failed to load account ledger');
      }
      
      const data = await response.json();
      setLedgerData(data);
    } catch (error) {
      console.error('Error loading ledger:', error);
      showAlert('error', 'Failed to load account ledger');
      setLedgerDialogOpen(false);
    } finally {
      setLedgerLoading(false);
    }
  };

  const handleCloseLedgerDialog = () => {
    setLedgerDialogOpen(false);
    setLedgerData(null);
    setSelectedAccountForLedger(null);
    setLedgerSearchTerm('');
    setLedgerPage(1);
  };

  const loadJournalEntryDetails = async (journalEntryId: number) => {
    setJournalEntryLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/gl/journal-entries/${journalEntryId}/`, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });
      if (!response.ok) {
        throw new Error('Failed to load journal entry details');
      }
      
      const data = await response.json();
      setViewingJournalEntry(data);
      setViewJournalDialog(true);
    } catch (error) {
      console.error('Error loading journal entry details:', error);
      showAlert('error', 'Failed to load journal entry details');
    } finally {
      setJournalEntryLoading(false);
    }
  };

  const handleViewJournalEntry = (journalEntryId: number) => {
    loadJournalEntryDetails(journalEntryId);
  };

  const handleCloseJournalDialog = () => {
    setViewJournalDialog(false);
    setViewingJournalEntry(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'POSTED': return 'success';
      case 'DRAFT': return 'warning';
      case 'PENDING': return 'info';
      case 'REVERSED': return 'error';
      default: return 'default';
    }
  };

  // Filter and paginate ledger entries
  const filteredLedgerEntries = ledgerData?.entries.filter(entry => {
    return (
      entry.entry_number.toLowerCase().includes(ledgerSearchTerm.toLowerCase()) ||
      entry.description.toLowerCase().includes(ledgerSearchTerm.toLowerCase()) ||
      (entry.reference_number && entry.reference_number.toLowerCase().includes(ledgerSearchTerm.toLowerCase())) ||
      (entry.contra_account && entry.contra_account.toLowerCase().includes(ledgerSearchTerm.toLowerCase()))
    );
  }) || [];

  const paginatedLedgerEntries = filteredLedgerEntries.slice(
    (ledgerPage - 1) * ledgerRowsPerPage, 
    ledgerPage * ledgerRowsPerPage
  );

  if (loading || currencyLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Stack alignItems="center" spacing={2}>
          <CircularProgress size={60} />
          <Typography variant="h6" color="textSecondary">
            {currencyLoading ? 'Loading currency information...' : 'Generating Trial Balance...'}
          </Typography>
        </Stack>
      </Box>
    );
  }

  // Show error state if currency info failed to load
  if (currencyError) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Box sx={{ p: 3 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            <AlertTitle>Currency Information Error</AlertTitle>
            {currencyError}
          </Alert>
        </Box>
      </LocalizationProvider>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <BalanceIcon />
            </Avatar>
            Trial Balance
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Comprehensive account balances for {getDateRangeDisplay()}
          </Typography>
          {currencyInfo && (
            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="body2" color="textSecondary">
                📊 {currencyInfo.company_name}
              </Typography>
              <Chip 
                icon={<CurrencyExchangeIcon />}
                label={`Displayed in ${getCurrentCurrency()} (${getCurrentCurrencySymbol()})`}
                size="small"
                color="primary"
                variant="outlined"
              />
            </Box>
          )}
        </Box>

        {/* Filters and Controls */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Report Period</InputLabel>
                  <Select
                    value={selectedPeriod}
                    label="Report Period"
                    onChange={handlePeriodChange}
                    startAdornment={<CalendarTodayIcon sx={{ mr: 1, color: 'action.active' }} />}
                  >
                    {PERIOD_OPTIONS.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        <Box>
                          <Typography variant="body2">{option.label}</Typography>
                          {option.description && (
                            <Typography variant="caption" color="textSecondary" display="block">
                              {option.description}
                            </Typography>
                          )}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Currency Display Toggle */}
              <Grid item xs={12} sm={6} md={2}>
                <Box>
                  <Typography variant="caption" color="textSecondary" gutterBottom display="block">
                    Display Currency
                  </Typography>
                  <ToggleButtonGroup
                    value={displayCurrency}
                    exclusive
                    onChange={(_, newValue) => newValue && setDisplayCurrency(newValue)}
                    size="small"
                    fullWidth
                  >
                    <ToggleButton value="functional" sx={{ fontSize: '0.75rem', px: 1 }}>
                      <Box textAlign="center">
                        <Typography variant="caption" display="block">Functional</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {currencyInfo?.functional_currency}
                        </Typography>
                      </Box>
                    </ToggleButton>
                    <ToggleButton value="reporting" sx={{ fontSize: '0.75rem', px: 1 }}>
                      <Box textAlign="center">
                        <Typography variant="caption" display="block">Reporting</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {currencyInfo?.reporting_currency}
                        </Typography>
                      </Box>
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Grid>
              
              {/* Custom Date Range Fields - only show when CUSTOM is selected */}
              {selectedPeriod === 'CUSTOM' && (
                <>
                  <Grid item xs={12} sm={6} md={2}>
                    <DatePicker
                      label="From Date"
                      value={customDateFrom}
                      onChange={handleCustomDateFromChange}
                      maxDate={customDateTo}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: 'small'
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={2}>
                    <DatePicker
                      label="To Date"
                      value={customDateTo}
                      onChange={handleCustomDateToChange}
                      minDate={customDateFrom}
                      maxDate={dayjs()}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: 'small'
                        }
                      }}
                    />
                  </Grid>
                </>
              )}
              
              <Grid item xs={12} sm={6} md={selectedPeriod === 'CUSTOM' ? 2 : 3}>
                <TextField
                  label="Search Accounts"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Account number or name..."
                  size="small"
                  fullWidth
                />
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={includeZeroBalances}
                      onChange={(e) => setIncludeZeroBalances(e.target.checked)}
                    />
                  }
                  label="Include Zero Balances"
                />
              </Grid>
            </Grid>

            {/* Action Buttons - Separated row for better layout */}
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  <strong>Selected Period:</strong> {PERIOD_OPTIONS.find(p => p.value === selectedPeriod)?.label} • 
                  <strong> Date Range:</strong> {getDateRangeDisplay()} • 
                  <strong> Currency:</strong> {displayCurrency === 'functional' ? 'Functional' : 'Reporting'} 
                  ({getCurrentCurrency()})
                </Typography>
                {trialBalanceData && (
                  <Typography variant="body2" color="textSecondary">
                    <strong>Accounts Shown:</strong> {filteredAccounts.length} of {trialBalanceData.total_accounts} active accounts
                  </Typography>
                )}
              </Box>
              
              <Stack direction="row" spacing={2}>
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={loadTrialBalance}
                  disabled={loading}
                  sx={{ minWidth: 120 }}
                >
                  Refresh
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={handleExportCSV}
                  disabled={!trialBalanceData}
                  sx={{ minWidth: 120 }}
                >
                  Export CSV
                </Button>
                <Button
                  variant="contained"
                  startIcon={<PrintIcon />}
                  onClick={handlePrint}
                  disabled={!trialBalanceData}
                  sx={{ minWidth: 120 }}
                >
                  Print Report
                </Button>
              </Stack>
            </Box>
          </CardContent>
        </Card>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <AlertTitle>Error Loading Trial Balance</AlertTitle>
            {error}
          </Alert>
        )}

        {trialBalanceData && (
          <>
            {/* Summary Cards */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'success.main' }}>
                        <TrendingUpIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {formatCurrency(trialBalanceData.total_debits)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Total Debits
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'error.main' }}>
                        <TrendingDownIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {formatCurrency(trialBalanceData.total_credits)}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Total Credits
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: trialBalanceData.is_balanced ? 'success.main' : 'warning.main' }}>
                        {trialBalanceData.is_balanced ? <CheckCircleIcon /> : <ErrorIcon />}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {trialBalanceData.is_balanced ? 'Balanced' : 'Variance'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {trialBalanceData.is_balanced 
                            ? 'Books are balanced' 
                            : formatCurrency(trialBalanceData.variance)
                          }
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'info.main' }}>
                        <AccountBalanceIcon />
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {trialBalanceData.total_accounts}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Active Accounts
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Balance Status Alert */}
            {!trialBalanceData.is_balanced && (
              <Alert severity="warning" sx={{ mb: 3 }}>
                <AlertTitle>Trial Balance Not Balanced</AlertTitle>
                There is a variance of {formatCurrency(trialBalanceData.variance)} between total debits and credits. 
                Please review your journal entries for errors.
              </Alert>
            )}

            {/* Account Type Summary */}
            <Accordion sx={{ mb: 3 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Summary by Account Type</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell><strong>Account Type</strong></TableCell>
                        <TableCell align="center"><strong>Accounts</strong></TableCell>
                        <TableCell align="right"><strong>Total Debits</strong></TableCell>
                        <TableCell align="right"><strong>Total Credits</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {trialBalanceData.account_type_summary.map((summary) => (
                        <TableRow key={summary.account_type}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Chip 
                                label={summary.normal_balance} 
                                size="small" 
                                color={summary.normal_balance === 'DEBIT' ? 'success' : 'error'}
                              />
                              {summary.account_type}
                            </Box>
                          </TableCell>
                          <TableCell align="center">
                            <Chip label={summary.account_count} size="small" variant="outlined" />
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(summary.total_debit_balance)}
                          </TableCell>
                          <TableCell align="right">
                            {formatCurrency(summary.total_credit_balance)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>

            {/* Main Trial Balance Table */}
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Trial Balance Detail ({filteredAccounts.length} accounts)
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Generated on {dayjs(trialBalanceData.generated_at).format('MMM D, YYYY [at] h:mm A')}
                  </Typography>
                </Box>

                <TableContainer component={Paper} elevation={0}>
                  <Table>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'grey.50' }}>
                        <TableCell><strong>Account</strong></TableCell>
                        <TableCell><strong>Type</strong></TableCell>
                        <TableCell align="right"><strong>Debit Balance</strong></TableCell>
                        <TableCell align="right"><strong>Credit Balance</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredAccounts.map((account) => (
                        <TableRow 
                          key={account.account_id}
                          hover
                          sx={{ 
                            '&:hover': { backgroundColor: 'action.hover' },
                            borderLeft: `4px solid ${
                              account.normal_balance === 'DEBIT' 
                                ? 'success.main' 
                                : 'error.main'
                            }`,
                          }}
                        >
                          <TableCell>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {account.account_number} - {account.account_name}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                {account.detail_type}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip 
                              label={account.account_type} 
                              size="small" 
                              variant="outlined"
                              color={account.normal_balance === 'DEBIT' ? 'success' : 'error'}
                            />
                          </TableCell>
                          <TableCell align="right">
                            {toNumber(account.debit_balance) > 0 ? (
                              <Button
                                variant="text"
                                onClick={() => handleBalanceClick(account)}
                                sx={{ 
                                  fontWeight: 'bold',
                                  color: 'success.main',
                                  textDecoration: 'underline',
                                  '&:hover': {
                                    backgroundColor: 'success.light',
                                    color: 'success.dark'
                                  }
                                }}
                              >
                                {formatCurrency(account.debit_balance)}
                              </Button>
                            ) : (
                              <Typography variant="body2" color="textSecondary">—</Typography>
                            )}
                          </TableCell>
                          <TableCell align="right">
                            {toNumber(account.credit_balance) > 0 ? (
                              <Button
                                variant="text"
                                onClick={() => handleBalanceClick(account)}
                                sx={{ 
                                  fontWeight: 'bold',
                                  color: 'error.main',
                                  textDecoration: 'underline',
                                  '&:hover': {
                                    backgroundColor: 'error.light',
                                    color: 'error.dark'
                                  }
                                }}
                              >
                                {formatCurrency(account.credit_balance)}
                              </Button>
                            ) : (
                              <Typography variant="body2" color="textSecondary">—</Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                      
                      {/* Totals Row */}
                      <TableRow sx={{ backgroundColor: 'primary.main', '& td': { color: 'white', fontWeight: 'bold' } }}>
                        <TableCell colSpan={2}>
                          <Typography variant="h6" color="inherit">
                            TOTALS
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="h6" color="inherit">
                            {formatCurrency(trialBalanceData.total_debits)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="h6" color="inherit">
                            {formatCurrency(trialBalanceData.total_credits)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </>
        )}

        {/* Account Ledger Dialog */}
        <Dialog 
          open={ledgerDialogOpen} 
          onClose={handleCloseLedgerDialog} 
          maxWidth="xl" 
          fullWidth
          PaperProps={{
            sx: { minHeight: '80vh' }
          }}
        >
          <DialogTitle sx={{ 
            bgcolor: 'primary.main', 
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <AccountBalanceIcon />
            Account Ledger
            {selectedAccountForLedger && (
              <Chip 
                label={`${selectedAccountForLedger.account_number} - ${selectedAccountForLedger.account_name}`} 
                sx={{ 
                  ml: 2, 
                  bgcolor: 'white', 
                  color: 'primary.main',
                  fontWeight: 600 
                }} 
              />
            )}
          </DialogTitle>
          
          <DialogContent sx={{ p: 3 }}>
            {ledgerLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : ledgerData && selectedAccountForLedger ? (
              <Grid container spacing={3}>
                {/* Account Summary */}
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <AccountBalanceIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
                          <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            {selectedAccountForLedger.account_number}
                          </Typography>
                          <Typography variant="body1" color="text.secondary">
                            {selectedAccountForLedger.account_name}
                          </Typography>
                          <Chip 
                            label={selectedAccountForLedger.account_type} 
                            color="primary" 
                            size="small" 
                            sx={{ mt: 1 }}
                          />
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={9}>
                        <Grid container spacing={2}>
                          <Grid item xs={6} sm={3}>
                            <Card variant="outlined" sx={{ p: 2, backgroundColor: 'info.light', color: 'info.contrastText' }}>
                              <Typography variant="caption" sx={{ fontWeight: 600 }}>Opening Balance</Typography>
                              <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                {formatCurrency(ledgerData.opening_balance)}
                              </Typography>
                            </Card>
                          </Grid>
                          
                          <Grid item xs={6} sm={3}>
                            <Card variant="outlined" sx={{ p: 2, backgroundColor: 'success.light', color: 'success.contrastText' }}>
                              <Typography variant="caption" sx={{ fontWeight: 600 }}>Total Debits</Typography>
                              <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                {formatCurrency(ledgerData.total_debits)}
                              </Typography>
                            </Card>
                          </Grid>
                          
                          <Grid item xs={6} sm={3}>
                            <Card variant="outlined" sx={{ p: 2, backgroundColor: 'error.light', color: 'error.contrastText' }}>
                              <Typography variant="caption" sx={{ fontWeight: 600 }}>Total Credits</Typography>
                              <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                {formatCurrency(ledgerData.total_credits)}
                              </Typography>
                            </Card>
                          </Grid>
                          
                          <Grid item xs={6} sm={3}>
                            <Card 
                              variant="outlined" 
                              sx={{ 
                                p: 2, 
                                backgroundColor: ledgerData.closing_balance >= 0 ? 'success.light' : 'warning.light',
                                color: ledgerData.closing_balance >= 0 ? 'success.contrastText' : 'warning.contrastText'
                              }}
                            >
                              <Typography variant="caption" sx={{ fontWeight: 600 }}>Closing Balance</Typography>
                              <Typography variant="h6" sx={{ fontWeight: 700 }}>
                                {formatCurrency(ledgerData.closing_balance)}
                              </Typography>
                            </Card>
                          </Grid>
                        </Grid>
                      </Grid>
                    </Grid>
                  </Card>
                </Grid>

                {/* Search */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    placeholder="Search entries by number, description, reference, or contra account..."
                    value={ledgerSearchTerm}
                    onChange={(e) => setLedgerSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <DownloadIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                {/* Ledger Entries Table */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Transaction History ({filteredLedgerEntries.length} entries)
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead sx={{ bgcolor: 'grey.100' }}>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600 }}>Date</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Entry #</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Reference</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Contra Account</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 600 }}>Debit</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 600 }}>Credit</TableCell>
                          <TableCell align="right" sx={{ fontWeight: 600 }}>Balance</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Status</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {paginatedLedgerEntries.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={10} sx={{ textAlign: 'center', py: 4 }}>
                              <Stack alignItems="center" spacing={2}>
                                <ReceiptIcon sx={{ fontSize: 64, color: 'text.disabled' }} />
                                <Typography variant="h6" color="text.secondary">
                                  No transactions found
                                </Typography>
                              </Stack>
                            </TableCell>
                          </TableRow>
                        ) : (
                          paginatedLedgerEntries.map((entry) => (
                            <TableRow key={entry.id} hover>
                              <TableCell>
                                {dayjs(entry.transaction_date).format('MMM DD, YYYY')}
                              </TableCell>
                              <TableCell sx={{ fontWeight: 500, color: 'primary.main' }}>
                                {entry.entry_number}
                              </TableCell>
                              <TableCell>
                                <Box>
                                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                    {entry.description}
                                  </Typography>
                                  {entry.memo && (
                                    <Typography variant="caption" color="text.secondary">
                                      {entry.memo}
                                    </Typography>
                                  )}
                                </Box>
                              </TableCell>
                              <TableCell>{entry.reference_number || '-'}</TableCell>
                              <TableCell>
                                <Typography variant="body2" color="text.secondary">
                                  {entry.contra_account || '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                {entry.debit_amount > 0 ? (
                                  <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
                                    {formatCurrency(entry.debit_amount)}
                                  </Typography>
                                ) : '-'}
                              </TableCell>
                              <TableCell align="right">
                                {entry.credit_amount > 0 ? (
                                  <Typography variant="body2" sx={{ fontWeight: 500, color: 'error.main' }}>
                                    {formatCurrency(entry.credit_amount)}
                                  </Typography>
                                ) : '-'}
                              </TableCell>
                              <TableCell align="right">
                                <Typography 
                                  variant="body2" 
                                  sx={{ 
                                    fontWeight: 600,
                                    color: entry.running_balance >= 0 ? 'success.main' : 'error.main'
                                  }}
                                >
                                  {formatCurrency(entry.running_balance)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip 
                                  label={entry.journal_entry_status} 
                                  color={getStatusColor(entry.journal_entry_status)}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell>
                                <Tooltip title="View Journal Entry">
                                  <IconButton 
                                    size="small"
                                    onClick={() => handleViewJournalEntry(entry.journal_entry_id)}
                                    disabled={journalEntryLoading}
                                  >
                                    {journalEntryLoading ? <CircularProgress size={16} /> : <ViewIcon />}
                                  </IconButton>
                                </Tooltip>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Ledger Pagination */}
                  {filteredLedgerEntries.length > ledgerRowsPerPage && (
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <Pagination
                        count={Math.ceil(filteredLedgerEntries.length / ledgerRowsPerPage)}
                        page={ledgerPage}
                        onChange={(_, newPage) => setLedgerPage(newPage)}
                        color="primary"
                        size="large"
                      />
                    </Box>
                  )}
                </Grid>
              </Grid>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No ledger data available
              </Typography>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={handleCloseLedgerDialog} variant="outlined">
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Journal Entry View Dialog */}
        <Dialog 
          open={viewJournalDialog} 
          onClose={handleCloseJournalDialog} 
          maxWidth="lg" 
          fullWidth
          PaperProps={{
            sx: { minHeight: '70vh' }
          }}
        >
          <DialogTitle sx={{ 
            bgcolor: 'primary.main', 
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <ReceiptIcon />
            Journal Entry Details
            {viewingJournalEntry && (
              <Chip 
                label={viewingJournalEntry.entry_number} 
                sx={{ 
                  ml: 2, 
                  bgcolor: 'white', 
                  color: 'primary.main',
                  fontWeight: 600 
                }} 
              />
            )}
          </DialogTitle>
          
          <DialogContent sx={{ p: 3 }}>
            {journalEntryLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : viewingJournalEntry ? (
              <Grid container spacing={3}>
                {/* Header Information */}
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">Entry Number</Typography>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                          {viewingJournalEntry.entry_number}
                        </Typography>
                        
                        <Typography variant="subtitle2" color="text.secondary">Transaction Date</Typography>
                        <Typography variant="body1" sx={{ mb: 2 }}>
                          {dayjs(viewingJournalEntry.transaction_date).format('MMM DD, YYYY')}
                        </Typography>
                        
                        <Typography variant="subtitle2" color="text.secondary">Entry Type</Typography>
                        <Typography variant="body1" sx={{ mb: 2 }}>
                          {viewingJournalEntry.entry_type}
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                        <Chip 
                          label={viewingJournalEntry.status} 
                          color={getStatusColor(viewingJournalEntry.status)}
                          sx={{ mb: 2 }}
                        />
                        
                        <Typography variant="subtitle2" color="text.secondary">Total Amount</Typography>
                        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                          {formatCurrency(viewingJournalEntry.total_debits)}
                        </Typography>
                        
                        <Typography variant="subtitle2" color="text.secondary">Balance Status</Typography>
                        <Chip 
                          label={viewingJournalEntry.is_balanced ? 'Balanced' : 'Unbalanced'} 
                          color={viewingJournalEntry.is_balanced ? 'success' : 'error'}
                          sx={{ mb: 2 }}
                        />
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                        <Typography variant="body1" sx={{ mb: 2 }}>{viewingJournalEntry.description}</Typography>
                        
                        {viewingJournalEntry.reference_number && (
                          <>
                            <Typography variant="subtitle2" color="text.secondary">Reference Number</Typography>
                            <Typography variant="body1" sx={{ mb: 2 }}>{viewingJournalEntry.reference_number}</Typography>
                          </>
                        )}
                        
                        {viewingJournalEntry.memo && (
                          <>
                            <Typography variant="subtitle2" color="text.secondary">Memo</Typography>
                            <Typography variant="body1">{viewingJournalEntry.memo}</Typography>
                          </>
                        )}
                      </Grid>
                    </Grid>
                  </Card>
                </Grid>

                {/* Journal Lines */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>Journal Lines</Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead sx={{ bgcolor: 'grey.100' }}>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 600 }}>Line #</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Account</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Description</TableCell>
                          <TableCell sx={{ fontWeight: 600 }}>Memo</TableCell>
                          <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Debit</TableCell>
                          <TableCell sx={{ fontWeight: 600, textAlign: 'right' }}>Credit</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {viewingJournalEntry.journal_lines.map((line) => (
                          <TableRow key={line.id}>
                            <TableCell sx={{ fontWeight: 500 }}>{line.line_number}</TableCell>
                            <TableCell>
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {line.account_number} - {line.account_name}
                                </Typography>
                              </Box>
                            </TableCell>
                            <TableCell>{line.description}</TableCell>
                            <TableCell>{line.memo || '-'}</TableCell>
                            <TableCell sx={{ textAlign: 'right' }}>
                              {line.debit_amount > 0 ? (
                                <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
                                  {formatCurrency(line.debit_amount)}
                                </Typography>
                              ) : '-'}
                            </TableCell>
                            <TableCell sx={{ textAlign: 'right' }}>
                              {line.credit_amount > 0 ? (
                                <Typography variant="body2" sx={{ fontWeight: 500, color: 'error.main' }}>
                                  {formatCurrency(line.credit_amount)}
                                </Typography>
                              ) : '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                        
                        {/* Totals Row */}
                        <TableRow sx={{ bgcolor: 'grey.50', borderTop: 2, borderColor: 'divider' }}>
                          <TableCell colSpan={4} sx={{ fontWeight: 600, textAlign: 'right' }}>
                            TOTALS:
                          </TableCell>
                          <TableCell sx={{ textAlign: 'right', fontWeight: 600, color: 'success.main' }}>
                            {formatCurrency(viewingJournalEntry.total_debits)}
                          </TableCell>
                          <TableCell sx={{ textAlign: 'right', fontWeight: 600, color: 'error.main' }}>
                            {formatCurrency(viewingJournalEntry.total_credits)}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No journal entry data available
              </Typography>
            )}
          </DialogContent>
          
          <DialogActions>
            <Button onClick={handleCloseJournalDialog} variant="outlined">
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={Boolean(alert)}
          autoHideDuration={6000}
          onClose={() => setAlert(null)}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          {alert && (
            <Alert onClose={() => setAlert(null)} severity={alert.type} sx={{ width: '100%' }}>
              {alert.message}
            </Alert>
          )}
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default TrialBalancePage; 