"""
Cash Management System Serializers

These serializers handle the conversion between Django models and JSON API responses
for all Cash Management functionality. They include comprehensive business logic
validation to ensure cash management integrity and proper treasury controls.

Key Features:
- Cash account management with banking integration
- Cash transaction processing with approval workflows
- Bank reconciliation with automated matching
- Cash flow forecasting and analysis
- Multi-currency support and exchange rate handling
"""

from rest_framework import serializers
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from .models import (
    CashAccountType, CashAccount, CashTransaction, CashTransfer,
    BankStatement, BankStatementEntry, CashFlowForecast
)
from gl.models import Account


class CashAccountTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for Cash Account Types
    
    Handles different types of cash accounts like checking, savings,
    petty cash, investment accounts, etc.
    """
    
    # Count of accounts using this type
    accounts_count = serializers.SerializerMethodField()
    
    class Meta:
        model = CashAccountType
        fields = [
            'id', 'code', 'name', 'description',
            'requires_bank_details', 'allows_overdraft', 
            'requires_reconciliation', 'is_investment_account',
            'sort_order', 'is_active', 'accounts_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'accounts_count']
    
    def get_accounts_count(self, obj):
        """Get count of cash accounts using this type."""
        return obj.cash_accounts.filter(is_active=True).count()
    
    def validate_code(self, value):
        """Validate account type code format."""
        value = value.strip().upper()
        if not value.replace('_', '').isalnum():
            raise serializers.ValidationError(
                "Account type code must contain only letters, numbers, and underscores"
            )
        return value


class CashAccountSerializer(serializers.ModelSerializer):
    """
    Serializer for Cash Accounts
    
    Handles all types of cash accounts including bank accounts,
    petty cash, investment accounts with comprehensive validation.
    """
    
    # Related field display names
    account_type_name = serializers.CharField(source='account_type.name', read_only=True)
    account_type_code = serializers.CharField(source='account_type.code', read_only=True)
    gl_account_name = serializers.CharField(source='gl_account.account_name', read_only=True)
    gl_account_number = serializers.CharField(source='gl_account.account_number', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    account_manager_username = serializers.CharField(source='account_manager.username', read_only=True)
    
    # Calculated fields
    calculated_balance = serializers.SerializerMethodField()
    available_balance = serializers.SerializerMethodField()
    transaction_count = serializers.SerializerMethodField()
    last_transaction_date = serializers.SerializerMethodField()
    days_since_reconciliation = serializers.SerializerMethodField()
    
    class Meta:
        model = CashAccount
        fields = [
            'id', 'account_name', 'account_number', 'account_type', 
            'account_type_name', 'account_type_code',
            'bank_name', 'bank_branch', 'bank_routing_number', 
            'bank_swift_code', 'bank_iban',
            'currency', 'opening_balance', 'opening_balance_date', 
            'current_balance', 'calculated_balance', 'available_balance',
            'credit_limit', 'interest_rate',
            'gl_account', 'gl_account_name', 'gl_account_number',
            'account_manager', 'account_manager_username', 'description',
            'is_active', 'is_default', 'requires_approval',
            'last_reconciled_date', 'last_reconciled_balance',
            'transaction_count', 'last_transaction_date', 'days_since_reconciliation',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'current_balance', 'calculated_balance', 'available_balance',
            'transaction_count', 'last_transaction_date', 'days_since_reconciliation',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
    
    def get_calculated_balance(self, obj):
        """Get real-time calculated balance."""
        return float(obj.get_current_balance())
    
    def get_available_balance(self, obj):
        """Get available balance including credit limits."""
        return float(obj.get_available_balance())
    
    def get_transaction_count(self, obj):
        """Get count of transactions for this account."""
        return obj.cash_transactions.count()
    
    def get_last_transaction_date(self, obj):
        """Get date of last transaction."""
        last_transaction = obj.cash_transactions.order_by('-transaction_date').first()
        return last_transaction.transaction_date if last_transaction else None
    
    def get_days_since_reconciliation(self, obj):
        """Get days since last reconciliation."""
        if obj.last_reconciled_date:
            return (timezone.now().date() - obj.last_reconciled_date).days
        return None
    
    def validate_account_number(self, value):
        """Validate account number format."""
        value = value.strip()
        if not value:
            raise serializers.ValidationError("Account number is required")
        return value
    
    def validate(self, data):
        """Cross-field validation for cash accounts."""
        
        # Validate credit limit for appropriate account types
        account_type = data.get('account_type')
        credit_limit = data.get('credit_limit')
        
        if credit_limit and account_type and account_type.code not in ['BANK_CREDIT_LINE']:
            if credit_limit > 0:
                raise serializers.ValidationError({
                    'credit_limit': f'Credit limits are only applicable to credit line accounts'
                })
        
        # Validate banking details if required
        if account_type and account_type.requires_bank_details:
            required_fields = ['bank_name']
            for field in required_fields:
                if not data.get(field):
                    raise serializers.ValidationError({
                        field: f'{field.replace("_", " ").title()} is required for {account_type.name}'
                    })
        
        # Ensure only one default account per currency
        if data.get('is_default'):
            currency = data.get('currency', 'USD')
            existing_default = CashAccount.objects.filter(
                currency=currency,
                is_default=True,
                is_active=True
            )
            
            # Exclude current instance when updating
            if self.instance:
                existing_default = existing_default.exclude(id=self.instance.id)
            
            if existing_default.exists():
                raise serializers.ValidationError({
                    'is_default': f'Another account is already set as default for {currency}'
                })
        
        return data
    
    def create(self, validated_data):
        """Create cash account with proper user assignment."""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class CashTransactionSerializer(serializers.ModelSerializer):
    """
    Serializer for Cash Transactions
    
    Handles all types of cash receipts, payments, and adjustments
    with comprehensive validation and approval workflow.
    """
    
    # Related field display names
    cash_account_name = serializers.CharField(source='cash_account.account_name', read_only=True)
    cash_account_number = serializers.CharField(source='cash_account.account_number', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    posted_by_username = serializers.CharField(source='posted_by.username', read_only=True)
    approved_by_username = serializers.CharField(source='approved_by.username', read_only=True)
    
    # Calculated fields
    account_currency_amount = serializers.SerializerMethodField()
    requires_approval_flag = serializers.SerializerMethodField()
    can_be_posted = serializers.SerializerMethodField()
    
    class Meta:
        model = CashTransaction
        fields = [
            'id', 'transaction_number', 'transaction_date', 'transaction_type',
            'cash_account', 'cash_account_name', 'cash_account_number',
            'amount', 'currency', 'exchange_rate', 'account_currency_amount',
            'description', 'reference_number', 'memo',
            'customer', 'vendor', 'employee',
            'gl_journal_entry', 'bank_reference', 'cleared_date',
            'status', 'requires_approval', 'requires_approval_flag', 'can_be_posted',
            'approved_by', 'approved_by_username', 'approved_at',
            'created_by', 'created_by_username', 'posted_by', 'posted_by_username',
            'created_at', 'updated_at', 'posted_at'
        ]
        read_only_fields = [
            'account_currency_amount', 'requires_approval_flag', 'can_be_posted',
            'approved_by_username', 'created_by', 'created_by_username',
            'posted_by', 'posted_by_username', 'created_at', 'updated_at', 'posted_at'
        ]
    
    def get_account_currency_amount(self, obj):
        """Calculate amount in account currency."""
        if obj.currency == obj.cash_account.currency:
            return float(obj.amount)
        return float(obj.amount * obj.exchange_rate)
    
    def get_requires_approval_flag(self, obj):
        """Check if transaction requires approval based on amount/type."""
        # You can implement approval rules here
        return obj.amount > Decimal('10000') or obj.transaction_type in ['ADJUSTMENT']
    
    def get_can_be_posted(self, obj):
        """Check if transaction can be posted."""
        return obj.can_be_posted()
    
    def validate_transaction_date(self, value):
        """Validate transaction date."""
        # Allow future dates up to 7 days for cash transactions
        max_future_date = timezone.now().date() + timezone.timedelta(days=7)
        if value > max_future_date:
            raise serializers.ValidationError(
                "Transaction date cannot be more than 7 days in the future"
            )
        return value
    
    def validate_amount(self, value):
        """Validate transaction amount."""
        if value <= 0:
            raise serializers.ValidationError("Amount must be greater than zero")
        return value
    
    def validate(self, data):
        """Cross-field validation for cash transactions."""
        
        # Validate currency matches account currency or exchange rate provided
        cash_account = data.get('cash_account')
        currency = data.get('currency')
        exchange_rate = data.get('exchange_rate', Decimal('1.0'))
        
        if cash_account and currency:
            if currency != cash_account.currency and exchange_rate == Decimal('1.0'):
                raise serializers.ValidationError({
                    'exchange_rate': f'Exchange rate required when transaction currency ({currency}) differs from account currency ({cash_account.currency})'
                })
        
        # Validate overdraft limits
        if cash_account and data.get('transaction_type') == 'PAYMENT':
            amount = data.get('amount', Decimal('0'))
            current_balance = cash_account.get_current_balance()
            
            if not cash_account.account_type.allows_overdraft:
                if current_balance < amount:
                    raise serializers.ValidationError({
                        'amount': f'Insufficient funds. Available balance: {current_balance}'
                    })
        
        return data
    
    def create(self, validated_data):
        """Create cash transaction with proper user assignment."""
        validated_data['created_by'] = self.context['request'].user
        
        # Set currency to account currency if not specified
        if not validated_data.get('currency'):
            validated_data['currency'] = validated_data['cash_account'].currency
        
        return super().create(validated_data)


class CashTransferSerializer(serializers.ModelSerializer):
    """
    Serializer for Cash Transfers
    
    Handles transfers between cash accounts with currency conversion
    and fee calculation.
    """
    
    # Related field display names
    from_account_name = serializers.CharField(source='from_account.account_name', read_only=True)
    from_account_number = serializers.CharField(source='from_account.account_number', read_only=True)
    to_account_name = serializers.CharField(source='to_account.account_name', read_only=True)
    to_account_number = serializers.CharField(source='to_account.account_number', read_only=True)
    fee_account_name = serializers.CharField(source='fee_account.account_name', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    posted_by_username = serializers.CharField(source='posted_by.username', read_only=True)
    
    # Calculated fields
    calculated_destination_amount = serializers.SerializerMethodField()
    total_cost = serializers.SerializerMethodField()
    can_be_posted = serializers.SerializerMethodField()
    
    class Meta:
        model = CashTransfer
        fields = [
            'id', 'transfer_number', 'transfer_date',
            'from_account', 'from_account_name', 'from_account_number',
            'to_account', 'to_account_name', 'to_account_number',
            'amount', 'exchange_rate', 'destination_amount', 'calculated_destination_amount',
            'description', 'reference_number', 'memo',
            'transfer_fee', 'fee_account', 'fee_account_name', 'total_cost',
            'gl_journal_entry', 'status', 'can_be_posted',
            'created_by', 'created_by_username', 'posted_by', 'posted_by_username',
            'created_at', 'updated_at', 'posted_at'
        ]
        read_only_fields = [
            'calculated_destination_amount', 'total_cost', 'can_be_posted',
            'created_by', 'created_by_username', 'posted_by', 'posted_by_username',
            'created_at', 'updated_at', 'posted_at'
        ]
    
    def get_calculated_destination_amount(self, obj):
        """Calculate destination amount with exchange rate."""
        if obj.exchange_rate and obj.exchange_rate != 1:
            return float(obj.amount * obj.exchange_rate)
        return float(obj.amount)
    
    def get_total_cost(self, obj):
        """Calculate total cost including fees."""
        return float(obj.amount + obj.transfer_fee)
    
    def get_can_be_posted(self, obj):
        """Check if transfer can be posted."""
        return obj.can_be_posted()
    
    def validate_amount(self, value):
        """Validate transfer amount."""
        if value <= 0:
            raise serializers.ValidationError("Transfer amount must be greater than zero")
        return value
    
    def validate(self, data):
        """Cross-field validation for cash transfers."""
        
        from_account = data.get('from_account')
        to_account = data.get('to_account')
        amount = data.get('amount', Decimal('0'))
        transfer_fee = data.get('transfer_fee', Decimal('0'))
        
        # Validate accounts are different
        if from_account and to_account and from_account == to_account:
            raise serializers.ValidationError({
                'to_account': 'Source and destination accounts must be different'
            })
        
        # Validate sufficient funds
        if from_account and amount:
            current_balance = from_account.get_current_balance()
            total_cost = amount + transfer_fee
            
            if not from_account.account_type.allows_overdraft:
                if current_balance < total_cost:
                    raise serializers.ValidationError({
                        'amount': f'Insufficient funds. Available: {current_balance}, Required: {total_cost}'
                    })
        
        # Set exchange rate and destination amount for currency conversion
        if from_account and to_account:
            if from_account.currency != to_account.currency:
                exchange_rate = data.get('exchange_rate', Decimal('1.0'))
                if exchange_rate == Decimal('1.0'):
                    raise serializers.ValidationError({
                        'exchange_rate': f'Exchange rate required for transfer from {from_account.currency} to {to_account.currency}'
                    })
                data['destination_amount'] = amount * exchange_rate
            else:
                data['destination_amount'] = amount
        
        return data
    
    def create(self, validated_data):
        """Create cash transfer with proper user assignment."""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class BankStatementSerializer(serializers.ModelSerializer):
    """
    Serializer for Bank Statements
    
    Handles imported bank statements for reconciliation.
    """
    
    # Related field display names
    cash_account_name = serializers.CharField(source='cash_account.account_name', read_only=True)
    cash_account_number = serializers.CharField(source='cash_account.account_number', read_only=True)
    imported_by_username = serializers.CharField(source='imported_by.username', read_only=True)
    reconciled_by_username = serializers.CharField(source='reconciled_by.username', read_only=True)
    
    # Calculated fields
    entries_count = serializers.SerializerMethodField()
    matched_entries_count = serializers.SerializerMethodField()
    reconciliation_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = BankStatement
        fields = [
            'id', 'statement_number', 'cash_account', 'cash_account_name', 'cash_account_number',
            'statement_date', 'period_from', 'period_to',
            'opening_balance', 'closing_balance',
            'import_file', 'import_format',
            'is_reconciled', 'reconciled_by', 'reconciled_by_username', 'reconciled_at',
            'entries_count', 'matched_entries_count', 'reconciliation_percentage',
            'imported_by', 'imported_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'entries_count', 'matched_entries_count', 'reconciliation_percentage',
            'imported_by', 'imported_by_username', 'created_at', 'updated_at'
        ]
    
    def get_entries_count(self, obj):
        """Get total number of statement entries."""
        return obj.entries.count()
    
    def get_matched_entries_count(self, obj):
        """Get number of matched entries."""
        return obj.entries.filter(is_matched=True).count()
    
    def get_reconciliation_percentage(self, obj):
        """Calculate reconciliation completion percentage."""
        total = obj.entries.count()
        if total == 0:
            return 0
        matched = obj.entries.filter(is_matched=True).count()
        return round((matched / total) * 100, 1)
    
    def validate(self, data):
        """Cross-field validation for bank statements."""
        
        period_from = data.get('period_from')
        period_to = data.get('period_to')
        statement_date = data.get('statement_date')
        
        # Validate period dates
        if period_from and period_to and period_from > period_to:
            raise serializers.ValidationError({
                'period_to': 'Period end date must be after start date'
            })
        
        # Validate statement date
        if statement_date and period_to and statement_date < period_to:
            raise serializers.ValidationError({
                'statement_date': 'Statement date must be on or after period end date'
            })
        
        return data
    
    def create(self, validated_data):
        """Create bank statement with proper user assignment."""
        validated_data['imported_by'] = self.context['request'].user
        return super().create(validated_data)


class BankStatementEntrySerializer(serializers.ModelSerializer):
    """
    Serializer for Bank Statement Entries
    
    Individual entries from bank statements for reconciliation.
    """
    
    # Related field display names
    matched_transaction_number = serializers.CharField(source='matched_transaction.transaction_number', read_only=True)
    matched_by_username = serializers.CharField(source='matched_by.username', read_only=True)
    
    class Meta:
        model = BankStatementEntry
        fields = [
            'id', 'bank_statement', 'entry_date', 'entry_type',
            'amount', 'description', 'reference_number', 'bank_code',
            'is_matched', 'matched_transaction', 'matched_transaction_number',
            'matched_by', 'matched_by_username', 'matched_at',
            'running_balance'
        ]
        read_only_fields = [
            'matched_transaction_number', 'matched_by', 'matched_by_username', 'matched_at'
        ]


class CashFlowForecastSerializer(serializers.ModelSerializer):
    """
    Serializer for Cash Flow Forecasts
    
    Handles projected cash flows for planning and analysis.
    """
    
    # Related field display names
    cash_account_name = serializers.CharField(source='cash_account.account_name', read_only=True)
    cash_account_number = serializers.CharField(source='cash_account.account_number', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    # Calculated fields
    period_days = serializers.SerializerMethodField()
    daily_net_flow = serializers.SerializerMethodField()
    variance_from_actual = serializers.SerializerMethodField()
    
    class Meta:
        model = CashFlowForecast
        fields = [
            'id', 'forecast_name', 'forecast_type',
            'period_from', 'period_to', 'period_days',
            'cash_account', 'cash_account_name', 'cash_account_number',
            'projected_receipts', 'projected_payments', 'net_cash_flow', 'daily_net_flow',
            'confidence_level', 'assumptions', 'variance_from_actual',
            'is_active', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'period_days', 'daily_net_flow', 'variance_from_actual',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
    
    def get_period_days(self, obj):
        """Calculate number of days in forecast period."""
        return (obj.period_to - obj.period_from).days + 1
    
    def get_daily_net_flow(self, obj):
        """Calculate average daily net cash flow."""
        days = (obj.period_to - obj.period_from).days + 1
        if days > 0:
            return float(obj.net_cash_flow / days)
        return 0
    
    def get_variance_from_actual(self, obj):
        """Calculate variance from actual cash flows (if period has passed)."""
        # This would compare forecast vs actual - implement based on requirements
        return None
    
    def validate(self, data):
        """Cross-field validation for cash flow forecasts."""
        
        period_from = data.get('period_from')
        period_to = data.get('period_to')
        
        # Validate period dates
        if period_from and period_to and period_from > period_to:
            raise serializers.ValidationError({
                'period_to': 'Period end date must be after start date'
            })
        
        # Calculate net cash flow
        receipts = data.get('projected_receipts', Decimal('0'))
        payments = data.get('projected_payments', Decimal('0'))
        data['net_cash_flow'] = receipts - payments
        
        return data
    
    def create(self, validated_data):
        """Create cash flow forecast with proper user assignment."""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data) 