import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Divider,
  Alert,
  Snackbar,
  Autocomplete,
} from '@mui/material';
import {
  AttachFile as AttachFileIcon,
  Close as CloseIcon,
  Print as PrintIcon,
  Repeat as RepeatIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { 
  JournalLineTable,
  FormDatePicker,
  CurrencyDisplay,
  type JournalLineItem,
  type AccountOption
} from '../../../shared/components';
import { salesTaxService, type SalesTaxOption } from '../../../services/sales-tax.service';
import EmployeeService from '../../../services/employee.service';
import { Employee } from '../../../shared/types/employee.types';
import { customerService, type CustomerOption } from '../../../services/customer.service';

// Interfaces
interface ReceiptAccount {
  id: number;
  account_name: string;
  account_number: string;
  balance: number;
  currency: string;
}

interface TDSRate {
  id: number;
  name: string;
  percentage: number;
  description: string;
  section?: string;
  tds_type?: string;
  threshold_limit?: number;
}

// Use the reusable types
type GLAccount = AccountOption;
type ReceiptLine = JournalLineItem & {
  category_id: number | null;
  category_name: string;
};

interface CashReceiptData {
  payer: string;
  receipt_account_id: number | null;
  receipt_date: string;
  ref_no: string;
  tags: string[];
  memo: string;
  tds_rate_id: number | null;
  receipt_lines: ReceiptLine[];
  attachments: File[];
}

const CashReceiptForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();
  
  // Determine mode based on URL
  const mode = id 
    ? location.pathname.includes('/view') ? 'view' 
    : location.pathname.includes('/edit') ? 'edit' 
    : 'create'
    : 'create';
  
  const isViewMode = mode === 'view';
  const isEditMode = mode === 'edit';
  const isCreateMode = mode === 'create';

  // State
  const [receiptAccounts, setReceiptAccounts] = useState<ReceiptAccount[]>([]);
  const [glAccounts, setGLAccounts] = useState<GLAccount[]>([]);
  const [tdsRates, setTdsRates] = useState<TDSRate[]>([]);
  const [salesTaxes, setSalesTaxes] = useState<SalesTaxOption[]>([]);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [error] = useState<string | null>(null);
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({
    open: false,
    message: '',
    severity: 'success'
  });
  const [resetTableColumns, setResetTableColumns] = useState(false);

  // Form state
  const [formData, setFormData] = useState<CashReceiptData>({
    payer: '',
    receipt_account_id: null,
    receipt_date: dayjs().format('YYYY-MM-DD'),
    ref_no: '',
    tags: [],
    memo: '',
    tds_rate_id: null,
    receipt_lines: [
      {
        id: '1',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
      {
        id: '2',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
      {
        id: '3',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
      {
        id: '4',
        account_id: null,
        category_id: null,
        category_name: '',
        description: '',
        amount: 0,
      },
    ],
    attachments: [],
  });

  const loadCustomers = async () => {
    try {
      const customerData = await customerService.getActiveCustomers();
      console.log(`Cash Receipt: Loaded ${customerData.length} active customers`);
      setCustomers(customerData);
    } catch (error) {
      console.error('Error loading customers:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load customer list', 
        severity: 'error' 
      });
    }
  };

  const loadTransactionData = async (transactionId: string) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // First, get the transaction data
      const transactionResponse = await fetch(`http://localhost:8000/api/cms/transactions/${transactionId}/`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!transactionResponse.ok) {
        throw new Error('Failed to load transaction data');
      }

      const transactionData = await transactionResponse.json();
      console.log('Loaded transaction data:', transactionData);
      
      // Initialize receipt lines array
      let receiptLines = [{
        id: '1',
        account_id: null,
        category_id: null,
        category_name: '',
        description: transactionData.description || '',
        amount: transactionData.amount || 0,
      }];

      // If there's a linked journal entry, load the detailed lines
      if (transactionData.gl_journal_entry) {
        try {
          const journalResponse = await fetch(`http://localhost:8000/api/gl/journal-entries/${transactionData.gl_journal_entry}/`, {
            headers: {
              'Authorization': `Token ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (journalResponse.ok) {
            const journalData = await journalResponse.json();
            console.log('Loaded journal entry data:', journalData);
            
            // Convert journal lines to receipt lines (exclude the cash account line)
            const incomeLines = journalData.journal_lines?.filter((line: any) => 
              line.credit_amount > 0 && line.account.id !== transactionData.cash_account?.id
            ) || [];
            
            if (incomeLines.length > 0) {
              receiptLines = incomeLines.map((line: any, index: number) => ({
                id: `${index + 1}`,
                account_id: line.account.id,
                category_id: line.account.id,
                category_name: line.account.account_name,
                description: line.description || line.memo || '',
                amount: line.credit_amount,
                sales_tax: line.sales_tax_id || null,
                sales_tax_amount: line.sales_tax_amount || 0,
                taxable_amount: line.taxable_amount || 0,
              }));
            }
          }
        } catch (journalError) {
          console.warn('Could not load journal entry details:', journalError);
        }
      }

      // Ensure we have at least 4 lines
      while (receiptLines.length < 4) {
        receiptLines.push({
          id: `${receiptLines.length + 1}`,
          account_id: null,
          category_id: null,
          category_name: '',
          description: '',
          amount: 0,
        });
      }

      // Transform transaction data to match form structure
      console.log('Setting form data:', {
        payer: transactionData.customer || transactionData.vendor || transactionData.employee || '',
        receipt_account_id: transactionData.cash_account?.id || null,
        receipt_date: transactionData.transaction_date || dayjs().format('YYYY-MM-DD'),
        ref_no: transactionData.reference_number || '',
        memo: transactionData.memo || '',
        receipt_lines: receiptLines
      });

      setFormData({
        payer: transactionData.customer || transactionData.vendor || transactionData.employee || '',
        receipt_account_id: transactionData.cash_account?.id || null,
        receipt_date: transactionData.transaction_date || dayjs().format('YYYY-MM-DD'),
        ref_no: transactionData.reference_number || '',
        tags: [],
        memo: transactionData.memo || '',
        tds_rate_id: null,
        receipt_lines: receiptLines,
        attachments: [],
      });

    } catch (error) {
      console.error('Error loading transaction data:', error);
      setSnackbar({
        open: true,
        message: `Failed to load receipt data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        loadReceiptAccounts(),
        loadGLAccounts(),
        loadTDSRates(),
        loadSalesTaxes(),
        loadEmployees(),
        loadCustomers(),
      ]);

      // Load transaction data if in edit/view mode
      if (id && (isEditMode || isViewMode)) {
        await loadTransactionData(id);
      }
    };

    loadData();
    
    // Reset table column widths to default when component mounts
    setResetTableColumns(true);
    setTimeout(() => setResetTableColumns(false), 100);
  }, [id, isEditMode, isViewMode]);

  const loadReceiptAccounts = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/gl/cash-accounts/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Receipt accounts response:', data);
        
        // Handle the response format - check if accounts are in data.accounts or data.results
        const accounts = data.accounts || data.results || [];
        
        // Transform to consistent format
        const formattedAccounts = accounts.map((account: any) => ({
          id: account.id,
          account_name: account.account_name,
          account_number: account.account_number,
          balance: account.current_balance || account.balance || 0,
          currency: account.currency || 'USD'
        }));

        console.log('Formatted receipt accounts:', formattedAccounts);
        setReceiptAccounts(formattedAccounts);
      } else {
        console.error('Failed to load receipt accounts:', response.status);
        setSnackbar({ 
          open: true, 
          message: 'Failed to load receipt accounts', 
          severity: 'error' 
        });
      }
    } catch (error) {
      console.error('Error loading receipt accounts:', error);
      setSnackbar({ 
        open: true, 
        message: 'Error loading receipt accounts', 
        severity: 'error' 
      });
    }
  };

  const loadGLAccounts = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/gl/income-accounts/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('GL income accounts response:', data);
        
        // Handle both paginated and direct response formats
        const accounts = data.results || data || [];
        
        // Transform to consistent format
        const formattedAccounts = accounts.map((account: any) => ({
          id: account.id,
          account_number: account.account_number,
          account_name: account.account_name,
          account_type: account.account_type_name || account.account_type || '',
          detail_type: account.detail_type_name || account.detail_type || ''
        }));
        
        setGLAccounts(formattedAccounts);
      } else {
        console.error('Failed to load GL accounts:', response.statusText);
      }
    } catch (error) {
      console.error('Error loading GL accounts:', error);
    }
  };

  const loadTDSRates = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/tds/tds/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('TDS rates response:', data);
        
        const rates = data.results || data || [];
        const formattedRates = rates.map((rate: any) => ({
          id: rate.id,
          name: rate.name || rate.description,
          percentage: rate.percentage || rate.rate,
          description: rate.description,
          section: rate.section,
          tds_type: rate.tds_type,
          threshold_limit: rate.threshold_limit
        }));

        console.log('Formatted TDS rates:', formattedRates);
        setTdsRates(formattedRates);
      } else {
        console.error('Failed to load TDS rates:', response.status);
      }
    } catch (error) {
      console.error('Error loading TDS rates:', error);
    }
  };

  const loadSalesTaxes = async () => {
    try {
      const salesTaxData = await salesTaxService.getAllSalesTaxes();
      console.log(`Cash Receipt: Loaded ${salesTaxData.length} active sales taxes`);
      setSalesTaxes(salesTaxData);
    } catch (error) {
      console.error('Error loading sales taxes:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load sales tax list', 
        severity: 'error' 
      });
    }
  };

  const loadEmployees = async () => {
    try {
      const employeeData = await EmployeeService.getActiveEmployees();
      console.log(`Cash Receipt: Loaded ${employeeData.length} active employees`);
      setEmployees(employeeData);
    } catch (error) {
      console.error('Error loading employees:', error);
      setSnackbar({ 
        open: true, 
        message: 'Failed to load employee list', 
        severity: 'error' 
      });
    }
  };

  const getBaseAmount = () => {
    return formData.receipt_lines.reduce((sum, line) => sum + (line.amount || 0), 0);
  };

  const getTotalSalesTax = () => {
    return formData.receipt_lines.reduce((sum, line) => sum + (line.sales_tax_amount || 0), 0);
  };

  const getTotalAmount = () => {
    return getBaseAmount() + getTotalSalesTax();
  };

  const getTDSAmount = () => {
    if (!formData.tds_rate_id) return 0;
    const tdsRate = tdsRates.find(rate => rate.id === formData.tds_rate_id);
    if (!tdsRate) return 0;
    return (getTotalAmount() * tdsRate.percentage) / 100;
  };

  const getNetReceipt = () => {
    return getTotalAmount() - getTDSAmount();
  };

  const handleAddLine = () => {
    const newLine: ReceiptLine = {
      id: `${formData.receipt_lines.length + 1}`,
      account_id: null,
      category_id: null,
      category_name: '',
      description: '',
      amount: 0,
    };
    setFormData(prev => ({
      ...prev,
      receipt_lines: [...prev.receipt_lines, newLine]
    }));
  };

  const handleRemoveLine = (lineId: string) => {
    if (formData.receipt_lines.length > 1) {
      setFormData(prev => ({
        ...prev,
        receipt_lines: prev.receipt_lines.filter(line => line.id !== lineId)
      }));
    }
  };

  const handleLineChange = (lineId: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      receipt_lines: prev.receipt_lines.map(line => {
        if (line.id === lineId) {
          const updatedLine = { 
            ...line, 
            // Handle other fields normally (except account_selection)
            ...(field !== 'account_selection' && { [field]: value })
          };

          // Handle new account_selection field from JournalLineTable
          if (field === 'account_selection') {
            updatedLine.account_id = value.account_id;
            updatedLine.category_id = value.account_id;
            updatedLine.category_name = value.account_name;
          }
          // Handle account selection (legacy support)
          else if (field === 'account_id' || field === 'category_id') {
            const selectedAccount = glAccounts.find(acc => acc.id === value);
            if (selectedAccount) {
              updatedLine.category_id = selectedAccount.id;
              updatedLine.category_name = selectedAccount.account_name;
              updatedLine.account_id = selectedAccount.id;
            } else {
              updatedLine.category_id = null;
              updatedLine.category_name = '';
              updatedLine.account_id = null;
            }
          }

          // Handle sales tax calculations
          if (field === 'sales_tax') {
            const selectedSalesTax = salesTaxes.find(tax => Number(tax.id) === Number(value));
            if (selectedSalesTax && updatedLine.amount > 0) {
              updatedLine.taxable_amount = updatedLine.amount;
              updatedLine.sales_tax_amount = (updatedLine.amount * selectedSalesTax.rate) / 100;
              updatedLine.sales_tax_description = selectedSalesTax.description;
              updatedLine.sales_tax_rate = selectedSalesTax.rate;
            } else {
              updatedLine.sales_tax_amount = 0;
              updatedLine.taxable_amount = 0;
              updatedLine.sales_tax_description = '';
              updatedLine.sales_tax_rate = 0;
            }
          }

          // Recalculate sales tax when amount changes
          if (field === 'amount' && updatedLine.sales_tax) {
            const selectedSalesTax = salesTaxes.find(tax => Number(tax.id) === Number(updatedLine.sales_tax));
            if (selectedSalesTax && updatedLine.amount > 0) {
              updatedLine.taxable_amount = updatedLine.amount;
              updatedLine.sales_tax_amount = (updatedLine.amount * selectedSalesTax.rate) / 100;
              updatedLine.sales_tax_description = selectedSalesTax.description;
              updatedLine.sales_tax_rate = selectedSalesTax.rate;
            } else {
              updatedLine.sales_tax_amount = 0;
              updatedLine.taxable_amount = 0;
              updatedLine.sales_tax_description = '';
              updatedLine.sales_tax_rate = 0;
            }
          }

          return updatedLine;
        }
        return line;
      }),
    }));
  };

  const formatCurrency = (amount: number, currencyCode?: string) => {
    const currency = currencyCode || currencyInfo?.functional_currency || 'USD';
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  const handleSave = async () => {
    const validLines = formData.receipt_lines.filter(line => line.category_id && line.amount > 0);

    if (!formData.payer) {
      setSnackbar({ open: true, message: '❌ Payer is required', severity: 'error' });
      return;
    }

    if (!formData.receipt_account_id) {
      setSnackbar({ open: true, message: '❌ Receipt account is required', severity: 'error' });
      return;
    }

    if (validLines.length === 0) {
      setSnackbar({ open: true, message: '❌ At least one valid receipt line is required', severity: 'error' });
      return;
    }

    if (!formData.receipt_date) {
      setSnackbar({ open: true, message: '❌ Receipt date is required', severity: 'error' });
      return;
    }

    setLoading(true);
    console.log('=== USING NEW RECEIPT API ===');
    console.log('Sending base amounts + tax selections to backend for automatic calculation');
    
    try {
      const token = localStorage.getItem('token');
      console.log('Token:', token ? 'Present' : 'Missing');
      
      // Prepare receipt data for new API
      const receiptData = {
        payer: formData.payer,
        receipt_account_id: formData.receipt_account_id,
        receipt_date: formData.receipt_date,
        reference_number: formData.ref_no || '',
        memo: formData.memo || '',
        tds_rate_id: formData.tds_rate_id,
        receipt_lines: validLines.map(line => ({
          category_id: line.category_id,
          amount: line.amount, // Base amount only
          description: line.description || `Receipt from ${formData.payer}`,
          memo: line.description || '',
          sales_tax_id: line.sales_tax,
          sales_tax_rate: line.sales_tax_rate
        }))
      };

      console.log('Receipt data to send:', JSON.stringify(receiptData, null, 2));
      
      // Call new receipt creation API
      const response = await fetch('http://localhost:8000/api/cms/api/receipts/create/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(receiptData),
      });
      
      console.log('Response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('Receipt created successfully:', result);
        
        const calculations = result.calculations;
        
        // Show detailed breakdown in alert
        alert(`✅ SUCCESS: Receipt created as DRAFT!

Journal Entry: ${result.entry_number}
Status: ${result.journal_entry_status}

💰 CALCULATION BREAKDOWN:
Base Amount: ${formatCurrency(calculations.base_amount)}
Sales Tax: ${formatCurrency(calculations.sales_tax_amount)}
Total Amount: ${formatCurrency(calculations.total_amount)}
TDS Deduction: ${formatCurrency(calculations.tds_amount)}
Net Receipt: ${formatCurrency(calculations.net_receipt)}

The receipt has been created as DRAFT. Review and post it from the transactions page when ready.`);

        setSnackbar({
          open: true,
          message: isEditMode 
            ? `✅ SUCCESS: Receipt updated!`
            : `✅ SUCCESS: Receipt created as DRAFT! Entry #${result.entry_number} - Ready for posting`,
          severity: 'success'
        });

        // Reset form only if creating new receipt
        if (isCreateMode) {
          setFormData({
            payer: '',
            receipt_account_id: null,
            receipt_date: dayjs().format('YYYY-MM-DD'),
            ref_no: '',
            tags: [],
            memo: '',
            tds_rate_id: null,
            receipt_lines: [
              {
                id: '1',
                account_id: null,
                category_id: null,
                category_name: '',
                amount: 0,
                description: '',
              },
              {
                id: '2',
                account_id: null,
                category_id: null,
                category_name: '',
                amount: 0,
                description: '',
              },
              {
                id: '3',
                account_id: null,
                category_id: null,
                category_name: '',
                amount: 0,
                description: '',
              },
              {
                id: '4',
                account_id: null,
                category_id: null,
                category_name: '',
                amount: 0,
                description: '',
              },
            ],
            attachments: []
          });
        }

      } else {
        const errorData = await response.text();
        console.error('Failed to create receipt. Status:', response.status);
        console.error('Response body:', errorData);
        
        let parsedError;
        try {
          parsedError = JSON.parse(errorData);
        } catch {
          parsedError = { error: errorData };
        }
        
        const errorMessage = parsedError.error || parsedError.message || errorData;
        setSnackbar({
          open: true,
          message: `❌ ERROR: ${errorMessage}`,
          severity: 'error'
        });
        
        alert(`❌ ERROR: Failed to save receipt\n\nDetails: ${errorMessage}`);
      }
      
    } catch (error) {
      console.error('Error creating receipt:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setSnackbar({
        open: true,
        message: `❌ ERROR: ${errorMessage}`,
        severity: 'error'
      });
      
      alert(`❌ ERROR: Failed to save receipt\n\nError: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAndClose = async () => {
    await handleSave();
    if (!error) {
      navigate('/dashboard/cms/transactions');
    }
  };

  const handleCancel = () => {
    navigate('/dashboard/cms/transactions');
  };

  if (currencyLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <>
      {/* Full Screen Layout */}
      <Box
        sx={{
          position: 'fixed',
          top: '64px', // Account for navigation bar height
          left: 0,
          right: 0,
          bottom: 0,
          bgcolor: 'background.default',
          zIndex: 1200,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderBottom: 1,
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" color="success.main">
              {isViewMode ? 'VIEW CASH RECEIPT' : isEditMode ? 'EDIT CASH RECEIPT' : 'CASH RECEIPT FORM'}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {isViewMode ? 'View cash receipt details' : isEditMode ? 'Edit cash receipt information' : 'Record cash receipts and income efficiently'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            {/* Amount Display */}
            <Box sx={{ textAlign: 'right', minWidth: '200px' }}>
              <CurrencyDisplay 
                value={getNetReceipt()}
                currencyCode={currencyInfo?.functional_currency}
                variant="h4"
                color="success.main"
                sx={{ fontWeight: 'bold' }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Net Receipt Amount
              </Typography>
            </Box>
            <IconButton 
              onClick={handleCancel} 
              sx={{ 
                color: 'text.primary',
                '&:hover': {
                  backgroundColor: 'action.hover',
                  color: 'error.main'
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          <Grid container spacing={3}>
            {/* Main Form - Full Width */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Grid container spacing={3}>
                    {/* Basic Information */}
                    <Grid item xs={12} md={4}>
                      <Autocomplete
                        fullWidth
                        freeSolo
                        disabled={isViewMode}
                        options={[
                          ...employees.map(emp => ({ ...emp, type: 'employee' })),
                          ...customers.map(cust => ({ ...cust, type: 'customer' }))
                        ]}
                        groupBy={(option) => option.type === 'employee' ? 'Employees' : 'Customers'}
                        getOptionLabel={(option) => {
                          if (typeof option === 'string') {
                            return option;
                          }
                          return option.type === 'employee' ? (option as any).full_name : (option as any).fullName;
                        }}
                        value={formData.payer}
                        onChange={(event, newValue) => {
                          if (typeof newValue === 'string') {
                            setFormData(prev => ({ ...prev, payer: newValue }));
                          } else if (newValue) {
                            const name = newValue.type === 'employee' ? (newValue as any).full_name : (newValue as any).fullName;
                            setFormData(prev => ({ ...prev, payer: name }));
                          } else {
                            setFormData(prev => ({ ...prev, payer: '' }));
                          }
                        }}
                        onInputChange={(event, newInputValue) => {
                          setFormData(prev => ({ ...prev, payer: newInputValue }));
                        }}
                        renderInput={(params) => (
                          <TextField 
                            {...params} 
                            label="Payer"
                            placeholder="Who paid you? (Type or select from employees/customers)"
                            required
                          />
                        )}
                        renderOption={(props, option) => (
                          <Box component="li" {...props}>
                            <Box>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {option.type === 'employee' ? (option as any).full_name : (option as any).fullName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {option.type === 'employee' 
                                  ? `${'position' in option ? option.position : ''} - ${'department' in option ? option.department : ''}`
                                  : `${'customerType' in option ? option.customerType : ''} - ${option.email || 'No email'}`
                                }
                              </Typography>
                            </Box>
                          </Box>
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormDatePicker
                        label="Receipt Date"
                        value={dayjs(formData.receipt_date)}
                        onChange={(date) => setFormData(prev => ({ ...prev, receipt_date: date?.format('YYYY-MM-DD') || '' }))}
                        required
                        disabled={isViewMode}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <FormControl fullWidth required disabled={isViewMode}>
                        <InputLabel>Receipt Account</InputLabel>
                        <Select
                          value={formData.receipt_account_id || ''}
                          onChange={(e) => setFormData(prev => ({ ...prev, receipt_account_id: Number(e.target.value) }))}
                          label="Receipt Account"
                        >
                          {receiptAccounts.length === 0 ? (
                            <MenuItem disabled>
                              <em>No cash accounts found</em>
                            </MenuItem>
                          ) : (
                            receiptAccounts.map((account) => (
                              <MenuItem key={account.id} value={account.id}>
                                <Box>
                                  <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                                    {account.account_number} - {account.account_name}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                    Balance: {formatCurrency(account.balance, account.currency)}
                                  </Typography>
                                </Box>
                              </MenuItem>
                            ))
                          )}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={2}>
                      <TextField
                        fullWidth
                        label="Ref No."
                        value={formData.ref_no}
                        onChange={(e) => setFormData(prev => ({ ...prev, ref_no: e.target.value }))}
                        disabled={isViewMode}
                      />
                    </Grid>

                    {/* Tags */}
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label="Tags"
                        multiline
                        rows={2}
                        value={formData.tags.join(', ')}
                        onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value.split(',').map(tag => tag.trim()) }))}
                        placeholder="Separate with commas"
                        disabled={isViewMode}
                      />
                    </Grid>

                    {/* Amount Summary - Integrated */}
                    <Grid item xs={12} md={6}>
                      <Card variant="outlined" sx={{ bgcolor: '#f0f8f0', p: 2 }}>
                        <Typography variant="h6" gutterBottom color="success.main">
                          Amount Summary
                        </Typography>
                        
                        <Box sx={{ mb: 2 }}>
                          {/* Base Amount */}
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography color="text.secondary">Base Amount:</Typography>
                            <CurrencyDisplay 
                              value={getBaseAmount()}
                              currencyCode={currencyInfo?.functional_currency}
                              color="text.secondary"
                            />
                          </Box>

                          {/* Sales Tax (if any) */}
                          {getTotalSalesTax() > 0 && (
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography color="text.secondary">Sales Tax:</Typography>
                              <CurrencyDisplay 
                                value={getTotalSalesTax()}
                                currencyCode={currencyInfo?.functional_currency}
                                color="info.main"
                                sx={{ fontWeight: 'bold' }}
                              />
                            </Box>
                          )}

                          <Divider sx={{ my: 1 }} />

                          {/* Total Amount (Base + Sales Tax) */}
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="h6" fontWeight="bold">Total Amount:</Typography>
                            <CurrencyDisplay 
                              value={getTotalAmount()}
                              currencyCode={currencyInfo?.functional_currency}
                              variant="h6"
                              color="success.main"
                              sx={{ fontWeight: 'bold' }}
                            />
                          </Box>

                          {/* TDS Section */}
                          <FormControl fullWidth size="small" sx={{ mb: 1 }} disabled={isViewMode}>
                            <InputLabel>TDS Rate</InputLabel>
                            <Select
                              value={formData.tds_rate_id || ''}
                              onChange={(e) => setFormData(prev => ({ ...prev, tds_rate_id: Number(e.target.value) || null }))}
                              label="TDS Rate"
                            >
                              <MenuItem value="">
                                <em>No TDS</em>
                              </MenuItem>
                              {tdsRates.map((rate) => (
                                <MenuItem key={rate.id} value={rate.id}>
                                  <Box>
                                    <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                                      {rate.description} - {rate.percentage}%
                                    </Typography>
                                    {rate.section && (
                                      <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                                        Section: {rate.section}
                                      </Typography>
                                    )}
                                  </Box>
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>

                          {formData.tds_rate_id && (
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography color="text.secondary">
                                TDS ({tdsRates.find(r => r.id === formData.tds_rate_id)?.percentage}%):
                              </Typography>
                              <CurrencyDisplay 
                                value={-getTDSAmount()}
                                currencyCode={currencyInfo?.functional_currency}
                                color="error.main"
                                sx={{ fontWeight: 'bold' }}
                              />
                            </Box>
                          )}

                          <Divider sx={{ my: 1 }} />
                          
                          <Box display="flex" justifyContent="space-between">
                            <Typography variant="h5" fontWeight="bold">Net Amount:</Typography>
                            <CurrencyDisplay 
                              value={getNetReceipt()}
                              currencyCode={currencyInfo?.functional_currency}
                              variant="h5"
                              color="success.main"
                              sx={{ fontWeight: 'bold' }}
                            />
                          </Box>
                        </Box>
                      </Card>
                    </Grid>

                    {/* Line Items */}
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom sx={{ mb: 1 }}>
                        Receipt Details
                      </Typography>
                      <JournalLineTable
                        tableMode="payment"
                        lines={formData.receipt_lines}
                        accounts={glAccounts}
                        salesTaxes={salesTaxes}
                        onLineChange={handleLineChange}
                        onAddLine={handleAddLine}
                        onRemoveLine={handleRemoveLine}
                        currencySymbol={currencyInfo?.functional_currency_symbol || '$'}
                        accountColumnLabel="Account"
                        accountPlaceholder="Select Account"
                        descriptionPlaceholder="Enter description"
                        salesTaxPlaceholder="Select Sales Tax"
                        minLines={4}
                        resetColumnWidths={resetTableColumns}
                        tableHeight="300px"
                        readOnly={isViewMode}
                      />
                    </Grid>

                    {/* Memo */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Memo"
                        multiline
                        rows={3}
                        value={formData.memo}
                        onChange={(e) => setFormData(prev => ({ ...prev, memo: e.target.value }))}
                        placeholder="Add any additional notes..."
                        disabled={isViewMode}
                      />
                    </Grid>

                    {/* Attachments */}
                    {!isViewMode && (
                      <Grid item xs={12}>
                        <Button
                          startIcon={<AttachFileIcon />}
                          variant="outlined"
                          component="label"
                        >
                          Attach Files
                          <input type="file" hidden multiple />
                        </Button>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>

        {/* Footer Actions */}
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderTop: 1,
            borderColor: 'divider',
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Button
            startIcon={<CancelIcon />}
            onClick={handleCancel}
            color="inherit"
          >
            {isViewMode ? 'Close' : 'Cancel'}
          </Button>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              color="inherit"
            >
              Print
            </Button>
            {!isViewMode && (
              <>
                <Button
                  startIcon={<RepeatIcon />}
                  variant="outlined"
                  color="inherit"
                >
                  Make Recurring
                </Button>
                <Button
                  startIcon={<SaveIcon />}
                  variant="outlined"
                  onClick={handleSave}
                  disabled={loading}
                >
                  Save
                </Button>
                <Button
                  startIcon={<SaveAltIcon />}
                  variant="contained"
                  onClick={handleSaveAndClose}
                  disabled={loading}
                  sx={{ 
                    background: 'linear-gradient(45deg, #2e7d32 30%, #4caf50 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #1b5e20 30%, #2e7d32 90%)',
                    }
                  }}
                >
                  Save and Close
                </Button>
              </>
            )}
          </Box>
        </Box>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={10000}
          onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        >
          <Alert 
            onClose={() => setSnackbar(prev => ({ ...prev, open: false }))} 
            severity={snackbar.severity}
            sx={{ 
              fontSize: '1rem',
              fontWeight: 'bold',
              minWidth: '400px'
            }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </>
  );
};

export default CashReceiptForm; 