import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface PaymentTerm {
  id: number;
  name: string;
  code: string;
  days: number;
  description?: string;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: number;
}

interface PaymentTermsContextType {
  paymentTerms: PaymentTerm[];
  loading: boolean;
  error: string | null;
  loadPaymentTerms: () => Promise<void>;
  addPaymentTerm: (paymentTerm: Omit<PaymentTerm, 'id' | 'created_at' | 'updated_at' | 'created_by'>) => Promise<PaymentTerm>;
  updatePaymentTerm: (id: number, paymentTerm: Partial<PaymentTerm>) => Promise<PaymentTerm>;
  deletePaymentTerm: (id: number) => Promise<void>;
  getDefaultPaymentTerm: () => PaymentTerm | null;
  setDefaultPaymentTerm: (id: number) => Promise<void>;
}

const PaymentTermsContext = createContext<PaymentTermsContextType>({
  paymentTerms: [],
  loading: false,
  error: null,
  loadPaymentTerms: async () => {},
  addPaymentTerm: async () => ({} as PaymentTerm),
  updatePaymentTerm: async () => ({} as PaymentTerm),
  deletePaymentTerm: async () => {},
  getDefaultPaymentTerm: () => null,
  setDefaultPaymentTerm: async () => {},
});

interface PaymentTermsProviderProps {
  children: ReactNode;
}

export const PaymentTermsProvider: React.FC<PaymentTermsProviderProps> = ({ children }) => {
  const [paymentTerms, setPaymentTerms] = useState<PaymentTerm[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getAuthHeaders = () => {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Token ${token}`,
      'Content-Type': 'application/json',
    };
  };

  const loadPaymentTerms = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Loading payment terms from API...');
      
      // Try with auth first, then without auth if it fails
      let response;
      try {
        response = await fetch('http://localhost:8000/api/sales/payment-terms/', {
          headers: getAuthHeaders(),
        });
      } catch (authError) {
        console.log('Auth request failed, trying without auth:', authError);
        response = await fetch('http://localhost:8000/api/sales/payment-terms/');
      }

      console.log('Payment terms API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Payment terms API error response:', errorText);
        throw new Error(`Failed to load payment terms: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Payment terms API response data:', data);
      
      // Handle paginated response from DRF
      const terms = Array.isArray(data) ? data : (data.results || []);
      console.log('Setting payment terms:', terms);
      setPaymentTerms(terms);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load payment terms');
      console.error('Load payment terms error:', err);
    } finally {
      setLoading(false);
    }
  };

  const addPaymentTerm = async (paymentTermData: Omit<PaymentTerm, 'id' | 'created_at' | 'updated_at' | 'created_by'>): Promise<PaymentTerm> => {
    try {
      setError(null);

      const response = await fetch('http://localhost:8000/api/sales/payment-terms/', {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(paymentTermData),
      });

      if (!response.ok) {
        throw new Error('Failed to add payment term');
      }

      const newPaymentTerm = await response.json();
      setPaymentTerms(prev => [...prev, newPaymentTerm]);
      return newPaymentTerm;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add payment term';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updatePaymentTerm = async (id: number, paymentTermData: Partial<PaymentTerm>): Promise<PaymentTerm> => {
    try {
      setError(null);

      const response = await fetch(`http://localhost:8000/api/sales/payment-terms/${id}/`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify(paymentTermData),
      });

      if (!response.ok) {
        throw new Error('Failed to update payment term');
      }

      const updatedPaymentTerm = await response.json();
      setPaymentTerms(prev => prev.map(term => term.id === id ? updatedPaymentTerm : term));
      return updatedPaymentTerm;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update payment term';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deletePaymentTerm = async (id: number): Promise<void> => {
    try {
      setError(null);

      const response = await fetch(`http://localhost:8000/api/sales/payment-terms/${id}/`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to delete payment term');
      }

      setPaymentTerms(prev => prev.filter(term => term.id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete payment term';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const getDefaultPaymentTerm = (): PaymentTerm | null => {
    return paymentTerms.find(term => term.is_default) || null;
  };

  const setDefaultPaymentTerm = async (id: number): Promise<void> => {
    try {
      setError(null);

      const response = await fetch(`http://localhost:8000/api/sales/payment-terms/${id}/set_default/`, {
        method: 'POST',
        headers: getAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error('Failed to set default payment term');
      }

      const updatedTerm = await response.json();
      
      // Update local state
      setPaymentTerms(prev => prev.map(term => ({
        ...term,
        is_default: term.id === id
      })));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set default payment term';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Load payment terms on mount
  useEffect(() => {
    loadPaymentTerms();
  }, []);

  const value: PaymentTermsContextType = {
    paymentTerms,
    loading,
    error,
    loadPaymentTerms,
    addPaymentTerm,
    updatePaymentTerm,
    deletePaymentTerm,
    getDefaultPaymentTerm,
    setDefaultPaymentTerm,
  };

  return (
    <PaymentTermsContext.Provider value={value}>
      {children}
    </PaymentTermsContext.Provider>
  );
};

export const usePaymentTerms = (): PaymentTermsContextType => {
  const context = useContext(PaymentTermsContext);
  if (!context) {
    throw new Error('usePaymentTerms must be used within a PaymentTermsProvider');
  }
  return context;
}; 