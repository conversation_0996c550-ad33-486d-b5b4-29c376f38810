from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from sales.models import PaymentTerm


class Command(BaseCommand):
    help = 'Create default payment terms'

    def handle(self, *args, **options):
        # Get first admin user or create a default one
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.filter(is_staff=True).first()

        # Default payment terms
        terms = [
            {'name': 'Due on Receipt', 'code': 'due_on_receipt', 'days': 0, 'is_default': True, 'description': 'Payment due immediately upon receipt'},
            {'name': 'Net 7', 'code': 'net_7', 'days': 7, 'description': 'Payment due within 7 days'},
            {'name': 'Net 15', 'code': 'net_15', 'days': 15, 'description': 'Payment due within 15 days'},
            {'name': 'Net 30', 'code': 'net_30', 'days': 30, 'description': 'Payment due within 30 days'},
            {'name': 'Net 45', 'code': 'net_45', 'days': 45, 'description': 'Payment due within 45 days'},
            {'name': 'Net 60', 'code': 'net_60', 'days': 60, 'description': 'Payment due within 60 days'},
            {'name': 'Net 90', 'code': 'net_90', 'days': 90, 'description': 'Payment due within 90 days'},
        ]

        created_count = 0
        for term_data in terms:
            term, created = PaymentTerm.objects.get_or_create(
                code=term_data['code'],
                defaults={
                    'name': term_data['name'],
                    'days': term_data['days'],
                    'description': term_data['description'],
                    'is_default': term_data.get('is_default', False),
                    'is_active': True,
                    'created_by': admin_user
                }
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created payment term: {term.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Payment term already exists: {term.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} payment terms')
        )
        self.stdout.write(f'Total payment terms in database: {PaymentTerm.objects.count()}') 