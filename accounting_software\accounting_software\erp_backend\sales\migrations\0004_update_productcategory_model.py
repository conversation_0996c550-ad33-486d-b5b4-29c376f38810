# Generated by custom migration to fix ProductCategory model

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


def populate_category_codes(apps, schema_editor):
    """Populate unique codes for existing categories"""
    ProductCategory = apps.get_model('sales', 'ProductCategory')
    
    # Create unique codes based on category names
    code_mapping = {
        'Software': 'SW',
        'Hardware': 'HW', 
        'Services': 'SV',
        'Consulting': 'CS',
        'Training': 'TR'
    }
    
    for category in ProductCategory.objects.all():
        if category.name in code_mapping:
            category.code = code_mapping[category.name]
        else:
            # Fallback: use first 3 characters of name, uppercase
            category.code = category.name[:3].upper()
        category.save()


def reverse_populate_codes(apps, schema_editor):
    """Reverse migration - not needed since we're adding fields"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0003_alter_customer_payment_terms'),
    ]

    operations = [
        # Step 1: Change Meta options
        migrations.AlterModelOptions(
            name='productcategory',
            options={'ordering': ['sort_order', 'name'], 'verbose_name_plural': 'Product Categories'},
        ),
        
        # Step 2: Add code field as nullable first
        migrations.AddField(
            model_name='productcategory',
            name='code',
            field=models.CharField(help_text='Short code for the category (e.g., FRT, VEG)', max_length=20, null=True, blank=True),
        ),
        
        # Step 3: Add other nullable fields
        migrations.AddField(
            model_name='productcategory',
            name='level',
            field=models.PositiveIntegerField(default=1, help_text='Category hierarchy level'),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='division_type',
            field=models.CharField(choices=[('perishable', 'Perishable'), ('refrigerated', 'Refrigerated'), ('frozen', 'Frozen'), ('controlled-substance', 'Controlled Substance'), ('non-perishable', 'Non-Perishable')], default='non-perishable', max_length=30),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='image_url',
            field=models.URLField(blank=True, help_text='Category image URL', null=True),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='tax_category',
            field=models.CharField(blank=True, help_text='Default tax category for products', max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='margin_percentage',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Default profit margin %', max_digits=5),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='sort_order',
            field=models.PositiveIntegerField(default=1, help_text='Display order'),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='allow_subcategories',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='requires_expiry_tracking',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='requires_batch_tracking',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='default_unit_of_measure',
            field=models.CharField(choices=[('piece', 'Piece'), ('kg', 'Kilogram'), ('gram', 'Gram'), ('liter', 'Liter'), ('ml', 'Milliliter'), ('box', 'Box'), ('pack', 'Pack'), ('dozen', 'Dozen'), ('meter', 'Meter'), ('cm', 'Centimeter')], default='piece', max_length=20),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='productcategory',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='categories_created', to=settings.AUTH_USER_MODEL),
        ),
        
        # Step 4: Populate the codes for existing categories
        migrations.RunPython(populate_category_codes, reverse_populate_codes),
        
        # Step 5: Make code field unique and not null
        migrations.AlterField(
            model_name='productcategory',
            name='code',
            field=models.CharField(help_text='Short code for the category (e.g., FRT, VEG)', max_length=20, unique=True),
        ),
        
        # Step 6: Update foreign key constraints
        migrations.AlterField(
            model_name='product',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='sales.productcategory'),
        ),
        migrations.AlterField(
            model_name='productcategory',
            name='parent_category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='sales.productcategory'),
        ),
        
        # Step 7: Add unique constraint
        migrations.AlterUniqueTogether(
            name='productcategory',
            unique_together={('parent_category', 'name')},
        ),
    ] 