import React from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Visibility, GetApp } from '@mui/icons-material';

const RecentSales: React.FC = () => {
  const sales = [
    {
      id: 'INV-001',
      customer: 'Tech Solutions Ltd',
      date: '2024-03-10',
      amount: 2500.00,
      status: 'Paid',
    },
    {
      id: 'INV-002',
      customer: 'Global Traders Inc',
      date: '2024-03-09',
      amount: 1800.00,
      status: 'Pending',
    },
    {
      id: 'INV-003',
      customer: 'ABC Corporation',
      date: '2024-03-08',
      amount: 3200.00,
      status: 'Paid',
    },
    {
      id: 'INV-004',
      customer: 'XYZ Industries',
      date: '2024-03-07',
      amount: 1500.00,
      status: 'Overdue',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'success';
      case 'pending':
        return 'warning';
      case 'overdue':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
        Recent Sales
      </Typography>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Invoice ID</TableCell>
            <TableCell>Customer</TableCell>
            <TableCell>Date</TableCell>
            <TableCell align="right">Amount</TableCell>
            <TableCell>Status</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sales.map((sale) => (
            <TableRow key={sale.id}>
              <TableCell>{sale.id}</TableCell>
              <TableCell>{sale.customer}</TableCell>
              <TableCell>{sale.date}</TableCell>
              <TableCell align="right">
                ${sale.amount.toFixed(2)}
              </TableCell>
              <TableCell>
                <Chip
                  label={sale.status}
                  color={getStatusColor(sale.status) as any}
                  size="small"
                />
              </TableCell>
              <TableCell align="right">
                <Tooltip title="View">
                  <IconButton size="small">
                    <Visibility fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Download">
                  <IconButton size="small">
                    <GetApp fontSize="small" />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Box>
  );
};

export default RecentSales;