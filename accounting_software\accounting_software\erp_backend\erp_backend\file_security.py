"""
File Upload Security Utilities

This module provides comprehensive file upload validation and security measures
to prevent malicious file uploads and ensure data integrity.
"""

import os
import magic
import hashlib
import logging
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from typing import List, Optional

logger = logging.getLogger(__name__)

class FileSecurityValidator:
    """
    Comprehensive file upload security validator
    """
    
    # Dangerous file extensions that should never be allowed
    DANGEROUS_EXTENSIONS = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs',
        '.js', '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi',
        '.php', '.asp', '.aspx', '.jsp', '.cgi', '.pl', '.py',
        '.sh', '.bash', '.ps1', '.psm1'
    ]
    
    # MIME types that are allowed
    ALLOWED_MIME_TYPES = {
        # Documents
        'application/pdf': ['.pdf'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        'application/vnd.ms-excel': ['.xls'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        'application/vnd.ms-powerpoint': ['.ppt'],
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
        
        # Images
        'image/jpeg': ['.jpg', '.jpeg'],
        'image/png': ['.png'],
        'image/gif': ['.gif'],
        'image/bmp': ['.bmp'],
        'image/svg+xml': ['.svg'],
        
        # Text files
        'text/plain': ['.txt'],
        'text/csv': ['.csv'],
        
        # Archives (be careful with these)
        'application/zip': ['.zip'],
        'application/x-rar-compressed': ['.rar'],
    }
    
    def __init__(self, max_size: int = None):
        self.max_size = max_size or getattr(settings, 'MAX_UPLOAD_SIZE', 20 * 1024 * 1024)
        self.allowed_extensions = getattr(settings, 'ALLOWED_FILE_EXTENSIONS', [])
    
    def validate_file(self, uploaded_file) -> dict:
        """
        Comprehensive file validation
        
        Args:
            uploaded_file: Django UploadedFile object
            
        Returns:
            dict: Validation result with 'valid' boolean and 'errors' list
        """
        errors = []
        
        try:
            # 1. Check file size
            if uploaded_file.size > self.max_size:
                errors.append(
                    f"File size ({self._format_bytes(uploaded_file.size)}) exceeds "
                    f"maximum allowed size ({self._format_bytes(self.max_size)})"
                )
            
            # 2. Check file extension
            file_extension = os.path.splitext(uploaded_file.name)[1].lower()
            
            if file_extension in self.DANGEROUS_EXTENSIONS:
                errors.append(f"File extension '{file_extension}' is not allowed for security reasons")
            
            if self.allowed_extensions and file_extension not in self.allowed_extensions:
                errors.append(f"File extension '{file_extension}' is not in allowed list")
            
            # 3. Validate MIME type
            mime_type = self._get_mime_type(uploaded_file)
            if mime_type not in self.ALLOWED_MIME_TYPES:
                errors.append(f"File type '{mime_type}' is not allowed")
            
            # 4. Check MIME type matches extension
            expected_extensions = self.ALLOWED_MIME_TYPES.get(mime_type, [])
            if expected_extensions and file_extension not in expected_extensions:
                errors.append(
                    f"File extension '{file_extension}' does not match "
                    f"detected file type '{mime_type}'"
                )
            
            # 5. Scan for malicious content
            malware_check = self._scan_for_malware(uploaded_file)
            if not malware_check['safe']:
                errors.extend(malware_check['errors'])
            
            # 6. Validate file integrity
            integrity_check = self._validate_file_integrity(uploaded_file)
            if not integrity_check['valid']:
                errors.extend(integrity_check['errors'])
            
            # Log the validation attempt
            logger.info(
                f"File validation: {uploaded_file.name} "
                f"({self._format_bytes(uploaded_file.size)}) - "
                f"{'PASSED' if not errors else 'FAILED'}"
            )
            
            if errors:
                logger.warning(f"File validation failed for {uploaded_file.name}: {errors}")
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'file_info': {
                    'name': uploaded_file.name,
                    'size': uploaded_file.size,
                    'mime_type': mime_type,
                    'extension': file_extension,
                    'checksum': self._calculate_checksum(uploaded_file)
                }
            }
            
        except Exception as e:
            logger.error(f"Error during file validation: {str(e)}")
            return {
                'valid': False,
                'errors': [f"File validation error: {str(e)}"],
                'file_info': None
            }
    
    def _get_mime_type(self, uploaded_file) -> str:
        """
        Get the real MIME type of the file using python-magic
        """
        try:
            # Read first 1KB for MIME type detection
            uploaded_file.seek(0)
            file_start = uploaded_file.read(1024)
            uploaded_file.seek(0)
            
            # Use python-magic to detect MIME type
            mime_type = magic.from_buffer(file_start, mime=True)
            return mime_type
        except Exception as e:
            logger.warning(f"Could not detect MIME type: {str(e)}")
            return uploaded_file.content_type or 'application/octet-stream'
    
    def _scan_for_malware(self, uploaded_file) -> dict:
        """
        Basic malware scanning (can be extended with antivirus integration)
        """
        try:
            uploaded_file.seek(0)
            content = uploaded_file.read(1024)  # Read first 1KB
            uploaded_file.seek(0)
            
            # Check for suspicious patterns
            suspicious_patterns = [
                b'<script',
                b'javascript:',
                b'vbscript:',
                b'<?php',
                b'<%',
                b'eval(',
                b'exec(',
                b'system(',
                b'shell_exec('
            ]
            
            errors = []
            for pattern in suspicious_patterns:
                if pattern in content.lower():
                    errors.append(f"Suspicious content detected: {pattern.decode('utf-8', errors='ignore')}")
            
            return {
                'safe': len(errors) == 0,
                'errors': errors
            }
            
        except Exception as e:
            logger.warning(f"Malware scan failed: {str(e)}")
            return {'safe': True, 'errors': []}
    
    def _validate_file_integrity(self, uploaded_file) -> dict:
        """
        Validate file integrity and structure
        """
        try:
            # Basic file header validation
            uploaded_file.seek(0)
            header = uploaded_file.read(16)
            uploaded_file.seek(0)
            
            # Check if file is not empty
            if len(header) == 0:
                return {'valid': False, 'errors': ['File appears to be empty']}
            
            # Check for common file format headers
            file_extension = os.path.splitext(uploaded_file.name)[1].lower()
            
            # PDF files should start with %PDF
            if file_extension == '.pdf' and not header.startswith(b'%PDF'):
                return {'valid': False, 'errors': ['Invalid PDF file format']}
            
            # ZIP files should start with PK
            if file_extension in ['.zip', '.docx', '.xlsx', '.pptx'] and not header.startswith(b'PK'):
                return {'valid': False, 'errors': ['Invalid ZIP-based file format']}
            
            return {'valid': True, 'errors': []}
            
        except Exception as e:
            logger.warning(f"File integrity check failed: {str(e)}")
            return {'valid': True, 'errors': []}
    
    def _calculate_checksum(self, uploaded_file) -> str:
        """
        Calculate SHA-256 checksum of the file
        """
        try:
            uploaded_file.seek(0)
            hasher = hashlib.sha256()
            
            for chunk in iter(lambda: uploaded_file.read(8192), b""):
                hasher.update(chunk)
            
            uploaded_file.seek(0)
            return hasher.hexdigest()
            
        except Exception as e:
            logger.warning(f"Checksum calculation failed: {str(e)}")
            return ""
    
    def _format_bytes(self, bytes_size: int) -> str:
        """
        Format bytes into human readable format
        """
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_size < 1024.0:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024.0
        return f"{bytes_size:.1f} TB"


def validate_uploaded_file(uploaded_file):
    """
    Django validator function for file uploads
    """
    validator = FileSecurityValidator()
    result = validator.validate_file(uploaded_file)
    
    if not result['valid']:
        raise ValidationError(
            _('File validation failed: %(errors)s'),
            params={'errors': ', '.join(result['errors'])},
        )
    
    return result['file_info']


def secure_filename(filename: str) -> str:
    """
    Generate a secure filename to prevent directory traversal and other attacks
    """
    # Remove path components
    filename = os.path.basename(filename)
    
    # Remove or replace dangerous characters
    dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
    for char in dangerous_chars:
        filename = filename.replace(char, '_')
    
    # Limit filename length
    name, ext = os.path.splitext(filename)
    if len(name) > 100:
        name = name[:100]
    
    # Ensure filename is not empty
    if not name:
        name = 'uploaded_file'
    
    return f"{name}{ext}"


def get_secure_upload_path(instance, filename: str) -> str:
    """
    Generate secure upload path for file storage
    """
    secure_name = secure_filename(filename)
    
    # Create directory structure based on date and user
    from datetime import datetime
    now = datetime.now()
    
    path = os.path.join(
        'uploads',
        str(now.year),
        f"{now.month:02d}",
        secure_name
    )
    
    return path 