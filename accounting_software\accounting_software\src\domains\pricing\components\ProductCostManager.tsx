// src/components/pricing/ProductCostManager.tsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress
} from '@mui/material';
import { Edit as EditIcon, TrendingUp as TrendingUpIcon, Assessment as AssessmentIcon, History as HistoryIcon } from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { DataTable } from '../../../shared/components';
import { pricingService, ProductCost, CostAnalysis } from '../../../services/pricingService';
import { inventoryService, StockTransaction, Warehouse } from '../../../domains/inventory/services/inventory.service';
import dayjs from 'dayjs';
import { DatePicker } from '@mui/x-date-pickers';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`cost-tabpanel-${index}`}
      aria-labelledby={`cost-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Add interface for cost history
interface CostHistoryEntry {
  date: string;
  document_type: string;
  document_number: string;
  previous_cost: number;
  new_cost: number;
  quantity: number;
  total_value: number;
  warehouse_name: string;
}

// Add these interfaces after the existing CostHistoryEntry interface
interface CostSummary {
  total_purchases: number;
  total_value: number;
  average_unit_cost: number;
  lowest_cost: number;
  highest_cost: number;
  cost_volatility: number;
}

interface CostTrend {
  period: string;
  average_cost: number;
  total_quantity: number;
  total_value: number;
}

// Add these utility functions before the component
const calculateVolatility = (costs: number[]): number => {
  if (costs.length < 2) return 0;
  const mean = costs.reduce((sum, c) => sum + c, 0) / costs.length;
  const variance = costs.reduce((sum, c) => sum + Math.pow(c - mean, 2), 0) / (costs.length - 1);
  return Math.sqrt(variance) / mean * 100; // Return as percentage
};

const calculateMonthlyTrends = (transactions: StockTransaction[]): CostTrend[] => {
  const monthlyData = transactions.reduce((acc: { [key: string]: CostTrend }, txn) => {
    const month = dayjs(txn.txn_date).format('YYYY-MM');
    if (!acc[month]) {
      acc[month] = { period: month, average_cost: 0, total_quantity: 0, total_value: 0 };
    }
    acc[month].total_quantity += txn.quantity;
    acc[month].total_value += txn.total_cost;
    acc[month].average_cost = acc[month].total_value / acc[month].total_quantity;
    return acc;
  }, {});
  
  return Object.values(monthlyData).sort((a, b) => a.period.localeCompare(b.period));
};

const ProductCostManager: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [products, setProducts] = useState<ProductCost[]>([]);
  const [costAnalysis, setCostAnalysis] = useState<CostAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<ProductCost | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    cost_method: 'standard' as 'standard' | 'average' | 'fifo',
    standard_cost: 0
  });
  const [selectedProduct, setSelectedProduct] = useState<ProductCost | null>(null);
  const [costHistory, setCostHistory] = useState<CostHistoryEntry[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [costSummary, setCostSummary] = useState<CostSummary | null>(null);
  const [costTrends, setCostTrends] = useState<CostTrend[]>([]);
  const [dateRange, setDateRange] = useState<{start: string | null, end: string | null}>({
    start: null,
    end: null
  });
  const [filterWarehouse, setFilterWarehouse] = useState<number | null>(null);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    const loadWarehouses = async () => {
      try {
        const response = await inventoryService.getAllWarehouses();
        setWarehouses(response);
      } catch (error) {
        console.error('Failed to load warehouses:', error);
      }
    };
    loadWarehouses();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [productsResponse, analysisResponse] = await Promise.all([
        pricingService.getProductCosts(),
        pricingService.getProductCostAnalysis()
      ]);
      setProducts(productsResponse.data);
      setCostAnalysis(analysisResponse.data);
    } catch (error) {
      console.error('Failed to fetch cost data:', error);
      enqueueSnackbar('Failed to fetch cost data', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (product: ProductCost) => {
    setCurrentProduct(product);
    setFormData({
      cost_method: product.cost_method,
      standard_cost: product.standard_cost
    });
    setDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!currentProduct) return;

    try {
      await pricingService.updateProductCost(currentProduct.id, formData);
      enqueueSnackbar('Product cost updated successfully', { variant: 'success' });
      setDialogOpen(false);
      fetchData();
    } catch (error) {
      console.error('Failed to update product cost:', error);
      enqueueSnackbar('Failed to update product cost', { variant: 'error' });
    }
  };

  const handleViewHistory = async (product: ProductCost) => {
    setSelectedProduct(product);
    setHistoryLoading(true);
    try {
      // Get stock transactions with filters
      const params: any = {
        product: product.id,
        transaction_type: 'RECEIPT',
        page_size: 1000
      };
      
      if (dateRange.start) params.start_date = dateRange.start;
      if (dateRange.end) params.end_date = dateRange.end;
      if (filterWarehouse) params.warehouse = filterWarehouse;

      const response = await inventoryService.getStockTransactions(params);
      const transactions = Array.isArray(response) ? response : response.results || [];
      
      // Process transactions for history
      const history = transactions.map((txn: StockTransaction, index: number, arr: StockTransaction[]) => ({
        date: txn.txn_date,
        document_type: txn.reference_type,
        document_number: `${txn.reference_type}-${txn.reference_id}`,
        previous_cost: index > 0 ? arr[index-1].unit_cost : product.standard_cost,
        new_cost: txn.unit_cost,
        quantity: txn.quantity,
        total_value: txn.total_cost,
        warehouse_name: txn.warehouse_name
      }));

      // Calculate cost summary
      const costs = transactions.map((t: StockTransaction) => t.unit_cost);
      const summary: CostSummary = {
        total_purchases: transactions.length,
        total_value: transactions.reduce((sum: number, t: StockTransaction) => sum + t.total_cost, 0),
        average_unit_cost: costs.reduce((sum: number, c: number) => sum + c, 0) / costs.length || 0,
        lowest_cost: costs.length ? Math.min(...costs) : 0,
        highest_cost: costs.length ? Math.max(...costs) : 0,
        cost_volatility: calculateVolatility(costs)
      };

      // Calculate monthly trends
      const trends = calculateMonthlyTrends(transactions);

      setCostHistory(history);
      setCostSummary(summary);
      setCostTrends(trends);
    } catch (error) {
      console.error('Failed to load cost history:', error);
      enqueueSnackbar('Failed to load cost history', { variant: 'error' });
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleDateRangeChange = (type: 'start' | 'end', value: string | null) => {
    setDateRange(prev => ({
      ...prev,
      [type]: value
    }));
    if (selectedProduct) {
      handleViewHistory(selectedProduct);
    }
  };

  const handleWarehouseChange = (warehouseId: number | null) => {
    setFilterWarehouse(warehouseId);
    if (selectedProduct) {
      handleViewHistory(selectedProduct);
    }
  };

  const renderFilters = () => (
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={4}>
        <FormControl fullWidth>
          <InputLabel>Warehouse</InputLabel>
          <Select
            value={filterWarehouse || ''}
            onChange={(e) => handleWarehouseChange(e.target.value as number)}
            label="Warehouse"
          >
            <MenuItem value="">All Warehouses</MenuItem>
            {warehouses.map(wh => (
              <MenuItem key={wh.warehouse_id} value={wh.warehouse_id}>
                {wh.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={4}>
        <DatePicker
          label="Start Date"
          value={dateRange.start ? dayjs(dateRange.start) : null}
          onChange={(date) => handleDateRangeChange('start', date?.format('YYYY-MM-DD') || null)}
          slotProps={{ textField: { fullWidth: true } }}
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <DatePicker
          label="End Date"
          value={dateRange.end ? dayjs(dateRange.end) : null}
          onChange={(date) => handleDateRangeChange('end', date?.format('YYYY-MM-DD') || null)}
          slotProps={{ textField: { fullWidth: true } }}
        />
      </Grid>
    </Grid>
  );

  const costColumns = [
    {
      field: 'code',
      headerName: 'Product Code',
      width: 120,
    },
    {
      field: 'name',
      headerName: 'Product Name',
      flex: 1,
    },
    {
      field: 'type',
      headerName: 'Type',
      width: 100,
      renderCell: (params: any) => (
        <Chip 
          label={params.value} 
          color={params.value === 'product' ? 'primary' : 'secondary'} 
          size="small" 
        />
      ),
    },
    {
      field: 'cost_method',
      headerName: 'Cost Method',
      width: 120,
      renderCell: (params: any) => (
        <Chip 
          label={params.value.toUpperCase()} 
          variant="outlined" 
          size="small"
          color={
            params.value === 'standard' ? 'primary' :
            params.value === 'average' ? 'secondary' : 'default'
          }
        />
      ),
    },
    {
      field: 'standard_cost',
      headerName: 'Standard Cost',
      width: 120,
      renderCell: (params: any) => `$${Number(params.value).toFixed(2)}`,
    },
    {
      field: 'average_cost',
      headerName: 'Average Cost',
      width: 120,
      renderCell: (params: any) => `$${Number(params.value).toFixed(2)}`,
    },
    {
      field: 'last_cost',
      headerName: 'Last Cost',
      width: 120,
      renderCell: (params: any) => `$${Number(params.value).toFixed(2)}`,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 160,
      renderCell: (params: any) => (
        <Box>
          <IconButton 
            size="small" 
            onClick={() => handleEdit(params.row)}
            color="primary"
          >
            <EditIcon />
          </IconButton>
          <IconButton 
            size="small" 
            onClick={() => handleViewHistory(params.row)}
            color="info"
          >
            <HistoryIcon />
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Card>
      <CardContent>
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs 
              value={tabValue} 
              onChange={(e, newValue) => setTabValue(newValue)}
              aria-label="cost management tabs"
            >
              <Tab 
                label="Product Costs" 
                icon={<TrendingUpIcon />} 
                id="cost-tab-0"
                aria-controls="cost-tabpanel-0"
              />
              <Tab 
                label="Cost Analysis" 
                icon={<AssessmentIcon />} 
                id="cost-tab-1"
                aria-controls="cost-tabpanel-1"
              />
              <Tab 
                label="Cost History" 
                icon={<HistoryIcon />} 
                id="cost-tab-2"
                aria-controls="cost-tabpanel-2"
              />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5">Product Cost Management</Typography>
            </Box>
            <DataTable
              columns={costColumns}
              rows={products}
              loading={loading}
              pageSize={10}
            />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Typography variant="h5" gutterBottom>Cost Analysis</Typography>
            {costAnalysis && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Cost Method Distribution</Typography>
                      <Box sx={{ mt: 2 }}>
                        <Typography>Standard Cost: {costAnalysis.cost_methods.standard} products</Typography>
                        <Typography>Average Cost: {costAnalysis.cost_methods.average} products</Typography>
                        <Typography>FIFO: {costAnalysis.cost_methods.fifo} products</Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Cost Averages</Typography>
                      <Box sx={{ mt: 2 }}>
                        <Typography>Average Standard Cost: ${costAnalysis.avg_standard_cost.toFixed(2)}</Typography>
                        <Typography>Average Last Cost: ${costAnalysis.avg_last_cost.toFixed(2)}</Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h5" gutterBottom>Cost History</Typography>
            {selectedProduct ? (
              <>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6">{selectedProduct.name} ({selectedProduct.code})</Typography>
                  <Typography color="textSecondary">
                    Current Cost Method: {selectedProduct.cost_method.toUpperCase()}
                  </Typography>
                </Box>

                {renderFilters()}

                {historyLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                    <CircularProgress />
                  </Box>
                ) : (
                  <Grid container spacing={3}>
                    {costSummary && (
                      <Grid item xs={12}>
                        <Card>
                          <CardContent>
                            <Typography variant="h6" gutterBottom>Cost Analysis Summary</Typography>
                            <Grid container spacing={2}>
                              <Grid item xs={12} sm={4}>
                                <Typography variant="subtitle2" color="textSecondary">Total Purchases</Typography>
                                <Typography variant="h6">{costSummary.total_purchases}</Typography>
                              </Grid>
                              <Grid item xs={12} sm={4}>
                                <Typography variant="subtitle2" color="textSecondary">Total Value</Typography>
                                <Typography variant="h6">${costSummary.total_value.toFixed(2)}</Typography>
                              </Grid>
                              <Grid item xs={12} sm={4}>
                                <Typography variant="subtitle2" color="textSecondary">Average Unit Cost</Typography>
                                <Typography variant="h6">${costSummary.average_unit_cost.toFixed(2)}</Typography>
                              </Grid>
                              <Grid item xs={12} sm={4}>
                                <Typography variant="subtitle2" color="textSecondary">Lowest Cost</Typography>
                                <Typography variant="h6">${costSummary.lowest_cost.toFixed(2)}</Typography>
                              </Grid>
                              <Grid item xs={12} sm={4}>
                                <Typography variant="subtitle2" color="textSecondary">Highest Cost</Typography>
                                <Typography variant="h6">${costSummary.highest_cost.toFixed(2)}</Typography>
                              </Grid>
                              <Grid item xs={12} sm={4}>
                                <Typography variant="subtitle2" color="textSecondary">Cost Volatility</Typography>
                                <Typography variant="h6">
                                  <Chip 
                                    label={`${costSummary.cost_volatility.toFixed(1)}%`}
                                    color={costSummary.cost_volatility > 20 ? 'error' : costSummary.cost_volatility > 10 ? 'warning' : 'success'}
                                    size="small"
                                  />
                                </Typography>
                              </Grid>
                            </Grid>
                          </CardContent>
                        </Card>
                      </Grid>
                    )}
                    <Grid item xs={12}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>Monthly Cost Trends</Typography>
                          <TableContainer>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>Period</TableCell>
                                  <TableCell align="right">Average Cost</TableCell>
                                  <TableCell align="right">Total Quantity</TableCell>
                                  <TableCell align="right">Total Value</TableCell>
                                  <TableCell align="right">Change %</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {costTrends.map((trend, index) => {
                                  const prevTrend = index > 0 ? costTrends[index - 1] : null;
                                  const costChange = prevTrend 
                                    ? ((trend.average_cost - prevTrend.average_cost) / prevTrend.average_cost * 100)
                                    : 0;
                                  return (
                                    <TableRow key={trend.period}>
                                      <TableCell>{trend.period}</TableCell>
                                      <TableCell align="right">${trend.average_cost.toFixed(2)}</TableCell>
                                      <TableCell align="right">{trend.total_quantity.toFixed(2)}</TableCell>
                                      <TableCell align="right">${trend.total_value.toFixed(2)}</TableCell>
                                      <TableCell align="right">
                                        {index > 0 && (
                                          <Chip
                                            label={`${costChange.toFixed(1)}%`}
                                            color={costChange > 0 ? 'error' : costChange < 0 ? 'success' : 'default'}
                                            size="small"
                                          />
                                        )}
                                      </TableCell>
                                    </TableRow>
                                  );
                                })}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={12}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>Transaction History</Typography>
                          <TableContainer>
                            <Table>
                              <TableHead>
                                <TableRow>
                                  <TableCell>Date</TableCell>
                                  <TableCell>Document</TableCell>
                                  <TableCell align="right">Previous Cost</TableCell>
                                  <TableCell align="right">New Cost</TableCell>
                                  <TableCell align="right">Change %</TableCell>
                                  <TableCell align="right">Quantity</TableCell>
                                  <TableCell align="right">Total Value</TableCell>
                                  <TableCell align="right">Warehouse</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {costHistory.map((entry, index) => {
                                  const costChange = entry.previous_cost > 0 
                                    ? ((entry.new_cost - entry.previous_cost) / entry.previous_cost * 100)
                                    : 0;
                                  return (
                                    <TableRow key={index}>
                                      <TableCell>{dayjs(entry.date).format('YYYY-MM-DD')}</TableCell>
                                      <TableCell>{entry.document_number}</TableCell>
                                      <TableCell align="right">${entry.previous_cost.toFixed(2)}</TableCell>
                                      <TableCell align="right">${entry.new_cost.toFixed(2)}</TableCell>
                                      <TableCell align="right">
                                        <Chip
                                          label={`${costChange.toFixed(1)}%`}
                                          color={costChange > 0 ? 'error' : costChange < 0 ? 'success' : 'default'}
                                          size="small"
                                        />
                                      </TableCell>
                                      <TableCell align="right">{entry.quantity.toFixed(2)}</TableCell>
                                      <TableCell align="right">${entry.total_value.toFixed(2)}</TableCell>
                                      <TableCell align="right">{entry.warehouse_name}</TableCell>
                                    </TableRow>
                                  );
                                })}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                )}
              </>
            ) : (
              <Typography color="textSecondary">
                Select a product from the Product Costs tab to view its cost history
              </Typography>
            )}
          </TabPanel>
        </Box>

        <Dialog 
          open={dialogOpen} 
          onClose={() => setDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Edit Product Cost - {currentProduct?.code} {currentProduct?.name}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Cost Method</InputLabel>
                  <Select
                    value={formData.cost_method}
                    label="Cost Method"
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      cost_method: e.target.value as 'standard' | 'average' | 'fifo' 
                    }))}
                  >
                    <MenuItem value="standard">Standard Cost</MenuItem>
                    <MenuItem value="average">Average Cost</MenuItem>
                    <MenuItem value="fifo">FIFO (Last Cost)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Standard Cost"
                  type="number"
                  value={formData.standard_cost}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    standard_cost: parseFloat(e.target.value) || 0 
                  }))}
                  inputProps={{ min: 0, step: 0.01 }}
                  helperText="Only editable when using Standard Cost method"
                  disabled={formData.cost_method !== 'standard'}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained">
              Update Cost
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default ProductCostManager;
