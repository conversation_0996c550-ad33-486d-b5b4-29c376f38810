import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  IconButton,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Chip,
  Card,
  CardContent,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Tabs,
  Tab,
  CircularProgress,
  Tooltip,
  Badge,
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Category as CategoryIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  PhotoCamera as PhotoCameraIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
  AccountTree as AccountTreeIcon,
  ShoppingCart as PurchaseIcon,
} from '@mui/icons-material';
import { useProductCategories, ProductCategory, ProductCategoryFormData } from '../../contexts/ProductCategoriesContext';

interface ProductCategoryModalProps {
  open: boolean;
  onClose: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`category-tabpanel-${index}`}
      aria-labelledby={`category-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ProductCategoryModal: React.FC<ProductCategoryModalProps> = ({ open, onClose }) => {
  const {
    categories,
    loading,
    error,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoriesByDivision,
    getSubcategories,
  } = useProductCategories();

  const [tabValue, setTabValue] = useState(0);
  const [formMode, setFormMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory | null>(null);
  const [formData, setFormData] = useState<ProductCategoryFormData>({
    name: '',
    code: '',
    description: '',
    divisionType: 'non-perishable',
    parentCategoryId: '',
    imageUrl: '',
    taxCategory: '',
    marginPercentage: 0,
    isActive: true,
    sortOrder: 1,
    allowSubcategories: true,
    requiresExpiryTracking: false,
    requiresBatchTracking: false,
    defaultUnitOfMeasure: 'piece',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Division type options
  const divisionTypeOptions = [
    { value: 'perishable', label: '🥬 Perishable', color: 'success' as const },
    { value: 'refrigerated', label: '🧊 Refrigerated', color: 'info' as const },
    { value: 'frozen', label: '❄️ Frozen', color: 'primary' as const },
    { value: 'controlled-substance', label: '💊 Controlled Substance', color: 'warning' as const },
    { value: 'non-perishable', label: '📦 Non-Perishable', color: 'default' as const },
  ];

  // Tax categories for purchase department
  const taxCategories = [
    { id: 'standard', name: 'Standard Rate (18%)' },
    { id: 'reduced', name: 'Reduced Rate (5%)' },
    { id: 'zero', name: 'Zero Rate (0%)' },
    { id: 'exempt', name: 'Exempt' },
    { id: 'input_tax', name: 'Input Tax Credit' },
  ];

  // Unit of measure options
  const unitOptions = [
    'piece', 'kg', 'gram', 'liter', 'ml', 'box', 'pack', 'dozen', 'meter', 'cm'
  ];

  useEffect(() => {
    if (open) {
      loadCategories();
    }
  }, [open, loadCategories]);

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      description: '',
      divisionType: 'non-perishable',
      parentCategoryId: '',
      imageUrl: '',
      taxCategory: '',
      marginPercentage: 0,
      isActive: true,
      sortOrder: 1,
      allowSubcategories: true,
      requiresExpiryTracking: false,
      requiresBatchTracking: false,
      defaultUnitOfMeasure: 'piece',
    });
    setFormErrors({});
    setSelectedCategory(null);
    setFormMode('create');
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Category name is required';
    }

    if (!formData.code.trim()) {
      errors.code = 'Category code is required';
    }

    if (formData.marginPercentage < 0 || formData.marginPercentage > 100) {
      errors.marginPercentage = 'Margin percentage must be between 0 and 100';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      if (formMode === 'create') {
        await createCategory(formData);
        setSnackbar({
          open: true,
          message: `✅ Category "${formData.name}" created successfully!`,
          severity: 'success',
        });
      } else if (formMode === 'edit' && selectedCategory) {
        await updateCategory(selectedCategory.id, formData);
        setSnackbar({
          open: true,
          message: `✅ Category "${formData.name}" updated successfully!`,
          severity: 'success',
        });
      }
      resetForm();
      setTabValue(0); // Switch back to list view
    } catch (error) {
      setSnackbar({
        open: true,
        message: `❌ Failed to ${formMode} category: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    }
  };

  const handleEdit = (category: ProductCategory) => {
    setSelectedCategory(category);
    setFormData({
      name: category.name,
      code: category.code,
      description: category.description,
      divisionType: category.divisionType,
      parentCategoryId: category.parentCategoryId || '',
      imageUrl: category.imageUrl || '',
      taxCategory: category.taxCategory || '',
      marginPercentage: category.marginPercentage || 0,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      allowSubcategories: category.allowSubcategories,
      requiresExpiryTracking: category.requiresExpiryTracking || false,
      requiresBatchTracking: category.requiresBatchTracking || false,
      defaultUnitOfMeasure: category.defaultUnitOfMeasure || 'piece',
    });
    setFormMode('edit');
    setTabValue(1); // Switch to form tab
  };

  const handleView = (category: ProductCategory) => {
    setSelectedCategory(category);
    setFormMode('view');
    setTabValue(2); // Switch to view tab
  };

  const handleDelete = async (category: ProductCategory) => {
    if (window.confirm(`Are you sure you want to delete "${category.name}"?\n\nThis action cannot be undone.`)) {
      try {
        await deleteCategory(category.id);
        setSnackbar({
          open: true,
          message: `✅ Category "${category.name}" deleted successfully!`,
          severity: 'success',
        });
      } catch (error) {
        setSnackbar({
          open: true,
          message: `❌ Failed to delete category: ${error instanceof Error ? error.message : 'Unknown error'}`,
          severity: 'error',
        });
      }
    }
  };

  const getDivisionTypeInfo = (divisionType: string) => {
    return divisionTypeOptions.find(option => option.value === divisionType) || divisionTypeOptions[4];
  };

  const renderCategoryList = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" color="primary">
          🛒 Purchase Categories ({categories.length})
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            resetForm();
            setFormMode('create');
            setTabValue(1);
          }}
        >
          New Category
        </Button>
      </Box>

      {loading && (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      )}

      {!loading && categories.length === 0 && (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <CategoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Categories Found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Create your first product category to organize your purchase inventory
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => {
                resetForm();
                setFormMode('create');
                setTabValue(1);
              }}
            >
              Create First Category
            </Button>
          </CardContent>
        </Card>
      )}

      {!loading && categories.length > 0 && (
        <Grid container spacing={2}>
          {categories.map((category) => (
            <Grid item xs={12} sm={6} md={4} key={category.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                    <Avatar sx={{ bgcolor: getDivisionTypeInfo(category.divisionType).color + '.light' }}>
                      <CategoryIcon />
                    </Avatar>
                    <Chip
                      size="small"
                      label={category.isActive ? 'Active' : 'Inactive'}
                      color={category.isActive ? 'success' : 'default'}
                    />
                  </Box>
                  
                  <Typography variant="h6" gutterBottom>
                    {category.name}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Code: {category.code}
                  </Typography>
                  
                  <Chip
                    size="small"
                    label={getDivisionTypeInfo(category.divisionType).label}
                    color={getDivisionTypeInfo(category.divisionType).color}
                    sx={{ mb: 1 }}
                  />
                  
                  {category.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      {category.description.length > 100 
                        ? `${category.description.substring(0, 100)}...` 
                        : category.description}
                    </Typography>
                  )}
                  
                  <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                    <Typography variant="caption" color="text.secondary">
                      Margin: {category.marginPercentage || 0}%
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Order: {category.sortOrder}
                    </Typography>
                  </Box>
                </CardContent>
                
                <Box sx={{ p: 1, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                  <Tooltip title="View Details">
                    <IconButton size="small" onClick={() => handleView(category)}>
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit Category">
                    <IconButton size="small" onClick={() => handleEdit(category)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete Category">
                    <IconButton size="small" color="error" onClick={() => handleDelete(category)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );

  const renderCategoryForm = () => (
    <Box>
      <Typography variant="h6" gutterBottom color="primary">
        {formMode === 'create' ? '➕ Create New Category' : '✏️ Edit Category'}
      </Typography>
      
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
            📋 Basic Information
          </Typography>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Category Name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            error={!!formErrors.name}
            helperText={formErrors.name || 'Enter a descriptive category name'}
            required
          />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Category Code"
            value={formData.code}
            onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
            error={!!formErrors.code}
            helperText={formErrors.code || 'Short code for the category (e.g., ELEC, FOOD)'}
            required
          />
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Description"
            multiline
            rows={3}
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            helperText="Detailed description of the category"
          />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Division Type</InputLabel>
            <Select
              value={formData.divisionType}
              onChange={(e) => setFormData({ ...formData, divisionType: e.target.value as any })}
              label="Division Type"
            >
              {divisionTypeOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Parent Category (Optional)</InputLabel>
            <Select
              value={formData.parentCategoryId}
              onChange={(e) => setFormData({ ...formData, parentCategoryId: e.target.value })}
              label="Parent Category (Optional)"
            >
              <MenuItem value="">
                <em>None (Top Level Category)</em>
              </MenuItem>
              {categories
                .filter(cat => 
                  cat.id !== selectedCategory?.id &&
                  cat.level < 3 && // Limit to 3 levels deep
                  cat.allowSubcategories // Only show categories that allow subcategories
                )
                .map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {'  '.repeat(category.level - 1)}{category.name} ({category.code})
                  </MenuItem>
                ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Business Settings */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
            💼 Business Settings
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Tax Category</InputLabel>
            <Select
              value={formData.taxCategory}
              onChange={(e) => setFormData({ ...formData, taxCategory: e.target.value })}
              label="Tax Category"
            >
              <MenuItem value="">
                <em>No specific tax category</em>
              </MenuItem>
              {taxCategories.map((tax) => (
                <MenuItem key={tax.id} value={tax.id}>
                  {tax.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Default Margin %"
            type="number"
            value={formData.marginPercentage}
            onChange={(e) => setFormData({ ...formData, marginPercentage: Number(e.target.value) })}
            error={!!formErrors.marginPercentage}
            helperText={formErrors.marginPercentage || 'Default profit margin for products in this category'}
            InputProps={{
              inputProps: { min: 0, max: 100, step: 0.1 }
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Sort Order"
            type="number"
            value={formData.sortOrder}
            onChange={(e) => setFormData({ ...formData, sortOrder: Number(e.target.value) })}
            helperText="Lower numbers appear first"
            InputProps={{
              inputProps: { min: 1, step: 1 }
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Default Unit of Measure</InputLabel>
            <Select
              value={formData.defaultUnitOfMeasure}
              onChange={(e) => setFormData({ ...formData, defaultUnitOfMeasure: e.target.value })}
              label="Default Unit of Measure"
            >
              {unitOptions.map((unit) => (
                <MenuItem key={unit} value={unit}>
                  {unit.charAt(0).toUpperCase() + unit.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Advanced Settings */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
            ⚙️ Advanced Settings
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              />
            }
            label="Active Category"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.allowSubcategories}
                onChange={(e) => setFormData({ ...formData, allowSubcategories: e.target.checked })}
              />
            }
            label="Allow Subcategories"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.requiresExpiryTracking}
                onChange={(e) => setFormData({ ...formData, requiresExpiryTracking: e.target.checked })}
              />
            }
            label="Requires Expiry Tracking"
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControlLabel
            control={
              <Switch
                checked={formData.requiresBatchTracking}
                onChange={(e) => setFormData({ ...formData, requiresBatchTracking: e.target.checked })}
              />
            }
            label="Requires Batch Tracking"
          />
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
            <Button
              variant="outlined"
              startIcon={<CancelIcon />}
              onClick={() => {
                resetForm();
                setTabValue(0);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Saving...' : formMode === 'create' ? 'Create Category' : 'Update Category'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  const renderCategoryView = () => {
    if (!selectedCategory) return null;

    return (
      <Box>
        <Typography variant="h6" gutterBottom color="primary">
          👁️ Category Details
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText
                      primary="Category Name"
                      secondary={selectedCategory.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Category Code"
                      secondary={selectedCategory.code}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Description"
                      secondary={selectedCategory.description || 'No description provided'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Division Type"
                      secondary={getDivisionTypeInfo(selectedCategory.divisionType).label}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Sort Order"
                      secondary={selectedCategory.sortOrder}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Default Unit"
                      secondary={selectedCategory.defaultUnitOfMeasure || 'piece'}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Business Settings
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText
                      primary="Default Margin"
                      secondary={`${selectedCategory.marginPercentage || 0}%`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Tax Category"
                      secondary={taxCategories.find(opt => opt.id === selectedCategory.taxCategory)?.name || 'Standard Rate'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Status"
                      secondary={
                        <Chip
                          size="small"
                          label={selectedCategory.isActive ? 'Active' : 'Inactive'}
                          color={selectedCategory.isActive ? 'success' : 'default'}
                        />
                      }
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Allow Subcategories"
                      secondary={selectedCategory.allowSubcategories ? 'Yes' : 'No'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Tracking Requirements"
                      secondary={
                        <Box>
                          {selectedCategory.requiresExpiryTracking && <Chip size="small" label="Expiry Tracking" sx={{ mr: 1 }} />}
                          {selectedCategory.requiresBatchTracking && <Chip size="small" label="Batch Tracking" />}
                          {!selectedCategory.requiresExpiryTracking && !selectedCategory.requiresBatchTracking && 'None'}
                        </Box>
                      }
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
          <Button
            variant="outlined"
            onClick={() => setTabValue(0)}
          >
            Back to List
          </Button>
          <Button
            variant="contained"
            startIcon={<EditIcon />}
            onClick={() => handleEdit(selectedCategory)}
          >
            Edit Category
          </Button>
        </Box>
      </Box>
    );
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <PurchaseIcon color="primary" />
              <Typography variant="h6">Purchase Department - Product Categories</Typography>
            </Box>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        
        <DialogContent dividers>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab label="Categories List" />
              <Tab label={formMode === 'create' ? 'Create Category' : 'Edit Category'} />
              {selectedCategory && <Tab label="View Details" />}
            </Tabs>
          </Box>
          
          <TabPanel value={tabValue} index={0}>
            {renderCategoryList()}
          </TabPanel>
          
          <TabPanel value={tabValue} index={1}>
            {renderCategoryForm()}
          </TabPanel>
          
          {selectedCategory && (
            <TabPanel value={tabValue} index={2}>
              {renderCategoryView()}
            </TabPanel>
          )}
        </DialogContent>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ProductCategoryModal; 