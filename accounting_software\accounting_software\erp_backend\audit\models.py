"""
Comprehensive Audit Trail System for ERP Accounting Software

This module provides complete audit logging functionality required for:
- SOX Compliance (Sarbanes-Oxley Act)
- GAAP Requirements (Generally Accepted Accounting Principles)  
- International Auditing Standards
- Internal Controls and Security
- Forensic Accounting Support

Key Features:
- Every data change is logged with before/after values
- User authentication and session tracking
- IP address and browser tracking
- Complete transaction audit trails
- Immutable audit records
- Data retention policies
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeign<PERSON><PERSON>
from django.utils import timezone
import json
import uuid

User = get_user_model()

class AuditLog(models.Model):
    """
    Master Audit Log - Records every significant action in the system
    
    This model captures all user actions for complete audit trail:
    - Data changes (CREATE, UPDATE, DELETE)
    - User authentication events
    - Permission changes
    - Financial transaction actions
    - Report generation and access
    
    Required for SOX compliance and financial auditing.
    """
    
    ACTION_CHOICES = [
        # Data Operations
        ('CREATE', 'Create Record'),
        ('UPDATE', 'Update Record'),
        ('DELETE', 'Delete Record'),
        ('VIEW', 'View Record'),
        
        # Authentication
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('LOGIN_FAILED', 'Failed Login Attempt'),
        ('PASSWORD_CHANGE', 'Password Changed'),
        
        # Financial Operations
        ('POST_JOURNAL', 'Post Journal Entry'),
        ('REVERSE_JOURNAL', 'Reverse Journal Entry'),
        ('CLOSE_PERIOD', 'Close Accounting Period'),
        ('REOPEN_PERIOD', 'Reopen Accounting Period'),
        
        # Reports and Exports
        ('GENERATE_REPORT', 'Generate Financial Report'),
        ('EXPORT_DATA', 'Export Data'),
        ('IMPORT_DATA', 'Import Data'),
        
        # System Administration
        ('PERMISSION_CHANGE', 'Change User Permissions'),
        ('BACKUP_CREATE', 'Create System Backup'),
        ('RESTORE_DATA', 'Restore from Backup'),
        
        # Compliance and Security
        ('DATA_ACCESS', 'Access Sensitive Data'),
        ('AUDIT_REVIEW', 'Audit Trail Review'),
        ('COMPLIANCE_CHECK', 'Compliance Verification'),
    ]
    
    RISK_LEVEL_CHOICES = [
        ('LOW', 'Low Risk'),
        ('MEDIUM', 'Medium Risk'),
        ('HIGH', 'High Risk'),
        ('CRITICAL', 'Critical Risk'),
    ]
    
    # Unique identifier for each audit record
    audit_id = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        editable=False,
        help_text="Unique identifier for this audit record"
    )
    
    # When and who
    timestamp = models.DateTimeField(
        default=timezone.now,
        help_text="Exact timestamp when action occurred"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        help_text="User who performed the action"
    )
    
    # What action was performed
    action = models.CharField(
        max_length=30,
        choices=ACTION_CHOICES,
        help_text="Type of action performed"
    )
    description = models.TextField(
        help_text="Human-readable description of the action"
    )
    
    # Where (which record was affected)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Type of object that was affected"
    )
    object_id = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="ID of the affected object"
    )
    affected_object = GenericForeignKey('content_type', 'object_id')
    
    # Data changes (before and after values)
    old_values = models.TextField(
        null=True,
        blank=True,
        help_text="Values before the change (JSON format)"
    )
    new_values = models.TextField(
        null=True,
        blank=True,
        help_text="Values after the change (JSON format)"
    )
    
    # Technical details
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user"
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        help_text="Browser/application user agent"
    )
    session_key = models.CharField(
        max_length=40,
        null=True,
        blank=True,
        help_text="User session identifier"
    )
    
    # Business context
    module = models.CharField(
        max_length=50,
        help_text="Application module (GL, AR, AP, etc.)"
    )
    risk_level = models.CharField(
        max_length=10,
        choices=RISK_LEVEL_CHOICES,
        default='LOW',
        help_text="Risk level of this action"
    )
    
    # Additional metadata
    metadata = models.TextField(
        null=True,
        blank=True,
        help_text="Additional context data (JSON format)"
    )
    
    # Compliance flags
    is_financial = models.BooleanField(
        default=False,
        help_text="Whether this affects financial data"
    )
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether this action requires approval"
    )
    is_approved = models.BooleanField(
        default=False,
        help_text="Whether this action has been approved"
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_audit_logs',
        help_text="User who approved this action"
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this action was approved"
    )
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Audit Log Entry"
        verbose_name_plural = "Audit Log Entries"
        indexes = [
            models.Index(fields=['timestamp']),
            models.Index(fields=['user']),
            models.Index(fields=['action']),
            models.Index(fields=['module']),
            models.Index(fields=['is_financial']),
            models.Index(fields=['content_type', 'object_id']),
        ]
    
    def __str__(self):
        return f"{self.timestamp}: {self.user} - {self.action} in {self.module}"
    
    def get_old_values_dict(self):
        """Get old values as dictionary."""
        if self.old_values:
            try:
                return json.loads(self.old_values)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def get_new_values_dict(self):
        """Get new values as dictionary."""
        if self.new_values:
            try:
                return json.loads(self.new_values)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def get_metadata_dict(self):
        """Get metadata as dictionary."""
        if self.metadata:
            try:
                return json.loads(self.metadata)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def set_old_values(self, values_dict):
        """Set old values from dictionary."""
        self.old_values = json.dumps(values_dict) if values_dict else None
    
    def set_new_values(self, values_dict):
        """Set new values from dictionary."""
        self.new_values = json.dumps(values_dict) if values_dict else None
    
    def set_metadata(self, metadata_dict):
        """Set metadata from dictionary."""
        self.metadata = json.dumps(metadata_dict) if metadata_dict else None

    def get_changes_summary(self):
        """Get a human-readable summary of what changed."""
        old_vals = self.get_old_values_dict()
        new_vals = self.get_new_values_dict()
        
        if not old_vals or not new_vals:
            return "No field changes tracked"
        
        changes = []
        for field, new_value in new_vals.items():
            old_value = old_vals.get(field, 'N/A')
            if old_value != new_value:
                changes.append(f"{field}: '{old_value}' → '{new_value}'")
        
        return "; ".join(changes) if changes else "No changes detected"

class UserSession(models.Model):
    """
    User Session Tracking for Enhanced Security
    
    Tracks user sessions for security and audit purposes:
    - When users log in/out
    - How long sessions last
    - Concurrent session detection
    - Unusual activity detection
    """
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='user_sessions'
    )
    session_key = models.CharField(
        max_length=40,
        unique=True,
        help_text="Django session key"
    )
    
    # Session timing
    login_time = models.DateTimeField(
        default=timezone.now,
        help_text="When user logged in"
    )
    logout_time = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When user logged out"
    )
    last_activity = models.DateTimeField(
        default=timezone.now,
        help_text="Last activity timestamp"
    )
    
    # Technical details
    ip_address = models.GenericIPAddressField(
        help_text="IP address of the session"
    )
    user_agent = models.TextField(
        help_text="Browser/application user agent"
    )
    
    # Session status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether session is still active"
    )
    logout_reason = models.CharField(
        max_length=50,
        choices=[
            ('USER', 'User Logout'),
            ('TIMEOUT', 'Session Timeout'),
            ('ADMIN', 'Admin Termination'),
            ('SECURITY', 'Security Violation'),
        ],
        null=True,
        blank=True,
        help_text="Reason for session termination"
    )
    
    class Meta:
        ordering = ['-login_time']
        verbose_name = "User Session"
        verbose_name_plural = "User Sessions"
    
    def __str__(self):
        return f"{self.user.username} - {self.login_time}"
    
    def get_session_duration(self):
        """Calculate session duration."""
        end_time = self.logout_time or timezone.now()
        return end_time - self.login_time

# ComplianceReport model will be added in a future version 