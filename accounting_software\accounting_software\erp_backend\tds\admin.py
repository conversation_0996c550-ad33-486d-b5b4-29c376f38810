from django.contrib import admin
from .models import TDS

@admin.register(TDS)
class TDSAdmin(admin.ModelAdmin):
    list_display = ['description', 'tds_type', 'rate', 'section', 'threshold_limit', 'created_by', 'created_at']
    list_filter = ['tds_type', 'created_at', 'created_by']
    search_fields = ['description', 'section', 'remarks']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('tds_type', 'description', 'rate')
        }),
        ('Tax Details', {
            'fields': ('section', 'threshold_limit')
        }),
        ('Additional Information', {
            'fields': ('remarks',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
