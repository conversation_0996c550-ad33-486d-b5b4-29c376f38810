from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import SalesTax
from .serializers import SalesTaxSerializer
import logging

logger = logging.getLogger(__name__)

# Create your views here.

class SalesTaxViewSet(viewsets.ModelViewSet):
    queryset = SalesTax.objects.all().order_by('tax_type', 'description')
    serializer_class = SalesTaxSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination for small dataset

    def perform_create(self, serializer):
        logger.info(f"Creating SalesTax for user: {self.request.user}")
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        logger.info(f"Updating SalesTax for user: {self.request.user}")
        # Don't change created_by on update
        serializer.save()

    def list(self, request, *args, **kwargs):
        logger.info(f"Listing SalesTax for user: {request.user}")
        return super().list(request, *args, **kwargs)
