/**
 * Chart of Accounts Management Page
 * 
 * Professional Chart of Accounts interface following QuickBooks and Oracle Financials patterns.
 * Features:
 * - Hierarchical account display with expand/collapse
 * - Advanced filtering and search capabilities
 * - Account creation and editing (superuser only)
 * - Real-time balance display
 * - Account type and detail type management
 * - Export functionality for reporting
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  IconButton,
  Tooltip,
  Checkbox,
  Collapse,
  Snackbar,
  Tabs,
  Tab,
  Divider,
  Grid,
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon,
  AccountTree as AccountTreeIcon,
  Category as CategoryIcon,
  Visibility as ActivateIcon,
  VisibilityOff as DeactivateIcon,
} from '@mui/icons-material';
import { Account, AccountType, DetailType, AccountFormData } from '../../../shared/types/gl.types';
import { accountsApi, accountTypesApi, detailTypesApi, formatCurrency, getAccountTypeColor, loadChartOfAccountsFast } from '../../../services/gl.service';
import { useAuth } from '../../../contexts/AuthContext';
import COAUploadForm from '../components/COAUploadForm';

const ChartOfAccountsPage: React.FC = () => {
  const { user, isLoading: authLoading, isAuthenticated } = useAuth();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [accountTypes, setAccountTypes] = useState<AccountType[]>([]);
  const [detailTypes, setDetailTypes] = useState<DetailType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openCreateDialog, setOpenCreateDialog] = useState(false);
  // Removed expandedAccounts - showing all accounts in simple list
  const [formData, setFormData] = useState<AccountFormData>({
    account_number: '',
    account_name: '',
    description: '',
    account_type: '',
    detail_type: '',
    parent_account: '',
    currency: 'USD',
    is_active: true,
    is_header_account: false,
    opening_balance: '0.00',
    opening_balance_date: new Date().toISOString().split('T')[0],
    tax_line: '',
    bank_account_number: '',
    bank_routing_number: '',
  });
  const [saving, setSaving] = useState(false);
  const [isSubAccount, setIsSubAccount] = useState(false);
  const [searchFilter, setSearchFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });

  // Detail Type Management State
  const [currentTab, setCurrentTab] = useState(0);
  const [openDetailTypeDialog, setOpenDetailTypeDialog] = useState(false);
  const [editingDetailType, setEditingDetailType] = useState<DetailType | null>(null);
  const [detailTypeForm, setDetailTypeForm] = useState({
    code: '',
    name: '',
    description: '',
    account_type: '' as number | '',
    requires_customer: false,
    requires_vendor: false,
    requires_item: false,
    is_tax_account: false,
    default_tax_code: '',
    balance_sheet_classification: '' as 'CURRENT' | 'NON_CURRENT' | '',
    sort_order: 0,
    is_active: true,
  });
  const [savingDetailType, setSavingDetailType] = useState(false);

  const isSuperUser = user?.is_superuser || false;

  useEffect(() => {
    console.log('ChartOfAccountsPage useEffect - Auth state:', {
      user: user?.username,
      isAuthenticated,
      authLoading,
      token: !!localStorage.getItem('token')
    });
    
    if (!authLoading && isAuthenticated) {
      loadData();
    } else if (!authLoading && !isAuthenticated) {
      setLoading(false);
      setError('Please log in to access the Chart of Accounts.');
    }
  }, [isAuthenticated, authLoading]);

  // No need for expand/collapse - we'll show all accounts in a simple list

  // Helper to fetch all paginated accounts
  const fetchAllAccounts = async () => {
    let allAccounts: Account[] = [];
    let nextUrl: string | null = '/gl/accounts/';
    const token = localStorage.getItem('token');
    while (nextUrl) {
      const response = await fetch(`http://localhost:8000/api${nextUrl}`, {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      if (Array.isArray(data)) {
        // Non-paginated fallback
        allAccounts = data;
        nextUrl = null;
      } else if (data && data.results) {
        allAccounts = allAccounts.concat(data.results);
        // next can be absolute or relative
        if (data.next) {
          // If absolute, strip base URL
          const nextPath = data.next.startsWith('http') ? data.next.replace('http://localhost:8000/api', '') : data.next;
          nextUrl = nextPath;
        } else {
          nextUrl = null;
        }
      } else {
        // Unexpected format
        break;
      }
    }
    return allAccounts;
  };

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Starting FAST COA data load...');
      
      // Single optimized API call that fetches everything
      const coaData = await loadChartOfAccountsFast();
      
      // Data is already sorted by account_number on the backend
      setAccounts(coaData.accounts);
      setAccountTypes(coaData.account_types);
      setDetailTypes(coaData.detail_types);
      
      console.log('FAST COA load complete:', {
        accountsCount: coaData.accounts.length,
        accountTypesCount: coaData.account_types.length,
        detailTypesCount: coaData.detail_types.length,
        serverLoadTime: coaData.metadata.load_time_ms + 'ms',
        totalQueries: coaData.metadata.database_queries,
        activeAccounts: coaData.metadata.active_accounts,
        transactionAccounts: coaData.metadata.transaction_accounts
      });
      
    } catch (err) {
      console.error('Error loading COA data:', err);
      console.error('Error details:', {
        message: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
        response: err instanceof Response ? err.statusText : undefined
      });
      
      // More specific error handling
      let errorMessage = 'Failed to load Chart of Accounts data.';
      if (err instanceof Error) {
        if (err.message.includes('Failed to fetch')) {
          errorMessage = 'Cannot connect to the backend server. Please check if the backend is running at http://localhost:8000';
        } else if (err.message.includes('401')) {
          errorMessage = 'Authentication failed. Please log in again.';
        } else if (err.message.includes('403')) {
          errorMessage = 'You do not have permission to access this data.';
        } else if (err.message.includes('404')) {
          errorMessage = 'Chart of Accounts endpoint not found. Please check the backend configuration.';
        } else {
          errorMessage = `Error: ${err.message}`;
        }
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = async () => {
    if (!isSuperUser) return;

    // Convert form data to proper types and remove empty values
    const accountData = {
      ...formData,
      account_type: formData.account_type ? Number(formData.account_type) : undefined,
      detail_type: formData.detail_type ? Number(formData.detail_type) : undefined,
      parent_account: isSubAccount && formData.parent_account ? Number(formData.parent_account) : undefined,
      // QuickBooks logic: if it's a sub-account, it's NOT a header account
      is_header_account: isSubAccount ? false : false, // User can set this manually later
      is_active: true, // Always active by default
    };
    
    // Remove undefined values
    Object.keys(accountData).forEach(key => {
      if (accountData[key as keyof typeof accountData] === undefined || accountData[key as keyof typeof accountData] === '') {
        delete accountData[key as keyof typeof accountData];
      }
    });

    try {
      setSaving(true);
      console.log('Creating account with data:', accountData);
      const newAccount = await accountsApi.create(accountData);
      setAccounts(prev => {
        const updatedAccounts = [...prev, newAccount];
        // Sort accounts by account number
        return updatedAccounts.sort((a, b) => {
          // Parse account numbers as numbers for proper sorting
          const numA = parseInt(a.account_number.replace(/\D/g, '')) || 0;
          const numB = parseInt(b.account_number.replace(/\D/g, '')) || 0;
          return numA - numB;
        });
      });
      setOpenCreateDialog(false);
      resetForm();
    } catch (err) {
      console.error('Error creating account:', err);
      console.error('Account data that failed:', accountData);
      
      let errorMessage = 'Failed to create account. Please try again.';
      if (err instanceof Error) {
        if (err.message.includes('400')) {
          errorMessage = `Validation error: ${err.message}. Please check all required fields and try again.`;
        } else {
          errorMessage = err.message;
        }
      }
      setError(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleToggleActive = async (accountId: number, currentStatus: boolean) => {
    if (!isSuperUser) return;

    try {
      const updatedAccount = await accountsApi.partialUpdate(accountId, { is_active: !currentStatus });
      setAccounts(prev => 
        prev.map(account => 
          account.id === accountId ? { ...account, is_active: updatedAccount.is_active } : account
        )
      );
      setSnackbar({
        open: true,
        message: updatedAccount.is_active ? 'Account activated' : 'Account deactivated',
        severity: 'success',
      });
    } catch (err) {
      console.error('Error updating account status:', err);
      setSnackbar({ open: true, message: 'Failed to update account status. Please try again.', severity: 'error' });
    }
  };

  const resetForm = () => {
    setFormData({
      account_number: '',
      account_name: '',
      description: '',
      account_type: '',
      detail_type: '',
      parent_account: '',
      currency: 'USD',
      is_active: true,
      is_header_account: false,
      opening_balance: '0.00',
      opening_balance_date: new Date().toISOString().split('T')[0],
      tax_line: '',
      bank_account_number: '',
      bank_routing_number: '',
    });
    setIsSubAccount(false);
  };

  // Detail Type Management Functions
  const resetDetailTypeForm = () => {
    setDetailTypeForm({
      code: '',
      name: '',
      description: '',
      account_type: '',
      requires_customer: false,
      requires_vendor: false,
      requires_item: false,
      is_tax_account: false,
      default_tax_code: '',
      balance_sheet_classification: '',
      sort_order: 0,
      is_active: true,
    });
    setEditingDetailType(null);
  };

  const handleCreateDetailType = () => {
    resetDetailTypeForm();
    setOpenDetailTypeDialog(true);
  };

  const handleEditDetailType = (detailType: DetailType) => {
    setEditingDetailType(detailType);
    setDetailTypeForm({
      code: detailType.code,
      name: detailType.name,
      description: detailType.description || '',
      account_type: detailType.account_type,
      requires_customer: detailType.requires_customer,
      requires_vendor: detailType.requires_vendor,
      requires_item: detailType.requires_item,
      is_tax_account: detailType.is_tax_account,
      default_tax_code: detailType.default_tax_code || '',
      balance_sheet_classification: detailType.balance_sheet_classification || '',
      sort_order: detailType.sort_order,
      is_active: detailType.is_active,
    });
    setOpenDetailTypeDialog(true);
  };

  const handleSaveDetailType = async () => {
    if (!detailTypeForm.name || !detailTypeForm.code || !detailTypeForm.account_type) {
      setSnackbar({ open: true, message: 'Name, Code, and Account Type are required.', severity: 'error' });
      return;
    }

    try {
      setSavingDetailType(true);
      
      // Convert empty string to undefined for API compatibility
      const apiData = {
        ...detailTypeForm,
        account_type: detailTypeForm.account_type || undefined
      };
      
      if (editingDetailType) {
        await detailTypesApi.update(editingDetailType.id, apiData);
        setSnackbar({ open: true, message: 'Detail type updated successfully.', severity: 'success' });
      } else {
        await detailTypesApi.create(apiData);
        setSnackbar({ open: true, message: 'Detail type created successfully.', severity: 'success' });
      }

      setOpenDetailTypeDialog(false);
      resetDetailTypeForm();
      await loadData(); // Reload data to reflect changes
    } catch (err) {
      console.error('Error saving detail type:', err);
      setSnackbar({ 
        open: true, 
        message: editingDetailType ? 'Failed to update detail type.' : 'Failed to create detail type.', 
        severity: 'error' 
      });
    } finally {
      setSavingDetailType(false);
    }
  };

  const handleToggleDetailTypeActive = async (detailTypeId: number, currentStatus: boolean) => {
    try {
      await detailTypesApi.partialUpdate(detailTypeId, { is_active: !currentStatus });
      setSnackbar({ 
        open: true, 
        message: `Detail type ${!currentStatus ? 'activated' : 'deactivated'} successfully.`, 
        severity: 'success' 
      });
      await loadData(); // Reload data to reflect changes
    } catch (err) {
      console.error('Error toggling detail type status:', err);
      setSnackbar({ 
        open: true, 
        message: 'Failed to update detail type status.', 
        severity: 'error' 
      });
    }
  };

  const filteredDetailTypes = detailTypes.filter(dt => 
    formData.account_type ? dt.account_type === Number(formData.account_type) : true
  );

  // Debug logging for detail types
  console.log('DetailTypes filtering debug:', {
    allDetailTypes: detailTypes.length,
    filteredDetailTypes: filteredDetailTypes.length,
    selectedAccountType: formData.account_type,
    detailTypesData: detailTypes.map(dt => ({
      id: dt.id,
      name: dt.name,
      account_type: dt.account_type,
      account_type_name: dt.account_type_name,
      is_active: dt.is_active
    }))
  });

  // Filter accounts based on search and status filters
  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = searchFilter === '' || 
      account.account_number.toLowerCase().includes(searchFilter.toLowerCase()) ||
      account.account_name.toLowerCase().includes(searchFilter.toLowerCase()) ||
      account.description?.toLowerCase().includes(searchFilter.toLowerCase()) ||
      account.account_type_name.toLowerCase().includes(searchFilter.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && account.is_active) ||
      (statusFilter === 'inactive' && !account.is_active);
    
    return matchesSearch && matchesStatus;
  });

  // Helper: Build hierarchical, sorted list for display
  function buildHierarchicalAccounts(accounts: Account[]) {
    // 1. Group by account_type, then detail_type
    const typeMap: Record<number, { name: string; details: Record<number, { name: string; accounts: Account[] }> }> = {};
    accounts.forEach(acc => {
      if (!typeMap[acc.account_type]) {
        typeMap[acc.account_type] = { name: acc.account_type_name, details: {} };
      }
      if (!typeMap[acc.account_type].details[acc.detail_type]) {
        typeMap[acc.account_type].details[acc.detail_type] = { name: acc.detail_type_name, accounts: [] };
      }
      typeMap[acc.account_type].details[acc.detail_type].accounts.push(acc);
    });

    // 2. For each detail_type group, build parent/child tree
    function buildTree(accs: Account[]): Account[] {
      const idMap: Record<number, Account & { children?: Account[] }> = {};
      accs.forEach(acc => { idMap[acc.id] = { ...acc, children: [] }; });
      const roots: (Account & { children?: Account[] })[] = [];
      accs.forEach(acc => {
        if (acc.parent_account && idMap[acc.parent_account]) {
          idMap[acc.parent_account].children!.push(idMap[acc.id]);
        } else {
          roots.push(idMap[acc.id]);
        }
      });
      // Sort children and roots by account_number
      function sortTree(nodes: (Account & { children?: Account[] })[]) {
        nodes.sort((a, b) => {
          const numA = parseInt(a.account_number.replace(/\D/g, '')) || 0;
          const numB = parseInt(b.account_number.replace(/\D/g, '')) || 0;
          return numA - numB;
        });
        nodes.forEach(n => n.children && sortTree(n.children));
      }
      sortTree(roots);
      return roots;
    }

    // 3. Flatten for rendering, with indentation
    type Row = { account: Account; indent: number } | { group: string; indent: number };
    const rows: Row[] = [];
    Object.entries(typeMap).sort((a, b) => a[1].name.localeCompare(b[1].name)).forEach(([typeId, typeObj]) => {
      rows.push({ group: typeObj.name, indent: 0 });
      Object.entries(typeObj.details).sort((a, b) => a[1].name.localeCompare(b[1].name)).forEach(([detailId, detailObj]) => {
        rows.push({ group: detailObj.name, indent: 1 });
        const tree = buildTree(detailObj.accounts);
        function walk(nodeList: (Account & { children?: Account[] })[], indent: number) {
          nodeList.forEach(acc => {
            rows.push({ account: acc, indent });
            if (acc.children && acc.children.length > 0) {
              walk(acc.children, indent + 1);
            }
          });
        }
        walk(tree, 2);
      });
    });
    return rows;
  }

  // Instead of filteredAccounts.map, use hierarchicalRows
  const hierarchicalRows = buildHierarchicalAccounts(filteredAccounts);

  if (loading || authLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            {authLoading ? 'Authenticating...' : 'Loading Chart of Accounts...'}
          </Typography>
        </Box>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Please log in to access the Chart of Accounts.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AccountTreeIcon sx={{ mr: 2, color: 'primary.main' }} />
              Chart of Accounts Management
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                onClick={loadData}
                size="small"
              >
                Refresh
              </Button>

              {isSuperUser && (
                <>
                  {currentTab === 0 ? (
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={() => setOpenCreateDialog(true)}
                      size="small"
                    >
                      New Account
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      startIcon={<CategoryIcon />}
                      onClick={handleCreateDetailType}
                      size="small"
                    >
                      New Detail Type
                    </Button>
                  )}
                </>
              )}
            </Box>
          }
        />
        
        {/* Tabs for switching between Chart of Accounts and Detail Types */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
            <Tab label="Chart of Accounts" />
            {isSuperUser && <Tab label="Detail Types Management" />}
            {isSuperUser && <Tab label="Upload COA" />}
          </Tabs>
        </Box>
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Tab Panel for Chart of Accounts */}
          {currentTab === 0 && (
            <Box>

          {/* Search and Filter Controls */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
            <TextField
              label="Search accounts..."
              variant="outlined"
              size="small"
              value={searchFilter}
              onChange={(e) => setSearchFilter(e.target.value)}
              sx={{ minWidth: 250 }}
              placeholder="Search by number, name, type, or description"
            />
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
                label="Status"
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
            {(searchFilter || statusFilter !== 'all') && (
              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  setSearchFilter('');
                  setStatusFilter('all');
                }}
              >
                Clear Filters
              </Button>
            )}
          </Box>

          {accounts.length === 0 ? (
            <Typography variant="body1" color="text.secondary" align="center" sx={{ py: 4 }}>
              No accounts found. Click "New Account" to get started or load sample data.
            </Typography>
          ) : (
            <Box>
              {/* Account Counter */}
              <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body2" color="text.secondary">
                  Showing {filteredAccounts.length} of {accounts.length} account{accounts.length !== 1 ? 's' : ''}
                  {(searchFilter || statusFilter !== 'all') && (
                    <Chip 
                      label="Filtered" 
                      size="small" 
                      variant="outlined" 
                      sx={{ ml: 1, height: 20 }}
                    />
                  )}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active: {filteredAccounts.filter(a => a.is_active).length} | 
                  Inactive: {filteredAccounts.filter(a => !a.is_active).length}
                </Typography>
              </Box>

              {/* Scrollable Table Container */}
              <TableContainer 
                sx={{ 
                  maxHeight: '70vh', 
                  overflow: 'auto',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 1
                }}
              >
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Account #</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Account Name</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Type</TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Balance</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Currency</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Status</TableCell>
                      {isSuperUser && <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Actions</TableCell>}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {hierarchicalRows.map((row, index) => {
                      if ('group' in row) {
                        return (
                          <TableRow key={`group-${row.group}-${index}`}>
                            <TableCell colSpan={isSuperUser ? 7 : 6} style={{ fontWeight: 'bold', paddingLeft: row.indent * 24, background: row.indent === 0 ? '#f5f5f5' : '#fafafa' }}>
                              {row.group}
                            </TableCell>
                          </TableRow>
                        );
                      }
                      const account = row.account;
                      return (
                        <TableRow 
                          key={account.id} 
                          hover 
                          sx={{ 
                            backgroundColor: account.is_header_account ? 'grey.50' : (index % 2 === 0 ? 'background.default' : 'grey.25'),
                            '&:hover': { backgroundColor: account.is_header_account ? 'grey.100' : 'action.hover' }
                          }}
                        >
                          <TableCell style={{ paddingLeft: row.indent * 24 }}>
                            <Typography 
                              variant="body2" 
                              fontWeight={account.is_header_account ? "bold" : "normal"}
                            >
                              {account.account_number}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box>
                              <Typography 
                                variant="body2" 
                                fontWeight={account.is_header_account ? "bold" : "normal"}
                              >
                                {account.account_name}
                                {account.is_header_account && (
                                  <Chip 
                                    label="Header" 
                                    size="small" 
                                    sx={{ ml: 1, height: 20 }}
                                    variant="outlined"
                                    color="primary"
                                  />
                                )}
                              </Typography>
                              {account.description && (
                                <Typography variant="caption" color="text.secondary" display="block">
                                  {account.description}
                                </Typography>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={account.account_type_name}
                              size="small"
                              sx={{
                                backgroundColor: getAccountTypeColor(account.account_type_name),
                                color: 'white',
                                fontWeight: 'medium',
                              }}
                            />
                            <Typography variant="caption" display="block" color="text.secondary">
                              {account.detail_type_name}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            {!account.is_header_account && (
                              <Typography
                                variant="body2"
                                fontWeight="medium"
                                color={parseFloat(account.current_balance) >= 0 ? 'success.main' : 'error.main'}
                              >
                                {formatCurrency(account.current_balance, account.currency)}
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">{account.currency}</Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={account.is_active ? 'Active' : 'Inactive'}
                              size="small"
                              color={account.is_active ? 'success' : 'default'}
                              variant="outlined"
                            />
                          </TableCell>
                          {isSuperUser && (
                            <TableCell>
                              <Tooltip title={account.is_active ? 'Deactivate Account' : 'Activate Account'}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleToggleActive(account.id, account.is_active)}
                                  color={account.is_active ? 'error' : 'success'}
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          )}
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
            </Box>
          )}

          {/* Tab Panel for Detail Types Management */}
          {currentTab === 1 && isSuperUser && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>Detail Types Management</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Manage detail types that provide specific categorization within account types (similar to QuickBooks detail types).
              </Typography>

              {/* Detail Types Table */}
              <TableContainer sx={{ border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Code</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Name</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Account Type</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Classification</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Description</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Accounts Count</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Status</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: 'grey.50' }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {detailTypes
                      .sort((a, b) => a.account_type_name?.localeCompare(b.account_type_name || '') || 0)
                      .map((detailType) => (
                        <TableRow key={detailType.id}>
                          <TableCell>
                            <Chip label={detailType.code} size="small" variant="outlined" />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {detailType.name}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={detailType.account_type_name}
                              size="small"
                              sx={{
                                backgroundColor: getAccountTypeColor(detailType.account_type_name || ''),
                                color: 'white',
                                fontWeight: 'medium',
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            {detailType.balance_sheet_classification && (
                              <Chip
                                label={detailType.balance_sheet_classification === 'CURRENT' ? 'Current' : 'Non-Current'}
                                size="small"
                                color={detailType.balance_sheet_classification === 'CURRENT' ? 'success' : 'warning'}
                                variant="outlined"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {detailType.description || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {detailType.accounts_count || 0}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={detailType.is_active ? 'Active' : 'Inactive'}
                              size="small"
                              color={detailType.is_active ? 'success' : 'default'}
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Tooltip title="Edit Detail Type">
                                <IconButton
                                  size="small"
                                  onClick={() => handleEditDetailType(detailType)}
                                  color="primary"
                                >
                                  <EditIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title={detailType.is_active ? 'Deactivate' : 'Activate'}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleToggleDetailTypeActive(detailType.id, detailType.is_active)}
                                  color={detailType.is_active ? 'error' : 'success'}
                                >
                                  {detailType.is_active ? <DeactivateIcon /> : <ActivateIcon />}
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Tab Panel for COA Upload */}
          {currentTab === 2 && isSuperUser && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>Upload Chart of Accounts</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Upload your Chart of Accounts from a CSV file. You can validate the file first before importing.
              </Typography>
              <COAUploadForm onUploadSuccess={loadData} />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* EXACT QuickBooks Create Account Dialog */}
      <Dialog open={openCreateDialog} onClose={() => setOpenCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          New account
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            
            {/* Account Type - FIRST like QuickBooks */}
            <FormControl fullWidth required>
              <InputLabel>Account Type *</InputLabel>
              <Select
                value={formData.account_type}
                onChange={(e) => {
                  setFormData(prev => ({ 
                    ...prev, 
                    account_type: e.target.value as number | '',
                    detail_type: '' // Reset detail type when account type changes
                  }));
                }}
                label="Account Type *"
              >
                {accountTypes.map((type) => (
                  <MenuItem key={type.id} value={type.id}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Detail Type - SECOND like QuickBooks */}
            <FormControl fullWidth required>
              <InputLabel>Detail Type *</InputLabel>
              <Select
                value={formData.detail_type}
                onChange={(e) => setFormData(prev => ({ ...prev, detail_type: e.target.value as number | '' }))}
                label="Detail Type *"
                disabled={!formData.account_type}
              >
                {filteredDetailTypes.map((type) => (
                  <MenuItem key={type.id} value={type.id}>
                    {type.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Make this a sub-account - EXACT QuickBooks checkbox */}
            <FormControlLabel
              control={
                <Checkbox
                  checked={isSubAccount}
                  onChange={(e) => {
                    setIsSubAccount(e.target.checked);
                    if (!e.target.checked) {
                      setFormData(prev => ({ ...prev, parent_account: '' }));
                    }
                  }}
                />
              }
              label="Make this a sub-account"
            />
            
            {/* Parent Account dropdown - shows when checkbox is checked */}
            {isSubAccount && (
              <FormControl fullWidth sx={{ ml: 3 }}>
                <InputLabel>Parent account *</InputLabel>
                <Select
                  value={formData.parent_account}
                  onChange={(e) => setFormData(prev => ({ ...prev, parent_account: e.target.value as number | '' }))}
                  label="Parent account *"
                >
                  {accounts
                    .sort((a, b) => {
                      // Sort by account number for better organization
                      const numA = parseInt(a.account_number.replace(/\D/g, '')) || 0;
                      const numB = parseInt(b.account_number.replace(/\D/g, '')) || 0;
                      return numA - numB;
                    })
                    .map((account) => (
                      <MenuItem key={account.id} value={account.id}>
                        {account.account_number} - {account.account_name}
                        {account.is_header_account && <span style={{ color: '#1976d2', fontSize: '0.8em' }}> (Header)</span>}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            )}

            {/* Account Number */}
            <TextField
              label="Account number"
              value={formData.account_number}
              onChange={(e) => setFormData(prev => ({ ...prev, account_number: e.target.value }))}
              fullWidth
            />

            {/* Account Name */}
            <TextField
              label="Account name *"
              value={formData.account_name}
              onChange={(e) => setFormData(prev => ({ ...prev, account_name: e.target.value }))}
              required
              fullWidth
            />

            {/* Description */}
            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              multiline
              rows={3}
              fullWidth
            />

            {/* Opening balance section - like QuickBooks */}
            <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
              Opening balance
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                label="Opening balance"
                type="number"
                value={formData.opening_balance}
                onChange={(e) => setFormData(prev => ({ ...prev, opening_balance: e.target.value }))}
                fullWidth
                inputProps={{ step: "0.01" }}
              />
              <TextField
                label="as of"
                type="date"
                value={formData.opening_balance_date}
                onChange={(e) => setFormData(prev => ({ ...prev, opening_balance_date: e.target.value }))}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />
            </Box>

          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenCreateDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button 
            onClick={handleCreateAccount} 
            variant="contained"
            disabled={saving || !formData.account_name || !formData.account_type || !formData.detail_type}
          >
            {saving ? <CircularProgress size={20} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Detail Type Dialog */}
      <Dialog open={openDetailTypeDialog} onClose={() => setOpenDetailTypeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingDetailType ? 'Edit Detail Type' : 'New Detail Type'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ pt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Code *"
                value={detailTypeForm.code}
                onChange={(e) => setDetailTypeForm(prev => ({ ...prev, code: e.target.value.toUpperCase() }))}
                fullWidth
                required
                placeholder="e.g., CASH_BANK, AR_TRADE"
                helperText="Unique identifier for this detail type"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Name *"
                value={detailTypeForm.name}
                onChange={(e) => setDetailTypeForm(prev => ({ ...prev, name: e.target.value }))}
                fullWidth
                required
                placeholder="e.g., Cash and Bank, Accounts Receivable"
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Account Type *</InputLabel>
                <Select
                  value={detailTypeForm.account_type}
                  onChange={(e) => setDetailTypeForm(prev => ({ ...prev, account_type: e.target.value as number }))}
                  label="Account Type *"
                >
                  {accountTypes.map((type) => (
                    <MenuItem key={type.id} value={type.id}>
                      {type.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                value={detailTypeForm.description}
                onChange={(e) => setDetailTypeForm(prev => ({ ...prev, description: e.target.value }))}
                fullWidth
                multiline
                rows={3}
                placeholder="Detailed description of when to use this detail type"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Sort Order"
                type="number"
                value={detailTypeForm.sort_order}
                onChange={(e) => setDetailTypeForm(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                fullWidth
                helperText="Order for displaying in lists"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                label="Default Tax Code"
                value={detailTypeForm.default_tax_code}
                onChange={(e) => setDetailTypeForm(prev => ({ ...prev, default_tax_code: e.target.value }))}
                fullWidth
                placeholder="e.g., GST, VAT"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Balance Sheet Classification</InputLabel>
                <Select
                  value={detailTypeForm.balance_sheet_classification}
                  onChange={(e) => setDetailTypeForm(prev => ({ ...prev, balance_sheet_classification: e.target.value as 'CURRENT' | 'NON_CURRENT' | '' }))}
                  label="Balance Sheet Classification"
                >
                  <MenuItem value="">Not Applicable</MenuItem>
                  <MenuItem value="CURRENT">Current (&lt; 1 year)</MenuItem>
                  <MenuItem value="NON_CURRENT">Non-Current (&gt; 1 year)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={
                  <Switch
                    checked={detailTypeForm.is_active}
                    onChange={(e) => setDetailTypeForm(prev => ({ ...prev, is_active: e.target.checked }))}
                  />
                }
                label="Active"
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Tracking Requirements</Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={detailTypeForm.requires_customer}
                      onChange={(e) => setDetailTypeForm(prev => ({ ...prev, requires_customer: e.target.checked }))}
                    />
                  }
                  label="Requires Customer"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={detailTypeForm.requires_vendor}
                      onChange={(e) => setDetailTypeForm(prev => ({ ...prev, requires_vendor: e.target.checked }))}
                    />
                  }
                  label="Requires Vendor"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={detailTypeForm.requires_item}
                      onChange={(e) => setDetailTypeForm(prev => ({ ...prev, requires_item: e.target.checked }))}
                    />
                  }
                  label="Requires Item"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={detailTypeForm.is_tax_account}
                      onChange={(e) => setDetailTypeForm(prev => ({ ...prev, is_tax_account: e.target.checked }))}
                    />
                  }
                  label="Tax Account"
                />
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenDetailTypeDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button 
            onClick={handleSaveDetailType} 
            variant="contained"
            disabled={savingDetailType || !detailTypeForm.name || !detailTypeForm.code || !detailTypeForm.account_type}
          >
            {savingDetailType ? <CircularProgress size={20} /> : (editingDetailType ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSnackbar(s => ({ ...s, open: false }))} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ChartOfAccountsPage; 