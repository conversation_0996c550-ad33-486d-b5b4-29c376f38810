#!/usr/bin/env python
"""
Setup Input Tax Account for GRN Journal Entries

This management command creates the necessary Input Tax account
if it doesn't exist in the Chart of Accounts.

Run with: python manage.py setup_tax_accounts
"""

from django.core.management.base import BaseCommand
from decimal import Decimal
from django.contrib.auth.models import User


class Command(BaseCommand):
    help = 'Setup Input Tax account for GRN journal entries'

    def handle(self, *args, **options):
        self.stdout.write('Setting up Input Tax account for GRN journal entries...')
        
        try:
            from gl.models import Account, AccountType
            
            # Get a system user for account creation
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.first()
            
            # Get existing account types
            asset_type = AccountType.objects.filter(name='CURRENT_ASSET').first()
            liability_type = AccountType.objects.filter(name='CURRENT_LIABILITY').first()
            
            # Fallback if specific types don't exist
            if not asset_type:
                asset_type = AccountType.objects.filter(type='ASSET').first()
            if not liability_type:
                liability_type = AccountType.objects.filter(type='LIABILITY').first()
            
            # Check if Input Tax account exists
            input_tax_account = Account.objects.filter(
                account_name__icontains='Input Tax'
            ).first()
            
            if input_tax_account:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ Input Tax account already exists: '
                        f'{input_tax_account.account_number} - {input_tax_account.account_name}'
                    )
                )
            else:
                # Create Input Tax account
                # Find the highest account number in Assets (1000-1999) for Tax accounts
                max_tax_account = Account.objects.filter(
                    account_number__startswith='1'
                ).order_by('-account_number').first()
                
                # Set account number (use 1500+ range for tax accounts)
                if max_tax_account:
                    try:
                        last_number = int(max_tax_account.account_number)
                        # Start from 1500 if no tax accounts exist
                        new_number = max(last_number + 1, 1500)
                    except:
                        new_number = 1500
                else:
                    new_number = 1500
                
                input_tax_account = Account.objects.create(
                    account_number=str(new_number),
                    account_name='Input Tax Receivable',
                    account_type=asset_type,
                    opening_balance=Decimal('0.00'),
                    description='Input tax on purchases (GST/VAT receivable)',
                    is_active=True,
                    created_by=admin_user
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ Created Input Tax account: '
                        f'{input_tax_account.account_number} - {input_tax_account.account_name}'
                    )
                )
            
            # Check if Accounts Payable exists
            payable_account = Account.objects.filter(
                account_name__icontains='Accounts Payable'
            ).first()
            
            if payable_account:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ Accounts Payable account exists: '
                        f'{payable_account.account_number} - {payable_account.account_name}'
                    )
                )
            else:
                # Create Accounts Payable account
                # Find the highest account number in Liabilities (2000-2999)
                max_liability_account = Account.objects.filter(
                    account_number__startswith='2'
                ).order_by('-account_number').first()
                
                if max_liability_account:
                    try:
                        last_number = int(max_liability_account.account_number)
                        new_number = max(last_number + 1, 2000)
                    except:
                        new_number = 2000
                else:
                    new_number = 2000
                
                payable_account = Account.objects.create(
                    account_number=str(new_number),
                    account_name='Accounts Payable',
                    account_type=liability_type,
                    opening_balance=Decimal('0.00'),
                    description='Amounts owed to vendors for goods and services',
                    is_active=True,
                    created_by=admin_user
                )
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ Created Accounts Payable account: '
                        f'{payable_account.account_number} - {payable_account.account_name}'
                    )
                )
            
            self.stdout.write(
                self.style.SUCCESS(
                    '\n🎯 Tax accounts setup complete! '
                    'GRN journal entries will now properly handle input tax.'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error setting up tax accounts: {str(e)}')
            ) 