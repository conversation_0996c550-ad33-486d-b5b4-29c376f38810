# SearchableSelect Component

The SearchableSelect component is a reusable dropdown component that provides searchable functionality for selecting options from a list. It wraps Material-UI's Autocomplete component with common functionality needed for dropdown fields in the application.

## Features

- Searchable dropdown with autocomplete
- Support for custom option rendering
- Support for grouping options
- Customizable styling
- Error handling and validation
- Disabled state
- Required field indication
- Placeholder text

## Usage

```tsx
import SearchableSelect from '../../../shared/components/SearchableSelect';

// Define options
const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];

// Use in a component
<SearchableSelect
  options={options}
  value={options.find(option => option.value === selectedValue) || null}
  onChange={(newValue) => {
    setSelectedValue(newValue ? newValue.value : '');
  }}
  getOptionLabel={(option) => option.label}
  label="Select an option"
  error={Boolean(error)}
  helperText={error}
  required
/>
```

## Props

| Prop | Type | Description |
|------|------|-------------|
| `options` | `T[]` | Array of options to display in the dropdown |
| `value` | `T \| null` | Currently selected option |
| `onChange` | `(value: T \| null) => void` | Callback when selection changes |
| `getOptionLabel` | `(option: T) => string` | Function to get the display label for an option |
| `label` | `string` | Label for the dropdown |
| `error` | `boolean` | Whether the field has an error |
| `helperText` | `string` | Helper text to display (usually error message) |
| `placeholder` | `string` | Placeholder text when no option is selected |
| `required` | `boolean` | Whether the field is required |
| `disabled` | `boolean` | Whether the field is disabled |
| `fullWidth` | `boolean` | Whether the field should take up the full width |
| `size` | `'small' \| 'medium'` | Size of the field |
| `groupBy` | `(option: T) => string` | Function to group options by |
| `renderGroup` | `(params: any) => React.ReactNode` | Custom rendering for option groups |
| `renderOption` | `(props: React.HTMLAttributes<HTMLLIElement>, option: T, state: object) => React.ReactNode` | Custom rendering for options |
| `noOptionsText` | `string` | Text to display when no options match the search |
| `loadingText` | `string` | Text to display when options are loading |
| `loading` | `boolean` | Whether options are loading |
| `disableClearable` | `boolean` | Whether to disable the clear button |

## Examples

### Basic Usage

```tsx
<SearchableSelect
  options={countries}
  value={countries.find(country => country.code === formik.values.country) || null}
  onChange={(newValue) => {
    formik.setFieldValue('country', newValue ? newValue.code : '');
  }}
  getOptionLabel={(option) => option.name}
  label="Country"
/>
```

### With Grouping

```tsx
<SearchableSelect
  options={products}
  value={products.find(p => p.id === selectedProductId) || null}
  onChange={(newValue) => {
    setSelectedProductId(newValue ? newValue.id : '');
  }}
  getOptionLabel={(option) => option.name}
  label="Product"
  groupBy={(option) => option.category}
/>
```

### With Error Handling

```tsx
<SearchableSelect
  options={options}
  value={options.find(option => option.value === formik.values.option) || null}
  onChange={(newValue) => {
    formik.setFieldValue('option', newValue ? newValue.value : '');
  }}
  getOptionLabel={(option) => option.label}
  label="Option"
  error={formik.touched.option && Boolean(formik.errors.option)}
  helperText={formik.touched.option && formik.errors.option}
  required
/>
```

## Integration with Formik

The SearchableSelect component works well with Formik. Here's an example of how to use it with Formik:

```tsx
<SearchableSelect
  options={options}
  value={options.find(option => option.value === formik.values.option) || null}
  onChange={(newValue) => {
    formik.setFieldValue('option', newValue ? newValue.value : '');
  }}
  getOptionLabel={(option) => option.label}
  label="Option"
  error={formik.touched.option && Boolean(formik.errors.option)}
  helperText={formik.touched.option && formik.errors.option}
  required
/>
``` 