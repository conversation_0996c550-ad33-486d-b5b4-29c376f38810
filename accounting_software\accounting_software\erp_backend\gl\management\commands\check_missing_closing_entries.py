"""
Django management command to check for missing closing entries
"""

from django.core.management.base import BaseCommand
from django.db.models import Sum
from gl.models import Account, JournalEntryLine, JournalEntry
from gl.views import calculate_net_income_for_period
from account.models import Company
from datetime import date
from decimal import Decimal


class Command(BaseCommand):
    help = 'Check for revenue/expense accounts that need to be closed to Retained Earnings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--year',
            type=int,
            default=2024,
            help='Fiscal year to check (default: 2024)',
        )

    def handle(self, *args, **options):
        year = options['year']
        
        self.stdout.write(
            self.style.SUCCESS(f'🔍 Checking Missing Closing Entries for FY{year}')
        )
        
        # Get company fiscal year settings
        company = Company.objects.first()
        if not company:
            self.stdout.write(
                self.style.ERROR('❌ No company configured')
            )
            return

        # Calculate fiscal year dates
        fy_start = company.fiscal_year_start.replace(year=year)
        fy_end = company.fiscal_year_end.replace(year=year)
        
        # Adjust for fiscal years that span calendar years
        if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
           (company.fiscal_year_end.month == company.fiscal_year_start.month and 
            company.fiscal_year_end.day < company.fiscal_year_start.day):
            fy_end = fy_end.replace(year=year + 1)
        
        self.stdout.write(f'📅 Fiscal Year {year}: {fy_start} to {fy_end}')
        
        # Check Revenue Accounts
        self.stdout.write(f'\n💰 REVENUE ACCOUNTS:')
        revenue_accounts = Account.objects.filter(
            account_type__type='REVENUE',
            is_active=True
        ).exclude(account_number='39999')
        
        total_revenue = Decimal('0.00')
        revenue_count = 0
        
        for account in revenue_accounts:
            debits = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__status='POSTED',
                journal_entry__transaction_date__gte=fy_start,
                journal_entry__transaction_date__lte=fy_end
            ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
            
            credits = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__status='POSTED',
                journal_entry__transaction_date__gte=fy_start,
                journal_entry__transaction_date__lte=fy_end
            ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
            
            balance = credits - debits  # Revenue normal balance is credit
            if balance != 0:
                self.stdout.write(f'   {account.account_number} - {account.account_name}: {balance:,.2f}')
                total_revenue += balance
                revenue_count += 1
        
        if revenue_count == 0:
            self.stdout.write('   ⚠️  No revenue entries found')
        else:
            self.stdout.write(f'   📊 Total Revenue: {total_revenue:,.2f}')
        
        # Check Expense Accounts
        self.stdout.write(f'\n💸 EXPENSE ACCOUNTS:')
        expense_accounts = Account.objects.filter(
            account_type__type='EXPENSE',
            is_active=True
        ).exclude(account_number='39999')
        
        total_expenses = Decimal('0.00')
        expense_count = 0
        
        for account in expense_accounts:
            debits = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__status='POSTED',
                journal_entry__transaction_date__gte=fy_start,
                journal_entry__transaction_date__lte=fy_end
            ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
            
            credits = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__status='POSTED',
                journal_entry__transaction_date__gte=fy_start,
                journal_entry__transaction_date__lte=fy_end
            ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
            
            balance = debits - credits  # Expense normal balance is debit
            if balance != 0:
                self.stdout.write(f'   {account.account_number} - {account.account_name}: {balance:,.2f}')
                total_expenses += balance
                expense_count += 1
        
        if expense_count == 0:
            self.stdout.write('   ⚠️  No expense entries found')
        else:
            self.stdout.write(f'   📊 Total Expenses: {total_expenses:,.2f}')
        
        # Calculate manual net income
        manual_net_income = total_revenue - total_expenses
        
        # Calculate using the function
        function_net_income = calculate_net_income_for_period(fy_start, fy_end)
        
        self.stdout.write(f'\n📈 NET INCOME COMPARISON:')
        self.stdout.write(f'   Manual Calculation: {manual_net_income:,.2f}')
        self.stdout.write(f'   Function Calculation: {function_net_income:,.2f}')
        
        if abs(manual_net_income - function_net_income) < Decimal('0.01'):
            self.stdout.write('   ✅ Calculations match!')
        else:
            self.stdout.write('   ⚠️  Calculations do not match - investigate discrepancy')
        
        # Check if closing entry exists
        closing_entry = JournalEntry.objects.filter(
            entry_number=f'CLOSE-{year}',
            status='POSTED'
        ).first()
        
        if closing_entry:
            self.stdout.write(f'\n📝 CLOSING ENTRY FOUND:')
            self.stdout.write(f'   Entry: {closing_entry.entry_number}')
            
            # Check retained earnings impact
            re_line = closing_entry.journal_lines.filter(
                account__account_name__icontains='retained earnings'
            ).first()
            
            if re_line:
                re_impact = re_line.credit_amount - re_line.debit_amount
                self.stdout.write(f'   Retained Earnings Impact: {re_impact:,.2f}')
                
                if abs(re_impact - manual_net_income) < Decimal('0.01'):
                    self.stdout.write('   ✅ Closing entry matches calculated net income')
                else:
                    self.stdout.write('   ❌ Closing entry does NOT match - may need recalculation')
        else:
            self.stdout.write(f'\n❌ NO CLOSING ENTRY FOUND for FY{year}')
            if revenue_count > 0 or expense_count > 0:
                self.stdout.write(f'   🔧 Run: python manage.py fix_retained_earnings_closing --year {year} --confirm')
        
        # Summary
        self.stdout.write(f'\n🎯 SUMMARY:')
        if revenue_count > 0 or expense_count > 0:
            self.stdout.write(f'   📊 Found {revenue_count} revenue and {expense_count} expense accounts with activity')
            self.stdout.write(f'   💰 Net Income: {manual_net_income:,.2f}')
            if not closing_entry:
                self.stdout.write('   ❌ Missing closing entry - Balance Sheet will not balance')
            else:
                self.stdout.write('   ✅ Closing entry exists')
        else:
            self.stdout.write('   ⚠️  No Income Statement activity found for this period') 