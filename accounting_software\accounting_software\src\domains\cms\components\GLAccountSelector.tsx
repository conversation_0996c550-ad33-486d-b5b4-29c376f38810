import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  AccountBalance as AccountBalanceIcon,
  Info as InfoIcon
} from '@mui/icons-material';
// No auth service import needed - using localStorage directly

interface GLAccount {
  id: number;
  account_number: string;
  account_name: string;
  balance: number;
  currency: string;
}

interface GLCashAccountsResponse {
  accounts: GLAccount[];
  account_type: string;
  detail_type: string;
  message?: string;
}

interface GLAccountSelectorProps {
  value: number | null;
  onChange: (accountId: number | null) => void;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  label?: string;
}

const GLAccountSelector: React.FC<GLAccountSelectorProps> = ({
  value,
  onChange,
  error = false,
  helperText,
  disabled = false,
  label = "Link to GL Cash Account"
}) => {
  const [glAccounts, setGlAccounts] = useState<GLAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [accountType, setAccountType] = useState<string>('');
  const [detailType, setDetailType] = useState<string>('');
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    fetchGLCashAccounts();
  }, []);

  const fetchGLCashAccounts = async () => {
    setLoading(true);
    setApiError(null);
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/gl/cash-accounts/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch GL cash accounts');
      }

      const data: GLCashAccountsResponse = await response.json();
      setGlAccounts(data.accounts);
      setAccountType(data.account_type);
      setDetailType(data.detail_type);
      setMessage(data.message || '');

    } catch (error) {
      console.error('Error fetching GL cash accounts:', error);
      setApiError(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const selectedAccount = glAccounts.find(account => account.id === value);

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(amount);
  };

  return (
    <Box>
      {/* GL Account Selection */}
      <FormControl fullWidth error={error} disabled={disabled}>
        <InputLabel>{label}</InputLabel>
        <Select
          value={value || ''}
          onChange={(e) => onChange(e.target.value ? Number(e.target.value) : null)}
          label={label}
        >
          <MenuItem value="">
            <em>Select a GL Cash Account</em>
          </MenuItem>
          {loading && (
            <MenuItem disabled>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              Loading accounts...
            </MenuItem>
          )}
          {glAccounts.map((account) => (
            <MenuItem key={account.id} value={account.id}>
              <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {account.account_number} - {account.account_name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Balance: {formatCurrency(account.balance, account.currency)} • {account.currency}
                </Typography>
              </Box>
            </MenuItem>
          ))}
        </Select>
        {helperText && (
          <Typography variant="caption" color={error ? 'error' : 'text.secondary'} sx={{ mt: 0.5, ml: 1.5 }}>
            {helperText}
          </Typography>
        )}
      </FormControl>

      {/* Error Display */}
      {apiError && (
        <Alert severity="error" sx={{ mt: 2 }}>
          <Typography variant="body2">{apiError}</Typography>
        </Alert>
      )}

      {/* Message from API */}
      {message && (
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">{message}</Typography>
        </Alert>
      )}

      {/* Selected Account Details */}
      {selectedAccount && (
        <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            <AccountBalanceIcon sx={{ fontSize: 16, mr: 1, verticalAlign: 'middle' }} />
            Selected GL Account
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 1 }}>
            <Chip 
              label={`${accountType} > ${detailType}`} 
              size="small" 
              color="primary" 
              variant="outlined" 
            />
            <Chip 
              label={selectedAccount.currency} 
              size="small" 
              color="secondary" 
              variant="outlined" 
            />
          </Box>
          <Typography variant="body2" color="text.secondary">
            {selectedAccount.account_number} - {selectedAccount.account_name}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Current Balance: {formatCurrency(selectedAccount.balance, selectedAccount.currency)}
          </Typography>
        </Box>
      )}

      {/* Information Section */}
      <Accordion sx={{ mt: 2 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="subtitle2">
              About GL Integration
            </Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            <ListItem>
              <ListItemText
                primary="Cash accounts must be created in Chart of Accounts (GL Module)"
                secondary="Navigate to GL > Chart of Accounts to create new cash accounts"
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemText
                primary="Account Requirements"
                secondary="Only Asset accounts with Cash and Bank detail type are available"
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemText
                primary="Integration Benefits"
                secondary="Centralized accounting, automatic GL posting, unified financial reporting"
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemText
                primary="Balance Synchronization"
                secondary="Account balances are synchronized between CMS and GL automatically"
              />
            </ListItem>
          </List>
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default GLAccountSelector; 