# Generated by Django 4.2.21 on 2025-06-16 19:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0003_add_buyer_info_and_uom'),
    ]

    operations = [
        migrations.AddField(
            model_name='purchaseorder',
            name='buyer_email',
            field=models.EmailField(blank=True, help_text='Email of the buyer/purchaser', max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='buyer_name',
            field=models.CharField(blank=True, help_text='Name of the buyer/purchaser', max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='buyer_phone',
            field=models.CharField(blank=True, help_text='Phone of the buyer/purchaser', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='notes',
            field=models.TextField(blank=True, help_text='Notes to vendor', null=True),
        ),
        migrations.AlterField(
            model_name='purchaseorder',
            name='memo',
            field=models.TextField(blank=True, help_text='Internal memo', null=True),
        ),
    ]
