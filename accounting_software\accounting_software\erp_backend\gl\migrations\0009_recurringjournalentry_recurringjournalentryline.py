# Generated by Django 4.2.21 on 2025-06-22 20:43

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('gl', '0008_add_balance_sheet_classification'),
    ]

    operations = [
        migrations.CreateModel(
            name='RecurringJournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_name', models.CharField(help_text='Descriptive name for this recurring template', max_length=200)),
                ('description', models.CharField(help_text='Description for journal entries created from this template', max_length=500)),
                ('reference_number', models.CharField(blank=True, help_text='Reference number template', max_length=100)),
                ('memo', models.TextField(blank=True, help_text='Memo template for generated entries')),
                ('frequency', models.CharField(choices=[('DAILY', 'Daily'), ('WEEKLY', 'Weekly'), ('MONTHLY', 'Monthly'), ('QUARTERLY', 'Quarterly'), ('YEARLY', 'Yearly')], default='MONTHLY', help_text='How often to generate journal entries', max_length=20)),
                ('start_date', models.DateField(help_text='Date to start generating recurring entries')),
                ('end_date', models.DateField(blank=True, help_text='Date to stop generating entries (leave blank for indefinite)', null=True)),
                ('next_generation_date', models.DateField(blank=True, help_text='Next date when entry should be generated', null=True)),
                ('currency', models.CharField(default='INR', help_text='Currency for this recurring template', max_length=3)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this template is active for generation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_generated', models.PositiveIntegerField(default=0, help_text='Total number of journal entries generated from this template')),
                ('last_generated_at', models.DateTimeField(blank=True, help_text='When the last journal entry was generated', null=True)),
                ('created_by', models.ForeignKey(help_text='User who created this template', on_delete=django.db.models.deletion.PROTECT, related_name='created_recurring_entries', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Recurring Journal Entry',
                'verbose_name_plural': 'Recurring Journal Entries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='RecurringJournalEntryLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Debit amount template', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Credit amount template', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('description', models.CharField(help_text='Description template for this line', max_length=500)),
                ('memo', models.TextField(blank=True, help_text='Memo template for this line')),
                ('line_number', models.PositiveIntegerField(help_text='Line sequence number')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(help_text='The account for this line', on_delete=django.db.models.deletion.PROTECT, related_name='recurring_journal_lines', to='gl.account')),
                ('recurring_entry', models.ForeignKey(help_text='The recurring template this line belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='recurring_lines', to='gl.recurringjournalentry')),
            ],
            options={
                'verbose_name': 'Recurring Journal Entry Line',
                'verbose_name_plural': 'Recurring Journal Entry Lines',
                'ordering': ['recurring_entry', 'line_number'],
                'unique_together': {('recurring_entry', 'line_number')},
            },
        ),
    ]
