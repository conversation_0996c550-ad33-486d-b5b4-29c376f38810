/**
 * Formats a number as currency with thousands separators and decimal places
 * @param value - The numeric value to format
 * @param currencyCode - The currency code (e.g., 'USD', 'EUR')
 * @param locale - The locale to use for formatting (defaults to 'en-US')
 * @param decimalPlaces - The number of decimal places to show (defaults to 2)
 * @param hideSymbol - Whether to hide the currency symbol (defaults to false)
 * @param showBlankForZero - Whether to show blank instead of 0.00 (defaults to true, like QuickBooks)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number,
  currencyCode: string = 'USD',
  locale: string = 'en-US',
  decimalPlaces: number = 2,
  hideSymbol: boolean = false,
  showBlankForZero: boolean = true
): string => {
  // Handle undefined or null values
  if (value === undefined || value === null) {
    return '';
  }

  // Show blank for zero values (QuickBooks style)
  if (showBlankForZero && value === 0) {
    return '';
  }

  try {
    // Use Intl.NumberFormat for locale-aware formatting
    if (hideSymbol) {
      return new Intl.NumberFormat(locale, {
        style: 'decimal',
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces,
      }).format(value);
    } else {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces,
      }).format(value);
    }
  } catch (error) {
    // Fallback to basic formatting if Intl.NumberFormat fails
    const sign = value < 0 ? '-' : '';
    const absValue = Math.abs(value);
    const parts = absValue.toFixed(decimalPlaces).split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    // Get currency symbol based on currency code
    let symbol = '$';
    switch (currencyCode) {
      case 'EUR': symbol = '€'; break;
      case 'GBP': symbol = '£'; break;
      case 'JPY': symbol = '¥'; break;
      case 'INR': symbol = '₹'; break;
      case 'CAD': symbol = 'C$'; break;
      case 'AUD': symbol = 'A$'; break;
      case 'CHF': symbol = 'Fr'; break;
      case 'CNY': symbol = '¥'; break;
      case 'SEK': symbol = 'kr'; break;
      case 'NZD': symbol = 'NZ$'; break;
      default: symbol = '$';
    }
    
    return hideSymbol ? `${sign}${parts.join('.')}` : `${sign}${symbol}${parts.join('.')}`;
  }
};

/**
 * Formats a date string or Date object for display
 * @param date - The date to format (string, Date, or null/undefined)
 * @param format - The format type ('short', 'medium', 'long', 'full', or custom format)
 * @param locale - The locale to use for formatting (defaults to 'en-US')
 * @returns Formatted date string
 */
export const formatDate = (
  date: string | Date | null | undefined,
  format: 'short' | 'medium' | 'long' | 'full' | string = 'medium',
  locale: string = 'en-US'
): string => {
  // Handle null or undefined values
  if (!date) {
    return '';
  }

  try {
    // Convert string to Date if necessary
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Handle predefined formats
    switch (format) {
      case 'short':
        return new Intl.DateTimeFormat(locale, {
          year: '2-digit',
          month: 'numeric',
          day: 'numeric'
        }).format(dateObj);
      
      case 'medium':
        return new Intl.DateTimeFormat(locale, {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        }).format(dateObj);
      
      case 'long':
        return new Intl.DateTimeFormat(locale, {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }).format(dateObj);
      
      case 'full':
        return new Intl.DateTimeFormat(locale, {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }).format(dateObj);
      
      default:
        // For custom formats, use the format string as options
        return new Intl.DateTimeFormat(locale).format(dateObj);
    }
  } catch (error) {
    // Fallback to basic formatting
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return '';
    }
    
    // Simple fallback format: MM/DD/YYYY
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const day = dateObj.getDate().toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    
    return `${month}/${day}/${year}`;
  }
};

/**
 * Formats a date and time string or Date object for display
 * @param date - The date to format (string, Date, or null/undefined)
 * @param format - The format type ('short', 'medium', 'long', 'full')
 * @param locale - The locale to use for formatting (defaults to 'en-US')
 * @returns Formatted date and time string
 */
export const formatDateTime = (
  date: string | Date | null | undefined,
  format: 'short' | 'medium' | 'long' | 'full' = 'medium',
  locale: string = 'en-US'
): string => {
  // Handle null or undefined values
  if (!date) {
    return '';
  }

  try {
    // Convert string to Date if necessary
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    // Check if date is valid
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    // Handle predefined formats
    switch (format) {
      case 'short':
        return new Intl.DateTimeFormat(locale, {
          year: '2-digit',
          month: 'numeric',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit'
        }).format(dateObj);
      
      case 'medium':
        return new Intl.DateTimeFormat(locale, {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit'
        }).format(dateObj);
      
      case 'long':
        return new Intl.DateTimeFormat(locale, {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
          second: '2-digit'
        }).format(dateObj);
      
      case 'full':
        return new Intl.DateTimeFormat(locale, {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: 'numeric',
          minute: '2-digit',
          second: '2-digit',
          timeZoneName: 'short'
        }).format(dateObj);
      
      default:
        return new Intl.DateTimeFormat(locale).format(dateObj);
    }
  } catch (error) {
    // Fallback to basic formatting
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return '';
    }
    
    // Simple fallback format: MM/DD/YYYY HH:MM
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const day = dateObj.getDate().toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    const hours = dateObj.getHours().toString().padStart(2, '0');
    const minutes = dateObj.getMinutes().toString().padStart(2, '0');
    
    return `${month}/${day}/${year} ${hours}:${minutes}`;
  }
};

/**
 * Formats a number with thousands separators (no currency symbol)
 * @param value - The numeric value to format
 * @param decimalPlaces - The number of decimal places to show (defaults to 2)
 * @param showBlankForZero - Whether to show blank instead of 0.00 (defaults to true)
 * @returns Formatted number string
 */
export const formatNumber = (
  value: number,
  decimalPlaces: number = 2,
  showBlankForZero: boolean = true
): string => {
  // Handle undefined or null values
  if (value === undefined || value === null) {
    return '';
  }

  // Show blank for zero values (QuickBooks style)
  if (showBlankForZero && value === 0) {
    return '';
  }

  try {
    // Use Intl.NumberFormat for locale-aware formatting
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    }).format(value);
  } catch (error) {
    // Fallback to basic formatting
    const parts = value.toFixed(decimalPlaces).split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return parts.join('.');
  }
};

/**
 * Parses a formatted currency string back to a number
 * @param value - The formatted currency string
 * @returns The numeric value
 */
export const parseCurrencyValue = (value: string): number => {
  // Return 0 for empty strings (QuickBooks style)
  if (!value || value.trim() === '') {
    return 0;
  }

  // Remove currency symbols, commas, and other non-numeric characters except decimal point and minus sign
  const cleanedStr = value.replace(/[^0-9\-\.]/g, '');
  
  // Handle case where only a minus sign is present
  if (cleanedStr === '-') return 0;
  
  // Parse the cleaned string to a number
  const parsedValue = parseFloat(cleanedStr);
  
  // Return 0 if parsing results in NaN
  return isNaN(parsedValue) ? 0 : parsedValue;
};

/**
 * Get currency symbol from currency code
 * @param currencyCode - The currency code (e.g., 'USD', 'EUR')
 * @returns The currency symbol
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  const symbols: Record<string, string> = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'INR': '₹',
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'Fr',
    'CNY': '¥',
    'SEK': 'kr',
    'NZD': 'NZ$',
  };
  
  return symbols[currencyCode] || '$';
};

/**
 * Format currency for display in cards, tables, etc. (QuickBooks style)
 * Shows blank for zero values, formatted currency for non-zero values
 * @param value - The numeric value to format
 * @param currencyCode - The currency code
 * @param options - Formatting options
 */
export const formatCurrencyDisplay = (
  value: number,
  currencyCode: string = 'USD',
  options?: {
    showSymbol?: boolean;
    decimalPlaces?: number;
    locale?: string;
  }
): string => {
  return formatCurrency(
    value,
    currencyCode,
    options?.locale || (currencyCode === 'INR' ? 'en-IN' : 'en-US'),
    options?.decimalPlaces || 2,
    !(options?.showSymbol ?? true),
    true // Always show blank for zero in display contexts
  );
}; 