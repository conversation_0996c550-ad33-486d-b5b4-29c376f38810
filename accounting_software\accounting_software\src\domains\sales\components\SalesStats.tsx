import React from 'react';
import { Grid, Paper, Typography, Box } from '@mui/material';
import {
  TrendingUp,
  Receipt,
  People,
  AttachMoney,
} from '@mui/icons-material';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend: string;
  trendValue: string;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, trend, trendValue, color }) => (
  <Paper sx={{ p: 2, borderRadius: 2 }}>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
      <Box>
        <Typography variant="subtitle2" color="textSecondary">
          {title}
        </Typography>
        <Typography variant="h4" sx={{ my: 1, fontWeight: 'bold' }}>
          {value}
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography
            variant="subtitle2"
            color={trend === 'up' ? 'success.main' : 'error.main'}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            {trend === 'up' ? '↑' : '↓'} {trendValue}
          </Typography>
          <Typography variant="subtitle2" color="textSecondary" sx={{ ml: 1 }}>
            vs last month
          </Typography>
        </Box>
      </Box>
      <Box
        sx={{
          backgroundColor: color,
          p: 1,
          borderRadius: 2,
          color: 'white',
        }}
      >
        {icon}
      </Box>
    </Box>
  </Paper>
);

const SalesStats: React.FC = () => {
  const stats = [
    {
      title: 'Total Sales',
      value: '$48,500',
      icon: <AttachMoney />,
      trend: 'up',
      trendValue: '12%',
      color: '#2E7D32',
    },
    {
      title: 'Invoices',
      value: '182',
      icon: <Receipt />,
      trend: 'up',
      trendValue: '8%',
      color: '#1976D2',
    },
    {
      title: 'Customers',
      value: '321',
      icon: <People />,
      trend: 'up',
      trendValue: '5%',
      color: '#9C27B0',
    },
    {
      title: 'Growth',
      value: '15.3%',
      icon: <TrendingUp />,
      trend: 'up',
      trendValue: '2%',
      color: '#ED6C02',
    },
  ];

  return (
    <Grid container spacing={3}>
      {stats.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <StatCard {...stat} />
        </Grid>
      ))}
    </Grid>
  );
};

export default SalesStats;