"""
Django Management Command: Setup General Ledger Data

This command sets up initial data for the General Ledger module including:
- Standard account types following IFRS
- QuickBooks-style detail types
- Sample Chart of Accounts structure
- Basic journal entries for demonstration

Usage: python manage.py setup_gl_data
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from decimal import Decimal
from gl.models import AccountType, DetailType, Account, JournalEntry, JournalEntryLine

User = get_user_model()

class Command(BaseCommand):
    help = 'Setup initial General Ledger data with account types, detail types, and sample accounts'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up General Ledger data...'))
        
        # Create account types
        self.create_account_types()
        
        # Create detail types
        self.create_detail_types()
        
        # Create sample accounts
        self.create_sample_accounts()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully set up General Ledger data!')
        )

    def create_account_types(self):
        """Create standard account types following IFRS."""
        
        account_types = [
            {
                'code': 'ASSET',
                'name': 'Assets',
                'type': 'ASSET',
                'normal_balance': 'DEBIT',
                'financial_statement': 'BALANCE_SHEET',
                'sort_order': 10
            },
            {
                'code': 'LIAB',
                'name': 'Liabilities',
                'type': 'LIABILITY',
                'normal_balance': 'CREDIT',
                'financial_statement': 'BALANCE_SHEET',
                'sort_order': 20
            },
            {
                'code': 'EQUITY',
                'name': 'Equity',
                'type': 'EQUITY',
                'normal_balance': 'CREDIT',
                'financial_statement': 'BALANCE_SHEET',
                'sort_order': 30
            },
            {
                'code': 'REV',
                'name': 'Revenue',
                'type': 'REVENUE',
                'normal_balance': 'CREDIT',
                'financial_statement': 'INCOME_STATEMENT',
                'sort_order': 40
            },
            {
                'code': 'EXP',
                'name': 'Expenses',
                'type': 'EXPENSE',
                'normal_balance': 'DEBIT',
                'financial_statement': 'INCOME_STATEMENT',
                'sort_order': 50
            },
        ]
        
        for account_type_data in account_types:
            account_type, created = AccountType.objects.get_or_create(
                code=account_type_data['code'],
                defaults=account_type_data
            )
            if created:
                self.stdout.write(f'Created account type: {account_type.name}')

    def create_detail_types(self):
        """Create QuickBooks-style detail types."""
        
        # Get account types
        asset_type = AccountType.objects.get(code='ASSET')
        liability_type = AccountType.objects.get(code='LIAB')
        equity_type = AccountType.objects.get(code='EQUITY')
        revenue_type = AccountType.objects.get(code='REV')
        expense_type = AccountType.objects.get(code='EXP')
        
        detail_types = [
            # Asset Detail Types
            {
                'account_type': asset_type,
                'code': 'CASH_BANK',
                'name': 'Cash and Bank',
                'description': 'Cash on hand and bank accounts',
                'sort_order': 10
            },
            {
                'account_type': asset_type,
                'code': 'AR_TRADE',
                'name': 'Accounts Receivable',
                'description': 'Trade receivables from customers',
                'requires_customer': True,
                'sort_order': 20
            },
            {
                'account_type': asset_type,
                'code': 'INVENTORY',
                'name': 'Inventory',
                'description': 'Stock and inventory items',
                'requires_item': True,
                'sort_order': 30
            },
            {
                'account_type': asset_type,
                'code': 'PREPAID',
                'name': 'Prepaid Expenses',
                'description': 'Expenses paid in advance',
                'sort_order': 40
            },
            {
                'account_type': asset_type,
                'code': 'FIXED_ASSET',
                'name': 'Fixed Assets',
                'description': 'Property, plant, and equipment',
                'sort_order': 50
            },
            
            # Liability Detail Types
            {
                'account_type': liability_type,
                'code': 'AP_TRADE',
                'name': 'Accounts Payable',
                'description': 'Trade payables to vendors',
                'requires_vendor': True,
                'sort_order': 10
            },
            {
                'account_type': liability_type,
                'code': 'ACCRUED',
                'name': 'Accrued Liabilities',
                'description': 'Accrued expenses and liabilities',
                'sort_order': 20
            },
            {
                'account_type': liability_type,
                'code': 'TAX_PAYABLE',
                'name': 'Tax Payable',
                'description': 'Taxes owed to government',
                'is_tax_account': True,
                'sort_order': 30
            },
            {
                'account_type': liability_type,
                'code': 'LOAN',
                'name': 'Loans and Debt',
                'description': 'Bank loans and other debt',
                'sort_order': 40
            },
            
            # Equity Detail Types
            {
                'account_type': equity_type,
                'code': 'CAPITAL',
                'name': 'Owner Capital',
                'description': 'Owner investments and capital',
                'sort_order': 10
            },
            {
                'account_type': equity_type,
                'code': 'RETAINED',
                'name': 'Retained Earnings',
                'description': 'Accumulated profits and losses',
                'sort_order': 20
            },
            
            # Revenue Detail Types
            {
                'account_type': revenue_type,
                'code': 'SALES',
                'name': 'Sales Revenue',
                'description': 'Revenue from product sales',
                'requires_customer': True,
                'sort_order': 10
            },
            {
                'account_type': revenue_type,
                'code': 'SERVICE',
                'name': 'Service Revenue',
                'description': 'Revenue from services provided',
                'requires_customer': True,
                'sort_order': 20
            },
            {
                'account_type': revenue_type,
                'code': 'OTHER_REV',
                'name': 'Other Revenue',
                'description': 'Miscellaneous revenue sources',
                'sort_order': 30
            },
            
            # Expense Detail Types
            {
                'account_type': expense_type,
                'code': 'COGS',
                'name': 'Cost of Goods Sold',
                'description': 'Direct costs of producing goods sold',
                'sort_order': 10
            },
            {
                'account_type': expense_type,
                'code': 'ADVERTISING',
                'name': 'Advertising',
                'description': 'Marketing and advertising expenses',
                'sort_order': 20
            },
            {
                'account_type': expense_type,
                'code': 'OFFICE_SUPPLIES',
                'name': 'Office Supplies',
                'description': 'Office supplies and materials',
                'sort_order': 30
            },
            {
                'account_type': expense_type,
                'code': 'TRAVEL',
                'name': 'Travel & Entertainment',
                'description': 'Business travel and entertainment',
                'sort_order': 40
            },
            {
                'account_type': expense_type,
                'code': 'UTILITIES',
                'name': 'Utilities',
                'description': 'Electricity, water, gas, internet',
                'sort_order': 50
            },
            {
                'account_type': expense_type,
                'code': 'RENT',
                'name': 'Rent Expense',
                'description': 'Office and equipment rent',
                'sort_order': 60
            },
            {
                'account_type': expense_type,
                'code': 'SALARIES',
                'name': 'Salaries & Wages',
                'description': 'Employee compensation',
                'sort_order': 70
            },
        ]
        
        for detail_type_data in detail_types:
            detail_type, created = DetailType.objects.get_or_create(
                account_type=detail_type_data['account_type'],
                code=detail_type_data['code'],
                defaults=detail_type_data
            )
            if created:
                self.stdout.write(f'Created detail type: {detail_type.name}')

    def create_sample_accounts(self):
        """Create a sample Chart of Accounts."""
        
        # Get admin user for created_by field
        try:
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                self.stdout.write(
                    self.style.WARNING('No admin user found. Creating accounts without created_by.')
                )
                return
        
        # Get account types and detail types
        asset_type = AccountType.objects.get(code='ASSET')
        liability_type = AccountType.objects.get(code='LIAB')
        equity_type = AccountType.objects.get(code='EQUITY')
        revenue_type = AccountType.objects.get(code='REV')
        expense_type = AccountType.objects.get(code='EXP')
        
        cash_bank_detail = DetailType.objects.get(code='CASH_BANK')
        ar_detail = DetailType.objects.get(code='AR_TRADE')
        ap_detail = DetailType.objects.get(code='AP_TRADE')
        capital_detail = DetailType.objects.get(code='CAPITAL')
        sales_detail = DetailType.objects.get(code='SALES')
        cogs_detail = DetailType.objects.get(code='COGS')
        office_detail = DetailType.objects.get(code='OFFICE_SUPPLIES')
        rent_detail = DetailType.objects.get(code='RENT')
        
        sample_accounts = [
            # Assets
            {
                'account_number': '1000',
                'account_name': 'Cash in Hand',
                'account_type': asset_type,
                'detail_type': cash_bank_detail,
                'opening_balance': Decimal('5000.00'),
                'description': 'Petty cash and cash on hand'
            },
            {
                'account_number': '1010',
                'account_name': 'Business Checking Account',
                'account_type': asset_type,
                'detail_type': cash_bank_detail,
                'opening_balance': Decimal('25000.00'),
                'description': 'Primary business bank account'
            },
            {
                'account_number': '1200',
                'account_name': 'Accounts Receivable',
                'account_type': asset_type,
                'detail_type': ar_detail,
                'opening_balance': Decimal('8500.00'),
                'description': 'Money owed by customers'
            },
            
            # Liabilities
            {
                'account_number': '2000',
                'account_name': 'Accounts Payable',
                'account_type': liability_type,
                'detail_type': ap_detail,
                'opening_balance': Decimal('4200.00'),
                'description': 'Money owed to suppliers'
            },
            
            # Equity
            {
                'account_number': '3000',
                'account_name': 'Owner Equity',
                'account_type': equity_type,
                'detail_type': capital_detail,
                'opening_balance': Decimal('30000.00'),
                'description': 'Owner investment in the business'
            },
            
            # Revenue
            {
                'account_number': '4000',
                'account_name': 'Sales Revenue',
                'account_type': revenue_type,
                'detail_type': sales_detail,
                'description': 'Revenue from product sales'
            },
            
            # Expenses
            {
                'account_number': '5000',
                'account_name': 'Cost of Goods Sold',
                'account_type': expense_type,
                'detail_type': cogs_detail,
                'description': 'Direct cost of products sold'
            },
            {
                'account_number': '6000',
                'account_name': 'Office Supplies Expense',
                'account_type': expense_type,
                'detail_type': office_detail,
                'description': 'Office supplies and materials'
            },
            {
                'account_number': '6100',
                'account_name': 'Rent Expense',
                'account_type': expense_type,
                'detail_type': rent_detail,
                'description': 'Monthly office rent'
            },
        ]
        
        for account_data in sample_accounts:
            account, created = Account.objects.get_or_create(
                account_number=account_data['account_number'],
                defaults={
                    **account_data,
                    'created_by': admin_user,
                    'opening_balance_date': '2024-01-01'
                }
            )
            if created:
                self.stdout.write(f'Created account: {account.account_number} - {account.account_name}') 