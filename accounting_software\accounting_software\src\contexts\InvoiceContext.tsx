import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { Invoice, InvoiceFormData, InvoiceStatus, InvoicePayment, FileAttachment, RecurringInvoiceSettings } from '../shared/types/invoice.types';
import { useCustomers } from './CustomerContext';
import { PAYMENT_TERMS } from '../shared/types/customer.types';

// Sample data for initial invoices
const INITIAL_INVOICES: Invoice[] = [
  {
    id: '1',
    customerId: '1',
    invoiceNumber: 'INV-001',
    invoiceDate: '2023-05-01',
    dueDate: '2023-05-31',
    items: [
      {
        id: '1',
        productId: '1',
        name: 'Laptop Computer',
        description: 'High-performance laptop for business use',
        quantity: 2,
        unitPrice: 1200,
        subtotal: 2400,
        taxRate: 10,
        taxAmount: 240,
        total: 2640,
        type: 'product'
      }
    ],
    notes: 'Thank you for your business!',
    terms: 'Payment due within 30 days.',
    subtotal: 2400,
    taxAmount: 240,
    total: 2640,
    currency: 'USD',
    paymentTerms: 'net_30',
    createdAt: '2023-05-01T10:30:00Z',
    updatedAt: '2023-05-01T10:30:00Z',
    status: 'paid',
    customer: {
      id: '1',
      title: 'Mr',
      firstName: 'John',
      lastName: 'Doe',
      displayName: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      isSubCustomer: false,
      billingAddress: {
        street: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        postalCode: '12345',
        country: 'USA'
      },
      shippingAddress: {
        sameAsBilling: true,
        street: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        postalCode: '12345',
        country: 'USA'
      },
      paymentTerms: 'net_30',
      currency: 'USD',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z',
      status: 'active',
      totalRevenue: 5000
    },
    paidAmount: 2640,
    balanceDue: 0
  },
  {
    id: '2',
    customerId: '2',
    invoiceNumber: 'INV-002',
    invoiceDate: '2023-05-15',
    dueDate: '2023-06-14',
    items: [
      {
        id: '2',
        productId: '2',
        name: 'Office Chair',
        description: 'Ergonomic office chair with lumbar support',
        quantity: 5,
        unitPrice: 250,
        subtotal: 1250,
        taxRate: 10,
        taxAmount: 125,
        total: 1375,
        type: 'product'
      }
    ],
    notes: 'Thank you for your business!',
    terms: 'Payment due within 30 days.',
    subtotal: 1250,
    taxAmount: 125,
    total: 1375,
    currency: 'USD',
    paymentTerms: 'net_30',
    createdAt: '2023-05-15T11:45:00Z',
    updatedAt: '2023-05-15T11:45:00Z',
    status: 'partial',
    customer: {
      id: '2',
      title: 'Ms',
      firstName: 'Jane',
      lastName: 'Smith',
      displayName: 'Jane Smith',
      email: '<EMAIL>',
      phone: '************',
      isSubCustomer: false,
      billingAddress: {
        street: '456 Oak Ave',
        city: 'Somewhere',
        state: 'NY',
        postalCode: '67890',
        country: 'USA'
      },
      shippingAddress: {
        sameAsBilling: true,
        street: '456 Oak Ave',
        city: 'Somewhere',
        state: 'NY',
        postalCode: '67890',
        country: 'USA'
      },
      paymentTerms: 'net_30',
      currency: 'USD',
      createdAt: '2023-01-02T00:00:00Z',
      updatedAt: '2023-01-02T00:00:00Z',
      status: 'active',
      totalRevenue: 3000
    },
    paidAmount: 700,
    balanceDue: 675
  },
  {
    id: '3',
    customerId: '3',
    invoiceNumber: 'INV-003',
    invoiceDate: '2023-06-01',
    dueDate: '2023-06-15',
    items: [
      {
        id: '3',
        productId: '3',
        name: 'Consulting Services',
        description: 'Business strategy consulting - 10 hours',
        quantity: 10,
        unitPrice: 150,
        subtotal: 1500,
        taxRate: 0,
        taxAmount: 0,
        total: 1500,
        type: 'service'
      }
    ],
    notes: 'Thank you for your business!',
    terms: 'Payment due within 15 days.',
    subtotal: 1500,
    taxAmount: 0,
    total: 1500,
    currency: 'USD',
    paymentTerms: 'net_15',
    createdAt: '2023-06-01T09:00:00Z',
    updatedAt: '2023-06-01T09:00:00Z',
    status: 'sent',
    customer: {
      id: '3',
      title: 'Dr',
      firstName: 'Robert',
      lastName: 'Johnson',
      displayName: 'Robert Johnson',
      email: '<EMAIL>',
      phone: '************',
      isSubCustomer: false,
      billingAddress: {
        street: '789 Pine St',
        city: 'Elsewhere',
        state: 'TX',
        postalCode: '54321',
        country: 'USA'
      },
      shippingAddress: {
        sameAsBilling: true,
        street: '789 Pine St',
        city: 'Elsewhere',
        state: 'TX',
        postalCode: '54321',
        country: 'USA'
      },
      paymentTerms: 'net_15',
      currency: 'USD',
      createdAt: '2023-01-03T00:00:00Z',
      updatedAt: '2023-01-03T00:00:00Z',
      status: 'active',
      totalRevenue: 1500
    },
    paidAmount: 0,
    balanceDue: 1500
  }
];

// Sample payments
const INITIAL_PAYMENTS: InvoicePayment[] = [
  {
    id: '1',
    invoiceId: '1',
    amount: 2640,
    paymentDate: '2023-05-20',
    paymentMethod: 'bank_transfer',
    reference: 'TRF12345',
    notes: 'Full payment received',
    createdAt: '2023-05-20T14:30:00Z'
  },
  {
    id: '2',
    invoiceId: '2',
    amount: 700,
    paymentDate: '2023-06-01',
    paymentMethod: 'credit_card',
    reference: 'CC98765',
    notes: 'Partial payment received',
    createdAt: '2023-06-01T10:15:00Z'
  }
];

interface InvoiceContextType {
  invoices: Invoice[];
  payments: InvoicePayment[];
  addInvoice: (invoiceData: InvoiceFormData) => void;
  updateInvoice: (updatedInvoice: Invoice) => void;
  deleteInvoice: (id: string) => void;
  getInvoiceById: (id: string) => Invoice | undefined;
  addPayment: (paymentData: Omit<InvoicePayment, 'id' | 'createdAt'>) => void;
  getPaymentsByInvoiceId: (invoiceId: string) => InvoicePayment[];
  getNextInvoiceNumber: () => string;
  getPaymentTermLabel: (value: string) => string;
  calculateDueDate: (invoiceDate: string, paymentTerms: string) => string;
  getOverdueInvoices: () => Invoice[];
  getTotalRevenue: () => number;
  getTotalOutstanding: () => number;
  getInvoicesByStatus: (status: InvoiceStatus) => Invoice[];
  generateRecurringInvoice: (parentInvoice: Invoice) => void;
  getRecurringInvoices: (parentInvoiceId: string) => Invoice[];
}

const InvoiceContext = createContext<InvoiceContextType | undefined>(undefined);

export const useInvoices = (): InvoiceContextType => {
  const context = useContext(InvoiceContext);
  if (!context) {
    throw new Error('useInvoices must be used within an InvoiceProvider');
  }
  return context;
};

interface InvoiceProviderProps {
  children: ReactNode;
}

export const InvoiceProvider: React.FC<InvoiceProviderProps> = ({ children }) => {
  const [invoices, setInvoices] = useState<Invoice[]>(INITIAL_INVOICES);
  const [payments, setPayments] = useState<InvoicePayment[]>(INITIAL_PAYMENTS);
  const { customers, getCustomerById } = useCustomers();

  // Add a new invoice
  const addInvoice = (invoiceData: InvoiceFormData) => {
    const customer = getCustomerById(invoiceData.customerId);
    if (!customer) {
      throw new Error('Customer not found');
    }

    // Process attachments
    const processedAttachments = invoiceData.attachments?.map(attachment => {
      // For attachments with File objects, we'd normally upload to server here
      if (attachment.file && !attachment.url) {
        return {
          ...attachment,
          url: URL.createObjectURL(attachment.file),
          uploadedAt: new Date().toISOString()
        };
      }
      return attachment;
    }) || [];

    const newInvoice: Invoice = {
      id: uuidv4(),
      customer: customers.find(c => c.id === invoiceData.customerId)!,
      invoiceNumber: invoiceData.invoiceNumber,
      invoiceDate: invoiceData.invoiceDate,
      dueDate: invoiceData.dueDate,
      paymentTerms: invoiceData.paymentTerms,
      currency: invoiceData.currency,
      items: invoiceData.items,
      notes: invoiceData.notes,
      terms: invoiceData.terms,
      subtotal: invoiceData.subtotal,
      taxAmount: invoiceData.taxAmount,
      total: invoiceData.total,
      status: 'draft',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      paidAmount: 0,
      balanceDue: invoiceData.total,
      attachments: processedAttachments,
      recurringSettings: invoiceData.recurringSettings
    };

    setInvoices((prevInvoices) => [...prevInvoices, newInvoice]);
  };

  // Update an existing invoice
  const updateInvoice = (updatedInvoice: Invoice) => {
    // Process any new attachments
    const processedAttachments = updatedInvoice.attachments?.map(attachment => {
      // For new attachments with File objects, we'd normally upload to server here
      if (attachment.file && !attachment.url) {
        return {
          ...attachment,
          url: URL.createObjectURL(attachment.file),
          uploadedAt: new Date().toISOString()
        };
      }
      return attachment;
    }) || [];

    // Update the invoice with processed attachments
    const finalInvoice = {
      ...updatedInvoice,
      updatedAt: new Date().toISOString(),
      attachments: processedAttachments
    };

    setInvoices((prevInvoices) =>
      prevInvoices.map((invoice) =>
        invoice.id === updatedInvoice.id
          ? finalInvoice
          : invoice
      )
    );
  };

  // Delete an invoice
  const deleteInvoice = (id: string) => {
    setInvoices((prevInvoices) => prevInvoices.filter((invoice) => invoice.id !== id));
    // Also delete associated payments
    setPayments((prevPayments) => prevPayments.filter((payment) => payment.invoiceId !== id));
  };

  // Get an invoice by ID
  const getInvoiceById = (id: string) => {
    return invoices.find((invoice) => invoice.id === id);
  };

  // Add a payment
  const addPayment = (paymentData: Omit<InvoicePayment, 'id' | 'createdAt'>) => {
    const invoice = getInvoiceById(paymentData.invoiceId);
    if (!invoice) {
      throw new Error('Invoice not found');
    }

    const newPayment: InvoicePayment = {
      ...paymentData,
      id: uuidv4(),
      createdAt: new Date().toISOString()
    };

    setPayments((prevPayments) => [...prevPayments, newPayment]);

    // Update invoice paid amount and status
    const totalPaid = payments
      .filter((p) => p.invoiceId === invoice.id)
      .reduce((sum, p) => sum + p.amount, 0) + paymentData.amount;
    
    const balanceDue = invoice.total - totalPaid;
    let newStatus: InvoiceStatus = invoice.status;

    if (balanceDue <= 0) {
      newStatus = 'paid';
    } else if (totalPaid > 0) {
      newStatus = 'partial';
    }

    const updatedInvoice: Invoice = {
      ...invoice,
      paidAmount: totalPaid,
      balanceDue,
      status: newStatus,
      updatedAt: new Date().toISOString()
    };

    updateInvoice(updatedInvoice);
  };

  // Get payments for an invoice
  const getPaymentsByInvoiceId = (invoiceId: string) => {
    return payments.filter((payment) => payment.invoiceId === invoiceId);
  };

  // Generate the next invoice number
  const getNextInvoiceNumber = () => {
    const prefix = 'INV-';
    const lastInvoice = [...invoices].sort((a, b) => {
      const numA = parseInt(a.invoiceNumber.replace(prefix, ''));
      const numB = parseInt(b.invoiceNumber.replace(prefix, ''));
      return numB - numA;
    })[0];

    if (!lastInvoice) {
      return `${prefix}001`;
    }

    const lastNumber = parseInt(lastInvoice.invoiceNumber.replace(prefix, ''));
    const nextNumber = lastNumber + 1;
    return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
  };

  // Get payment term label
  const getPaymentTermLabel = (value: string) => {
    const term = PAYMENT_TERMS.find((term) => term.value === value);
    return term ? term.label : value;
  };

  // Calculate due date based on invoice date and payment terms
  const calculateDueDate = (invoiceDate: string, paymentTerms: string) => {
    const term = PAYMENT_TERMS.find((term) => term.value === paymentTerms);
    if (!term) {
      return invoiceDate; // Return the same date if term not found
    }
    return dayjs(invoiceDate).add(term.days, 'day').format('YYYY-MM-DD');
  };

  // Get overdue invoices
  const getOverdueInvoices = () => {
    const today = dayjs().format('YYYY-MM-DD');
    return invoices.filter(
      (invoice) => 
        invoice.status !== 'paid' && 
        invoice.status !== 'cancelled' && 
        dayjs(invoice.dueDate).isBefore(today)
    );
  };

  // Get total revenue
  const getTotalRevenue = () => {
    return payments.reduce((sum, payment) => sum + payment.amount, 0);
  };

  // Get total outstanding
  const getTotalOutstanding = () => {
    return invoices.reduce((sum, invoice) => sum + invoice.balanceDue, 0);
  };

  // Get invoices by status
  const getInvoicesByStatus = (status: InvoiceStatus) => {
    return invoices.filter((invoice) => invoice.status === status);
  };

  // Update invoice statuses based on due dates
  useEffect(() => {
    const today = dayjs().format('YYYY-MM-DD');
    const updatedInvoices = invoices.map((invoice) => {
      if (
        (invoice.status === 'sent' || invoice.status === 'viewed' || invoice.status === 'partial') &&
        dayjs(invoice.dueDate).isBefore(today)
      ) {
        return { ...invoice, status: 'overdue' as InvoiceStatus, updatedAt: new Date().toISOString() };
      }
      return invoice;
    });

    if (JSON.stringify(updatedInvoices) !== JSON.stringify(invoices)) {
      setInvoices(updatedInvoices);
    }
  }, [invoices]);

  // Generate a recurring invoice based on a parent invoice
  const generateRecurringInvoice = (parentInvoice: Invoice) => {
    if (!parentInvoice.recurringSettings?.isRecurring) {
      throw new Error('Parent invoice is not a recurring invoice');
    }

    // Get the sequence number for this recurring invoice
    const recurringInvoices = getRecurringInvoices(parentInvoice.id);
    const sequence = recurringInvoices.length + 1;

    // Generate a new invoice number
    const nextInvoiceNumber = getNextInvoiceNumber();

    // Calculate the new invoice date and due date based on recurring settings
    const settings = parentInvoice.recurringSettings;
    const lastInvoiceDate = recurringInvoices.length > 0 
      ? recurringInvoices[recurringInvoices.length - 1].invoiceDate 
      : parentInvoice.invoiceDate;
    
    let newInvoiceDate = new Date(lastInvoiceDate);
    
    // Add the appropriate interval based on frequency
    switch (settings.frequency) {
      case 'weekly':
        newInvoiceDate.setDate(newInvoiceDate.getDate() + (7 * settings.interval));
        break;
      case 'monthly':
        newInvoiceDate.setMonth(newInvoiceDate.getMonth() + settings.interval);
        break;
      case 'quarterly':
        newInvoiceDate.setMonth(newInvoiceDate.getMonth() + (3 * settings.interval));
        break;
      case 'yearly':
        newInvoiceDate.setFullYear(newInvoiceDate.getFullYear() + settings.interval);
        break;
    }
    
    // Adjust to the specific day of month/week if specified
    if (settings.dayOfMonth && ['monthly', 'quarterly', 'yearly'].includes(settings.frequency)) {
      // Set to the specified day of month, but don't exceed the month's max days
      const maxDays = new Date(newInvoiceDate.getFullYear(), newInvoiceDate.getMonth() + 1, 0).getDate();
      newInvoiceDate.setDate(Math.min(settings.dayOfMonth, maxDays));
    } else if (settings.dayOfWeek !== undefined && settings.frequency === 'weekly') {
      // Adjust to the specified day of week
      const currentDay = newInvoiceDate.getDay();
      const diff = settings.dayOfWeek - currentDay;
      newInvoiceDate.setDate(newInvoiceDate.getDate() + diff);
    }
    
    const newInvoiceDateStr = newInvoiceDate.toISOString().split('T')[0];
    const newDueDate = calculateDueDate(newInvoiceDateStr, parentInvoice.paymentTerms);

    // Create the new recurring invoice with the same structure as the parent
    // Note: There's a type mismatch between Invoice interface and implementation
    // The interface extends InvoiceFormData which requires customerId, but our implementation
    // uses customer object directly. This is a known issue to be fixed in a future refactoring.
    // @ts-ignore
    const newInvoice: Invoice = {
      id: uuidv4(),
      customer: parentInvoice.customer,
      invoiceNumber: nextInvoiceNumber,
      invoiceDate: newInvoiceDateStr,
      dueDate: newDueDate,
      paymentTerms: parentInvoice.paymentTerms,
      currency: parentInvoice.currency,
      items: [...parentInvoice.items],
      notes: parentInvoice.notes,
      terms: parentInvoice.terms,
      subtotal: parentInvoice.subtotal,
      taxAmount: parentInvoice.taxAmount,
      total: parentInvoice.total,
      status: 'draft' as InvoiceStatus,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      paidAmount: 0,
      balanceDue: parentInvoice.total,
      parentInvoiceId: parentInvoice.id,
      recurringSequence: sequence,
      // Don't copy attachments from parent invoice
      attachments: [],
      // Copy recurring settings
      recurringSettings: parentInvoice.recurringSettings
    };

    setInvoices((prevInvoices) => [...prevInvoices, newInvoice]);
    return newInvoice;
  };

  // Get all recurring invoices for a parent invoice
  const getRecurringInvoices = (parentInvoiceId: string) => {
    return invoices
      .filter(invoice => invoice.parentInvoiceId === parentInvoiceId)
      .sort((a, b) => {
        if (a.recurringSequence && b.recurringSequence) {
          return a.recurringSequence - b.recurringSequence;
        }
        return 0;
      });
  };

  const value = {
    invoices,
    payments,
    addInvoice,
    updateInvoice,
    deleteInvoice,
    getInvoiceById,
    addPayment,
    getPaymentsByInvoiceId,
    getNextInvoiceNumber,
    getPaymentTermLabel,
    calculateDueDate,
    getOverdueInvoices,
    getTotalRevenue,
    getTotalOutstanding,
    getInvoicesByStatus,
    generateRecurringInvoice,
    getRecurringInvoices
  };

  return <InvoiceContext.Provider value={value}>{children}</InvoiceContext.Provider>;
}; 