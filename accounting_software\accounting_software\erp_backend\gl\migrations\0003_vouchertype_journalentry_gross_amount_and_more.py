# Generated by Django 4.2.21 on 2025-06-07 21:15

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales_tax', '0001_initial'),
        ('tds', '0001_initial'),
        ('gl', '0002_add_hierarchy_optimization'),
    ]

    operations = [
        migrations.CreateModel(
            name='VoucherType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('voucher_code', models.CharField(choices=[('JV', 'Journal Voucher - General Entry'), ('PV', 'Payment Voucher - Payments Made'), ('RV', 'Receipt Voucher - Receipts Received'), ('BV', 'Bank Voucher - Bank Transactions'), ('CV', 'Cash Voucher - Cash Transactions')], help_text='Voucher type code', max_length=5, unique=True)),
                ('voucher_name', models.CharField(help_text='Display name of the voucher type', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Description of when to use this voucher type')),
                ('number_prefix', models.CharField(help_text='Prefix for auto-generated voucher numbers', max_length=10)),
                ('next_number', models.PositiveIntegerField(default=1, help_text='Next number in sequence for this voucher type')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this voucher type is available')),
            ],
            options={
                'verbose_name': 'Voucher Type',
                'verbose_name_plural': 'Voucher Types',
                'ordering': ['voucher_code'],
            },
        ),
        migrations.AddField(
            model_name='journalentry',
            name='gross_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Gross amount before tax calculations', max_digits=15),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='input_tax',
            field=models.ForeignKey(blank=True, help_text='Input tax (for payments)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='input_journal_entries', to='sales_tax.salestax'),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='net_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Net amount after all tax calculations', max_digits=15),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='output_tax',
            field=models.ForeignKey(blank=True, help_text='Output tax (for receipts)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='output_journal_entries', to='sales_tax.salestax'),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='sales_tax_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total sales tax amount', max_digits=15),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='tds_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total TDS deduction amount', max_digits=15),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='tds_rate',
            field=models.ForeignKey(blank=True, help_text='TDS rate applied to this entry', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_entries', to='tds.tds'),
        ),
        migrations.AddField(
            model_name='journalentryline',
            name='quantity',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Quantity for product/service lines', max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='journalentryline',
            name='unit_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Unit price for product/service lines', max_digits=15, null=True),
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('project_code', models.CharField(help_text='Unique project code for identification', max_length=20, unique=True)),
                ('project_name', models.CharField(help_text='Descriptive name of the project', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the project')),
                ('start_date', models.DateField(blank=True, help_text='Project start date', null=True)),
                ('end_date', models.DateField(blank=True, help_text='Expected or actual project end date', null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this project is available for new transactions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Project',
                'verbose_name_plural': 'Projects',
                'ordering': ['project_code'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_code', models.CharField(help_text='Unique product code for identification', max_length=20, unique=True)),
                ('product_name', models.CharField(help_text='Descriptive name of the product/service', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the product/service')),
                ('product_type', models.CharField(choices=[('PRODUCT', 'Physical Product'), ('SERVICE', 'Service'), ('DIGITAL', 'Digital Product')], default='PRODUCT', help_text='Type of product or service', max_length=20)),
                ('unit_price', models.DecimalField(blank=True, decimal_places=2, help_text='Standard unit price', max_digits=15, null=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this product is available for transactions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_products', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'ordering': ['product_code'],
            },
        ),
        migrations.AddField(
            model_name='journalentry',
            name='voucher_type',
            field=models.ForeignKey(blank=True, help_text='Type of voucher/form used for this entry', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='journal_entries', to='gl.vouchertype'),
        ),
        migrations.AddField(
            model_name='journalentryline',
            name='product',
            field=models.ForeignKey(blank=True, help_text='Product/service associated with this transaction line', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_lines', to='gl.product'),
        ),
        migrations.AlterField(
            model_name='journalentryline',
            name='project',
            field=models.ForeignKey(blank=True, help_text='Project associated with this transaction line', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_lines', to='gl.project'),
        ),
    ]
