import React, { createContext, useContext, useState, useEffect } from 'react';
import { Customer, PAYMENT_TERMS } from '../shared/types/customer.types';

interface CustomerContextType {
  customers: Customer[];
  addCustomer: (customer: Omit<Customer, 'id' | 'created_at' | 'updated_at' | 'status'>) => void;
  updateCustomer: (id: number, customer: Partial<Customer>) => void;
  deleteCustomer: (id: number) => void;
  getCustomerById: (id: number) => Customer | undefined;
  getPaymentTermLabel: (value: string) => string;
}

const CustomerContext = createContext<CustomerContextType>({
  customers: [],
  addCustomer: () => {},
  updateCustomer: () => {},
  deleteCustomer: () => {},
  getCustomerById: () => undefined,
  getPaymentTermLabel: () => '',
});

export const CustomerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [customers, setCustomers] = useState<Customer[]>([]);

  useEffect(() => {
    // In a real application, you would fetch customers from an API
    const storedCustomers = localStorage.getItem('customers');
    if (storedCustomers) {
      try {
        setCustomers(JSON.parse(storedCustomers));
      } catch (error) {
        console.error('Error parsing stored customers:', error);
      }
    }
  }, []);

  // Update localStorage when customers change
  useEffect(() => {
    if (customers.length > 0) {
      localStorage.setItem('customers', JSON.stringify(customers));
    }
  }, [customers]);

  const addCustomer = (customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at' | 'status'>) => {
    try {
      const newCustomer: Customer = {
        ...customerData,
        id: Date.now(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: 'active',
      };
      
      setCustomers(prev => [...prev, newCustomer]);
    } catch (error) {
      console.error('Error adding customer:', error);
      throw error; // Re-throw the error so it can be caught by the component
    }
  };

  const updateCustomer = (id: number, customerData: Partial<Customer>) => {
    try {
      setCustomers(prev => 
        prev.map(customer => 
          customer.id === id 
            ? { 
                ...customer, 
                ...customerData, 
                updated_at: new Date().toISOString() 
              } 
            : customer
        )
      );
    } catch (error) {
      console.error('Error updating customer:', error);
      throw error;
    }
  };

  const deleteCustomer = (id: number) => {
    try {
      setCustomers(prev => prev.filter(customer => customer.id !== id));
    } catch (error) {
      console.error('Error deleting customer:', error);
      throw error;
    }
  };

  const getCustomerById = (id: number) => {
    return customers.find(customer => customer.id === id);
  };

  const getPaymentTermLabel = (value: string): string => {
    const term = PAYMENT_TERMS.find(term => term.value === value);
    return term ? term.label : value;
  };

  return (
    <CustomerContext.Provider 
      value={{ 
        customers, 
        addCustomer, 
        updateCustomer, 
        deleteCustomer, 
        getCustomerById,
        getPaymentTermLabel
      }}
    >
      {children}
    </CustomerContext.Provider>
  );
};

export const useCustomers = () => {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomers must be used within a CustomerProvider');
  }
  return context;
}; 