// API Configuration
const API_BASE_URL = 'http://localhost:8000/api';

export interface PurchaseOrderLineItem {
  id?: number;
  product?: number;
  description: string;
  quantity: number;
  unit_of_measure?: string;
  unit_price: number;
  discount_percent: number;
  line_total: number;
  quantity_received: number;
  quantity_pending: number;
  taxable: boolean;
  tax_rate: number;
  tax_amount: number;
  notes?: string;
  line_order: number;
}

export interface PurchaseOrder {
  id?: number;
  po_id: string;
  po_number: string;
  vendor: number;
  vendor_name?: string;
  po_date: string;
  expected_date?: string;
  
  // Buyer Information
  buyer_name?: string;
  buyer_email?: string;
  buyer_phone?: string;
  
  // Financial Information
  subtotal: number;
  discount_percent: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  amount_received: number;
  balance_due: number;
  currency: string;
  
  // Settings
  status: 'draft' | 'pending' | 'sent' | 'acknowledged' | 'partial' | 'received' | 'closed' | 'cancelled';
  payment_terms?: string;
  
  // Additional Information
  reference_number?: string;
  memo?: string;
  notes?: string;
  ship_to_address?: string;
  
  // Email Tracking
  email_sent?: boolean;
  email_sent_date?: string;
  acknowledged_date?: string;
  
  // Line Items
  line_items?: PurchaseOrderLineItem[];
  
  // Metadata
  created_at?: string;
  updated_at?: string;
}

export interface PurchaseOrderFormData {
  vendor: number;
  vendorName: string;
  poDate: string;
  expectedDate?: string;
  referenceNumber?: string;
  memo?: string;
  shipToAddress?: string;
  paymentTerms?: string;
  currency: string;
  // Additional fields for complete PO data
  buyerName?: string;
  buyerEmail?: string;
  buyerPhone?: string;
  status?: string;
  notes?: string;
  lineItems: {
    description: string;
    quantity: number;
    unitPrice: number;
    discountPercent: number;
    taxable: boolean;
    taxRate: number;
    productId?: number | null;
  }[];
}

class PurchaseOrderService {
  private baseUrl = `${API_BASE_URL}/purchase/purchase-orders`;

  async getPurchaseOrders(): Promise<PurchaseOrder[]> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(this.baseUrl + '/', {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch purchase orders');
    }

    const data = await response.json();
    return data.results || data;
  }

  async getPurchaseOrder(id: number): Promise<PurchaseOrder> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(this.baseUrl + `/${id}/`, {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch purchase order');
    }

    return response.json();
  }

  async createPurchaseOrder(poData: PurchaseOrderFormData): Promise<PurchaseOrder> {
    const token = localStorage.getItem('token');
    
    // Transform form data to API format
    const apiData = {
      vendor: poData.vendor,
      po_date: poData.poDate,
      expected_date: poData.expectedDate || null,
      reference_number: poData.referenceNumber || null,
      memo: poData.memo || null,
      ship_to_address: poData.shipToAddress || null,
      payment_terms: poData.paymentTerms || null,
      currency: poData.currency,
      // Additional buyer and PO fields
      buyer_name: poData.buyerName || null,
      buyer_email: poData.buyerEmail || null,
      buyer_phone: poData.buyerPhone || null,
      status: poData.status || 'draft',
      notes: poData.notes || null,
      line_items: poData.lineItems.map((item, index) => ({
        description: item.description || 'No description',  // Ensure description is never empty
        quantity: item.quantity,
        unit_price: item.unitPrice,
        discount_percent: item.discountPercent || 0,
        taxable: item.taxable,
        tax_rate: item.taxRate || 0,
        line_order: index + 1,
        unit_of_measure: 'pcs',  // Add required field
        product: item.productId || null,  // Include product ID for proper linking
      })),
    };

    console.log('🚀 Creating PO with data:', apiData);
    console.log('🔗 API URL:', this.baseUrl + '/');
    console.log('📦 Line Items Details:', JSON.stringify(apiData.line_items, null, 2));

    const response = await fetch(this.baseUrl + '/', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData),
    });

    console.log('📡 Response status:', response.status);

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
        console.error('❌ API Error Response:', errorData);
      } catch (e) {
        console.error('❌ Failed to parse error response');
        errorData = { detail: `HTTP ${response.status}: ${response.statusText}` };
      }
      
      // Create detailed error message
      let errorMessage = 'Failed to create purchase order';
      if (errorData.detail) {
        errorMessage = errorData.detail;
      } else if (errorData.non_field_errors) {
        errorMessage = errorData.non_field_errors.join(', ');
      } else if (typeof errorData === 'object') {
        // Handle field-specific errors
        const fieldErrors = Object.entries(errorData)
          .map(([field, errors]) => `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`)
          .join('; ');
        errorMessage = `Validation errors: ${fieldErrors}`;
      }
      
      throw new Error(errorMessage);
    }

    return response.json();
  }

  async updatePurchaseOrder(id: number, poData: PurchaseOrderFormData): Promise<PurchaseOrder> {
    const token = localStorage.getItem('token');
    
    // Transform form data to API format
    const apiData = {
      vendor: poData.vendor,
      po_date: poData.poDate,
      expected_date: poData.expectedDate || null,
      reference_number: poData.referenceNumber || null,
      memo: poData.memo || null,
      ship_to_address: poData.shipToAddress || null,
      payment_terms: poData.paymentTerms || null,
      currency: poData.currency,
      // Additional buyer and PO fields
      buyer_name: poData.buyerName || null,
      buyer_email: poData.buyerEmail || null,
      buyer_phone: poData.buyerPhone || null,
      status: poData.status || 'draft',
      notes: poData.notes || null,
      line_items: poData.lineItems.map((item, index) => ({
        description: item.description || 'No description',  // Ensure description is never empty
        quantity: item.quantity,
        unit_price: item.unitPrice,
        discount_percent: item.discountPercent || 0,
        taxable: item.taxable,
        tax_rate: item.taxRate || 0,
        line_order: index + 1,
        unit_of_measure: 'pcs',  // Add required field
        product: item.productId || null,  // Include product ID for proper linking
      })),
    };

    const response = await fetch(this.baseUrl + `/${id}/`, {
      method: 'PUT',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update purchase order');
    }

    return response.json();
  }

  async deletePurchaseOrder(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(this.baseUrl + `/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete purchase order');
    }
  }

  async sendPurchaseOrder(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(this.baseUrl + `/${id}/send/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to send purchase order');
    }
  }

  async receivePurchaseOrder(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(this.baseUrl + `/${id}/receive/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to receive purchase order');
    }
  }

  async getPurchaseOrderStats(): Promise<any> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(this.baseUrl + '/stats/', {
      method: 'GET',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch purchase order statistics');
    }

    return response.json();
  }
}

export const purchaseOrderService = new PurchaseOrderService(); 