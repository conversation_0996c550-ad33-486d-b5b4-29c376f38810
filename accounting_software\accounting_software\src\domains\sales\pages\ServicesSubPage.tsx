import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Grid,
  <PERSON>,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { 
  Build, 
  AttachMoney, 
  Category as CategoryIcon,
  TrendingUp,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Star as StarIcon,
} from '@mui/icons-material';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, ResponsiveContainer,
  PieChart, Pie, Cell, Legend
} from 'recharts';
import StatCard from '../../../shared/components/StatCard';
import DataTable, { RowAction, commonRowActions } from '../../../shared/components/DataTable';
import { PageContainer } from '../../../layouts/components/PageComponents';
import { useProducts } from '../../../contexts/ProductContext';
import { Product, PRODUCT_CATEGORIES } from '../../../shared/types/product.types';
import { useTheme } from '@mui/material/styles';
import ProductFormModal from '../components/ProductFormModal';

const ServicesSubPage: React.FC = () => {
  const theme = useTheme();
  const { products, getCategoryName, deleteProduct, updateProduct } = useProducts();
  const [isLoading] = useState(false);
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Filter only services (not products)
  const services = products.filter(p => p.type === 'service');

  // Calculate service-specific statistics
  const serviceStats = {
    totalServices: services.length,
    totalRevenue: services.reduce((sum, service) => sum + (service.totalSales || 0), 0),
    averagePrice: services.length > 0 
      ? services.reduce((sum, s) => sum + s.salesPrice, 0) / services.length 
      : 0,
    averageRating: services.length > 0 
      ? services.reduce((sum, s) => sum + (s.averageRating || 0), 0) / services.length 
      : 0,
    categoryCounts: Object.entries(
      services.reduce((acc, service) => {
        const category = service.category;
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    ).map(([category, count]) => ({
      name: getCategoryName(category),
      value: count,
    })),
    revenueByCategory: Object.entries(
      services.reduce((acc, service) => {
        const category = getCategoryName(service.category);
        acc[category] = (acc[category] || 0) + (service.totalSales || 0);
        return acc;
      }, {} as Record<string, number>)
    ).map(([category, revenue]) => ({
      name: category,
      value: revenue,
    })),
  };

  // Handle edit service
  const handleEditProduct = useCallback((product: Product) => {
    setSelectedProduct(product);
    setIsProductFormOpen(true);
  }, []);

  // Handle delete service
  const handleDeleteProduct = useCallback((product: Product) => {
    if (window.confirm(`Are you sure you want to delete ${product.name}?`)) {
      try {
        deleteProduct(product.id);
      } catch (error) {
        console.error('Error deleting service:', error);
      }
    }
  }, [deleteProduct]);

  // Handle view service
  const handleViewProduct = useCallback((product: Product) => {
    console.log('View service details:', product);
  }, []);

  // Custom row actions for the data table
  const serviceRowActions: RowAction[] = [
    commonRowActions.edit(handleEditProduct),
    commonRowActions.delete(handleDeleteProduct),
    {
      icon: <VisibilityIcon fontSize="small" color="info" />,
      label: 'View Details',
      tooltip: 'View service details',
      onClick: handleViewProduct,
    },
  ];

  // Handle close service form
  const handleCloseProductForm = useCallback(() => {
    setIsProductFormOpen(false);
    setSelectedProduct(null);
  }, []);

  // Get unique categories for the searchable dropdown
  const categoryOptions = PRODUCT_CATEGORIES.map(category => ({
    value: category.value,
    label: category.label
  }));

  // Handle cell change for searchable dropdown
  const handleCellChange = (row: Product, field: string, value: any) => {
    if (field === 'category') {
      const updatedProduct = { ...row, category: value };
      updateProduct(updatedProduct);
    }
  };

  // Colors for the pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  return (
    <PageContainer
      title="🛠️ Services"
      actions={
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setIsProductFormOpen(true)}
        >
          Add Service
        </Button>
      }
    >
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Services"
            value={serviceStats.totalServices}
            icon={<Build />}
            trend={+8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            value={`$${serviceStats.totalRevenue.toLocaleString()}`}
            icon={<AttachMoney />}
            trend={+15}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Average Price"
            value={`$${serviceStats.averagePrice.toFixed(2)}`}
            icon={<TrendingUp />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Average Rating"
            value={serviceStats.averageRating.toFixed(1)}
            icon={<StarIcon />}
            trend={+0.2}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Revenue by Service Category
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={serviceStats.revenueByCategory}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Revenue']} />
                    <Bar dataKey="value" fill={theme.palette.success.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Service Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={serviceStats.categoryCounts}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {serviceStats.categoryCounts.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Top Performing Services */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Performing Services
              </Typography>
              <Grid container spacing={2}>
                {services
                  .sort((a, b) => (b.totalSales || 0) - (a.totalSales || 0))
                  .slice(0, 3)
                  .map((service, index) => (
                    <Grid item xs={12} md={4} key={service.id}>
                      <Card variant="outlined" sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <CategoryIcon sx={{ mr: 1, color: 'primary.main' }} />
                          <Typography variant="h6" noWrap>
                            {service.name}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {service.description}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="h6" color="success.main">
                            ${(service.totalSales || 0).toLocaleString()}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <StarIcon sx={{ color: 'warning.main', fontSize: 16, mr: 0.5 }} />
                            <Typography variant="body2">
                              {service.averageRating?.toFixed(1) || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                      </Card>
                    </Grid>
                  ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Services Table */}
      <Box sx={{ mt: 4 }}>
        <DataTable
          title="Service List"
          columns={[
            { field: 'name', headerName: 'Service Name', flex: 1 },
            { field: 'sku', headerName: 'SKU', flex: 0.5 },
            { 
              field: 'category', 
              headerName: 'Category', 
              flex: 0.8,
              valueGetter: ({ row }) => getCategoryName(row.category),
              searchable: true,
              options: categoryOptions,
              optionLabel: 'label',
              optionValue: 'value'
            },
            { 
              field: 'salesPrice', 
              headerName: 'Price', 
              flex: 0.5,
              type: 'currency',
              align: 'right',
              currencyCode: ({ row }) => row.currency || 'USD'
            },
            { 
              field: 'totalSales', 
              headerName: 'Total Revenue', 
              flex: 0.6,
              type: 'currency',
              align: 'right',
              currencyCode: ({ row }) => row.currency || 'USD'
            },
            { 
              field: 'averageRating', 
              headerName: 'Rating', 
              flex: 0.4,
              align: 'center',
              renderCell: ({ row }) => {
                const rating = row.averageRating;
                if (!rating) return <Typography variant="body2" color="text.secondary">N/A</Typography>;
                return (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <StarIcon sx={{ color: 'warning.main', fontSize: 16, mr: 0.5 }} />
                    <Typography variant="body2">{rating.toFixed(1)}</Typography>
                  </Box>
                );
              }
            },
            { 
              field: 'status', 
              headerName: 'Status', 
              flex: 0.5,
              renderCell: ({ row }) => (
                <Chip 
                  label={row.status === 'active' ? 'Active' : 'Inactive'} 
                  color={row.status === 'active' ? 'success' : 'error'}
                  size="small"
                />
              )
            },
          ]}
          rows={services}
          loading={isLoading}
          checkboxSelection
          rowActions={serviceRowActions}
          onCellChange={handleCellChange}
        />
      </Box>

      {/* Service Form Modal */}
      <ProductFormModal
        open={isProductFormOpen}
        onClose={handleCloseProductForm}
        product={selectedProduct}
      />
    </PageContainer>
  );
};

export default ServicesSubPage; 