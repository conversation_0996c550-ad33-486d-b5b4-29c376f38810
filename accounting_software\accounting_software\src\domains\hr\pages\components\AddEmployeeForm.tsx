import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, ControllerRenderProps } from 'react-hook-form';
import * as z from 'zod';
import {
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Paper,
  Grid,
  FormHelperText,
} from '@mui/material';
import { useSnackbar } from 'notistack';

// Define the form schema using zod
const employeeSchema = z.object({
  // Personal Information
  firstName: z.string().min(1, { message: "First name is required" }),
  lastName: z.string().min(1, { message: "Last name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().optional(),
  
  // Employment Details
  position: z.string().min(1, { message: "Position is required" }),
  department: z.string().min(1, { message: "Department is required" }),
  joinDate: z.string().min(1, { message: "Join date is required" }),
  employmentType: z.string().min(1, { message: "Employment type is required" }),
  salary: z.string().min(1, { message: "Salary is required" }),
  status: z.string().min(1, { message: "Status is required" }),
  
  // Address Information
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),
});

type EmployeeFormValues = z.infer<typeof employeeSchema>;

interface AddEmployeeFormProps {
  onCancel: () => void;
  onSubmit?: (data: EmployeeFormValues) => void;
  initialValues?: Partial<EmployeeFormValues>;
  loading?: boolean;
}

interface FormFieldProps {
  field: ControllerRenderProps<EmployeeFormValues, keyof EmployeeFormValues>;
  error?: boolean;
  helperText?: string;
}

interface Department {
  id: number;
  name: string;
  code: string;
}

const AddEmployeeForm: React.FC<AddEmployeeFormProps> = ({ onCancel, onSubmit, initialValues, loading }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loadingDepartments, setLoadingDepartments] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  const form = useForm<EmployeeFormValues>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      firstName: initialValues?.firstName || "",
      lastName: initialValues?.lastName || "",
      email: initialValues?.email || "",
      phone: initialValues?.phone || "",
      dateOfBirth: initialValues?.dateOfBirth || "",
      gender: initialValues?.gender || "",
      position: initialValues?.position || "",
      department: initialValues?.department || "",
      joinDate: initialValues?.joinDate || new Date().toISOString().split('T')[0],
      employmentType: initialValues?.employmentType || "",
      salary: initialValues?.salary ? String(initialValues.salary) : "",
      status: initialValues?.status || "Active",
      address: initialValues?.address || "",
      city: initialValues?.city || "",
      state: initialValues?.state || "",
      postalCode: initialValues?.postalCode || "",
      country: initialValues?.country || "",
    },
  });

  useEffect(() => {
    const fetchDepartments = async () => {
      setLoadingDepartments(true);
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await fetch('http://localhost:8000/api/hr/departments/', {
          headers: {
            'Authorization': `Token ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch departments');
        }

        const data = await response.json();
        setDepartments(data.results || data);
      } catch (error) {
        enqueueSnackbar(error instanceof Error ? error.message : 'Error loading departments', {
          variant: 'error',
        });
      } finally {
        setLoadingDepartments(false);
      }
    };

    fetchDepartments();
  }, [enqueueSnackbar]);

  const handleSubmit = async (data: EmployeeFormValues) => {
    try {
      // Convert salary to a number if needed
      const payload = { ...data, salary: parseFloat(data.salary) };

      // Get the token from localStorage (or your auth context)
      const token = localStorage.getItem('token');

      const response = await fetch('http://localhost:8000/api/hr/employees/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        enqueueSnackbar(`${data.firstName} ${data.lastName} has been added successfully.`, {
          variant: 'success',
        });
        if (onSubmit) {
          onSubmit(data);
        }
        onCancel();
      } else {
        const errorData = await response.json();
        enqueueSnackbar(`Error: ${JSON.stringify(errorData)}`, { variant: 'error' });
      }
    } catch (error) {
      enqueueSnackbar(`Error: ${error}`, { variant: 'error' });
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box component="form" onSubmit={form.handleSubmit(handleSubmit)} sx={{ width: '100%' }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="Personal Information" />
          <Tab label="Employment Details" />
          <Tab label="Address" />
        </Tabs>

        {activeTab === 0 && (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name*"
                {...form.register('firstName')}
                error={!!form.formState.errors.firstName}
                helperText={form.formState.errors.firstName?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name*"
                {...form.register('lastName')}
                error={!!form.formState.errors.lastName}
                helperText={form.formState.errors.lastName?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email*"
                type="email"
                {...form.register('email')}
                error={!!form.formState.errors.email}
                helperText={form.formState.errors.email?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                {...form.register('phone')}
                error={!!form.formState.errors.phone}
                helperText={form.formState.errors.phone?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Date of Birth"
                type="date"
                InputLabelProps={{ shrink: true }}
                {...form.register('dateOfBirth')}
                error={!!form.formState.errors.dateOfBirth}
                helperText={form.formState.errors.dateOfBirth?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!form.formState.errors.gender}>
                <InputLabel>Gender</InputLabel>
                <Select
                  label="Gender"
                  {...form.register('gender')}
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                  <MenuItem value="prefer-not-to-say">Prefer not to say</MenuItem>
                </Select>
                {form.formState.errors.gender && (
                  <FormHelperText>{form.formState.errors.gender.message}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        )}

        {activeTab === 1 && (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Position*"
                {...form.register('position')}
                error={!!form.formState.errors.position}
                helperText={form.formState.errors.position?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!form.formState.errors.department}>
                <InputLabel>Department*</InputLabel>
                <Select
                  label="Department*"
                  {...form.register('department')}
                  disabled={loadingDepartments}
                >
                  {departments.map((dept) => (
                    <MenuItem key={dept.id} value={dept.name}>
                      {dept.name} ({dept.code})
                    </MenuItem>
                  ))}
                </Select>
                {form.formState.errors.department && (
                  <FormHelperText>{form.formState.errors.department.message}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Join Date*"
                type="date"
                InputLabelProps={{ shrink: true }}
                {...form.register('joinDate')}
                error={!!form.formState.errors.joinDate}
                helperText={form.formState.errors.joinDate?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!form.formState.errors.employmentType}>
                <InputLabel>Employment Type*</InputLabel>
                <Select
                  label="Employment Type*"
                  {...form.register('employmentType')}
                >
                  <MenuItem value="full-time">Full Time</MenuItem>
                  <MenuItem value="part-time">Part Time</MenuItem>
                  <MenuItem value="contract">Contract</MenuItem>
                  <MenuItem value="intern">Intern</MenuItem>
                </Select>
                {form.formState.errors.employmentType && (
                  <FormHelperText>{form.formState.errors.employmentType.message}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Salary*"
                type="number"
                {...form.register('salary')}
                error={!!form.formState.errors.salary}
                helperText={form.formState.errors.salary?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!form.formState.errors.status}>
                <InputLabel>Status*</InputLabel>
                <Select
                  label="Status*"
                  {...form.register('status')}
                >
                  <MenuItem value="Active">Active</MenuItem>
                  <MenuItem value="On Leave">On Leave</MenuItem>
                  <MenuItem value="Terminated">Terminated</MenuItem>
                </Select>
                {form.formState.errors.status && (
                  <FormHelperText>{form.formState.errors.status.message}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        )}

        {activeTab === 2 && (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Street Address"
                {...form.register('address')}
                error={!!form.formState.errors.address}
                helperText={form.formState.errors.address?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="City"
                {...form.register('city')}
                error={!!form.formState.errors.city}
                helperText={form.formState.errors.city?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="State/Province"
                {...form.register('state')}
                error={!!form.formState.errors.state}
                helperText={form.formState.errors.state?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Postal Code"
                {...form.register('postalCode')}
                error={!!form.formState.errors.postalCode}
                helperText={form.formState.errors.postalCode?.message}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Country"
                {...form.register('country')}
                error={!!form.formState.errors.country}
                helperText={form.formState.errors.country?.message}
              />
            </Grid>
          </Grid>
        )}

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button variant="outlined" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" variant="contained" color="primary" disabled={loading}>
            {loading ? 'Saving...' : 'Save Employee'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default AddEmployeeForm;
