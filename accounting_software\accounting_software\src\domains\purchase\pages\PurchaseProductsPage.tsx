import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  InputAdornment,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  Alert,
  Grid,
  Tabs,
  Tab,
  Badge,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  FilterList as FilterListIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Psychology as AIIcon,
  Inventory as InventoryIcon,
  RequestPage as RequestIcon,
  ShoppingCart as ShoppingCartIcon,
  Assessment as AssessmentIcon,
  Business as BusinessIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import { usePurchaseProducts } from '../../../contexts/PurchaseProductContext';
import PurchaseProductForm from '../components/PurchaseProductForm';
import ProductCategoryModal from '../../../shared/components/ProductCategoryModal';
import { PurchaseProduct } from '../../../shared/types/purchase.types';

const PurchaseProductsPage: React.FC = () => {
  const { purchaseProducts, deletePurchaseProduct, isLoading, error } = usePurchaseProducts();
  
  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // State for search
  const [searchTerm, setSearchTerm] = useState('');
  
  // State for product form dialog
  const [openProductForm, setOpenProductForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<PurchaseProduct | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit' | 'view'>('create');
  
  // State for delete confirmation dialog
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [productToDelete, setProductToDelete] = useState<string | null>(null);
  
  // State for category modal
  const [categoryModalOpen, setCategoryModalOpen] = useState(false);

  // Filter products based on search term
  const filteredProducts = purchaseProducts.filter((product) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.vendorName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle pagination change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Handle product form open
  const handleOpenProductForm = (mode: 'create' | 'edit' | 'view', product?: PurchaseProduct) => {
    if (mode === 'create') {
      alert('🛒 Opening Product Creation Form\n\nYou are about to create a new product that will be saved to the database.\n\nFeatures available:\n• AI-powered MOQ calculations\n• Chart of Accounts integration\n• Vendor management\n• Workflow status tracking');
    }
    setFormMode(mode);
    setSelectedProduct(product || null);
    setOpenProductForm(true);
  };

  // Handle product form close
  const handleCloseProductForm = () => {
    setOpenProductForm(false);
    setSelectedProduct(null);
  };

  // Handle delete dialog open
  const handleOpenDeleteDialog = (productId: string) => {
    setProductToDelete(productId);
    setOpenDeleteDialog(true);
  };

  // Handle delete dialog close
  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setProductToDelete(null);
  };

  // Handle product deletion
  const handleDeleteProduct = () => {
    if (productToDelete) {
      deletePurchaseProduct(productToDelete);
      handleCloseDeleteDialog();
    }
  };

  // Render product type chip
  const renderProductTypeChip = (type: string) => {
    let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'default';
    
    switch (type) {
      case 'product':
        color = 'primary';
        break;
      case 'service':
        color = 'secondary';
        break;
      case 'bundle':
        color = 'info';
        break;
      default:
        color = 'default';
    }
    
    return <Chip label={type} color={color} size="small" />;
  };

  // Render status chip
  const renderStatusChip = (status: string) => {
    let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'default';
    
    switch (status) {
      case 'active':
        color = 'success';
        break;
      case 'inactive':
        color = 'error';
        break;
      case 'discontinued':
        color = 'warning';
        break;
      default:
        color = 'default';
    }
    
    return <Chip label={status} color={color} size="small" />;
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1">
            🛒 Purchase Products & Services (Purchase Department)
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
            Full product creation, inventory management, and AI-powered MOQ calculations
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Chip 
            label="Purchase Department Access" 
            color="success" 
            variant="outlined"
            icon={<BusinessIcon />}
          />
          <Button
            variant="outlined"
            startIcon={<RequestIcon />}
            size="small"
          >
            View Requests
          </Button>
          <Button
            variant="outlined"
            startIcon={<AssessmentIcon />}
            size="small"
          >
            Purchase Reports
          </Button>
          <Button
            variant="outlined"
            startIcon={<CategoryIcon />}
            size="small"
            onClick={() => setCategoryModalOpen(true)}
          >
            Manage Categories
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenProductForm('create')}
          >
            Create New Product
          </Button>
        </Box>
      </Box>

      {/* Purchase Department Access Notice */}
      <Alert severity="success" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>🛒 Purchase Department Access:</strong> You have full access to create products, manage inventory, 
          set cost prices, configure suppliers, and use AI-powered MOQ calculations. Sales department requests will appear in your queue.
        </Typography>
      </Alert>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <TextField
              placeholder="Search products..."
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              sx={{ width: '40%' }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
            <Box>
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                sx={{ mr: 1 }}
              >
                Filter
              </Button>
              <Button variant="outlined">Export</Button>
            </Box>
          </Box>
        </CardContent>
      </Card>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>SKU</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Vendor</TableCell>
              <TableCell>Purchase Price</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredProducts
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((product) => (
                <TableRow key={product.id}>
                  <TableCell>{product.name}</TableCell>
                  <TableCell>{product.sku}</TableCell>
                  <TableCell>{renderProductTypeChip(product.type)}</TableCell>
                  <TableCell>{product.vendorName || 'N/A'}</TableCell>
                  <TableCell>
                    {product.purchasePrice.toLocaleString('en-US', {
                      style: 'currency',
                      currency: product.currency || 'USD',
                    })}
                  </TableCell>
                  <TableCell>{renderStatusChip(product.status)}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenProductForm('view', product)}
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenProductForm('edit', product)}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDeleteDialog(product.id)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            {filteredProducts.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} align="center">
                  No purchase products found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredProducts.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Product Form Dialog */}
      <Dialog
        open={openProductForm}
        onClose={handleCloseProductForm}
        maxWidth={false}
        fullWidth
        PaperProps={{
          sx: {
            width: 'calc(100vw - 1in)',
            height: 'calc(100vh - 1in)',
            maxWidth: 'none',
            maxHeight: 'none',
            margin: '0.5in',
          }
        }}
      >
        <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2 }}>
          <Typography variant="h5" fontWeight="bold">
            {formMode === 'create'
              ? '🛒 Create New Product (Purchase Department)'
              : formMode === 'edit'
              ? '✏️ Edit Product (Purchase Department)'
              : '👁️ View Product Details'}
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
            {formMode === 'create'
              ? 'Complete product creation with AI-powered MOQ calculations and full inventory management'
              : formMode === 'edit'
              ? 'Update product details, pricing, and inventory settings'
              : 'View complete product information and purchase history'}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ height: 'calc(100vh - 200px)', overflow: 'auto', p: 3 }}>
          <PurchaseProductForm
            mode={formMode}
            initialData={selectedProduct}
            onClose={handleCloseProductForm}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this purchase product? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDeleteProduct} color="error">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Product Category Management Modal */}
      <ProductCategoryModal
        open={categoryModalOpen}
        onClose={() => setCategoryModalOpen(false)}
      />
    </Box>
  );
};

export default PurchaseProductsPage; 