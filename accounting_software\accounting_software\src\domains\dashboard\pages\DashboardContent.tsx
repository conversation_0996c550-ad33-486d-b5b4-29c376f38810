import React from 'react';
import { 
  Box, Typography, Grid, Paper, Card, CardContent, 
  IconButton, Tooltip, Divider, Avatar, List, ListItem, 
  ListItemText, ListItemAvatar, Button, Chip
} from '@mui/material';
import { 
  TrendingUp, AttachMoney, Receipt, 
  ShowChart, AccountBalance, People,
  ArrowForward, CalendarToday, Inventory
} from '@mui/icons-material';
import {
  AreaChart, Area, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, ResponsiveContainer,
  PieChart, Pie, Cell
} from 'recharts';
import { useTheme } from '@mui/material/styles';
import StatCard from '../../../shared/components/StatCard';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { Link } from 'react-router-dom';

const DashboardContent: React.FC = () => {
  const theme = useTheme();

  // Mock data for statistics
  const stats = {
    totalRevenue: 124500,
    pendingInvoices: 18,
    totalCustomers: 156,
    accountBalance: 87350
  };

  // Mock data for revenue chart
  const revenueData = [
    { month: 'Jan', revenue: 15000, expenses: 12000 },
    { month: 'Feb', revenue: 18000, expenses: 13000 },
    { month: 'Mar', revenue: 16500, expenses: 12500 },
    { month: 'Apr', revenue: 21000, expenses: 14000 },
    { month: 'May', revenue: 19500, expenses: 13500 },
    { month: 'Jun', revenue: 23000, expenses: 15000 },
  ];

  // Mock data for expense breakdown
  const expenseData = [
    { name: 'Operations', value: 35 },
    { name: 'Marketing', value: 25 },
    { name: 'Salaries', value: 30 },
    { name: 'Other', value: 10 },
  ];

  const COLORS = [theme.palette.primary.main, theme.palette.secondary.main, theme.palette.success.main, theme.palette.warning.main];

  // Mock data for recent transactions
  const recentTransactions = [
    { id: 'TRX-001', description: 'Invoice Payment #INV-056', amount: 2500, type: 'income', date: '2024-03-12' },
    { id: 'TRX-002', description: 'Office Supplies', amount: 350, type: 'expense', date: '2024-03-11' },
    { id: 'TRX-003', description: 'Invoice Payment #INV-055', amount: 1800, type: 'income', date: '2024-03-10' },
    { id: 'TRX-004', description: 'Marketing Services', amount: 750, type: 'expense', date: '2024-03-09' },
  ];

  // Mock data for upcoming tasks
  const upcomingTasks = [
    { id: 1, title: 'Tax Filing Deadline', date: '2024-04-15', priority: 'high' },
    { id: 2, title: 'Quarterly Financial Review', date: '2024-03-31', priority: 'medium' },
    { id: 3, title: 'Vendor Payment Due', date: '2024-03-20', priority: 'medium' },
    { id: 4, title: 'Staff Meeting', date: '2024-03-15', priority: 'low' },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.palette.error.main;
      case 'medium': return theme.palette.warning.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.text.secondary;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4" fontWeight="bold">
          Financial Dashboard
        </Typography>
        <Button variant="contained" color="primary" endIcon={<ArrowForward />}>
          Generate Reports
        </Button>
      </PageHeader>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            value={`$${stats.totalRevenue.toLocaleString()}`}
            trend={+8.5}
            icon={<AttachMoney />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Invoices"
            value={stats.pendingInvoices}
            trend={-2.3}
            icon={<Receipt />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Customers"
            value={stats.totalCustomers}
            trend={+4.7}
            icon={<People />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Account Balance"
            value={`$${stats.accountBalance.toLocaleString()}`}
            trend={+6.2}
            icon={<AccountBalance />}
          />
        </Grid>
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Revenue Chart */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Revenue vs Expenses
            </Typography>
            <Box sx={{ width: '100%', height: 300 }}>
              <ResponsiveContainer>
                <AreaChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <RechartsTooltip formatter={(value) => `$${value}`} />
                  <Area
                    type="monotone"
                    dataKey="revenue"
                    name="Revenue"
                    stroke={theme.palette.primary.main}
                    fill={theme.palette.primary.light}
                    fillOpacity={0.3}
                  />
                  <Area
                    type="monotone"
                    dataKey="expenses"
                    name="Expenses"
                    stroke={theme.palette.error.main}
                    fill={theme.palette.error.light}
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>

        {/* Expense Breakdown */}
        <Grid item xs={12} md={6} lg={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Expense Breakdown
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={expenseData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {expenseData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>

        {/* Recent Transactions */}
        <Grid item xs={12} md={6} lg={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Recent Transactions
              </Typography>
              <Button size="small" endIcon={<ArrowForward />}>
                View All
              </Button>
            </Box>
            <List>
              {recentTransactions.map((transaction) => (
                <React.Fragment key={transaction.id}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar 
                        sx={{ 
                          bgcolor: transaction.type === 'income' 
                            ? theme.palette.success.light 
                            : theme.palette.error.light 
                        }}
                      >
                        {transaction.type === 'income' ? <TrendingUp /> : <ShowChart />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={transaction.description}
                      secondary={transaction.date}
                    />
                    <Typography 
                      variant="body1" 
                      fontWeight="bold"
                      color={transaction.type === 'income' ? 'success.main' : 'error.main'}
                    >
                      {transaction.type === 'income' ? '+' : '-'}${transaction.amount}
                    </Typography>
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Upcoming Tasks */}
        <Grid item xs={12} md={6} lg={6}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Upcoming Tasks
              </Typography>
              <Button size="small" endIcon={<ArrowForward />}>
                View Calendar
              </Button>
            </Box>
            <List>
              {upcomingTasks.map((task) => (
                <React.Fragment key={task.id}>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: getPriorityColor(task.priority) }}>
                        <CalendarToday />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={task.title}
                      secondary={task.date}
                    />
                    <Chip 
                      label={task.priority.toUpperCase()} 
                      size="small"
                      sx={{ 
                        bgcolor: `${getPriorityColor(task.priority)}20`,
                        color: getPriorityColor(task.priority),
                        fontWeight: 'bold'
                      }}
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Inventory Status */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" fontWeight="bold">
                Inventory Status
              </Typography>
              <Button size="small" endIcon={<ArrowForward />}>
                Manage Inventory
              </Button>
            </Box>
            <Grid container spacing={2}>
              {['Low Stock Items', 'Items to Reorder', 'Recently Added', 'Top Selling'].map((category, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Card sx={{ bgcolor: theme.palette.background.default }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Inventory sx={{ mr: 1, color: theme.palette.text.secondary }} />
                        <Typography variant="subtitle1" fontWeight="medium">
                          {category}
                        </Typography>
                      </Box>
                      <Typography variant="h4" color="primary" fontWeight="bold">
                        {Math.floor(Math.random() * 20) + 5}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {index % 2 === 0 ? 'Requires attention' : 'Items in this category'}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Quick Links
        </Typography>
        <Grid container spacing={2}>
          <Grid item>
            <Button 
              variant="contained" 
              component={Link} 
              to="/dashboard/sales/products"
            >
              Products & Services
            </Button>
          </Grid>
        </Grid>
      </Box>
    </PageContainer>
  );
};

export default DashboardContent;