import React from 'react';
import { TextField, TextFieldProps } from '@mui/material';

export interface FormTextFieldProps extends Omit<TextFieldProps, 'variant'> {
  variant?: 'outlined' | 'filled' | 'standard';
  showCharCount?: boolean;
  maxCharacters?: number;
}

const FormTextField: React.FC<FormTextFieldProps> = ({
  variant = 'outlined',
  showCharCount = false,
  maxCharacters,
  helperText,
  value,
  ...props
}) => {
  const charCount = typeof value === 'string' ? value.length : 0;
  const isOverLimit = Boolean(maxCharacters && charCount > maxCharacters);
  
  const getHelperText = () => {
    let text = helperText || '';
    
    if (showCharCount && maxCharacters) {
      const countText = `${charCount}/${maxCharacters}`;
      text = text ? `${text} (${countText})` : countText;
    } else if (showCharCount) {
      const countText = `${charCount} characters`;
      text = text ? `${text} (${countText})` : countText;
    }
    
    return text;
  };

  return (
    <TextField
      variant={variant}
      fullWidth
      value={value}
      helperText={getHelperText()}
      error={isOverLimit}
      inputProps={{
        maxLength: maxCharacters,
        ...props.inputProps,
      }}
      {...props}
    />
  );
};

export default FormTextField; 