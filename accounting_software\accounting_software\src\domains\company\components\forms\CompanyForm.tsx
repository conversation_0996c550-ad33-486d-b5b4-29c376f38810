import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  FormControlLabel,
  Switch,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { CompanyFormData } from '../../../../shared/types/company.types';
import { CURRENCIES } from '../../../../shared/types/product.types';
import SearchableSelect from '../../../../shared/components/SearchableSelect';
import dayjs from 'dayjs';

import { Group, ExpandMore, Business, ContactMail, AttachMoney } from '@mui/icons-material';

const validationSchema = Yup.object({
  companyName: Yup.string()
    .required('Company name is required')
    .min(3, 'Company name must be at least 3 characters'),
  registrationNumber: Yup.string()
    .required('Registration number is required'),
  address: Yup.string()
    .required('Address is required'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  phone: Yup.string()
    .required('Phone number is required')
    .matches(/^[0-9+\-() ]+$/, 'Invalid phone number'),
  taxId: Yup.string()
    .required('Tax ID is required'),
  functionalCurrency: Yup.string()
    .required('Functional currency is required'),
  reportingCurrency: Yup.string()
    .required('Reporting currency is required'),
  accountingYear: Yup.object({
    start: Yup.string().required('Accounting year start is required'),
    end: Yup.string().required('Accounting year end is required'),
  }),
  fiscalYear: Yup.object({
    start: Yup.string().required('Fiscal year start is required'),
    end: Yup.string().required('Fiscal year end is required'),
  }),
  salesTaxNumber: Yup.string()
    .when('salesTaxRegistered', (salesTaxRegistered, schema) => 
      salesTaxRegistered ? 
        schema.required('Sales tax number is required when registered for sales tax') : 
        schema
    ),
});

interface CompanyFormProps {
  onSubmit: (values: CompanyFormData) => void;
  initialValues?: Partial<CompanyFormData>;
}

const CompanyForm: React.FC<CompanyFormProps> = ({ onSubmit, initialValues = {} }) => {
  // State to track expanded accordion sections
  const [expanded, setExpanded] = useState<string | false>('basicInfo');

  const handleAccordionChange = (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const formik = useFormik<CompanyFormData>({
    initialValues: {
      companyName: initialValues.companyName || '',
      registrationNumber: initialValues.registrationNumber || '',
      address: initialValues.address || '',
      email: initialValues.email || '',
      phone: initialValues.phone || '',
      taxId: initialValues.taxId || '',
      functionalCurrency: initialValues.functionalCurrency || 'USD',
      reportingCurrency: initialValues.reportingCurrency || 'USD',
      accountingYear: initialValues.accountingYear || {
        start: dayjs().format('YYYY-MM-DD'),
        end: dayjs().add(1, 'year').subtract(1, 'day').format('YYYY-MM-DD'),
      },
      fiscalYear: initialValues.fiscalYear || {
        start: dayjs().format('YYYY-MM-DD'),
        end: dayjs().add(1, 'year').subtract(1, 'day').format('YYYY-MM-DD'),
      },
      salesTaxRegistered: initialValues.salesTaxRegistered || false,
      salesTaxNumber: initialValues.salesTaxNumber || '',
    },
    validationSchema,
    onSubmit,
  });

  return (
    <Card sx={{ margin: 0, padding: 0, borderRadius: 12, boxShadow: '0 4px 8px rgba(0,0,0,0.1)' }}>
      <CardContent sx={{ padding: 3, backgroundColor: '#fff' }}>
        <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 'bold', color: '#2c3e50' }}>
          Company Information
        </Typography>
        
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Information Section */}
            <Grid item xs={12}>
              <Accordion 
                expanded={expanded === 'basicInfo'} 
                onChange={handleAccordionChange('basicInfo')}
                sx={{ 
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  '&:before': { display: 'none' },
                  borderRadius: 2,
                  mb: 2
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore />}
                  aria-controls="basicInfo-content"
                  id="basicInfo-header"
                  sx={{ 
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px 8px 0 0',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Business sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Basic Information
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="companyName"
                        name="companyName"
                        label="Company Name"
                        value={formik.values.companyName}
                        onChange={formik.handleChange}
                        error={formik.touched.companyName && Boolean(formik.errors.companyName)}
                        helperText={formik.touched.companyName && formik.errors.companyName as string}
                        sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="registrationNumber"
                        name="registrationNumber"
                        label="Registration Number"
                        value={formik.values.registrationNumber}
                        onChange={formik.handleChange}
                        error={formik.touched.registrationNumber && Boolean(formik.errors.registrationNumber)}
                        helperText={formik.touched.registrationNumber && formik.errors.registrationNumber as string}
                        sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Contact Information Section */}
            <Grid item xs={12}>
              <Accordion 
                expanded={expanded === 'contactInfo'} 
                onChange={handleAccordionChange('contactInfo')}
                sx={{ 
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  '&:before': { display: 'none' },
                  borderRadius: 2,
                  mb: 2
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore />}
                  aria-controls="contactInfo-content"
                  id="contactInfo-header"
                  sx={{ 
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px 8px 0 0',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <ContactMail sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Contact Information
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="address"
                        name="address"
                        label="Address"
                        multiline
                        rows={3}
                        value={formik.values.address}
                        onChange={formik.handleChange}
                        error={formik.touched.address && Boolean(formik.errors.address)}
                        helperText={formik.touched.address && formik.errors.address as string}
                        sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="email"
                        name="email"
                        label="Email"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        error={formik.touched.email && Boolean(formik.errors.email)}
                        helperText={formik.touched.email && formik.errors.email as string}
                        sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="phone"
                        name="phone"
                        label="Phone"
                        value={formik.values.phone}
                        onChange={formik.handleChange}
                        error={formik.touched.phone && Boolean(formik.errors.phone)}
                        helperText={formik.touched.phone && formik.errors.phone as string}
                        sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Financial Information Section */}
            <Grid item xs={12}>
              <Accordion 
                expanded={expanded === 'financialInfo'} 
                onChange={handleAccordionChange('financialInfo')}
                sx={{ 
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  '&:before': { display: 'none' },
                  borderRadius: 2,
                  mb: 2
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore />}
                  aria-controls="financialInfo-content"
                  id="financialInfo-header"
                  sx={{ 
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px 8px 0 0',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AttachMoney sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Financial Information
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        id="taxId"
                        name="taxId"
                        label="Tax ID"
                        value={formik.values.taxId}
                        onChange={formik.handleChange}
                        error={formik.touched.taxId && Boolean(formik.errors.taxId)}
                        helperText={formik.touched.taxId && formik.errors.taxId as string}
                        sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                      />
                    </Grid>

                    {/* Currency Settings */}
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" sx={{ mb: 2, mt: 2 }}>
                        Currency Settings
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <SearchableSelect
                        options={CURRENCIES.map(curr => ({ label: `${curr.name} (${curr.code})`, value: curr.code }))}
                        value={CURRENCIES.map(curr => ({ label: `${curr.name} (${curr.code})`, value: curr.code })).find(option => option.value === formik.values.functionalCurrency) || null}
                        onChange={(newValue) => {
                          formik.setFieldValue('functionalCurrency', newValue ? newValue.value : 'USD');
                        }}
                        getOptionLabel={(option) => option.label}
                        label="Functional Currency"
                        error={formik.touched.functionalCurrency && Boolean(formik.errors.functionalCurrency)}
                        helperText={formik.touched.functionalCurrency ? formik.errors.functionalCurrency as string : 'The primary currency for your business operations'}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <SearchableSelect
                        options={CURRENCIES.map(curr => ({ label: `${curr.name} (${curr.code})`, value: curr.code }))}
                        value={CURRENCIES.map(curr => ({ label: `${curr.name} (${curr.code})`, value: curr.code })).find(option => option.value === formik.values.reportingCurrency) || null}
                        onChange={(newValue) => {
                          formik.setFieldValue('reportingCurrency', newValue ? newValue.value : 'USD');
                        }}
                        getOptionLabel={(option) => option.label}
                        label="Reporting Currency"
                        error={formik.touched.reportingCurrency && Boolean(formik.errors.reportingCurrency)}
                        helperText={formik.touched.reportingCurrency ? formik.errors.reportingCurrency as string : 'Currency used for financial reporting and consolidation'}
                      />
                    </Grid>

                    {/* Accounting Year */}
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" sx={{ mb: 2, mt: 2 }}>
                        Accounting Year
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="Start Date"
                        value={dayjs(formik.values.accountingYear.start)}
                        onChange={(date) => formik.setFieldValue('accountingYear.start', date?.format('YYYY-MM-DD'))}
                        sx={{ width: '100%' }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="End Date"
                        value={dayjs(formik.values.accountingYear.end)}
                        onChange={(date) => formik.setFieldValue('accountingYear.end', date?.format('YYYY-MM-DD'))}
                        sx={{ width: '100%' }}
                      />
                    </Grid>

                    {/* Fiscal Year */}
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" sx={{ mb: 2, mt: 2 }}>
                        Fiscal Year
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="Start Date"
                        value={dayjs(formik.values.fiscalYear.start)}
                        onChange={(date) => formik.setFieldValue('fiscalYear.start', date?.format('YYYY-MM-DD'))}
                        sx={{ width: '100%' }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <DatePicker
                        label="End Date"
                        value={dayjs(formik.values.fiscalYear.end)}
                        onChange={(date) => formik.setFieldValue('fiscalYear.end', date?.format('YYYY-MM-DD'))}
                        sx={{ width: '100%' }}
                      />
                    </Grid>
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Sales Tax Section */}
            <Grid item xs={12}>
              <Accordion 
                expanded={expanded === 'salesTaxInfo'} 
                onChange={handleAccordionChange('salesTaxInfo')}
                sx={{ 
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                  '&:before': { display: 'none' },
                  borderRadius: 2,
                  mb: 2
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore />}
                  aria-controls="salesTaxInfo-content"
                  id="salesTaxInfo-header"
                  sx={{ 
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px 8px 0 0',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Group sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      Sales Tax Information
                    </Typography>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.salesTaxRegistered}
                            onChange={(e) => formik.setFieldValue('salesTaxRegistered', e.target.checked)}
                            name="salesTaxRegistered"
                          />
                        }
                        label="Registered for Sales Tax"
                      />
                    </Grid>

                    {formik.values.salesTaxRegistered && (
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          id="salesTaxNumber"
                          name="salesTaxNumber"
                          label="Sales Tax Registration Number"
                          value={formik.values.salesTaxNumber}
                          onChange={formik.handleChange}
                          error={formik.touched.salesTaxNumber && Boolean(formik.errors.salesTaxNumber)}
                          helperText={formik.touched.salesTaxNumber && formik.errors.salesTaxNumber as string}
                          sx={{ '& .MuiInputBase-root': { borderRadius: 2 } }}
                        />
                      </Grid>
                    )}
                  </Grid>
                </AccordionDetails>
              </Accordion>
            </Grid>

            {/* Form Actions */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Button
                  type="button"
                  variant="outlined"
                  color="secondary"
                  size="large"
                  sx={{ borderRadius: 2 }}
                  onClick={() => formik.resetForm()}
                >
                  Reset
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  sx={{ borderRadius: 2 }}
                >
                  Save Company
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  );
};

export default CompanyForm;
