import api from '../../../services/api';

export interface InventoryTransferItem {
  transfer_item_id?: number;
  product: number;
  product_name?: string;
  product_sku?: string;
  quantity: number;
  batch_number?: string;
  notes?: string;
  line_order?: number;
}

export interface InventoryTransfer {
  transfer_id?: number;
  transfer_number?: string;
  from_warehouse: number;
  from_warehouse_name?: string;
  to_warehouse: number;
  to_warehouse_name?: string;
  transfer_date: string;
  notes?: string;
  status: 'DRAFT' | 'PENDING' | 'IN_TRANSIT' | 'COMPLETED' | 'CANCELLED';
  total_quantity?: number;
  total_items?: number;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  created_by_name?: string;
  completed_at?: string;
  completed_by?: number;
  completed_by_name?: string;
  items?: InventoryTransferItem[];
}

export interface TransferStats {
  total_transfers: number;
  draft_transfers: number;
  pending_transfers: number;
  completed_transfers: number;
  recent_transfers: number;
}

class TransferService {
  async getTransfers(params?: {
    page?: number;
    search?: string;
    status?: string;
    from_warehouse?: number;
    to_warehouse?: number;
  }) {
    const response = await api.get('/inventory/transfers/', { params });
    return response.data;
  }

  async getTransfer(id: number): Promise<InventoryTransfer> {
    const response = await api.get(`/inventory/transfers/${id}/`);
    return response.data;
  }

  async createTransfer(transfer: Partial<InventoryTransfer>): Promise<InventoryTransfer> {
    const response = await api.post('/inventory/transfers/', transfer);
    return response.data;
  }

  async updateTransfer(id: number, transfer: Partial<InventoryTransfer>): Promise<InventoryTransfer> {
    const response = await api.put(`/inventory/transfers/${id}/`, transfer);
    return response.data;
  }

  async deleteTransfer(id: number): Promise<void> {
    await api.delete(`/inventory/transfers/${id}/`);
  }

  async setPending(id: number): Promise<InventoryTransfer> {
    const response = await api.post(`/inventory/transfers/${id}/set_pending/`);
    return response.data;
  }

  async completeTransfer(id: number): Promise<InventoryTransfer> {
    const response = await api.post(`/inventory/transfers/${id}/complete/`);
    return response.data;
  }

  async getTransferStats(): Promise<TransferStats> {
    const response = await api.get('/inventory/transfers/stats/');
    return response.data;
  }
}

export const transferService = new TransferService(); 