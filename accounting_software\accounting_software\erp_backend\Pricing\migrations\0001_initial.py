# Generated by Django 4.2.21 on 2025-06-27 00:26

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sales', '0011_update_customer_references'),
        ('sales_tax', '0001_initial'),
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PriceList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('currency', models.CharField(choices=[('USD', 'US Dollar ($)'), ('EUR', 'Euro (€)'), ('GBP', 'British Pound (£)'), ('JPY', 'Japanese Yen (¥)'), ('CAD', 'Canadian Dollar (C$)'), ('AUD', 'Australian Dollar (A$)'), ('INR', 'Indian Rupee (₹)'), ('CNY', 'Chinese Yuan (¥)'), ('NGN', 'Nigerian Naira (₦)'), ('ZAR', 'South African Rand (R)')], default='USD', max_length=3)),
                ('valid_from', models.DateField(default=django.utils.timezone.now)),
                ('valid_to', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=False, help_text='Use as default price list when no specific list is assigned')),
            ],
            options={
                'verbose_name': 'Price List',
                'verbose_name_plural': 'Price Lists',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(help_text='Unique product identifier/SKU', max_length=50, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('type', models.CharField(choices=[('product', 'Product'), ('service', 'Service')], default='product', max_length=10)),
                ('uom', models.CharField(default='each', help_text='Unit of Measure (each, kg, liter, etc.)', max_length=20)),
                ('cost_method', models.CharField(choices=[('standard', 'Standard Cost'), ('average', 'Average Cost'), ('fifo', 'FIFO')], default='standard', max_length=10)),
                ('standard_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('average_cost', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('last_cost', models.DecimalField(decimal_places=2, default=0, editable=False, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.productcategory')),
                ('tax_code', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales_tax.salestax')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='DiscountRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('discount_percent', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('min_quantity', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('customer_group', models.CharField(blank=True, help_text='Customer group name for discount eligibility', max_length=100, null=True)),
                ('start_date', models.DateField(default=django.utils.timezone.now)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('product_category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='sales.productcategory')),
            ],
            options={
                'verbose_name': 'Discount Rule',
                'verbose_name_plural': 'Discount Rules',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PriceListItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)])),
                ('min_quantity', models.DecimalField(decimal_places=2, default=1, help_text='Minimum quantity for this price to apply', max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0, help_text='Additional discount percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('effective_date', models.DateField(default=django.utils.timezone.now)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('price_list', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='Pricing.pricelist')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Pricing.product')),
            ],
            options={
                'verbose_name': 'Price List Item',
                'verbose_name_plural': 'Price List Items',
                'ordering': ['price_list', 'product'],
                'unique_together': {('price_list', 'product', 'min_quantity')},
            },
        ),
        migrations.CreateModel(
            name='CustomerPriceList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_date', models.DateField(default=django.utils.timezone.now)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contacts.customer')),
                ('price_list', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Pricing.pricelist')),
            ],
            options={
                'verbose_name': 'Customer Price List',
                'verbose_name_plural': 'Customer Price Lists',
                'unique_together': {('customer', 'price_list')},
            },
        ),
    ]
