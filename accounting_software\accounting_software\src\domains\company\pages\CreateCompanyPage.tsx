import React, { useEffect, useState } from 'react';
import { Box, Typography, IconButton, styled, CircularProgress } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CompanyForm from '../components/forms/CompanyForm';
import { useNavigate } from 'react-router-dom';
import { CompanyFormData } from '../../../shared/types/company.types';
import { useSnackbar } from 'notistack';
import { useAuth } from '../../../contexts/AuthContext';

const PageContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(3),
  paddingTop: theme.spacing(1.3),
  marginTop: theme.spacing(1.3),
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  border: `1px solid ${theme.palette.divider}`,
}));

const PageHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
}));

const CreateCompanyPage: React.FC = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { isAuthenticated, isLoading: authLoading, fetchUser } = useAuth();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated) {
        navigate('/login');
        return;
      }
      // Check if company already exists
      checkCompany();
    }
  }, [isAuthenticated, authLoading]);

  const checkCompany = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await fetch('http://localhost:8000/api/account/company/current/', {
        headers: {
          'Authorization': `Token ${token}`
        }
      });
      
      if (response.ok) {
        // If company exists, redirect to dashboard
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Error checking company:', error);
    }
  };

  const handleSubmit = async (values: CompanyFormData) => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        enqueueSnackbar('Authentication token missing. Please log in again.', { variant: 'error' });
        navigate('/login');
        return;
      }

      const companyData = {
        name: values.companyName,
        registration_number: values.registrationNumber,
        tax_id: values.taxId,
        address: values.address,
        phone: values.phone,
        email: values.email,
        functional_currency: values.functionalCurrency,
        reporting_currency: values.reportingCurrency,
        accounting_year_start: values.accountingYear.start,
        accounting_year_end: values.accountingYear.end,
        fiscal_year_start: values.fiscalYear.start,
        fiscal_year_end: values.fiscalYear.end,
        sales_tax_registered: values.salesTaxRegistered,
        sales_tax_number: values.salesTaxNumber,
      };

      const response = await fetch('http://localhost:8000/api/account/companies/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`
        },
        body: JSON.stringify(companyData)
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle specific authentication errors
        if (response.status === 401) {
          localStorage.removeItem('token');
          enqueueSnackbar('Session expired. Please log in again.', { variant: 'error' });
          navigate('/login');
          return;
        }
        throw new Error(data.detail || Object.values(data).join(', ') || 'Failed to create company');
      }

      enqueueSnackbar('Company created successfully', { variant: 'success' });
      
      // Refresh user data and navigate to dashboard
      await fetchUser();
      navigate('/dashboard');
    } catch (error) {
      console.error('Error creating company:', error);
      enqueueSnackbar((error as Error).message || 'Failed to create company', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    navigate(-1);
  };

  if (loading || authLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4">
          Create New Company
        </Typography>
        <IconButton
          onClick={handleClose}
          size="large"
          sx={{ color: 'text.secondary' }}
        >
          <CloseIcon />
        </IconButton>
      </PageHeader>
      
      <Box sx={{ mb: 4 }}>
        <Typography variant="body1" color="text.secondary">
          Please fill in the company details below to create a new company profile.
        </Typography>
      </Box>

      <CompanyForm onSubmit={handleSubmit} />
    </PageContainer>
  );
};

export default CreateCompanyPage;
