from rest_framework import serializers
from .models import TDS

class TDSSerializer(serializers.ModelSerializer):
    created_by_username = serializers.Char<PERSON>ield(source='created_by.username', read_only=True)
    tds_type_display = serializers.Char<PERSON>ield(source='get_tds_type_display', read_only=True)
    is_liability = serializers.BooleanField(read_only=True)
    is_asset = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = TDS
        fields = [
            'id', 'tds_type', 'tds_type_display', 'description', 'rate', 'section', 
            'threshold_limit', 'remarks', 'created_by', 'created_by_username', 
            'created_at', 'updated_at', 'is_liability', 'is_asset'
        ]
        read_only_fields = ['created_by', 'created_by_username', 'created_at', 'updated_at', 'is_liability', 'is_asset'] 