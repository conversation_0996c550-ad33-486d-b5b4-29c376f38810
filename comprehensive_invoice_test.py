#!/usr/bin/env python3
"""
Comprehensive test script for invoice creation with line items
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000/api"
TOKEN = "e0d1048e1f14689c6eba6616845a074bad2167bc"

def test_invoice_with_line_items():
    """Test invoice creation with line items"""
    print("Testing invoice creation with line items...")
    
    # Invoice data with line items
    invoice_data = {
        "customer": 51,  # Contact ID of our test customer
        "invoice_date": "2025-06-29",
        "due_date": "2025-07-29",
        "status": "draft",
        "subtotal": 200.0,
        "tax_amount": 20.0,
        "total_amount": 220.0,
        "memo": "Test invoice with line items",
        "payment_terms": "Net 30",
        "line_items": [
            {
                "description": "Test Service 1",
                "quantity": 1.0,
                "unit_price": 100.0,
                "discount_percent": 0.0,
                "line_total": 100.0,
                "taxable": True,
                "tax_rate": 10.0,
                "tax_amount": 10.0,
                "line_order": 0
            },
            {
                "description": "Test Service 2",
                "quantity": 1.0,
                "unit_price": 100.0,
                "discount_percent": 0.0,
                "line_total": 100.0,
                "taxable": True,
                "tax_rate": 10.0,
                "tax_amount": 10.0,
                "line_order": 1
            }
        ]
    }
    
    print("Invoice data:")
    print(json.dumps(invoice_data, indent=2))
    
    try:
        response = requests.post(
            f"{BASE_URL}/sales/invoices/",
            json=invoice_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Token {TOKEN}"
            },
            timeout=10
        )
        
        print(f"\nPOST Status: {response.status_code}")
        print(f"POST Response Headers: {dict(response.headers)}")
        print(f"POST Response: {response.text}")
        
        if response.status_code == 201:
            invoice = response.json()
            print(f"\n✅ Invoice created successfully!")
            print(f"Invoice ID: {invoice.get('id')}")
            print(f"Invoice Number: {invoice.get('invoice_number')}")
            print(f"Line Items Count: {len(invoice.get('line_items', []))}")
            return True
        else:
            print(f"\n❌ Failed to create invoice")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print("Could not parse error response as JSON")
            return False
        
    except Exception as e:
        print(f"❌ POST Error: {e}")
        return False

def test_get_invoices():
    """Test getting list of invoices"""
    print("\nTesting invoice list retrieval...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/sales/invoices/",
            headers={"Authorization": f"Token {TOKEN}"},
            timeout=5
        )
        
        print(f"GET Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Retrieved {data.get('count', 0)} invoices")
            print(f"Results count: {len(data.get('results', []))}")
            return True
        else:
            print(f"❌ Failed to get invoices: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ GET Error: {e}")
        return False

if __name__ == "__main__":
    print("Starting comprehensive invoice tests...")
    
    # Test getting existing invoices
    if not test_get_invoices():
        print("❌ Failed to get invoices!")
        sys.exit(1)
    
    # Test creating invoice with line items
    if test_invoice_with_line_items():
        print("\n🎉 Comprehensive test passed!")
        
        # Test getting invoices again to see the new one
        test_get_invoices()
    else:
        print("\n💥 Comprehensive test failed!")
        sys.exit(1)
