import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Avatar,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  AlertTitle,
  Stack,
  Tooltip,
  Fab,
  CircularProgress,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Pagination,
  TablePagination,
  Snackbar,
  Tabs,
  Tab,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Receipt as ReceiptIcon,
  Payment as PaymentIcon,
  AccountBalance as AccountBalanceIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  GetApp as ExportIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  Description as DescriptionIcon,
  CalendarToday as CalendarIcon,
  CurrencyExchange as CurrencyIcon,
  Timeline as TimelineIcon,
  AssignmentTurnedIn as ApprovalIcon,
  SwapHoriz as TransferIcon,
  PostAdd as PostAddIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

// Transaction Interfaces
interface CashAccount {
  id: number;
  account_name: string;
  account_number: string;
  currency: string;
  is_active: boolean;
}

interface CashTransaction {
  id: number;
  transaction_number: string;
  transaction_date: string;
  transaction_type: 'RECEIPT' | 'PAYMENT' | 'ADJUSTMENT' | 'TRANSFER';
  cash_account: CashAccount;
  amount: number;
  currency: string;
  exchange_rate: number;
  description: string;
  reference_number?: string;
  memo?: string;
  customer?: string;
  vendor?: string;
  employee?: string;
  bank_reference?: string;
  cleared_date?: string;
  status: 'DRAFT' | 'PENDING' | 'POSTED' | 'CANCELLED';
  requires_approval: boolean;
  approved_by?: string;
  approved_at?: string;
  gl_journal_entry?: number;
  created_at: string;
  updated_at: string;
}

interface TransactionFormData {
  transaction_date: string;
  transaction_type: 'RECEIPT' | 'PAYMENT' | 'ADJUSTMENT' | 'TRANSFER';
  cash_account_id: number | '';
  amount: number;
  currency: string;
  exchange_rate: number;
  description: string;
  reference_number: string;
  memo: string;
  customer: string;
  vendor: string;
  employee: string;
  bank_reference: string;
  requires_approval: boolean;
}

const CashTransactionsPage: React.FC = () => {
  const navigate = useNavigate();
  const [transactions, setTransactions] = useState<CashTransaction[]>([]);
  const [accounts, setAccounts] = useState<CashAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterAccount, setFilterAccount] = useState<string>('all');
  const [dateFrom, setDateFrom] = useState<Dayjs | null>(dayjs().subtract(30, 'day'));
  const [dateTo, setDateTo] = useState<Dayjs | null>(dayjs());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedTab, setSelectedTab] = useState(0);

  // Dialog states
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedTransaction, setSelectedTransaction] = useState<CashTransaction | null>(null);
  const [formData, setFormData] = useState<TransactionFormData>({
    transaction_date: dayjs().format('YYYY-MM-DD'),
    transaction_type: 'RECEIPT',
    cash_account_id: '',
    amount: 0,
    currency: 'USD',
    exchange_rate: 1.0,
    description: '',
    reference_number: '',
    memo: '',
    customer: '',
    vendor: '',
    employee: '',
    bank_reference: '',
    requires_approval: false,
  });

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning',
  });

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadTransactions();
    loadAccounts();
  }, [page, rowsPerPage, searchTerm, filterType, filterStatus, filterAccount, dateFrom, dateTo, selectedTab]);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        page: (page + 1).toString(),
        page_size: rowsPerPage.toString(),
        search: searchTerm,
        ordering: '-transaction_date,-transaction_number',
      });

      if (filterType !== 'all') {
        params.append('transaction_type', filterType);
      }
      if (filterStatus !== 'all') {
        params.append('status', filterStatus);
      }
      if (filterAccount !== 'all') {
        params.append('cash_account', filterAccount);
      }
      if (dateFrom) {
        params.append('transaction_date__gte', dateFrom.format('YYYY-MM-DD'));
      }
      if (dateTo) {
        params.append('transaction_date__lte', dateTo.format('YYYY-MM-DD'));
      }

      // Filter by tab
      if (selectedTab === 1) {
        params.append('status', 'PENDING');
      } else if (selectedTab === 2) {
        params.append('requires_approval', 'true');
      }

      const response = await fetch(`http://localhost:8000/api/cms/transactions/?${params}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load transactions');
      }

      const data = await response.json();
      setTransactions(data.results || []);
      setTotalCount(data.count || 0);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load transactions');
      console.error('Load transactions error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAccounts = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/accounts/?is_active=true', {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load accounts');
      }

      const data = await response.json();
      setAccounts(data.results || data);
    } catch (err) {
      console.error('Load accounts error:', err);
    }
  };

  const handleCreate = (transactionType?: 'RECEIPT' | 'PAYMENT') => {
    // Navigate to dedicated forms for payments and receipts
    if (transactionType === 'PAYMENT') {
      navigate('/dashboard/cms/payments/new');
    } else if (transactionType === 'RECEIPT') {
      navigate('/dashboard/cms/receipts/new');
    } else {
      // For other transaction types, keep the current dialog approach
      setDialogMode('create');
      setSelectedTransaction(null);
      setFormData({
        transaction_date: dayjs().format('YYYY-MM-DD'),
        transaction_type: transactionType || 'RECEIPT',
        cash_account_id: '',
        amount: 0,
        currency: currencyInfo?.functional_currency || 'USD',
        exchange_rate: 1.0,
        description: '',
        reference_number: '',
        memo: '',
        customer: '',
        vendor: '',
        employee: '',
        bank_reference: '',
        requires_approval: false,
      });
      setOpenDialog(true);
    }
  };

  const handleEdit = (transaction: CashTransaction) => {
    // Navigate to dedicated forms for payments and receipts
    if (transaction.transaction_type === 'PAYMENT') {
      navigate(`/dashboard/cms/payments/${transaction.id}/edit`);
    } else if (transaction.transaction_type === 'RECEIPT') {
      navigate(`/dashboard/cms/receipts/${transaction.id}/edit`);
    } else {
      // For other transaction types, keep the current dialog approach for now
      setDialogMode('edit');
      setSelectedTransaction(transaction);
      setFormData({
        transaction_date: transaction.transaction_date,
        transaction_type: transaction.transaction_type,
        cash_account_id: transaction.cash_account.id,
        amount: transaction.amount,
        currency: transaction.currency,
        exchange_rate: transaction.exchange_rate,
        description: transaction.description,
        reference_number: transaction.reference_number || '',
        memo: transaction.memo || '',
        customer: transaction.customer || '',
        vendor: transaction.vendor || '',
        employee: transaction.employee || '',
        bank_reference: transaction.bank_reference || '',
        requires_approval: transaction.requires_approval,
      });
      setOpenDialog(true);
    }
  };

  const handleView = (transaction: CashTransaction) => {
    // Navigate to dedicated forms for payments and receipts
    if (transaction.transaction_type === 'PAYMENT') {
      navigate(`/dashboard/cms/payments/${transaction.id}/view`);
    } else if (transaction.transaction_type === 'RECEIPT') {
      navigate(`/dashboard/cms/receipts/${transaction.id}/view`);
    } else {
      // For other transaction types, keep the current dialog approach for now
      setDialogMode('view');
      setSelectedTransaction(transaction);
      setOpenDialog(true);
    }
  };

  const handleSave = async () => {
    try {
      const token = localStorage.getItem('token');
      const url = dialogMode === 'create' 
        ? 'http://localhost:8000/api/cms/transactions/'
        : `http://localhost:8000/api/cms/transactions/${selectedTransaction?.id}/`;
      
      const method = dialogMode === 'create' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to save transaction');
      }

      setSnackbar({
        open: true,
        message: `Transaction ${dialogMode === 'create' ? 'created' : 'updated'} successfully`,
        severity: 'success',
      });

      setOpenDialog(false);
      loadTransactions();

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to save transaction',
        severity: 'error',
      });
    }
  };

  const handleDelete = async (transaction: CashTransaction) => {
    if (!window.confirm(`Are you sure you want to delete transaction "${transaction.transaction_number}"?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/transactions/${transaction.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete transaction');
      }

      setSnackbar({
        open: true,
        message: 'Transaction deleted successfully',
        severity: 'success',
      });

      loadTransactions();

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete transaction',
        severity: 'error',
      });
    }
  };

  const handleApprove = async (transaction: CashTransaction) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/transactions/${transaction.id}/approve_transaction/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to approve transaction');
      }

      setSnackbar({
        open: true,
        message: 'Transaction approved successfully',
        severity: 'success',
      });

      loadTransactions();

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to approve transaction',
        severity: 'error',
      });
    }
  };

  const handlePost = async (transaction: CashTransaction) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/transactions/${transaction.id}/post_transaction/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to post transaction');
      }

      setSnackbar({
        open: true,
        message: 'Transaction posted successfully',
        severity: 'success',
      });

      loadTransactions();

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to post transaction',
        severity: 'error',
      });
    }
  };

  const formatCurrency = (amount: number, currency?: string) => {
    if (!currencyInfo) return '0.00';
    
    const currencyCode = currency || currencyInfo.functional_currency;
    const locale = currencyCode === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case 'RECEIPT': return <TrendingUpIcon color="success" />;
      case 'PAYMENT': return <TrendingDownIcon color="error" />;
      case 'ADJUSTMENT': return <TimelineIcon color="info" />;
      case 'TRANSFER': return <TransferIcon color="primary" />;
      default: return <MoneyIcon />;
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'RECEIPT': return 'success';
      case 'PAYMENT': return 'error';
      case 'ADJUSTMENT': return 'info';
      case 'TRANSFER': return 'primary';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'POSTED': return 'success';
      case 'PENDING': return 'warning';
      case 'DRAFT': return 'default';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'POSTED': return <CheckCircleIcon sx={{ fontSize: '1rem' }} />;
      case 'PENDING': return <ScheduleIcon sx={{ fontSize: '1rem' }} />;
      case 'DRAFT': return <DescriptionIcon sx={{ fontSize: '1rem' }} />;
      case 'CANCELLED': return <CancelIcon sx={{ fontSize: '1rem' }} />;
      default: return <WarningIcon sx={{ fontSize: '1rem' }} />;
    }
  };

  const getStatusDisplay = (transaction: CashTransaction) => {
    const isPosted = transaction.gl_journal_entry !== null;
    
    if (isPosted) {
      return (
        <Stack direction="column" spacing={0.5} alignItems="center">
          <Chip
            size="small"
            label="POSTED"
            color="success"
            icon={<PostAddIcon sx={{ fontSize: '1rem' }} />}
            sx={{
              fontWeight: 'bold',
              '& .MuiChip-icon': {
                fontSize: '1rem !important'
              }
            }}
          />
          <Typography variant="caption" color="text.secondary">
            JE #{transaction.gl_journal_entry}
          </Typography>
        </Stack>
      );
    }

    return (
      <Stack direction="column" spacing={1} alignItems="center">
        <Chip
          size="small"
          label={transaction.status}
          color={getStatusColor(transaction.status) as any}
          icon={getStatusIcon(transaction.status)}
          sx={{
            fontWeight: 'medium',
            '& .MuiChip-icon': {
              fontSize: '1rem !important'
            }
          }}
        />
        {transaction.requires_approval && transaction.status === 'PENDING' && (
          <Chip
            size="small"
            label="Needs Approval"
            color="warning"
            variant="outlined"
            sx={{ fontSize: '0.7rem' }}
          />
        )}
      </Stack>
    );
  };

  if (loading && transactions.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Cash Transactions...</Typography>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              💰 Cash Transactions
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Manage receipts, payments, and cash adjustments
            </Typography>
            {currencyInfo && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                📊 {currencyInfo.company_name} • Base Currency: {currencyInfo.functional_currency} ({currencyInfo.functional_currency_symbol})
              </Typography>
            )}
          </Box>
          <Stack direction="row" spacing={2}>
            <Button
              variant="contained"
              startIcon={<TrendingUpIcon />}
              onClick={() => navigate('/dashboard/cms/receipts/new')}
              color="success"
            >
              Cash Receipt
            </Button>
            <Button
              variant="contained"
              startIcon={<TrendingDownIcon />}
              onClick={() => navigate('/dashboard/cms/payments/new')}
              color="error"
            >
              Cash Payment
            </Button>
          </Stack>
        </Box>

        {/* Transaction Tabs */}
        <Card sx={{ mb: 3 }}>
          <Tabs
            value={selectedTab}
            onChange={(_, newValue) => setSelectedTab(newValue)}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="All Transactions" />
            <Tab label="Pending" />
            <Tab label="Awaiting Approval" />
          </Tabs>
        </Card>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={filterType}
                    label="Type"
                    onChange={(e) => setFilterType(e.target.value)}
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="RECEIPT">Receipt</MenuItem>
                    <MenuItem value="PAYMENT">Payment</MenuItem>
                    <MenuItem value="ADJUSTMENT">Adjustment</MenuItem>
                    <MenuItem value="TRANSFER">Transfer</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filterStatus}
                    label="Status"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="all">All Status</MenuItem>
                    <MenuItem value="DRAFT">Draft</MenuItem>
                    <MenuItem value="PENDING">Pending</MenuItem>
                    <MenuItem value="POSTED">Posted</MenuItem>
                    <MenuItem value="CANCELLED">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Account</InputLabel>
                  <Select
                    value={filterAccount}
                    label="Account"
                    onChange={(e) => setFilterAccount(e.target.value)}
                  >
                    <MenuItem value="all">All Accounts</MenuItem>
                    {accounts.map((account) => (
                      <MenuItem key={account.id} value={account.id.toString()}>
                        {account.account_name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <Stack direction="row" spacing={1}>
                  <DatePicker
                    label="From"
                    value={dateFrom}
                    onChange={setDateFrom}
                    slotProps={{ textField: { size: 'small' } }}
                  />
                  <DatePicker
                    label="To"
                    value={dateTo}
                    onChange={setDateTo}
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </Stack>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Transactions Table */}
        <Card>
          <CardContent>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <AlertTitle>Error</AlertTitle>
                {error}
              </Alert>
            )}

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Transaction</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Account</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar 
                            sx={{ 
                              width: 36, 
                              height: 36, 
                              mr: 2, 
                              bgcolor: `${getTransactionTypeColor(transaction.transaction_type)}.light` 
                            }}
                          >
                            {getTransactionTypeIcon(transaction.transaction_type)}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {transaction.transaction_number}
                            </Typography>
                            {transaction.reference_number && (
                              <Typography variant="caption" color="text.secondary">
                                Ref: {transaction.reference_number}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {dayjs(transaction.transaction_date).format('MMM DD, YYYY')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {dayjs(transaction.created_at).format('h:mm A')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          size="small"
                          label={transaction.transaction_type}
                          color={getTransactionTypeColor(transaction.transaction_type) as any}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {transaction.cash_account.account_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {transaction.cash_account.account_number}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography
                          variant="body1"
                          fontWeight="medium"
                          color={
                            transaction.transaction_type === 'RECEIPT' 
                              ? 'success.main' 
                              : transaction.transaction_type === 'PAYMENT'
                              ? 'error.main'
                              : 'text.primary'
                          }
                        >
                          {transaction.transaction_type === 'RECEIPT' ? '+' : 
                           transaction.transaction_type === 'PAYMENT' ? '-' : ''}
                          {formatCurrency(transaction.amount, transaction.currency)}
                        </Typography>
                        {transaction.currency !== (currencyInfo?.functional_currency || 'USD') && (
                          <Typography variant="caption" color="text.secondary">
                            @ {transaction.exchange_rate}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                          {transaction.description}
                        </Typography>
                        {(transaction.customer || transaction.vendor || transaction.employee) && (
                          <Typography variant="caption" color="text.secondary">
                            {transaction.customer || transaction.vendor || transaction.employee}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        {getStatusDisplay(transaction)}
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1} justifyContent="center">
                          <Tooltip title="View Details">
                            <IconButton size="small" onClick={() => handleView(transaction)}>
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          {transaction.status === 'DRAFT' && (
                            <Tooltip title="Edit Transaction">
                              <IconButton size="small" onClick={() => handleEdit(transaction)}>
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {/* Approve button for DRAFT transactions that require approval */}
                          {transaction.status === 'DRAFT' && transaction.requires_approval && (
                            <Tooltip title="Approve Transaction">
                              <IconButton 
                                size="small" 
                                onClick={() => handleApprove(transaction)}
                                color="success"
                              >
                                <CheckCircleIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {/* Post button for DRAFT transactions that don't require approval */}
                          {transaction.status === 'DRAFT' && !transaction.requires_approval && (
                            <Tooltip title="Post Transaction">
                              <IconButton 
                                size="small" 
                                onClick={() => handlePost(transaction)}
                                color="primary"
                              >
                                <PostAddIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {/* Post button for PENDING transactions that have been approved */}
                          {transaction.status === 'PENDING' && transaction.approved_by && (
                            <Tooltip title="Post Transaction">
                              <IconButton 
                                size="small" 
                                onClick={() => handlePost(transaction)}
                                color="primary"
                              >
                                <PostAddIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {/* Legacy approve button for PENDING with requires_approval */}
                          {transaction.requires_approval && transaction.status === 'PENDING' && (
                            <Tooltip title="Approve Transaction">
                              <IconButton 
                                size="small" 
                                onClick={() => handleApprove(transaction)}
                                color="success"
                              >
                                <CheckCircleIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {transaction.status === 'DRAFT' && (
                            <Tooltip title="Delete Transaction">
                              <IconButton 
                                size="small" 
                                onClick={() => handleDelete(transaction)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[10, 25, 50, 100]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
            />
          </CardContent>
        </Card>

        {/* Transaction Dialog */}
        <Dialog 
          open={openDialog} 
          onClose={() => setOpenDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" alignItems="center">
              <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                {formData.transaction_type === 'RECEIPT' ? <TrendingUpIcon /> : 
                 formData.transaction_type === 'PAYMENT' ? <TrendingDownIcon /> : <MoneyIcon />}
              </Avatar>
              {dialogMode === 'create' && `Create ${formData.transaction_type === 'RECEIPT' ? 'Cash Receipt' : 'Cash Payment'}`}
              {dialogMode === 'edit' && 'Edit Transaction'}
              {dialogMode === 'view' && 'Transaction Details'}
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {dialogMode === 'view' && selectedTransaction ? (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Transaction Information
                      </Typography>
                      <List dense>
                        <ListItem>
                          <ListItemText 
                            primary="Transaction Number" 
                            secondary={selectedTransaction.transaction_number} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Date" 
                            secondary={dayjs(selectedTransaction.transaction_date).format('MMMM DD, YYYY')} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Type" 
                            secondary={selectedTransaction.transaction_type} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Amount" 
                            secondary={formatCurrency(selectedTransaction.amount, selectedTransaction.currency)} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Status" 
                            secondary={selectedTransaction.status} 
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Additional Details
                      </Typography>
                      <List dense>
                        <ListItem>
                          <ListItemText 
                            primary="Account" 
                            secondary={selectedTransaction.cash_account.account_name} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Description" 
                            secondary={selectedTransaction.description} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Reference" 
                            secondary={selectedTransaction.reference_number || 'N/A'} 
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemText 
                            primary="Memo" 
                            secondary={selectedTransaction.memo || 'N/A'} 
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            ) : (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Transaction Date"
                    value={dayjs(formData.transaction_date)}
                    onChange={(newValue) => setFormData({...formData, transaction_date: newValue ? newValue.format('YYYY-MM-DD') : ''})}
                    slotProps={{ textField: { fullWidth: true, margin: 'normal', required: true } }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal" required>
                    <InputLabel>Transaction Type</InputLabel>
                    <Select
                      value={formData.transaction_type}
                      label="Transaction Type"
                      onChange={(e) => setFormData({...formData, transaction_type: e.target.value as any})}
                    >
                      <MenuItem value="RECEIPT">Cash Receipt</MenuItem>
                      <MenuItem value="PAYMENT">Cash Payment</MenuItem>
                      <MenuItem value="ADJUSTMENT">Adjustment</MenuItem>
                      <MenuItem value="TRANSFER">Transfer</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal" required>
                    <InputLabel>Cash Account</InputLabel>
                    <Select
                      value={formData.cash_account_id}
                      label="Cash Account"
                      onChange={(e) => setFormData({...formData, cash_account_id: e.target.value === '' ? '' : Number(e.target.value)})}
                    >
                      <MenuItem value="">
                        <em>Select an account</em>
                      </MenuItem>
                      {accounts.map((account) => (
                        <MenuItem key={account.id} value={account.id}>
                          {account.account_name} ({account.account_number})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Amount"
                    type="number"
                    value={formData.amount}
                    onChange={(e) => setFormData({...formData, amount: Number(e.target.value)})}
                    margin="normal"
                    required
                    InputProps={{
                      startAdornment: <InputAdornment position="start">$</InputAdornment>,
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    margin="normal"
                    required
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Reference Number"
                    value={formData.reference_number}
                    onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Bank Reference"
                    value={formData.bank_reference}
                    onChange={(e) => setFormData({...formData, bank_reference: e.target.value})}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Customer"
                    value={formData.customer}
                    onChange={(e) => setFormData({...formData, customer: e.target.value})}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Vendor"
                    value={formData.vendor}
                    onChange={(e) => setFormData({...formData, vendor: e.target.value})}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Employee"
                    value={formData.employee}
                    onChange={(e) => setFormData({...formData, employee: e.target.value})}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Memo"
                    multiline
                    rows={3}
                    value={formData.memo}
                    onChange={(e) => setFormData({...formData, memo: e.target.value})}
                    margin="normal"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.requires_approval}
                        onChange={(e) => setFormData({...formData, requires_approval: e.target.checked})}
                      />
                    }
                    label="Requires Approval"
                  />
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>
              {dialogMode === 'view' ? 'Close' : 'Cancel'}
            </Button>
            {dialogMode !== 'view' && (
              <Button variant="contained" onClick={handleSave}>
                {dialogMode === 'create' ? 'Create Transaction' : 'Update Transaction'}
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default CashTransactionsPage; 