import React, { useState, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Alert,
  LinearProgress,
  <PERSON>lapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  FormControlLabel,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Download as DownloadIcon,
  Info as InfoIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';

interface COAUploadResult {
  success: boolean;
  accounts_processed: number;
  errors: string[];
  warnings: string[];
  dry_run: boolean;
}

interface TemplateInfo {
  required_fields: string[];
  optional_fields: string[];
  account_types: Array<{
    code: string;
    name: string;
    type: string;
    normal_balance: string;
  }>;
  detail_types: Array<{
    code: string;
    name: string;
    account_type_code: string;
    description: string;
  }>;
  format_rules: Record<string, string>;
}

interface COAUploadFormProps {
  onUploadSuccess?: () => void;
}

const COAUploadForm: React.FC<COAUploadFormProps> = ({ onUploadSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [result, setResult] = useState<COAUploadResult | null>(null);
  const [dryRun, setDryRun] = useState(true);
  const [showTemplate, setShowTemplate] = useState(false);
  const [templateInfo, setTemplateInfo] = useState<TemplateInfo | null>(null);
  const [loadingTemplate, setLoadingTemplate] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        alert('Please select a CSV file');
        return;
      }
      if (selectedFile.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }
      setFile(selectedFile);
      setResult(null);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    setUploading(true);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('dry_run', dryRun.toString());

      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/gl/coa/upload/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${token}`,
        },
        body: formData,
      });

      const data = await response.json();
      setResult(data);

      if (data.success && !data.dry_run) {
        // Clear the file after successful upload
        setFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        // Call the callback to refresh the accounts list
        if (onUploadSuccess) {
          onUploadSuccess();
        }
      }

    } catch (error) {
      console.error('Upload error:', error);
      setResult({
        success: false,
        accounts_processed: 0,
        errors: [`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
        dry_run: dryRun,
      });
    } finally {
      setUploading(false);
    }
  };

  const loadTemplateInfo = async () => {
    setLoadingTemplate(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/gl/coa/template/', {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTemplateInfo(data);
        setShowTemplate(true);
      } else {
        alert('Failed to load template information');
      }
    } catch (error) {
      console.error('Template load error:', error);
      alert('Failed to load template information');
    } finally {
      setLoadingTemplate(false);
    }
  };

  const downloadTemplate = () => {
    // Create and download the template CSV
    const csvContent = `account_number,account_name,account_type_code,detail_type_code,parent_account_number,description,opening_balance,opening_balance_date,currency,is_active,is_header_account
1000,Cash in Hand,ASSET,CASH_BANK,,Petty cash and cash on hand,5000.00,2024-01-01,INR,TRUE,FALSE
1010,Business Checking Account,ASSET,CASH_BANK,,Primary business bank account,25000.00,2024-01-01,INR,TRUE,FALSE
2000,Accounts Payable,LIABILITY,AP_TRADE,,Money owed to suppliers,4200.00,2024-01-01,INR,TRUE,FALSE
3000,Owner Capital,EQUITY,CAPITAL,,Owner investment in business,100000.00,2024-01-01,INR,TRUE,FALSE
4000,Sales Revenue,REVENUE,SALES,,Revenue from product sales,0.00,2024-01-01,INR,TRUE,FALSE
5000,Cost of Goods Sold,EXPENSE,COGS,,Direct cost of products sold,0.00,2024-01-01,INR,TRUE,FALSE`;

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'COA_Template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6" component="h2">
              Upload Chart of Accounts
            </Typography>
            <Box>
              <Button
                startIcon={<DescriptionIcon />}
                onClick={loadTemplateInfo}
                disabled={loadingTemplate}
                sx={{ mr: 1 }}
              >
                View Format
              </Button>
              <Button
                startIcon={<DownloadIcon />}
                onClick={downloadTemplate}
                variant="outlined"
              >
                Download Template
              </Button>
            </Box>
          </Box>

          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Upload a CSV file with your Chart of Accounts. Use "Validate Only" first to check for errors before importing.
            </Typography>
          </Alert>

          {/* File Selection */}
          <Box sx={{ mb: 2 }}>
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              id="coa-file-input"
            />
            <label htmlFor="coa-file-input">
              <Button
                variant="outlined"
                component="span"
                startIcon={<CloudUploadIcon />}
                fullWidth
                sx={{ mb: 1, height: '56px' }}
              >
                {file ? file.name : 'Select CSV File'}
              </Button>
            </label>
            {file && (
              <Typography variant="caption" color="text.secondary">
                File size: {(file.size / 1024).toFixed(1)} KB
              </Typography>
            )}
          </Box>

          {/* Options */}
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={dryRun}
                  onChange={(e) => setDryRun(e.target.checked)}
                  color="primary"
                />
              }
              label="Validate Only (Don't Import)"
            />
            <Typography variant="caption" display="block" color="text.secondary">
              When enabled, the file will be validated without importing any data
            </Typography>
          </Box>

          {/* Upload Button */}
          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!file || uploading}
            fullWidth
            size="large"
            sx={{ mb: 2 }}
          >
            {uploading ? 'Processing...' : dryRun ? 'Validate File' : 'Upload & Import'}
          </Button>

          {/* Progress */}
          {uploading && <LinearProgress sx={{ mb: 2 }} />}

          {/* Results */}
          {result && (
            <Box>
              <Alert 
                severity={result.success ? 'success' : 'error'} 
                sx={{ mb: 2 }}
              >
                <Typography variant="h6">
                  {result.dry_run ? 'Validation' : 'Import'} {result.success ? 'Successful' : 'Failed'}
                </Typography>
                <Typography>
                  {result.accounts_processed} accounts {result.dry_run ? 'validated' : 'processed'}
                </Typography>
              </Alert>

              {/* Errors */}
              {result.errors.length > 0 && (
                <Collapse in={true}>
                  <Alert severity="error" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Errors ({result.errors.length}):
                    </Typography>
                    <List dense>
                      {result.errors.map((error, index) => (
                        <ListItem key={index} sx={{ py: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <ErrorIcon color="error" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={error}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Alert>
                </Collapse>
              )}

              {/* Warnings */}
              {result.warnings.length > 0 && (
                <Collapse in={true}>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>
                      Warnings ({result.warnings.length}):
                    </Typography>
                    <List dense>
                      {result.warnings.map((warning, index) => (
                        <ListItem key={index} sx={{ py: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <WarningIcon color="warning" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText 
                            primary={warning}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Alert>
                </Collapse>
              )}

              {/* Success message for validation */}
              {result.success && result.dry_run && (
                <Alert severity="info">
                  <Typography>
                    Validation successful! You can now uncheck "Validate Only" and upload to import the accounts.
                  </Typography>
                </Alert>
              )}
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Template Information Dialog */}
      <Dialog 
        open={showTemplate} 
        onClose={() => setShowTemplate(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <InfoIcon color="primary" />
            Chart of Accounts Upload Format
          </Box>
        </DialogTitle>
        <DialogContent>
          {templateInfo && (
            <Box>
              {/* Required Fields */}
              <Typography variant="h6" gutterBottom>
                Required Fields
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                {templateInfo.required_fields.map((field) => (
                  <Chip key={field} label={field} color="error" variant="outlined" />
                ))}
              </Box>

              {/* Optional Fields */}
              <Typography variant="h6" gutterBottom>
                Optional Fields
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={1} mb={3}>
                {templateInfo.optional_fields.map((field) => (
                  <Chip key={field} label={field} color="default" variant="outlined" />
                ))}
              </Box>

              {/* Account Types */}
              <Typography variant="h6" gutterBottom>
                Available Account Types
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Normal Balance</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {templateInfo.account_types.map((type) => (
                      <TableRow key={type.code}>
                        <TableCell>
                          <Chip label={type.code} size="small" />
                        </TableCell>
                        <TableCell>{type.name}</TableCell>
                        <TableCell>{type.type}</TableCell>
                        <TableCell>{type.normal_balance}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Detail Types */}
              <Typography variant="h6" gutterBottom>
                Available Detail Types
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 3, maxHeight: 400 }}>
                <Table size="small" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Code</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Account Type</TableCell>
                      <TableCell>Description</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {templateInfo.detail_types.map((type) => (
                      <TableRow key={`${type.account_type_code}_${type.code}`}>
                        <TableCell>
                          <Chip label={type.code} size="small" />
                        </TableCell>
                        <TableCell>{type.name}</TableCell>
                        <TableCell>
                          <Chip label={type.account_type_code} size="small" color="primary" />
                        </TableCell>
                        <TableCell>{type.description}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Format Rules */}
              <Typography variant="h6" gutterBottom>
                Format Rules
              </Typography>
              <List>
                {Object.entries(templateInfo.format_rules).map(([field, rule]) => (
                  <ListItem key={field}>
                    <ListItemText
                      primary={field}
                      secondary={rule}
                      primaryTypographyProps={{ fontWeight: 'bold' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={downloadTemplate} startIcon={<DownloadIcon />}>
            Download Template
          </Button>
          <Button onClick={() => setShowTemplate(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default COAUploadForm; 