import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Alert,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemSecondaryAction,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Receipt as ReceiptIcon,
  Undo as UndoIcon,
  Warehouse as WarehouseIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  LocalShipping as ShippingIcon,
  Assessment as ReportIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Timeline as TimelineIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import StatCard from '../../../shared/components/StatCard';
import { inventoryService, InventoryStats } from '../services/inventory.service';
import { grnService, GRNStats } from '../services/grn.service';
import { grnReturnService, GRNReturnStats } from '../services/grn-return.service';

const InventoryDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inventoryStats, setInventoryStats] = useState<InventoryStats | null>(null);
  const [grnStats, setGrnStats] = useState<GRNStats | null>(null);
  const [grnReturnStats, setGrnReturnStats] = useState<GRNReturnStats | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [inventoryData, grnData, grnReturnData] = await Promise.all([
        inventoryService.getInventoryStats(),
        grnService.getGRNStats(),
        grnReturnService.getGRNReturnStats(),
      ]);
      
      setInventoryStats(inventoryData);
      setGrnStats(grnData);
      setGrnReturnStats(grnReturnData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const quickActions = [
    {
      title: 'Create GRN',
      description: 'Record goods receipt',
      icon: <ReceiptIcon />,
      color: 'primary',
      onClick: () => navigate('/dashboard/inventory/grn/new'),
    },
    {
      title: 'Create Return',
      description: 'Return goods to vendor',
      icon: <UndoIcon />,
      color: 'error',
      onClick: () => navigate('/dashboard/inventory/grn-returns/new'),
    },
    {
      title: 'Transfer Stock',
      description: 'Transfer between warehouses',
      icon: <ShippingIcon />,
      color: 'info',
      onClick: () => navigate('/dashboard/inventory/transfers'),
    },
    {
      title: 'View Inventory',
      description: 'Check stock levels',
      icon: <InventoryIcon />,
      color: 'secondary',
      onClick: () => navigate('/dashboard/inventory/stock'),
    },
    {
      title: 'Stock Transactions',
      description: 'View transaction history',
      icon: <TimelineIcon />,
      color: 'info',
      onClick: () => navigate('/dashboard/inventory/transactions'),
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Inventory Management
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage goods receipt, stock levels, and warehouse operations
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadDashboardData}
            disabled={loading}
          >
            Refresh
          </Button>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => navigate('/dashboard/inventory/reports')}>
              <ListItemIcon>
                <ReportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Reports</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => navigate('/dashboard/inventory/settings')}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Settings</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem>
              <ListItemIcon>
                <DownloadIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export Data</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Inventory Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Items"
            value={inventoryStats?.total_items || 0}
            icon={<InventoryIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Low Stock Items"
            value={inventoryStats?.low_stock_items || 0}
            icon={<WarningIcon />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Out of Stock"
            value={inventoryStats?.out_of_stock_items || 0}
            icon={<WarningIcon />}
            color="error"
          />
        </Grid>

        {/* GRN Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total GRNs"
            value={grnStats?.total_grns || 0}
            icon={<ReceiptIcon />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Draft GRNs"
            value={grnStats?.draft_grns || 0}
            icon={<ReceiptIcon />}
            color="secondary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Recent GRNs"
            value={grnStats?.recent_grns || 0}
            icon={<ShippingIcon />}
            color="primary"
          />
        </Grid>

        {/* GRN Return Stats */}
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Returns"
            value={grnReturnStats?.total_returns || 0}
            icon={<UndoIcon />}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Draft Returns"
            value={grnReturnStats?.draft_returns || 0}
            icon={<UndoIcon />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Pending Approval"
            value={grnReturnStats?.draft_returns || 0}
            icon={<UndoIcon />}
            color="info"
          />
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
        </Grid>
        {quickActions.map((action, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 4,
                }
              }}
              onClick={action.onClick}
            >
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Box 
                    sx={{ 
                      p: 1, 
                      borderRadius: 1, 
                      bgcolor: `${action.color}.light`,
                      color: `${action.color}.contrastText`,
                      mr: 2 
                    }}
                  >
                    {action.icon}
                  </Box>
                  <Typography variant="h6" fontWeight="bold">
                    {action.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {action.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent Activity & Alerts */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Stock Alerts
              </Typography>
              {inventoryStats && (inventoryStats.low_stock_items > 0 || inventoryStats.out_of_stock_items > 0) ? (
                <List>
                  {inventoryStats.low_stock_items > 0 && (
                    <ListItem>
                      <ListItemText
                        primary="Low Stock Alert"
                        secondary={`${inventoryStats.low_stock_items} items below reorder point`}
                      />
                      <ListItemSecondaryAction>
                        <Chip label="Action Required" color="warning" size="small" />
                      </ListItemSecondaryAction>
                    </ListItem>
                  )}
                  {inventoryStats.out_of_stock_items > 0 && (
                    <ListItem>
                      <ListItemText
                        primary="Out of Stock Alert"
                        secondary={`${inventoryStats.out_of_stock_items} items completely out of stock`}
                      />
                      <ListItemSecondaryAction>
                        <Chip label="Critical" color="error" size="small" />
                      </ListItemSecondaryAction>
                    </ListItem>
                  )}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No stock alerts at this time
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                GRN Status
              </Typography>
              {grnStats ? (
                <List>
                  <ListItem>
                    <ListItemText
                      primary="Draft GRNs"
                      secondary="Pending completion"
                    />
                    <ListItemSecondaryAction>
                      <Chip label={grnStats.draft_grns} color="secondary" size="small" />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Received GRNs"
                      secondary="Ready to post to inventory"
                    />
                    <ListItemSecondaryAction>
                      <Chip label={grnStats.received_grns} color="primary" size="small" />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Posted GRNs"
                      secondary="Successfully posted to inventory"
                    />
                    <ListItemSecondaryAction>
                      <Chip label={grnStats.posted_grns} color="success" size="small" />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Loading GRN status...
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Return Status
              </Typography>
              {grnReturnStats ? (
                <List>
                  <ListItem>
                    <ListItemText
                      primary="Draft Returns"
                      secondary="Pending approval"
                    />
                    <ListItemSecondaryAction>
                      <Chip label={grnReturnStats.draft_returns} color="warning" size="small" />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Approved Returns"
                      secondary="Ready for shipment"
                    />
                    <ListItemSecondaryAction>
                      <Chip label={grnReturnStats.approved_returns} color="info" size="small" />
                    </ListItemSecondaryAction>
                  </ListItem>
                  <ListItem>
                    <ListItemText
                      primary="Posted Returns"
                      secondary="Inventory adjusted"
                    />
                    <ListItemSecondaryAction>
                      <Chip label={grnReturnStats.posted_returns} color="success" size="small" />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Loading return status...
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Links
              </Typography>
              <List>
                <ListItem>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ReceiptIcon />}
                    onClick={() => navigate('/dashboard/inventory/grn')}
                  >
                    View All GRNs
                  </Button>
                </ListItem>
                <ListItem>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<UndoIcon />}
                    onClick={() => navigate('/dashboard/inventory/grn-returns')}
                  >
                    View All Returns
                  </Button>
                </ListItem>
                <ListItem>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<InventoryIcon />}
                    onClick={() => navigate('/dashboard/inventory/stock')}
                  >
                    Stock Levels
                  </Button>
                </ListItem>
                <ListItem>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<ShippingIcon />}
                    onClick={() => navigate('/dashboard/inventory/transfers')}
                  >
                    Inventory Transfers
                  </Button>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default InventoryDashboard; 