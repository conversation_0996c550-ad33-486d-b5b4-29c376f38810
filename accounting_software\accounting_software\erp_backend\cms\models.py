"""
Cash Management System Models

This module defines all the data models for comprehensive cash management:
- Cash Accounts (Bank accounts, petty cash, investment accounts)
- Cash Transactions (Receipts, payments, transfers, adjustments)
- Bank Reconciliation (Statement imports, matching, reconciliation)
- Cash Flow Forecasting (Budgets, forecasts, scenarios)
- Treasury Management (Investment tracking, currency management)

Following accounting best practices and supporting multi-currency operations.
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid
from gl.models import Account, JournalEntry, JournalEntryLine


class CashAccountType(models.Model):
    """
    Cash Account Types for categorizing different cash management accounts
    
    Examples:
    - BANK_CHECKING: Regular checking accounts
    - BANK_SAVINGS: Savings accounts
    - PETTY_CASH: Small cash funds for office expenses
    - INVESTMENT: Short-term investment accounts
    - ESCROW: Client funds held in trust
    """
    
    # Account type codes
    BANK_CHECKING = 'BANK_CHECKING'
    BANK_SAVINGS = 'BANK_SAVINGS'
    BANK_CREDIT_LINE = 'BANK_CREDIT_LINE'
    PETTY_CASH = 'PETTY_CASH'
    CASH_ON_HAND = 'CASH_ON_HAND'
    INVESTMENT = 'INVESTMENT'
    ESCROW = 'ESCROW'
    FOREIGN_CURRENCY = 'FOREIGN_CURRENCY'
    
    TYPE_CHOICES = [
        (BANK_CHECKING, 'Bank Checking Account'),
        (BANK_SAVINGS, 'Bank Savings Account'),
        (BANK_CREDIT_LINE, 'Bank Credit Line'),
        (PETTY_CASH, 'Petty Cash Fund'),
        (CASH_ON_HAND, 'Cash on Hand'),
        (INVESTMENT, 'Investment Account'),
        (ESCROW, 'Escrow/Trust Account'),
        (FOREIGN_CURRENCY, 'Foreign Currency Account'),
    ]
    
    code = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        unique=True,
        help_text="Unique code for the cash account type"
    )
    name = models.CharField(
        max_length=100,
        help_text="Display name for the cash account type"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the account type"
    )
    
    # Account behavior settings
    requires_bank_details = models.BooleanField(
        default=True,
        help_text="Whether this account type requires banking information"
    )
    allows_overdraft = models.BooleanField(
        default=False,
        help_text="Whether negative balances are allowed"
    )
    requires_reconciliation = models.BooleanField(
        default=True,
        help_text="Whether this account needs bank reconciliation"
    )
    is_investment_account = models.BooleanField(
        default=False,
        help_text="Whether this is an investment/trading account"
    )
    
    # Display and sorting
    sort_order = models.PositiveIntegerField(
        default=1,
        help_text="Order for displaying account types"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this account type is available for use"
    )
    
    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['sort_order', 'name']
        verbose_name = "Cash Account Type"
        verbose_name_plural = "Cash Account Types"
    
    def __str__(self):
        return self.name


class CashAccount(models.Model):
    """
    Cash Accounts for managing all types of cash and bank accounts
    
    This model represents individual cash management accounts including:
    - Bank checking and savings accounts
    - Petty cash funds
    - Investment accounts
    - Foreign currency accounts
    - Credit lines and escrow accounts
    """
    
    # Basic account information
    account_name = models.CharField(
        max_length=200,
        help_text="Descriptive name for the cash account"
    )
    account_number = models.CharField(
        max_length=50,
        unique=True,
        help_text="Unique identifier for the account (can be bank account number or internal code)"
    )
    account_type = models.ForeignKey(
        CashAccountType,
        on_delete=models.PROTECT,
        related_name='cash_accounts',
        help_text="Type of cash account (checking, savings, petty cash, etc.)"
    )
    
    # Banking information
    bank_name = models.CharField(
        max_length=200,
        blank=True,
        help_text="Name of the financial institution"
    )
    bank_branch = models.CharField(
        max_length=200,
        blank=True,
        help_text="Bank branch or location"
    )
    bank_routing_number = models.CharField(
        max_length=20,
        blank=True,
        help_text="Bank routing or sort code"
    )
    bank_swift_code = models.CharField(
        max_length=20,
        blank=True,
        help_text="SWIFT/BIC code for international transfers"
    )
    bank_iban = models.CharField(
        max_length=34,
        blank=True,
        help_text="International Bank Account Number"
    )
    
    # Account settings
    currency = models.CharField(
        max_length=3,
        default='USD',
        help_text="Primary currency for this account (ISO 4217 code)"
    )
    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Opening balance when account was created"
    )
    opening_balance_date = models.DateField(
        default=timezone.now,
        help_text="Date of the opening balance"
    )
    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Current calculated balance (updated automatically)"
    )
    
    # Credit line information (for credit accounts)
    credit_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Credit limit for credit line accounts"
    )
    interest_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Interest rate for savings or credit accounts (annual %)"
    )
    
    # Account management
    account_manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_cash_accounts',
        help_text="User responsible for managing this account"
    )
    description = models.TextField(
        blank=True,
        help_text="Additional notes about the account"
    )
    
    # Status and controls
    is_active = models.BooleanField(
        default=True,
        help_text="Whether the account is currently active"
    )
    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is the default cash account for transactions"
    )
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether transactions require approval"
    )
    
    # Integration with GL
    gl_account = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='cash_accounts',
        help_text="Linked General Ledger account for automatic posting"
    )
    
    # Reconciliation settings
    last_reconciled_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date of last successful reconciliation"
    )
    last_reconciled_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Balance as of last reconciliation"
    )
    
    # Audit trail
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_cash_accounts',
        help_text="User who created this account"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['account_name']
        verbose_name = "Cash Account"
        verbose_name_plural = "Cash Accounts"
        
    def __str__(self):
        return f"{self.account_name} ({self.account_number})"
    
    def get_available_balance(self):
        """Calculate available balance considering credit limits."""
        if self.credit_limit:
            return self.current_balance + self.credit_limit
        return self.current_balance
    
    def get_current_balance(self):
        """Calculate current balance from all transactions."""
        from django.db.models import Sum
        
        receipts = self.cash_transactions.filter(
            transaction_type='RECEIPT',
            status='POSTED'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        payments = self.cash_transactions.filter(
            transaction_type='PAYMENT',
            status='POSTED'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        transfers_in = self.cash_transfers_to.filter(
            status='POSTED'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        transfers_out = self.cash_transfers_from.filter(
            status='POSTED'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        calculated_balance = (
            self.opening_balance + 
            receipts - 
            payments + 
            transfers_in - 
            transfers_out
        )
        
        return calculated_balance
    
    def update_current_balance(self):
        """Update the current balance field with calculated value."""
        self.current_balance = self.get_current_balance()
        self.save(update_fields=['current_balance'])


class CashTransaction(models.Model):
    """
    Cash Transactions for all cash receipts and payments
    
    This model handles all types of cash movements:
    - Customer payments received
    - Vendor payments made
    - Employee expense reimbursements
    - Bank fees and charges
    - Interest received/paid
    - Currency exchanges
    """
    
    # Transaction types
    RECEIPT = 'RECEIPT'
    PAYMENT = 'PAYMENT'
    ADJUSTMENT = 'ADJUSTMENT'
    FEE = 'FEE'
    INTEREST = 'INTEREST'
    EXCHANGE = 'EXCHANGE'
    
    TRANSACTION_TYPE_CHOICES = [
        (RECEIPT, 'Cash Receipt'),
        (PAYMENT, 'Cash Payment'),
        (ADJUSTMENT, 'Balance Adjustment'),
        (FEE, 'Bank Fee/Charge'),
        (INTEREST, 'Interest Received/Paid'),
        (EXCHANGE, 'Currency Exchange'),
    ]
    
    # Transaction status
    DRAFT = 'DRAFT'
    PENDING = 'PENDING'
    POSTED = 'POSTED'
    CANCELLED = 'CANCELLED'
    
    STATUS_CHOICES = [
        (DRAFT, 'Draft'),
        (PENDING, 'Pending Approval'),
        (POSTED, 'Posted'),
        (CANCELLED, 'Cancelled'),
    ]
    
    # Basic transaction information
    transaction_number = models.CharField(
        max_length=50,
        unique=True,
        help_text="Unique transaction number for tracking"
    )
    transaction_date = models.DateField(
        help_text="Date when the transaction occurred"
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPE_CHOICES,
        help_text="Type of cash transaction"
    )
    
    # Account and amount
    cash_account = models.ForeignKey(
        CashAccount,
        on_delete=models.PROTECT,
        related_name='cash_transactions',
        help_text="Cash account for this transaction"
    )
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Transaction amount (always positive)"
    )
    currency = models.CharField(
        max_length=3,
        help_text="Transaction currency (ISO 4217 code)"
    )
    exchange_rate = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        default=Decimal('1.000000'),
        help_text="Exchange rate to account currency"
    )
    
    # Transaction details
    description = models.CharField(
        max_length=500,
        help_text="Description of the transaction"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="External reference (check number, wire reference, etc.)"
    )
    memo = models.TextField(
        blank=True,
        help_text="Additional notes about the transaction"
    )
    
    # Related entities
    customer = models.CharField(
        max_length=200,
        blank=True,
        help_text="Customer name for receipts"
    )
    vendor = models.CharField(
        max_length=200,
        blank=True,
        help_text="Vendor name for payments"
    )
    employee = models.CharField(
        max_length=200,
        blank=True,
        help_text="Employee name for reimbursements"
    )
    
    # GL Integration
    gl_journal_entry = models.ForeignKey(
        'gl.JournalEntry',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cash_transactions',
        help_text="Related journal entry in General Ledger"
    )
    
    # Banking information
    bank_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Bank transaction reference or confirmation number"
    )
    cleared_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date when transaction cleared the bank"
    )
    
    # Transaction status and workflow
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=DRAFT,
        help_text="Current status of the transaction"
    )
    
    # Approval workflow
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether this transaction needs approval"
    )
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_cash_transactions',
        help_text="User who approved this transaction"
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when transaction was approved"
    )
    
    # Audit trail
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_cash_transactions',
        help_text="User who created this transaction"
    )
    posted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_cash_transactions',
        help_text="User who posted this transaction"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when transaction was posted"
    )
    
    class Meta:
        ordering = ['-transaction_date', '-transaction_number']
        verbose_name = "Cash Transaction"
        verbose_name_plural = "Cash Transactions"
        indexes = [
            models.Index(fields=['cash_account', 'transaction_date']),
            models.Index(fields=['status', 'transaction_date']),
            models.Index(fields=['customer']),
            models.Index(fields=['vendor']),
        ]
    
    def __str__(self):
        return f"{self.transaction_number} - {self.description} ({self.amount})"
    
    def can_be_posted(self):
        """Check if transaction can be posted."""
        return (
            self.status == self.DRAFT and
            self.amount > 0 and
            (not self.requires_approval or self.approved_by is not None)
        )
    
    def post_transaction(self, user):
        """Post the transaction and update account balance."""
        from django.utils import timezone
        
        if not self.can_be_posted():
            raise ValueError("Transaction cannot be posted")
        
        self.status = self.POSTED
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save()
        
        # Update cash account balance
        self.cash_account.update_current_balance()


class CashTransfer(models.Model):
    """
    Cash Transfers between cash accounts
    
    Handles transfers of funds between different cash accounts:
    - Bank to bank transfers
    - Cash deposits to bank
    - Cash withdrawals from bank
    - Investment fund movements
    """
    
    # Transfer status
    DRAFT = 'DRAFT'
    PENDING = 'PENDING'
    POSTED = 'POSTED'
    CANCELLED = 'CANCELLED'
    
    STATUS_CHOICES = [
        (DRAFT, 'Draft'),
        (PENDING, 'Pending'),
        (POSTED, 'Posted'),
        (CANCELLED, 'Cancelled'),
    ]
    
    # Basic transfer information
    transfer_number = models.CharField(
        max_length=50,
        unique=True,
        help_text="Unique transfer number for tracking"
    )
    transfer_date = models.DateField(
        help_text="Date when the transfer occurred"
    )
    
    # Source and destination accounts
    from_account = models.ForeignKey(
        CashAccount,
        on_delete=models.PROTECT,
        related_name='cash_transfers_from',
        help_text="Source cash account"
    )
    to_account = models.ForeignKey(
        CashAccount,
        on_delete=models.PROTECT,
        related_name='cash_transfers_to',
        help_text="Destination cash account"
    )
    
    # Transfer amount and currency
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Transfer amount in source account currency"
    )
    exchange_rate = models.DecimalField(
        max_digits=10,
        decimal_places=6,
        default=Decimal('1.000000'),
        help_text="Exchange rate if currencies differ"
    )
    destination_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Amount in destination currency (calculated)"
    )
    
    # Transfer details
    description = models.CharField(
        max_length=500,
        help_text="Description of the transfer"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Transfer reference number"
    )
    memo = models.TextField(
        blank=True,
        help_text="Additional notes about the transfer"
    )
    
    # Transfer fees
    transfer_fee = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Fee charged for the transfer"
    )
    fee_account = models.ForeignKey(
        CashAccount,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='transfer_fees',
        help_text="Account to debit transfer fees"
    )
    
    # GL Integration
    gl_journal_entry = models.ForeignKey(
        'gl.JournalEntry',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cash_transfers',
        help_text="Related journal entry in General Ledger"
    )
    
    # Status and workflow
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=DRAFT,
        help_text="Current status of the transfer"
    )
    
    # Audit trail
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_cash_transfers',
        help_text="User who created this transfer"
    )
    posted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_cash_transfers',
        help_text="User who posted this transfer"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    posted_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when transfer was posted"
    )
    
    class Meta:
        ordering = ['-transfer_date', '-transfer_number']
        verbose_name = "Cash Transfer"
        verbose_name_plural = "Cash Transfers"
    
    def __str__(self):
        return f"{self.transfer_number} - {self.from_account} to {self.to_account} ({self.amount})"
    
    def clean(self):
        """Validate transfer data."""
        from django.core.exceptions import ValidationError
        
        if self.from_account == self.to_account:
            raise ValidationError("Source and destination accounts must be different")
        
        # Calculate destination amount
        if self.exchange_rate and self.exchange_rate != 1:
            self.destination_amount = self.amount * self.exchange_rate
    
    def can_be_posted(self):
        """Check if transfer can be posted."""
        return (
            self.status == self.DRAFT and
            self.amount > 0 and
            self.from_account != self.to_account
        )
    
    def post_transfer(self, user):
        """Post the transfer and update account balances."""
        if not self.can_be_posted():
            raise ValueError("Transfer cannot be posted")
        
        self.status = self.POSTED
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save()
        
        # Update both account balances
        self.from_account.update_current_balance()
        self.to_account.update_current_balance()


class BankStatement(models.Model):
    """
    Bank Statements for reconciliation
    
    Imported bank statements for matching against cash transactions
    and performing bank reconciliation.
    """
    
    # Statement identification
    statement_number = models.CharField(
        max_length=50,
        help_text="Bank statement number or identifier"
    )
    cash_account = models.ForeignKey(
        CashAccount,
        on_delete=models.CASCADE,
        related_name='bank_statements',
        help_text="Cash account this statement belongs to"
    )
    
    # Statement period
    statement_date = models.DateField(
        help_text="Statement ending date"
    )
    period_from = models.DateField(
        help_text="Statement period start date"
    )
    period_to = models.DateField(
        help_text="Statement period end date"
    )
    
    # Statement balances
    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Opening balance on statement"
    )
    closing_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Closing balance on statement"
    )
    
    # Import information
    import_file = models.CharField(
        max_length=500,
        blank=True,
        help_text="Original file name if imported"
    )
    import_format = models.CharField(
        max_length=20,
        blank=True,
        help_text="Format of imported file (CSV, QIF, OFX, etc.)"
    )
    
    # Reconciliation status
    is_reconciled = models.BooleanField(
        default=False,
        help_text="Whether this statement has been reconciled"
    )
    reconciled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reconciled_statements',
        help_text="User who performed the reconciliation"
    )
    reconciled_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when reconciliation was completed"
    )
    
    # Audit trail
    imported_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='imported_statements',
        help_text="User who imported this statement"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-statement_date']
        unique_together = ['cash_account', 'statement_number']
        verbose_name = "Bank Statement"
        verbose_name_plural = "Bank Statements"
    
    def __str__(self):
        return f"{self.cash_account.account_name} - {self.statement_date} ({self.statement_number})"


class BankStatementEntry(models.Model):
    """
    Individual entries from bank statements
    
    Each line item from imported bank statements for reconciliation
    against cash transactions.
    """
    
    # Entry types
    DEBIT = 'DEBIT'
    CREDIT = 'CREDIT'
    
    ENTRY_TYPE_CHOICES = [
        (DEBIT, 'Debit (Money Out)'),
        (CREDIT, 'Credit (Money In)'),
    ]
    
    # Basic entry information
    bank_statement = models.ForeignKey(
        BankStatement,
        on_delete=models.CASCADE,
        related_name='entries',
        help_text="Bank statement this entry belongs to"
    )
    entry_date = models.DateField(
        help_text="Date of the bank transaction"
    )
    entry_type = models.CharField(
        max_length=10,
        choices=ENTRY_TYPE_CHOICES,
        help_text="Type of bank entry (debit or credit)"
    )
    
    # Transaction details
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        help_text="Transaction amount (always positive)"
    )
    description = models.CharField(
        max_length=500,
        help_text="Bank transaction description"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Bank reference or check number"
    )
    bank_code = models.CharField(
        max_length=20,
        blank=True,
        help_text="Bank transaction code"
    )
    
    # Reconciliation
    is_matched = models.BooleanField(
        default=False,
        help_text="Whether this entry has been matched to a cash transaction"
    )
    matched_transaction = models.ForeignKey(
        CashTransaction,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='bank_entries',
        help_text="Matched cash transaction"
    )
    matched_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='matched_bank_entries',
        help_text="User who performed the matching"
    )
    matched_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Timestamp when matching was performed"
    )
    
    # Running balance
    running_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Running balance after this transaction"
    )
    
    class Meta:
        ordering = ['bank_statement', 'entry_date', 'id']
        verbose_name = "Bank Statement Entry"
        verbose_name_plural = "Bank Statement Entries"
    
    def __str__(self):
        return f"{self.entry_date} - {self.description} ({self.amount})"


class CashFlowForecast(models.Model):
    """
    Cash Flow Forecasting for treasury management
    
    Projected cash flows for planning and analysis.
    """
    
    # Forecast types
    BUDGET = 'BUDGET'
    FORECAST = 'FORECAST'
    SCENARIO = 'SCENARIO'
    
    FORECAST_TYPE_CHOICES = [
        (BUDGET, 'Annual Budget'),
        (FORECAST, 'Rolling Forecast'),
        (SCENARIO, 'Scenario Planning'),
    ]
    
    # Basic forecast information
    forecast_name = models.CharField(
        max_length=200,
        help_text="Name or description of the forecast"
    )
    forecast_type = models.CharField(
        max_length=20,
        choices=FORECAST_TYPE_CHOICES,
        help_text="Type of cash flow forecast"
    )
    
    # Forecast period
    period_from = models.DateField(
        help_text="Forecast period start date"
    )
    period_to = models.DateField(
        help_text="Forecast period end date"
    )
    
    # Forecast data
    cash_account = models.ForeignKey(
        CashAccount,
        on_delete=models.CASCADE,
        related_name='cash_forecasts',
        help_text="Cash account this forecast applies to"
    )
    projected_receipts = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Projected cash receipts for the period"
    )
    projected_payments = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Projected cash payments for the period"
    )
    net_cash_flow = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Net projected cash flow (receipts - payments)"
    )
    
    # Confidence and assumptions
    confidence_level = models.DecimalField(
        max_digits=3,
        decimal_places=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        default=80,
        help_text="Confidence level in percentage (0-100)"
    )
    assumptions = models.TextField(
        blank=True,
        help_text="Key assumptions underlying this forecast"
    )
    
    # Status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this forecast is currently active"
    )
    
    # Audit trail
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_forecasts',
        help_text="User who created this forecast"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-period_from']
        verbose_name = "Cash Flow Forecast"
        verbose_name_plural = "Cash Flow Forecasts"
    
    def __str__(self):
        return f"{self.forecast_name} ({self.period_from} to {self.period_to})"
    
    def save(self, *args, **kwargs):
        """Calculate net cash flow before saving."""
        self.net_cash_flow = self.projected_receipts - self.projected_payments
        super().save(*args, **kwargs)


class CashAccountBalance(models.Model):
    """
    Cash Account Balance tracking for CMS
    Synchronized with GL Journal Entries
    """
    cash_account = models.OneToOneField(
        'CashAccount', 
        on_delete=models.CASCADE,
        related_name='balance_record',
        null=True,
        blank=True,
        help_text="Optional CMS cash account (legacy)"
    )
    gl_account = models.ForeignKey(
        Account,
        on_delete=models.CASCADE,
        help_text="Linked GL Asset account"
    )
    current_balance = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00,
        help_text="Current balance calculated from journal entries"
    )
    last_updated = models.DateTimeField(auto_now=True)
    last_journal_entry = models.ForeignKey(
        JournalEntry,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Last journal entry that affected this balance"
    )
    
    class Meta:
        db_table = 'cms_cash_account_balance'
        constraints = [
            models.UniqueConstraint(
                fields=['gl_account'], 
                name='unique_gl_account_balance'
            )
        ]
        
    def calculate_balance_from_journal(self):
        """Calculate balance from journal entry lines"""
        from django.db.models import Sum, Q
        
        # Get all journal entry lines for this GL account
        lines = JournalEntryLine.objects.filter(
            account=self.gl_account,
            journal_entry__status='POSTED'
        )
        
        # Calculate balance (debits - credits for asset accounts)
        debits = lines.aggregate(
            total=Sum('debit_amount')
        )['total'] or 0
        
        credits = lines.aggregate(
            total=Sum('credit_amount')
        )['total'] or 0
        
        self.current_balance = debits - credits
        self.save()
        
        return self.current_balance
    
    def __str__(self):
        return f"{self.cash_account.account_name} - Balance: {self.current_balance}"


class CashTransactionJournal(models.Model):
    """
    Link between CMS Cash Transactions and GL Journal Entries
    """
    cash_transaction = models.OneToOneField(
        'CashTransaction',
        on_delete=models.CASCADE,
        related_name='journal_link'
    )
    journal_entry = models.ForeignKey(
        JournalEntry,
        on_delete=models.CASCADE,
        related_name='cms_transactions'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'cms_transaction_journal_link'
        
    def __str__(self):
        return f"CMS Transaction {self.cash_transaction.transaction_number} → JE {self.journal_entry.entry_number}"


class CashTransferJournal(models.Model):
    """
    Link between CMS Cash Transfers and GL Journal Entries
    """
    cash_transfer = models.OneToOneField(
        'CashTransfer',
        on_delete=models.CASCADE,
        related_name='journal_link'
    )
    journal_entry = models.ForeignKey(
        JournalEntry,
        on_delete=models.CASCADE,
        related_name='cms_transfers'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'cms_transfer_journal_link'
        
    def __str__(self):
        return f"CMS Transfer {self.cash_transfer.transfer_number} → JE {self.journal_entry.entry_number}" 