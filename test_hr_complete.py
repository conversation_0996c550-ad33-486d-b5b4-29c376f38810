#!/usr/bin/env python3
"""
Complete test script to verify all HR APIs are working after fixes
"""
import requests
import json

def test_hr_complete():
    base_url = "http://localhost:8000"
    
    # Login credentials
    login_data = {
        "username": "admin", 
        "password": "admin123"
    }
    
    try:
        # Get authentication token
        print("🔐 Attempting authentication...")
        login_response = requests.post(f"{base_url}/api-token-auth/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {'Authorization': f'Token {token}'}
            print("✅ Authentication successful")
        else:
            print(f"❌ Authentication failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
        
        # Test HR Employees API
        print("\n📋 Testing HR Employees API...")
        employees_response = requests.get(f"{base_url}/api/hr/employees/", headers=headers)
        print(f"Status: {employees_response.status_code}")
        
        if employees_response.status_code == 200:
            employees_data = employees_response.json()
            results = employees_data.get('results', employees_data)
            print(f"✅ HR Employees API working - Found {len(results)} employees")
            
            if results:
                first_employee = results[0]
                print(f"  - Sample employee: {first_employee.get('full_name')} ({first_employee.get('email')})")
        else:
            print(f"❌ HR Employees API error: {employees_response.status_code}")
            print(f"Response: {employees_response.text[:200]}")
        
        # Test HR Departments API
        print("\n🏢 Testing HR Departments API...")
        departments_response = requests.get(f"{base_url}/api/hr/departments/", headers=headers)
        print(f"Status: {departments_response.status_code}")
        
        if departments_response.status_code == 200:
            departments_data = departments_response.json()
            results = departments_data.get('results', departments_data)
            print(f"✅ HR Departments API working - Found {len(results)} departments")
            
            if results:
                first_dept = results[0]
                print(f"  - Sample department: {first_dept.get('name')} ({first_dept.get('code')})")
                print(f"  - Manager: {first_dept.get('manager_name', 'No manager')}")
                print(f"  - Employee count: {first_dept.get('employee_count', 0)}")
        else:
            print(f"❌ HR Departments API error: {departments_response.status_code}")
            print(f"Response: {departments_response.text[:200]}")
            
        print("\n🎉 HR API Test Complete!")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Django server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_hr_complete() 