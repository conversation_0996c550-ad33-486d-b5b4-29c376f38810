import React, { useState, useRef, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  TextField,
  Autocomplete,
  Typography,
  Box,
  Button,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import FormattedCurrencyInput from './FormattedCurrencyInput';

// Generic interfaces for flexibility
export interface JournalLineItem {
  id: string;
  account_id: number | null;
  account_name?: string;
  description: string;
  amount: number;
  debit_amount?: number;
  credit_amount?: number;
  memo?: string;
  sales_tax?: number | null;
  sales_tax_description?: string;
  sales_tax_rate?: number;
  sales_tax_amount?: number;
  taxable_amount?: number;
  [key: string]: any; // Allow additional fields
}

export interface AccountOption {
  id: string | number;
  account_number: string;
  account_name: string;
  detail_type?: string;
  account_type?: string;
}

export interface SalesTaxOption {
  id: number;
  tax_type: 'input' | 'output';
  description: string;
  rate: number;
}

export interface JournalLineTableProps {
  lines: JournalLineItem[];
  accounts: AccountOption[];
  salesTaxes?: SalesTaxOption[];
  onLineChange: (lineId: string, field: string, value: any) => void;
  onAddLine: () => void;
  onRemoveLine: (lineId: string) => void;
  currencySymbol?: string;
  showValidation?: boolean;
  
  // Table Mode - determines column layout
  tableMode?: 'payment' | 'journal' | 'custom';
  
  // Column visibility (for custom mode)
  showAccountColumn?: boolean;
  showDescriptionColumn?: boolean;
  showMemoColumn?: boolean;
  showAmountColumn?: boolean;
  showDebitCreditColumns?: boolean;
  showSalesTaxColumn?: boolean;
  showActionsColumn?: boolean;
  
  // Column labels
  accountColumnLabel?: string;
  descriptionColumnLabel?: string;
  memoColumnLabel?: string;
  amountColumnLabel?: string;
  debitColumnLabel?: string;
  creditColumnLabel?: string;
  salesTaxColumnLabel?: string;
  
  // Column widths (percentages) - initial widths
  columnWidths?: {
    index?: number;
    account?: number;
    description?: number;
    memo?: number;
    amount?: number;
    debit?: number;
    credit?: number;
    salesTax?: number;
    actions?: number;
  };
  
  // Placeholders
  accountPlaceholder?: string;
  descriptionPlaceholder?: string;
  memoPlaceholder?: string;
  salesTaxPlaceholder?: string;
  
  // Additional columns configuration
  additionalColumns?: Array<{
    key: string;
    label: string;
    width?: number;
    render?: (line: JournalLineItem, onChange: (value: any) => void) => React.ReactNode;
  }>;
  
  // Table styling
  tableHeight?: string | number;
  stickyHeader?: boolean;
  minTableWidth?: number;
  
  // Validation
  readOnly?: boolean;
  minLines?: number;
  
  // Resizing
  enableResize?: boolean;
  resetColumnWidths?: boolean;

  // Loading state
  loading?: boolean;
}

const JournalLineTable: React.FC<JournalLineTableProps> = ({
  lines,
  accounts,
  salesTaxes = [],
  onLineChange,
  onAddLine,
  onRemoveLine,
  currencySymbol = '',
  showValidation = false,
  
  // Table mode configuration
  tableMode = 'payment',
  
  // Column visibility (overridden by tableMode)
  showAccountColumn,
  showDescriptionColumn,
  showMemoColumn,
  showAmountColumn,
  showDebitCreditColumns,
  showSalesTaxColumn,
  showActionsColumn,
  
  // Labels
  accountColumnLabel = 'Account',
  descriptionColumnLabel = 'Description',
  memoColumnLabel = 'Memo',
  amountColumnLabel = 'Amount',
  debitColumnLabel = 'Debit',
  creditColumnLabel = 'Credit',
  salesTaxColumnLabel = 'Sales Tax',
  
  // Column widths
  columnWidths,
  
  // Placeholders
  accountPlaceholder = 'Select Account',
  descriptionPlaceholder = 'Enter description',
  memoPlaceholder = 'Memo...',
  salesTaxPlaceholder = 'Select Sales Tax',
  
  // Additional columns
  additionalColumns = [],
  
  // Styling
  tableHeight = '500px', // Increased default height for better visibility
  stickyHeader = true,
  minTableWidth = 1200,
  
  // State
  readOnly = false,
  minLines = 1,
  enableResize = true,
  resetColumnWidths = false,
  loading = false,
}) => {
  
  // Resizable columns state
  const [columnWidthsState, setColumnWidthsState] = useState<{[key: string]: number}>({});
  const [isResizing, setIsResizing] = useState(false);
  const [resizingColumn, setResizingColumn] = useState<string | null>(null);
  const resizeStartX = useRef<number>(0);
  const resizeStartWidth = useRef<number>(0);
  const tableRef = useRef<HTMLTableElement>(null);

  // Reset column widths to default when component mounts or lines change
  React.useEffect(() => {
    setColumnWidthsState({});
  }, [tableMode]); // Reset when table mode changes

  // Reset column widths when resetColumnWidths prop changes
  React.useEffect(() => {
    if (resetColumnWidths) {
      setColumnWidthsState({});
    }
  }, [resetColumnWidths]);

  // Configure columns based on table mode
  const getTableConfig = () => {
    const defaultWidths = {
      payment: {
        index: 30,
        account: '25%', // Make flexible
        description: '35%', // Make flexible  
        amount: 90,
        salesTax: 120,
        actions: 60
      },
      journal: {
        index: 30,
        account: '25%', // Flexible for account visibility
        description: '25%', // Flexible for description space
        memo: '15%', // Flexible for memo space
        debit: 100, // Currency inputs
        credit: 100, // Currency inputs
        salesTax: 120, // Sales tax dropdown
        actions: 60
      },
      custom: {
        index: 30,
        account: '25%', // Make flexible
        description: '25%', // Make flexible
        memo: '15%', // Make flexible
        amount: 90,
        debit: 90,
        credit: 90,
        salesTax: 120,
        actions: 60
      }
    };

    const modeConfigs = {
      payment: {
        showAccountColumn: true,
        showDescriptionColumn: true,
        showMemoColumn: false,
        showAmountColumn: true,
        showDebitCreditColumns: false,
        showSalesTaxColumn: true,
        showActionsColumn: true,
        widths: { ...defaultWidths.payment, ...columnWidths, ...columnWidthsState }
      },
      journal: {
        showAccountColumn: true,
        showDescriptionColumn: true,
        showMemoColumn: true,
        showAmountColumn: false,
        showDebitCreditColumns: true,
        showSalesTaxColumn: true,
        showActionsColumn: true,
        widths: { ...defaultWidths.journal, ...columnWidths, ...columnWidthsState }
      },
      custom: {
        showAccountColumn: showAccountColumn ?? true,
        showDescriptionColumn: showDescriptionColumn ?? true,
        showMemoColumn: showMemoColumn ?? false,
        showAmountColumn: showAmountColumn ?? true,
        showDebitCreditColumns: showDebitCreditColumns ?? false,
        showSalesTaxColumn: showSalesTaxColumn ?? false,
        showActionsColumn: showActionsColumn ?? true,
        widths: { ...defaultWidths.custom, ...columnWidths, ...columnWidthsState }
      }
    };

    return modeConfigs[tableMode];
  };

  // Calculate total table width based on current column widths
  const getTotalTableWidth = () => {
    // With flexible widths, we'll use the minimum table width
    return minTableWidth;
  };

  // Resizing handlers
  const handleMouseDown = useCallback((e: React.MouseEvent, columnKey: string) => {
    if (!enableResize) return;
    
    e.preventDefault();
    setIsResizing(true);
    setResizingColumn(columnKey);
    resizeStartX.current = e.clientX;
    
    const config = getTableConfig();
    const currentWidth = (config.widths as any)[columnKey] || 100;
    resizeStartWidth.current = typeof currentWidth === 'string' ? 100 : currentWidth;
  }, [enableResize, getTableConfig]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !resizingColumn) return;
    
    const deltaX = e.clientX - resizeStartX.current;
    const currentWidth = typeof resizeStartWidth.current === 'string' ? 100 : resizeStartWidth.current;
    const newWidth = Math.max(50, currentWidth + deltaX); // Minimum width of 50px
    
    setColumnWidthsState(prev => ({
      ...prev,
      [resizingColumn]: newWidth
    }));
  }, [isResizing, resizingColumn]);

  const handleMouseUp = useCallback(() => {
    if (!isResizing) return;
    
    setIsResizing(false);
    setResizingColumn(null);
  }, [isResizing]);

  // Add global event listeners for mouse events
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const config = getTableConfig();
  const totalWidth = getTotalTableWidth();

  // Resize handle component
  const ResizeHandle: React.FC<{ columnKey: string }> = ({ columnKey }) => {
    if (!enableResize) return null;
    
    return (
      <Box
        onMouseDown={(e) => handleMouseDown(e, columnKey)}
        sx={{
          position: 'absolute',
          right: 0,
          top: 0,
          bottom: 0,
          width: '4px',
          cursor: 'col-resize',
          backgroundColor: 'transparent',
          '&:hover': {
            backgroundColor: '#1976d2',
            opacity: 0.7,
          },
          zIndex: 1,
        }}
      />
    );
  };

  const renderAccountCell = (line: JournalLineItem) => {
    console.log('🔄 Rendering account cell for line:', line);
    console.log('Available accounts:', accounts);
    
    const selectedAccount = accounts.find(acc => acc.id === line.account_id);
    console.log('Selected account:', selectedAccount);
    
    return (
      <Autocomplete
        size="small"
        options={accounts}
        value={selectedAccount || null}
        getOptionLabel={(option) => `${option.account_name} (${option.account_number})`}
        onChange={(_, newValue) => {
          console.log('Account selection changed:', newValue);
          onLineChange(line.id, 'account_selection', newValue || null);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder={accountPlaceholder}
            error={!line.account_id && showValidation}
          />
        )}
        renderOption={(props, option) => (
          <li {...props} key={option.id}>
            <Typography variant="body2">
              {option.account_name}
              <Typography variant="caption" color="text.secondary" display="block">
                {option.account_number}
              </Typography>
            </Typography>
          </li>
        )}
      />
    );
  };

  return (
    <Box>
      <TableContainer 
        component={Paper} 
        variant="outlined"
        sx={{ 
          width: '100%',
          overflowX: 'auto',
          height: tableHeight,
          position: 'relative',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: '8px',
          '& .MuiTable-root': {
            width: '100%', // Full width
            tableLayout: 'fixed', // Fixed layout for consistent column widths
            minWidth: minTableWidth, // Ensure minimum width
          },
          '& .MuiTableCell-root': {
            padding: '6px 8px',
            position: 'relative',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            border: '1px solid #e8e8e8',
            fontSize: '0.875rem',
          },
          '& .MuiTableCell-head': {
            backgroundColor: '#f8f9fa',
            fontWeight: 600,
            color: '#2c3e50',
            borderBottom: '2px solid #dee2e6',
          },
          '& .MuiTableRow-root:hover': {
            backgroundColor: '#f8f9fa',
          },
          '& .MuiTextField-root': {
            '& .MuiInputBase-input': {
              padding: '6px 8px',
              fontSize: '0.875rem',
            },
            '& .MuiInputLabel-root': {
              fontSize: '0.875rem',
            },
          },
          '& .MuiAutocomplete-root': {
            '& .MuiInputBase-root': {
              padding: '2px 8px !important',
            },
          },
        }}
      >
        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        )}
        <Table stickyHeader={stickyHeader} ref={tableRef}>
          <TableHead>
            <TableRow>
              <TableCell 
                width={config.widths.index} 
                sx={{ 
                  fontWeight: 'bold', 
                  backgroundColor: '#f8f9fa',
                  position: 'relative',
                  width: `${config.widths.index}px`,
                  minWidth: `${config.widths.index}px`,
                  maxWidth: `${config.widths.index}px`,
                  textAlign: 'center',
                  borderRight: '1px solid #dee2e6',
                }}
              >
                #
                <ResizeHandle columnKey="index" />
              </TableCell>
              
              {config.showAccountColumn && (
                <TableCell 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'relative',
                    width: typeof config.widths.account === 'string' ? config.widths.account : `${config.widths.account}px`,
                    borderRight: '1px solid #dee2e6',
                    textAlign: 'center',
                  }}
                >
                  {accountColumnLabel}
                  <ResizeHandle columnKey="account" />
                </TableCell>
              )}
              
              {config.showDescriptionColumn && (
                <TableCell 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'relative',
                    width: typeof config.widths.description === 'string' ? config.widths.description : `${config.widths.description}px`,
                    borderRight: '1px solid #dee2e6',
                    textAlign: 'center',
                  }}
                >
                  {descriptionColumnLabel}
                  <ResizeHandle columnKey="description" />
                </TableCell>
              )}
              
              {config.showMemoColumn && (
                <TableCell 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'relative',
                    width: typeof config.widths.memo === 'string' ? config.widths.memo : `${config.widths.memo}px`,
                    borderRight: '1px solid #dee2e6',
                    textAlign: 'center',
                  }}
                >
                  {memoColumnLabel}
                  <ResizeHandle columnKey="memo" />
                </TableCell>
              )}
              
              {additionalColumns.map((col) => (
                <TableCell 
                  key={col.key}
                  width={col.width || 100} 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'relative',
                    width: `${col.width || 100}px`,
                    minWidth: `${col.width || 100}px`,
                    maxWidth: `${col.width || 100}px`,
                    borderRight: '1px solid #dee2e6',
                    textAlign: 'center',
                  }}
                >
                  {col.label}
                  <ResizeHandle columnKey={col.key} />
                </TableCell>
              ))}
              
              {config.showAmountColumn && (
                <TableCell 
                  width={config.widths.amount} 
                  align="center" 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'relative',
                    width: `${config.widths.amount}px`,
                    minWidth: `${config.widths.amount}px`,
                    maxWidth: `${config.widths.amount}px`,
                    borderRight: '1px solid #dee2e6',
                    textAlign: 'center',
                  }}
                >
                  {amountColumnLabel}
                  <ResizeHandle columnKey="amount" />
                </TableCell>
              )}
              
              {config.showDebitCreditColumns && (
                <>
                  <TableCell 
                    width={config.widths.debit} 
                    align="center" 
                    sx={{ 
                      fontWeight: 'bold', 
                      backgroundColor: '#f8f9fa',
                      position: 'relative',
                      width: `${config.widths.debit}px`,
                      minWidth: `${config.widths.debit}px`,
                      maxWidth: `${config.widths.debit}px`,
                      borderRight: '1px solid #dee2e6',
                      textAlign: 'center',
                    }}
                  >
                    {debitColumnLabel}
                    <ResizeHandle columnKey="debit" />
                  </TableCell>
                  <TableCell 
                    width={config.widths.credit} 
                    align="center" 
                    sx={{ 
                      fontWeight: 'bold', 
                      backgroundColor: '#f8f9fa',
                      position: 'relative',
                      width: `${config.widths.credit}px`,
                      minWidth: `${config.widths.credit}px`,
                      maxWidth: `${config.widths.credit}px`,
                      borderRight: '1px solid #dee2e6',
                      textAlign: 'center',
                    }}
                  >
                    {creditColumnLabel}
                    <ResizeHandle columnKey="credit" />
                  </TableCell>
                </>
              )}
              
              {config.showSalesTaxColumn && (
                <TableCell 
                  width={config.widths.salesTax} 
                  align="center" 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'relative',
                    width: `${config.widths.salesTax}px`,
                    minWidth: `${config.widths.salesTax}px`,
                    maxWidth: `${config.widths.salesTax}px`,
                    borderRight: '1px solid #dee2e6',
                    textAlign: 'center',
                  }}
                >
                  {salesTaxColumnLabel}
                  <ResizeHandle columnKey="salesTax" />
                </TableCell>
              )}
              
              {config.showActionsColumn && (
                <TableCell 
                  width={config.widths.actions} 
                  align="center" 
                  sx={{ 
                    fontWeight: 'bold', 
                    backgroundColor: '#f8f9fa',
                    position: 'sticky',
                    right: 0,
                    width: `${config.widths.actions}px`,
                    minWidth: `${config.widths.actions}px`,
                    maxWidth: `${config.widths.actions}px`,
                    zIndex: 10,
                    borderLeft: '2px solid #dee2e6',
                  }}
                >
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line, index) => (
              <TableRow 
                key={line.id}
                sx={{
                  minHeight: '64px', // Increased row height for better visibility
                  '& > .MuiTableCell-root': {
                    padding: '12px 8px', // Increased cell padding
                    verticalAlign: 'middle', // Center content vertically
                  },
                  '&:nth-of-type(even)': {
                    backgroundColor: '#f9f9f9',
                  },
                  '&:hover': {
                    backgroundColor: '#e3f2fd !important',
                  },
                }}
              >
                <TableCell 
                  sx={{ 
                    textAlign: 'center',
                    fontWeight: 500,
                    color: '#666',
                    borderRight: '1px solid #dee2e6',
                  }}
                >
                  {index + 1}
                </TableCell>
                
                {config.showAccountColumn && (
                  <TableCell sx={{ borderRight: '1px solid #dee2e6' }}>
                    {readOnly ? (
                      // For read-only mode, display the account name directly
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          padding: '8px 12px',
                          minHeight: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          fontWeight: 500,
                          color: 'text.primary'
                        }}
                      >
                        {line.account_name || 'No Product Selected'}
                      </Typography>
                    ) : (
                      renderAccountCell(line)
                    )}
                  </TableCell>
                )}
                
                {config.showDescriptionColumn && (
                  <TableCell sx={{ borderRight: '1px solid #dee2e6' }}>
                    {readOnly ? (
                      // For read-only mode, display the description directly
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          padding: '8px 12px',
                          minHeight: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          color: 'text.secondary'
                        }}
                      >
                        {line.description || 'No Description'}
                      </Typography>
                    ) : (
                      <TextField
                        fullWidth
                        size="medium"
                        value={line.description || ''}
                        onChange={(e) => onLineChange(line.id, 'description', e.target.value)}
                        placeholder="Enter description"
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            minHeight: '48px',
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                            '&:hover fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.87)',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main',
                            },
                          },
                          '& .MuiOutlinedInput-input': {
                            padding: '12px 14px',
                            fontSize: '1rem',
                          }
                        }}
                      />
                    )}
                  </TableCell>
                )}
                
                {config.showMemoColumn && (
                  <TableCell sx={{ borderRight: '1px solid #dee2e6' }}>
                    {readOnly ? (
                      // For read-only mode, display the memo directly
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          padding: '8px 12px',
                          minHeight: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          color: 'text.secondary',
                          fontSize: '0.875rem'
                        }}
                      >
                        {line.memo || '-'}
                      </Typography>
                    ) : (
                      <TextField
                        fullWidth
                        size="medium"
                        value={line.memo || ''}
                        onChange={(e) => onLineChange(line.id, 'memo', e.target.value)}
                        placeholder="Enter memo"
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            minHeight: '48px',
                            '& fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.23)',
                            },
                            '&:hover fieldset': {
                              borderColor: 'rgba(0, 0, 0, 0.87)',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main',
                            },
                          },
                          '& .MuiOutlinedInput-input': {
                            padding: '12px 14px',
                            fontSize: '1rem',
                          }
                        }}
                      />
                    )}
                  </TableCell>
                )}
                
                {additionalColumns.map((col) => (
                  <TableCell key={col.key} sx={{ borderRight: '1px solid #dee2e6' }}>
                    {col.render ? col.render(line, (value) => onLineChange(line.id, col.key, value)) : null}
                  </TableCell>
                ))}
                
                {config.showAmountColumn && (
                  <TableCell align="right" sx={{ borderRight: '1px solid #dee2e6' }}>
                    <FormattedCurrencyInput
                      fullWidth
                      size="medium"
                      disabled={readOnly}
                      name={`amount_${line.id}`}
                      value={line.amount}
                      onChange={(e) => onLineChange(line.id, 'amount', parseFloat(e.target.value) || 0)}
                      currencySymbol={currencySymbol}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          minHeight: '48px', // Increased height for better visibility
                        },
                        '& .MuiOutlinedInput-input': {
                          padding: '12px 14px', // Increased padding for better touch targets
                        }
                      }}
                    />
                  </TableCell>
                )}
                
                {config.showDebitCreditColumns && (
                  <>
                    <TableCell align="right" sx={{ borderRight: '1px solid #dee2e6' }}>
                      <FormattedCurrencyInput
                        fullWidth
                        size="medium"
                        disabled={readOnly}
                        name={`debit_${line.id}`}
                        value={line.debit_amount || 0}
                        onChange={(e) => onLineChange(line.id, 'debit_amount', parseFloat(e.target.value) || 0)}
                        currencySymbol={currencySymbol}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            minHeight: '48px', // Increased height for better visibility
                          },
                          '& .MuiOutlinedInput-input': {
                            padding: '12px 14px', // Increased padding for better touch targets
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell align="right" sx={{ borderRight: '1px solid #dee2e6' }}>
                      <FormattedCurrencyInput
                        fullWidth
                        size="medium"
                        disabled={readOnly}
                        name={`credit_${line.id}`}
                        value={line.credit_amount || 0}
                        onChange={(e) => onLineChange(line.id, 'credit_amount', parseFloat(e.target.value) || 0)}
                        currencySymbol={currencySymbol}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            minHeight: '48px', // Increased height for better visibility
                          },
                          '& .MuiOutlinedInput-input': {
                            padding: '12px 14px', // Increased padding for better touch targets
                          }
                        }}
                      />
                    </TableCell>
                  </>
                )}
                
                {config.showSalesTaxColumn && (
                  <TableCell align="center" sx={{ borderRight: '1px solid #dee2e6' }}>
                    {readOnly ? (
                      // View mode - show the tax information as text
                      <Box sx={{ textAlign: 'center', py: 1 }}>
                        {line.sales_tax_description || (line.sales_tax_rate && line.sales_tax_rate > 0) ? (
                          <>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {line.sales_tax_description || `Input Tax ${line.sales_tax_rate}%`}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Rate: {line.sales_tax_rate}%
                              {line.sales_tax_amount && line.sales_tax_amount > 0 && (
                                <> • Amount: {currencySymbol}{(line.sales_tax_amount || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</>
                              )}
                            </Typography>
                          </>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No Tax Applied
                          </Typography>
                        )}
                      </Box>
                    ) : (
                      // Edit mode - show the dropdown
                      <Autocomplete
                        fullWidth
                        size="medium"
                        disabled={readOnly}
                        options={salesTaxes}
                        getOptionLabel={(option) => `${option.description} (${option.rate}%)`}
                        value={salesTaxes.find(tax => Number(tax.id) === Number(line.sales_tax)) || null}
                        onChange={(event, newValue) => {
                          onLineChange(line.id, 'sales_tax', newValue ? newValue.id : null);
                          if (newValue) {
                            onLineChange(line.id, 'sales_tax_description', newValue.description);
                            onLineChange(line.id, 'sales_tax_rate', newValue.rate);
                            // Calculate tax amount based on line amount and rate
                            const lineAmount = line.debit_amount || line.credit_amount || line.amount || 0;
                            const taxAmount = (lineAmount * newValue.rate) / 100;
                            onLineChange(line.id, 'sales_tax_amount', taxAmount);
                            onLineChange(line.id, 'taxable_amount', lineAmount);
                          } else {
                            onLineChange(line.id, 'sales_tax_description', '');
                            onLineChange(line.id, 'sales_tax_rate', 0);
                            onLineChange(line.id, 'sales_tax_amount', 0);
                            onLineChange(line.id, 'taxable_amount', 0);
                          }
                        }}
                        renderInput={(params) => (
                          <TextField 
                            {...params} 
                            label={salesTaxPlaceholder}
                            variant="outlined"
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                backgroundColor: 'white',
                                minHeight: '48px', // Increased height for better visibility
                              },
                              '& .MuiOutlinedInput-input': {
                                padding: '12px 14px', // Increased padding for better touch targets
                              }
                            }}
                          />
                        )}
                        renderOption={(props, option) => {
                          const { key, ...otherProps } = props;
                          return (
                            <Box component="li" key={key} {...otherProps}>
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {option.description}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {option.tax_type.toUpperCase()} - {option.rate}%
                                </Typography>
                              </Box>
                            </Box>
                          );
                        }}
                      />
                    )}
                  </TableCell>
                )}
                
                {config.showActionsColumn && (
                  <TableCell 
                    align="center"
                    sx={{
                      position: 'sticky',
                      right: 0,
                      backgroundColor: 'inherit',
                      zIndex: 9,
                      borderLeft: '2px solid #dee2e6',
                      width: `${config.widths.actions}px`,
                      minWidth: `${config.widths.actions}px`,
                      maxWidth: `${config.widths.actions}px`,
                    }}
                  >
                    {!readOnly && lines.length > minLines && (
                      <IconButton 
                        size="small" 
                        onClick={() => onRemoveLine(line.id)}
                        color="error"
                        sx={{
                          '&:hover': {
                            backgroundColor: '#ffebee',
                          }
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    )}
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Add Line Button */}
      {!readOnly && (
        <Box sx={{ mt: 1.5, display: 'flex', justifyContent: 'flex-start' }}>
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={onAddLine}
            size="small"
            sx={{
              borderColor: '#1976d2',
              color: '#1976d2',
              '&:hover': {
                backgroundColor: '#e3f2fd',
                borderColor: '#1976d2',
              }
            }}
          >
            Add Line
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default JournalLineTable; 