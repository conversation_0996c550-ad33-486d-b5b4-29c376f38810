from django.shortcuts import render
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import Is<PERSON><PERSON><PERSON><PERSON><PERSON>, IsAd<PERSON><PERSON>ser, AllowAny
from rest_framework.response import Response
from rest_framework import status, viewsets
from rest_framework.decorators import action
from django.contrib.auth import get_user_model
from django.middleware.csrf import get_token
from .models import Company
from .serializers import UserSerializer, CompanySerializer, CompanyCreateSerializer

# Get Django's User model for user operations
User = get_user_model()

# Create your views here.

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def current_user(request):
    """
    API endpoint to get current authenticated user details.
    
    This view returns information about the user who is currently logged in.
    It includes their role and permissions for the frontend to use.
    
    Required: User must be logged in (IsAuthenticated permission)
    Returns: User data including username, email, role, and permissions
    """
    serializer = UserSerializer(request.user)
    return Response(serializer.data)

class UserViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows users to be viewed, created, updated, or deleted.
    
    This handles all user management operations in the system.
    Only admin users (superusers) can perform these operations.
    
    Available endpoints:
    - GET /users/ : List all users
    - POST /users/ : Create a new user
    - GET /users/{id}/ : Get specific user details
    - PUT /users/{id}/ : Update a user completely
    - PATCH /users/{id}/ : Update a user partially
    - DELETE /users/{id}/ : Delete a user
    """
    
    # Get all users, ordered by username for consistent listing
    queryset = User.objects.all().order_by('username')
    # Use the UserSerializer to convert between model and JSON
    serializer_class = UserSerializer
    # Only superusers/admins can manage users
    permission_classes = [IsAdminUser]

class CompanyViewSet(viewsets.ModelViewSet):
    """
    API endpoint for company management with strict business rules.
    
    Business Rules:
    1. Only ONE company can exist in the system
    2. Only SUPERUSERS can create companies
    3. Only SUPERUSERS can edit company information
    4. All authenticated users can view company information
    """
    
    queryset = Company.objects.all()
    serializer_class = CompanySerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def create(self, request, *args, **kwargs):
        """Create a new company (superuser only)."""
        if Company.objects.exists():
            return Response(
                {"detail": "A company already exists in the system"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = CompanyCreateSerializer(data=request.data)
        if serializer.is_valid():
            company = serializer.save()
            return Response(
                CompanySerializer(company).data,
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get the current company in the system."""
        company = Company.objects.first()
        if not company:
            return Response(
                {"detail": "No company exists yet"},
                status=status.HTTP_404_NOT_FOUND
            )
        serializer = CompanySerializer(company)
        return Response(serializer.data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def current_company(request):
    """Get current company details (backward compatibility)."""
    try:
        company = Company.objects.first()
        if not company:
            return Response(
                {"detail": "No company exists yet"},
                status=status.HTTP_404_NOT_FOUND
            )
        serializer = CompanySerializer(company)
        return Response(serializer.data)
    except Company.DoesNotExist:
        return Response(
            {"detail": "No company exists yet"},
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def get_csrf_token(request):
    """
    API endpoint to get CSRF token.
    This is needed for the frontend to make POST requests.
    """
    return Response({'csrfToken': get_token(request)})
