import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Box,
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import BillForm from './BillForm';
import { BillFormData, Bill } from '../../../shared/types/bill.types';

interface BillFormModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: BillFormData) => void;
  bill?: Bill;
  title?: string;
}

const BillFormModal: React.FC<BillFormModalProps> = ({
  open,
  onClose,
  onSubmit,
  bill,
  title = 'Create Bill',
}) => {
  const handleSubmit = (values: BillFormData, action: 'save' | 'save-close' | 'save-new') => {
    onSubmit(values);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          height: '90vh',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <DialogTitle sx={{ m: 0, p: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {title}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent dividers sx={{ p: 2, flexGrow: 1, overflow: 'auto' }}>
        <BillForm
          onSubmit={handleSubmit}
          bill={bill}
        />
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} color="secondary" variant="outlined">
          Cancel
        </Button>
        <Box sx={{ flex: '1 1 auto' }} />
        <Button
          onClick={() => {
            document.getElementById('save-bill-button')?.click();
          }}
          color="primary"
          variant="contained"
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BillFormModal; 