import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Alert,
  CircularProgress,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Divider,
} from '@mui/material';
import {
  Edit as EditIcon,
  PostAdd as PostIcon,
  ArrowBack as ArrowBackIcon,
  Receipt as ReceiptIcon,
  Business as BusinessIcon,
  Warehouse as WarehouseIcon,
  CalendarToday as CalendarIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { grnService, GRN } from '../services/grn.service';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const GRNDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [grn, setGrn] = useState<GRN | null>(null);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    if (id) {
      loadGRN();
    }
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadGRN = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      console.log('🔍 Loading GRN with ID:', id);
      const grnData = await grnService.getGRN(parseInt(id));
      console.log('✅ GRN loaded successfully:', grnData);
      setGrn(grnData);
    } catch (err) {
      console.error('❌ Failed to load GRN:', err);
      setError(err instanceof Error ? err.message : 'Failed to load GRN');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/dashboard/inventory/grn/${id}/edit`);
  };

  const handleMarkAsReceived = async () => {
    if (!grn?.grn_id) return;
    
    try {
      // Use direct API call to update status
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/inventory/grns/${grn.grn_id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`
        },
        body: JSON.stringify({ status: 'RECEIVED' })
      });

      if (!response.ok) {
        throw new Error('Failed to mark GRN as received');
      }
      
      loadGRN(); // Reload to get updated status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark GRN as received');
    }
  };

  const handlePostToInventory = async () => {
    if (!grn?.grn_id) return;
    
    try {
      await grnService.postToInventory(grn.grn_id);
      loadGRN(); // Reload to get updated status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post GRN to inventory');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      DRAFT: { color: 'default' as const, label: 'Draft' },
      RECEIVED: { color: 'primary' as const, label: 'Received' },
      POSTED: { color: 'success' as const, label: 'Posted' },
      CANCELLED: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
    return <Chip label={config.label} color={config.color} />;
  };

  const getConditionChip = (condition: string) => {
    const conditionConfig = {
      GOOD: { color: 'success' as const, label: 'Good' },
      DAMAGED: { color: 'warning' as const, label: 'Damaged' },
      EXPIRED: { color: 'error' as const, label: 'Expired' },
      REJECTED: { color: 'error' as const, label: 'Rejected' },
    };
    
    const config = conditionConfig[condition as keyof typeof conditionConfig] || conditionConfig.GOOD;
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </PageContainer>
    );
  }

  if (!grn && !loading) {
    return (
      <PageContainer>
        <Alert severity="error">
          {error || 'GRN not found'}
        </Alert>
      </PageContainer>
    );
  }

  if (!grn) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            GRN {grn?.grn_number || 'Loading...'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Goods Receipt Note Details
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/inventory/grn')}
          >
            Back to List
          </Button>
          {grn?.status !== 'POSTED' && grn?.status !== 'CANCELLED' && (
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={handleEdit}
            >
              Edit
            </Button>
          )}
          {grn?.status === 'DRAFT' && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<CheckCircleIcon />}
              onClick={handleMarkAsReceived}
            >
              Mark as Received
            </Button>
          )}
          {grn?.status === 'RECEIVED' && (
            <Button
              variant="contained"
              color="success"
              startIcon={<PostIcon />}
              onClick={handlePostToInventory}
            >
              Post to Inventory
            </Button>
          )}
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* GRN Header Information */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" display="flex" alignItems="center" gap={1}>
                  <ReceiptIcon />
                  GRN Information
                </Typography>
                {getStatusChip(grn?.status || 'DRAFT')}
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">GRN Number</Typography>
                  <Typography variant="body1" fontWeight="medium">{grn?.grn_number || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">PO Number</Typography>
                  <Typography variant="body1">{grn?.purchase_order_number || 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">Receipt Date</Typography>
                  <Typography variant="body1">{grn?.receipt_date ? formatDate(grn.receipt_date) : 'N/A'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">Received By</Typography>
                  <Typography variant="body1">{grn?.received_by_name || 'N/A'}</Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Vendor and Warehouse Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" display="flex" alignItems="center" gap={1} mb={2}>
                <BusinessIcon />
                Vendor Information
              </Typography>
              <Typography variant="body2" color="text.secondary">Vendor Name</Typography>
              <Typography variant="body1" fontWeight="medium" mb={1}>{grn?.vendor_name || 'N/A'}</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" display="flex" alignItems="center" gap={1} mb={2}>
                <WarehouseIcon />
                Warehouse Information
              </Typography>
              <Typography variant="body2" color="text.secondary">Warehouse</Typography>
              <Typography variant="body1" fontWeight="medium" mb={1}>{grn?.warehouse_name || 'N/A'}</Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Items Table */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" mb={2}>Items Received</Typography>
              
              {grn?.status === 'RECEIVED' && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>📊 Accounting Impact:</strong> Posting this GRN will update inventory asset accounts 
                    and create <strong>DRAFT journal entries</strong> for manual review and posting.
                  </Typography>
                </Alert>
              )}
              
              {grn?.status === 'POSTED' && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>✅ Posted to Inventory:</strong> Stock transactions created and 
                    <strong> DRAFT journal entries</strong> generated. Review and post journal entries in GL module.
                  </Typography>
                </Alert>
              )}
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell align="right">Ordered</TableCell>
                      <TableCell align="right">Received</TableCell>
                      <TableCell align="right">Unit Cost</TableCell>
                      <TableCell>Condition</TableCell>
                      <TableCell>Batch No.</TableCell>
                      <TableCell align="right">Total Cost</TableCell>
                      {grn?.status !== 'POSTED' && (
                        <TableCell>Inventory Account</TableCell>
                      )}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {grn?.items?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Typography variant="body2">
                            {item?.product_name || `Product ${item?.product || 'N/A'}`}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">{Number(item?.quantity_ordered || 0).toFixed(2)}</TableCell>
                        <TableCell align="right">{Number(item?.quantity_received || 0).toFixed(2)}</TableCell>
                        <TableCell align="right">{formatCurrency(Number(item?.unit_cost || 0))}</TableCell>
                        <TableCell>{getConditionChip(item?.condition || 'GOOD')}</TableCell>
                        <TableCell>{item?.batch_number || '-'}</TableCell>
                        <TableCell align="right">
                          {formatCurrency(Number(item?.quantity_received || 0) * Number(item?.unit_cost || 0))}
                        </TableCell>
                        {grn?.status !== 'POSTED' && (
                          <TableCell>
                            <Typography variant="caption" color="text.secondary">
                              Product's Inventory Account
                            </Typography>
                          </TableCell>
                        )}
                      </TableRow>
                    )) || []}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <Divider sx={{ my: 2 }} />
              
              <Box display="flex" justifyContent="space-between">
                <Typography variant="h6">
                  Total Quantity: {Number(grn?.total_quantity || 0).toFixed(2)}
                </Typography>
                <Typography variant="h6">
                  Total Value: {formatCurrency(Number(grn?.total_value || 0))}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Notes */}
        {grn?.notes && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" mb={2}>Notes</Typography>
                <Typography variant="body1">{grn.notes}</Typography>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </PageContainer>
  );
};

export default GRNDetailPage; 