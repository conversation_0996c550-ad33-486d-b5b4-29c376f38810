# Generated by Django 4.2.21 on 2025-06-24 01:22

import datetime
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0006_remove_product_expense_account_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0003_add_goods_return_note'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventoryTransfer',
            fields=[
                ('transfer_id', models.AutoField(primary_key=True, serialize=False)),
                ('transfer_number', models.CharField(help_text='Transfer reference number', max_length=50, unique=True)),
                ('transfer_date', models.DateField(default=datetime.date.today)),
                ('notes', models.TextField(blank=True, help_text='Transfer notes and comments')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Transfer'), ('IN_TRANSIT', 'In Transit'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_items', models.PositiveIntegerField(default=0, help_text='Number of different items')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfers_completed', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_created', to=settings.AUTH_USER_MODEL)),
                ('from_warehouse', models.ForeignKey(help_text='Source warehouse', on_delete=django.db.models.deletion.CASCADE, related_name='transfers_out', to='inventory.warehouse')),
                ('to_warehouse', models.ForeignKey(help_text='Destination warehouse', on_delete=django.db.models.deletion.CASCADE, related_name='transfers_in', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'inventory_transfers',
                'ordering': ['-transfer_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InventoryTransferItem',
            fields=[
                ('transfer_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=4, help_text='Quantity to transfer', max_digits=15)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('notes', models.TextField(blank=True)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfer_items', to='sales.product')),
                ('transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.inventorytransfer')),
            ],
            options={
                'db_table': 'inventory_transfer_items',
                'ordering': ['line_order'],
                'unique_together': {('transfer', 'product', 'batch_number')},
            },
        ),
    ]
