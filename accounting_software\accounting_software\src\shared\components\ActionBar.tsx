import React from 'react';
import {
  Paper,
  Stack,
  Box,
  useTheme,
} from '@mui/material';

interface ActionBarProps {
  children: React.ReactNode;
  position?: 'fixed' | 'sticky';
  align?: 'left' | 'center' | 'right';
  padding?: number | string;
}

/**
 * A reusable action bar component for displaying action buttons at the bottom of forms
 * @param children - The action buttons to display
 * @param position - Whether the bar should be fixed to the bottom of the screen or sticky
 * @param align - The alignment of the action buttons
 * @param padding - The padding to apply to the action bar
 */
const ActionBar: React.FC<ActionBarProps> = ({ 
  children, 
  position = 'sticky',
  align = 'right',
  padding = 2
}) => {
  const theme = useTheme();
  
  const getJustifyContent = () => {
    switch (align) {
      case 'left':
        return 'flex-start';
      case 'center':
        return 'center';
      case 'right':
        return 'flex-end';
      default:
        return 'flex-end';
    }
  };
  
  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: padding, 
        borderTop: `1px solid ${theme.palette.divider}`,
        position: position,
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: theme.zIndex.appBar - 1,
        backgroundColor: theme.palette.background.paper,
      }}
    >
      <Stack 
        direction="row" 
        spacing={2} 
        justifyContent={getJustifyContent()}
      >
        {children}
      </Stack>
    </Paper>
  );
};

export default ActionBar; 