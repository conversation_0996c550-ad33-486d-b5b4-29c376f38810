import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Typography, Button } from '@mui/material';
import { useRouteError, isRouteErrorResponse } from 'react-router-dom';

interface Props {
    children: ReactNode;
}

interface State {
    hasError: boolean;
    error?: Error;
}

// Create a wrapper component to handle route errors
const RouteErrorBoundary: React.FC = () => {
    const error = useRouteError();
    
    if (isRouteErrorResponse(error)) {
        return (
            <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="h5" color="error" gutterBottom>
                    {error.status === 404 ? 'Page Not Found' : 'An Error Occurred'}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                    {error.statusText || error.data?.message || 'Something went wrong'}
                </Typography>
                <Button 
                    variant="contained" 
                    onClick={() => window.history.back()}
                >
                    Go Back
                </Button>
            </Box>
        );
    }

    return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="h5" color="error" gutterBottom>
                Unexpected Error
            </Typography>
            <Button 
                variant="contained" 
                onClick={() => window.history.back()}
            >
                Go Back
            </Button>
        </Box>
    );
};

class ErrorBoundary extends Component<Props, State> {
    public state: State = {
        hasError: false
    };

    public static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error };
    }

    public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('Uncaught error:', error, errorInfo);
    }

    public render() {
        if (this.state.hasError) {
            return (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                    <Typography variant="h5" color="error" gutterBottom>
                        Something went wrong
                    </Typography>
                    <Button 
                        variant="contained" 
                        onClick={() => window.location.reload()}
                    >
                        Reload Page
                    </Button>
                </Box>
            );
        }

        return this.props.children;
    }
}

export { ErrorBoundary, RouteErrorBoundary };
export default ErrorBoundary;