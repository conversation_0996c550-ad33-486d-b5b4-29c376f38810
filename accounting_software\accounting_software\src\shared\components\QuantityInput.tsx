import React from 'react';
import { TextField, TextFieldProps, InputAdornment } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledQuantityInput = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    '& fieldset': {
      borderColor: theme.palette.divider,
      borderWidth: '1px',
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
      borderWidth: '1px',
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
      borderWidth: '2px',
    },
    '& input': {
      textAlign: 'right',
      fontFamily: theme.typography.fontFamily, // Use default system font like summary
      fontSize: '0.875rem',
      fontWeight: 500,
      padding: theme.spacing(1.5),
    },
  },
  '& .MuiInputLabel-root': {
    fontSize: '0.875rem',
    fontWeight: 500,
  },
  '& .MuiFormHelperText-root': {
    fontSize: '0.75rem',
    marginLeft: 0,
    marginTop: theme.spacing(0.5),
  },
}));

export interface QuantityInputProps extends Omit<TextFieldProps, 'type' | 'inputProps'> {
  value?: string | number;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  min?: number;
  max?: number;
  step?: number;
  precision?: number;
  unit?: string;
  allowNegative?: boolean;
  placeholder?: string;
}

const QuantityInput: React.FC<QuantityInputProps> = ({
  value = '',
  onChange,
  onBlur,
  min = 0,
  max = 10000000, // Default max of 10 million
  step = 0.01,
  precision = 2,
  unit,
  allowNegative = false,
  placeholder = '',
  error,
  helperText,
  disabled,
  size = 'small',
  fullWidth = true,
  ...props
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    
    // Allow empty input
    if (inputValue === '') {
      onChange?.(event);
      return;
    }

    // Allow negative sign if allowNegative is true
    if (inputValue === '-' && allowNegative) {
      onChange?.(event);
      return;
    }

    // Validate numeric input
    const numericRegex = allowNegative 
      ? /^-?\d*\.?\d*$/ 
      : /^\d*\.?\d*$/;
    
    if (numericRegex.test(inputValue)) {
      const numValue = parseFloat(inputValue);
      
      // Check min/max constraints
      if (!isNaN(numValue)) {
        if (min !== undefined && numValue < min) return;
        if (max !== undefined && numValue > max) return;
      }
      
      onChange?.(event);
    }
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    
    if (inputValue && inputValue !== '-') {
      const numValue = parseFloat(inputValue);
      if (!isNaN(numValue)) {
        // Format to specified precision
        const formattedValue = numValue.toFixed(precision);
        event.target.value = formattedValue;
        
        // Create a new event with the formatted value
        const syntheticEvent = {
          ...event,
          target: {
            ...event.target,
            value: formattedValue,
          },
        } as React.ChangeEvent<HTMLInputElement>;
        
        onChange?.(syntheticEvent);
      }
    }
    
    onBlur?.(event);
  };

  const formatDisplayValue = (val: string | number): string => {
    // Handle all "empty" cases - return blank input
    if (val === '' || val === undefined || val === null || val === 0 || val === '0') {
      return '';
    }
    
    // Allow negative sign for allowNegative inputs
    if (typeof val === 'string' && val === '-') return val;
    
    // Convert to number and validate
    const numVal = typeof val === 'string' ? parseFloat(val) : val;
    if (isNaN(numVal)) return '';
    
    return numVal.toString();
  };

  return (
    <StyledQuantityInput
      {...props}
      type="text"
      value={formatDisplayValue(value)}
      onChange={handleChange}
      onBlur={handleBlur}
      placeholder={placeholder}
      error={error}
      helperText={helperText}
      disabled={disabled}
      size={size}
      fullWidth={fullWidth}
      inputProps={{
        min: allowNegative ? undefined : min,
        max,
        step,
        inputMode: 'decimal',
        pattern: allowNegative ? '[0-9]*\\.?[0-9]*|-[0-9]*\\.?[0-9]*' : '[0-9]*\\.?[0-9]*',
      }}
      InputProps={{
        endAdornment: unit ? (
          <InputAdornment position="end">
            <span style={{ fontSize: '0.75rem', color: '#666', fontWeight: 500 }}>
              {unit}
            </span>
          </InputAdornment>
        ) : undefined,
        ...props.InputProps,
      }}
    />
  );
};

// Utility function for forms to check if quantity has a valid value
export const hasValidQuantity = (value: string | number): boolean => {
  if (value === '' || value === undefined || value === null || value === 0 || value === '0') {
    return false;
  }
  const numVal = typeof value === 'string' ? parseFloat(value) : value;
  return !isNaN(numVal) && numVal > 0;
};

export default QuantityInput; 