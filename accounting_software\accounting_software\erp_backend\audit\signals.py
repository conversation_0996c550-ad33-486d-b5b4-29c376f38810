"""
Audit Trail Signals for Automatic Database Change Logging

This module uses Django signals to automatically capture:
- All model creation, updates, and deletions
- Before and after values for data changes
- User context for each change
- Financial transaction specific logging

Required for SOX compliance and complete audit trail.
"""

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.contenttypes.models import ContentType
from django.core import serializers
from django.forms.models import model_to_dict
from threading import local
import json

from .models import AuditLog

# Thread-local storage for request context
_thread_locals = local()

def set_current_user(user):
    """Set the current user for audit logging."""
    _thread_locals.user = user

def get_current_user():
    """Get the current user for audit logging."""
    return getattr(_thread_locals, 'user', None)

def set_request_context(ip_address, user_agent, session_key):
    """Set request context for audit logging."""
    _thread_locals.ip_address = ip_address
    _thread_locals.user_agent = user_agent
    _thread_locals.session_key = session_key

class AuditableModelMixin:
    """
    Mixin for models that should be audited.
    Add this to any model that needs audit trail logging.
    """
    
    def save(self, *args, **kwargs):
        # Store old values before saving
        if self.pk:
            try:
                old_instance = self.__class__.objects.get(pk=self.pk)
                self._old_values = model_to_dict(old_instance)
            except self.__class__.DoesNotExist:
                self._old_values = None
        else:
            self._old_values = None
        
        super().save(*args, **kwargs)

# Dictionary to store old values before updates
_old_values = {}

@receiver(pre_save)
def capture_old_values(sender, instance, **kwargs):
    """Capture old values before model update."""
    # Skip audit log models to prevent infinite loops
    if sender._meta.app_label == 'audit':
        return
    
    # Only capture for existing instances (updates)
    if instance.pk:
        try:
            old_instance = sender.objects.get(pk=instance.pk)
            old_values = {}
            
            # Get all field values
            for field in sender._meta.fields:
                if not field.name.endswith('_ptr'):  # Skip pointer fields
                    old_value = getattr(old_instance, field.name, None)
                    
                    # Handle special field types
                    if hasattr(old_value, 'isoformat'):  # DateTime fields
                        old_value = old_value.isoformat()
                    elif hasattr(old_value, '__str__') and not isinstance(old_value, (str, int, float, bool)):
                        old_value = str(old_value)
                    
                    old_values[field.name] = old_value
            
            _old_values[instance.pk] = old_values
            
        except sender.DoesNotExist:
            _old_values[instance.pk] = None

@receiver(post_save)
def log_model_save(sender, instance, created, **kwargs):
    """Log model creation and updates."""
    # Skip audit log models to prevent infinite loops
    if sender._meta.app_label == 'audit':
        return
    
    # Skip if this is a migration or fixture loading
    if kwargs.get('raw', False):
        return
    
    try:
        action = 'CREATE' if created else 'UPDATE'
        
        # Get new values
        new_values = {}
        for field in sender._meta.fields:
            if not field.name.endswith('_ptr'):  # Skip pointer fields
                new_value = getattr(instance, field.name, None)
                
                # Handle special field types
                if hasattr(new_value, 'isoformat'):  # DateTime fields
                    new_value = new_value.isoformat()
                elif hasattr(new_value, '__str__') and not isinstance(new_value, (str, int, float, bool)):
                    new_value = str(new_value)
                
                new_values[field.name] = new_value
        
        # Get old values for updates
        old_values = None
        if not created and instance.pk in _old_values:
            old_values = _old_values.pop(instance.pk)
        
        # Determine module based on app label
        module = determine_module_from_app(sender._meta.app_label)
        
        # Check if this is financial data
        is_financial = is_financial_model(sender)
        
        # Determine risk level
        risk_level = determine_risk_level_for_model(sender, action)
        
        # Create description
        description = create_model_description(instance, action, sender)
        
        # Create audit log
        audit_log = AuditLog.objects.create(
            user=get_current_user(),
            action=action,
            description=description,
            content_type=ContentType.objects.get_for_model(sender),
            object_id=instance.pk,
            ip_address=getattr(_thread_locals, 'ip_address', None),
            user_agent=getattr(_thread_locals, 'user_agent', None),
            session_key=getattr(_thread_locals, 'session_key', None),
            module=module,
            risk_level=risk_level,
            is_financial=is_financial,
        )
        
        # Set values using helper methods
        if old_values:
            audit_log.set_old_values(old_values)
        if new_values:
            audit_log.set_new_values(new_values)
        
        audit_log.set_metadata({
            'model_name': sender._meta.model_name,
            'app_label': sender._meta.app_label,
            'object_repr': str(instance),
        })
        audit_log.save()
        
    except Exception as e:
        # Don't let audit logging break the application
        import logging
        logger = logging.getLogger('audit')
        logger.error(f"Failed to log model save for {sender}: {str(e)}")

@receiver(post_delete)
def log_model_delete(sender, instance, **kwargs):
    """Log model deletions."""
    # Skip audit log models to prevent infinite loops
    if sender._meta.app_label == 'audit':
        return
    
    # Skip if this is a migration or fixture loading
    if kwargs.get('raw', False):
        return
    
    try:
        # Get all field values before deletion
        old_values = {}
        for field in sender._meta.fields:
            if not field.name.endswith('_ptr'):  # Skip pointer fields
                old_value = getattr(instance, field.name, None)
                
                # Handle special field types
                if hasattr(old_value, 'isoformat'):  # DateTime fields
                    old_value = old_value.isoformat()
                elif hasattr(old_value, '__str__') and not isinstance(old_value, (str, int, float, bool)):
                    old_value = str(old_value)
                
                old_values[field.name] = old_value
        
        # Determine module based on app label
        module = determine_module_from_app(sender._meta.app_label)
        
        # Check if this is financial data
        is_financial = is_financial_model(sender)
        
        # Create description
        description = create_model_description(instance, 'DELETE', sender)
        
        # Create audit log
        audit_log = AuditLog.objects.create(
            user=get_current_user(),
            action='DELETE',
            description=description,
            content_type=ContentType.objects.get_for_model(sender),
            object_id=instance.pk,
            ip_address=getattr(_thread_locals, 'ip_address', None),
            user_agent=getattr(_thread_locals, 'user_agent', None),
            session_key=getattr(_thread_locals, 'session_key', None),
            module=module,
            risk_level='HIGH',  # Deletions are always high risk
            is_financial=is_financial,
        )
        
        # Set old values
        audit_log.set_old_values(old_values)
        audit_log.set_metadata({
            'model_name': sender._meta.model_name,
            'app_label': sender._meta.app_label,
            'object_repr': str(instance),
        })
        audit_log.save()
        
    except Exception as e:
        # Don't let audit logging break the application
        import logging
        logger = logging.getLogger('audit')
        logger.error(f"Failed to log model delete for {sender}: {str(e)}")

def determine_module_from_app(app_label):
    """Determine module name from Django app label."""
    app_to_module = {
        'gl': 'GENERAL_LEDGER',
        'ar': 'ACCOUNTS_RECEIVABLE', 
        'ap': 'ACCOUNTS_PAYABLE',
        'sales': 'SALES',
        'purchases': 'PURCHASES',
        'inventory': 'INVENTORY',
        'hr': 'HUMAN_RESOURCES',
        'account': 'USER_MANAGEMENT',
        'auth': 'AUTHENTICATION',
        'admin': 'ADMINISTRATION',
    }
    return app_to_module.get(app_label, 'SYSTEM')

def is_financial_model(model_class):
    """Check if this model contains financial data."""
    financial_apps = ['gl', 'ar', 'ap', 'sales', 'purchases']
    financial_models = [
        'account', 'journalentry', 'journalentryline', 'invoice', 
        'payment', 'transaction', 'balance'
    ]
    
    app_label = model_class._meta.app_label
    model_name = model_class._meta.model_name.lower()
    
    # Check if it's in a financial app
    if app_label in financial_apps:
        return True
    
    # Check if it's a financial model
    if any(fin_model in model_name for fin_model in financial_models):
        return True
    
    return False

def determine_risk_level_for_model(model_class, action):
    """Determine risk level based on model and action."""
    app_label = model_class._meta.app_label
    model_name = model_class._meta.model_name.lower()
    
    # Critical risk models
    critical_models = ['journalentry', 'account', 'payment']
    if any(crit_model in model_name for crit_model in critical_models):
        return 'CRITICAL'
    
    # High risk for financial apps and deletions
    if app_label in ['gl', 'ar', 'ap'] or action == 'DELETE':
        return 'HIGH'
    
    # Medium risk for sales/purchases
    if app_label in ['sales', 'purchases']:
        return 'MEDIUM'
    
    return 'LOW'

def create_model_description(instance, action, model_class):
    """Create human-readable description for model actions."""
    user = get_current_user()
    user_str = f"User '{user.username}'" if user else "System"
    model_name = model_class._meta.verbose_name or model_class._meta.model_name
    
    if action == 'CREATE':
        return f"{user_str} created new {model_name}: {str(instance)}"
    elif action == 'UPDATE':
        return f"{user_str} updated {model_name}: {str(instance)}"
    elif action == 'DELETE':
        return f"{user_str} deleted {model_name}: {str(instance)}"
    else:
        return f"{user_str} performed {action} on {model_name}: {str(instance)}"

# Special handlers for financial transactions
@receiver(post_save, sender='gl.JournalEntry')
def log_journal_entry_changes(sender, instance, created, **kwargs):
    """Special logging for journal entry changes."""
    if kwargs.get('raw', False):
        return
    
    try:
        action = 'CREATE' if created else 'UPDATE'
        
        # Special handling for journal entry posting
        if not created and instance.status == 'POSTED':
            action = 'POST_JOURNAL'
        
        user = get_current_user()
        description = f"Journal Entry {instance.entry_number}: {action.replace('_', ' ').title()}"
        
        audit_log = AuditLog.objects.create(
            user=user,
            action=action,
            description=description,
            content_type=ContentType.objects.get_for_model(sender),
            object_id=instance.pk,
            ip_address=getattr(_thread_locals, 'ip_address', None),
            user_agent=getattr(_thread_locals, 'user_agent', None),
            session_key=getattr(_thread_locals, 'session_key', None),
            module='GENERAL_LEDGER',
            risk_level='CRITICAL',
            is_financial=True,
        )
        
        audit_log.set_metadata({
            'entry_number': instance.entry_number,
            'transaction_date': instance.transaction_date.isoformat(),
            'amount': str(instance.get_total_debits()),
            'status': instance.status,
            'entry_type': instance.entry_type,
        })
        audit_log.save()
        
    except Exception as e:
        import logging
        logger = logging.getLogger('audit')
        logger.error(f"Failed to log journal entry change: {str(e)}") 