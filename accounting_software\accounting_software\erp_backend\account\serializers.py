from django.contrib.auth import get_user_model
from rest_framework import serializers
from .models import UserRole, UserPermission, Company

# Get Django's User model (this allows us to work with user accounts)
User = get_user_model()

class UserPermissionSerializer(serializers.ModelSerializer):
    """
    This serializer converts UserPermission objects to/from JSON format.
    It handles the module and access level information for API responses.
    """
    class Meta:
        model = UserPermission
        fields = ('module', 'access_level')

class UserRoleSerializer(serializers.ModelSerializer):
    """
    This serializer converts UserRole objects to/from JSON format.
    It only includes the role field for API responses.
    """
    class Meta:
        model = UserRole
        fields = ('role',)

class UserSerializer(serializers.ModelSerializer):
    """
    This serializer handles user account data for API operations.
    It includes password handling, role information, and user permissions.
    """
    # Password field is write-only (can be set but not read in API responses)
    password = serializers.CharField(write_only=True)
    # Role is calculated dynamically from the user's role relationship
    role = serializers.SerializerMethodField()
    # Permissions are calculated dynamically from the user's permissions
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name', 'password', 'role', 'permissions', 'is_superuser', 'is_staff')
        # These fields cannot be modified by API requests
        read_only_fields = ('id', 'is_superuser', 'is_staff')

    def get_role(self, obj):
        """Get the user's role, return None if no role is assigned"""
        try:
            return obj.role.role
        except UserRole.DoesNotExist:
            return None

    def get_permissions(self, obj):
        """Get all permissions for this user as a list"""
        permissions = obj.permissions.all()
        return UserPermissionSerializer(permissions, many=True).data

    def create(self, validated_data):
        """Create a new user with role and permissions"""
        # Extract role and permissions from the data
        role = validated_data.pop('role', None)
        permissions = validated_data.pop('permissions', [])

        # Create the user account with encrypted password
        user = User.objects.create_user(
            username=validated_data['username'],
            email=validated_data.get('email', ''),
            password=validated_data['password'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', '')
        )

        # Assign role to the user if provided
        if role:
            UserRole.objects.create(user=user, role=role)

        # Create permissions for the user if provided
        for perm in permissions:
            UserPermission.objects.create(
                user=user,
                module=perm['module'],
                access_level=perm['access_level']
            )

        return user

    def update(self, instance, validated_data):
        """Update an existing user's information, role, and permissions"""
        # Extract role and permissions from the data
        role = validated_data.pop('role', None)
        permissions = validated_data.pop('permissions', None)

        # Update basic user information
        for attr, value in validated_data.items():
            if attr == 'password':
                # Encrypt the password before saving
                instance.set_password(value)
            else:
                # Update other fields directly
                setattr(instance, attr, value)
        instance.save()

        # Update or create user role if provided
        if role is not None:
            UserRole.objects.update_or_create(
                user=instance,
                defaults={'role': role}
            )

        # Update permissions if provided
        if permissions is not None:
            # Remove all existing permissions
            instance.permissions.all().delete()
            # Create new permissions based on the provided data
            for perm in permissions:
                UserPermission.objects.create(
                    user=instance,
                    module=perm['module'],
                    access_level=perm['access_level']
                )

        return instance

class CompanySerializer(serializers.ModelSerializer):
    """
    This serializer handles company data for API operations.
    It includes all company fields including the new currency settings.
    Only superusers can create or modify company information.
    """
    
    # Add display fields to show currency names alongside codes
    functional_currency_display = serializers.CharField(
        source='get_functional_currency_display', 
        read_only=True,
        help_text="Human-readable name of the functional currency"
    )
    reporting_currency_display = serializers.CharField(
        source='get_reporting_currency_display', 
        read_only=True,
        help_text="Human-readable name of the reporting currency"
    )
    
    # Add helper fields to get currency symbols
    functional_currency_symbol = serializers.SerializerMethodField()
    reporting_currency_symbol = serializers.SerializerMethodField()

    class Meta:
        model = Company
        fields = (
            # Basic company identification
            'id', 'name', 'registration_number', 'tax_id', 
            # Contact information
            'address', 'phone', 'email',
            # Currency settings (NEW FIELDS)
            'functional_currency', 'functional_currency_display', 'functional_currency_symbol',
            'reporting_currency', 'reporting_currency_display', 'reporting_currency_symbol',
            # Financial year settings
            'accounting_year_start', 'accounting_year_end',
            'fiscal_year_start', 'fiscal_year_end', 
            # Tax registration
            'sales_tax_registered', 'sales_tax_number', 
            # System timestamps
            'created_at', 'updated_at'
        )
        # These fields are automatically set by the system and cannot be modified
        read_only_fields = ('id', 'created_at', 'updated_at')

    def get_functional_currency_symbol(self, obj):
        """Get the symbol for the functional currency (e.g., '$' for USD)"""
        return obj.get_currency_symbol('functional')
    
    def get_reporting_currency_symbol(self, obj):
        """Get the symbol for the reporting currency (e.g., '€' for EUR)"""
        return obj.get_currency_symbol('reporting')

    def validate(self, data):
        """
        Custom validation method that ensures business rules are followed.
        This runs before saving the company data.
        """
        
        # Rule 1: If company is registered for sales tax, must provide tax number
        if data.get('sales_tax_registered') and not data.get('sales_tax_number'):
            raise serializers.ValidationError({
                'sales_tax_number': 'Sales tax registration number is required when company is registered for sales tax'
            })

        # Rule 2: Accounting year end date must be after start date
        if data.get('accounting_year_start') and data.get('accounting_year_end'):
            if data['accounting_year_start'] >= data['accounting_year_end']:
                raise serializers.ValidationError({
                    'accounting_year_end': 'Accounting year end date must be after the start date'
                })

        # Rule 3: Fiscal year end date must be after start date
        if data.get('fiscal_year_start') and data.get('fiscal_year_end'):
            if data['fiscal_year_start'] >= data['fiscal_year_end']:
                raise serializers.ValidationError({
                    'fiscal_year_end': 'Fiscal year end date must be after the start date'
                })

        # Rule 4: Validate currency codes are from approved list
        valid_currencies = [choice[0] for choice in Company.CURRENCY_CHOICES]
        
        if 'functional_currency' in data and data['functional_currency'] not in valid_currencies:
            raise serializers.ValidationError({
                'functional_currency': f'Functional currency must be one of: {", ".join(valid_currencies)}'
            })
            
        if 'reporting_currency' in data and data['reporting_currency'] not in valid_currencies:
            raise serializers.ValidationError({
                'reporting_currency': f'Reporting currency must be one of: {", ".join(valid_currencies)}'
            })

        return data

class CompanyCreateSerializer(serializers.ModelSerializer):
    """
    Specialized serializer for creating new companies.
    This includes additional validation to ensure only superusers can create companies.
    """
    
    class Meta:
        model = Company
        fields = (
            'name', 'registration_number', 'tax_id', 'address', 'phone', 'email',
            'functional_currency', 'reporting_currency',
            'accounting_year_start', 'accounting_year_end',
            'fiscal_year_start', 'fiscal_year_end', 
            'sales_tax_registered', 'sales_tax_number'
        )

    def validate(self, data):
        """Apply the same validation rules as the main CompanySerializer"""
        # Call the parent validation method
        return CompanySerializer().validate(data)

    def create(self, validated_data):
        """
        Create a new company record.
        Only superusers should be able to call this method.
        """
        return Company.objects.create(**validated_data) 