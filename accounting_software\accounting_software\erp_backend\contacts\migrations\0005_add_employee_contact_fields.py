# Generated by Django 4.2.21 on 2025-06-25 05:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contacts', '0004_customer_billing_city_customer_billing_country_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='employee',
            name='first_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='last_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='gender',
            field=models.CharField(blank=True, help_text='Gender (male, female, other, prefer-not-to-say)', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='mobile',
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='employee_id',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='street_address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='city',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='state',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='postal_code',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='country',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
