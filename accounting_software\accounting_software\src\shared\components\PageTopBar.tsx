import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  useTheme,
} from '@mui/material';

interface PageTopBarProps {
  title: string;
  children?: React.ReactNode;
}

/**
 * A reusable top bar component for page headers
 * @param title - The title to display in the top bar
 * @param children - Optional additional content to display in the top bar (buttons, etc.)
 */
const PageTopBar: React.FC<PageTopBarProps> = ({ title, children }) => {
  const theme = useTheme();
  
  return (
    <AppBar 
      position="static" 
      color="default" 
      elevation={1}
      sx={{
        backgroundColor: theme.palette.background.paper,
        borderBottom: `1px solid ${theme.palette.divider}`,
      }}
    >
      <Toolbar>
        <Typography 
          variant="h5" 
          component="h1" 
          sx={{ 
            flexGrow: 1,
            fontWeight: 500,
            color: theme.palette.text.primary,
          }}
        >
          {title}
        </Typography>
        {children && (
          <Box sx={{ display: 'flex', gap: 2 }}>
            {children}
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default PageTopBar; 