"""
Django management command to fix Retained Earnings by closing prior years' net income
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from gl.models import Account, JournalEntry, JournalEntryLine
from gl.views import calculate_net_income_for_period
from account.models import Company
from datetime import date
from decimal import Decimal


class Command(BaseCommand):
    help = 'Fix Retained Earnings by closing prior years net income'

    def add_arguments(self, parser):
        parser.add_argument(
            '--year',
            type=int,
            default=2024,
            help='Year to close to retained earnings (default: 2024)',
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Actually perform the closing entry (without this, it\'s a dry run)',
        )

    def handle(self, *args, **options):
        year = options['year']
        confirm = options['confirm']
        
        self.stdout.write(
            self.style.SUCCESS(f'🔧 Fixing Retained Earnings for Year {year}')
        )
        
        # Get company and retained earnings account
        company = Company.objects.first()
        if not company:
            self.stdout.write(
                self.style.ERROR('❌ No company configured in the system')
            )
            return

        retained_earnings = Account.objects.filter(
            account_name__icontains='retained earnings'
        ).first()
        
        if not retained_earnings:
            self.stdout.write(
                self.style.ERROR('❌ No Retained Earnings account found')
            )
            return

        self.stdout.write(f'🏢 Company: {company.name}')
        self.stdout.write(f'💰 Retained Earnings: {retained_earnings.account_number} - {retained_earnings.account_name}')
        self.stdout.write(f'💰 Current Balance: {retained_earnings.opening_balance}')
        
        # Use fiscal year for closing
        period_start = company.fiscal_year_start.replace(year=year)
        period_end = company.fiscal_year_end.replace(year=year)
        
        # Adjust for fiscal years that span calendar years
        if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
           (company.fiscal_year_end.month == company.fiscal_year_start.month and 
            company.fiscal_year_end.day < company.fiscal_year_start.day):
            period_end = period_end.replace(year=year + 1)
        
        self.stdout.write(f'📊 Fiscal Year {year}: {period_start} to {period_end}')
        
        # Calculate net income for the period
        try:
            net_income = calculate_net_income_for_period(period_start, period_end)
            self.stdout.write(f'💰 Net Income: {net_income:,.2f}')
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error calculating net income: {e}')
            )
            return
        
        if net_income == 0:
            self.stdout.write(
                self.style.WARNING('⚠️  Net income is zero - no closing entry needed')
            )
            return
        
        if not confirm:
            self.stdout.write(
                self.style.WARNING('🔍 DRY RUN - Use --confirm to create closing entry')
            )
            if net_income > 0:
                self.stdout.write(f'   Would Credit Retained Earnings: {abs(net_income):,.2f}')
            else:
                self.stdout.write(f'   Would Debit Retained Earnings: {abs(net_income):,.2f}')
            return
        
        # Create the closing entry
        try:
            with transaction.atomic():
                closing_entry = JournalEntry.objects.create(
                    entry_number=f'CLOSE-{year}',
                    transaction_date=period_end,
                    description=f'Year-end closing FY{year} - Net Income to Retained Earnings',
                    status='DRAFT',
                    entry_type='CLOSING',
                    currency=company.functional_currency,
                    exchange_rate=Decimal('1.00'),
                    created_by_id=1
                )
                
                # Create Income Summary account
                income_summary, created = Account.objects.get_or_create(
                    account_number='39999',
                    defaults={
                        'account_name': 'Income Summary',
                        'account_type_id': retained_earnings.account_type_id,
                        'detail_type_id': retained_earnings.detail_type_id,
                        'description': 'Temporary account for year-end closing',
                        'currency': company.functional_currency,
                        'is_active': True,
                        'opening_balance': Decimal('0.00'),
                        'created_by_id': 1
                    }
                )
                
                if net_income > 0:
                    # Profit: Dr Income Summary, Cr Retained Earnings
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        line_number=1,
                        account=income_summary,
                        description=f'FY{year} Net Income Closing',
                        debit_amount=abs(net_income),
                        credit_amount=Decimal('0.00')
                    )
                    
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        line_number=2,
                        account=retained_earnings,
                        description=f'FY{year} Net Income',
                        debit_amount=Decimal('0.00'),
                        credit_amount=abs(net_income)
                    )
                else:
                    # Loss: Dr Retained Earnings, Cr Income Summary
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        line_number=1,
                        account=retained_earnings,
                        description=f'FY{year} Net Loss',
                        debit_amount=abs(net_income),
                        credit_amount=Decimal('0.00')
                    )
                    
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        line_number=2,
                        account=income_summary,
                        description=f'FY{year} Net Loss Closing',
                        debit_amount=Decimal('0.00'),
                        credit_amount=abs(net_income)
                    )
                
                # Post the entry
                closing_entry.status = 'POSTED'
                closing_entry.posted_by_id = 1
                closing_entry.posted_at = timezone.now()
                closing_entry.save()
                
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created closing entry: {closing_entry.entry_number}')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error creating closing entry: {e}')
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(f'🎉 Retained Earnings updated with FY{year} net income!')
        ) 