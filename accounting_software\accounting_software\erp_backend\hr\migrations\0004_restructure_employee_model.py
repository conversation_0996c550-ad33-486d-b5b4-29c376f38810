# Generated by Django 4.2.21 on 2025-06-25 05:43

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


def migrate_employee_data_forward(apps, schema_editor):
    """
    Migrate employee data from old HR Employee model to new contacts-based structure
    """
    # Get model references
    OldEmployee = apps.get_model('hr', 'Employee')
    Contact = apps.get_model('contacts', 'Contact')
    ContactEmployee = apps.get_model('contacts', 'Employee')
    
    db_alias = schema_editor.connection.alias
    
    # Get all existing HR employees
    old_employees = OldEmployee.objects.using(db_alias).all()
    
    print(f"Migrating {old_employees.count()} employees to contacts structure...")
    
    for old_emp in old_employees:
        # Create Contact record
        contact = Contact.objects.using(db_alias).create(
            name=f"{old_emp.firstName} {old_emp.lastName}" if old_emp.firstName and old_emp.lastName else old_emp.email,
            contact_type='employee',
            email=old_emp.email,
            phone=old_emp.phone,
            address=old_emp.address
        )
        
        # Create ContactEmployee record
        contact_employee = ContactEmployee.objects.using(db_alias).create(
            contact=contact,
            first_name=old_emp.firstName,
            last_name=old_emp.lastName,
            date_of_birth=old_emp.dateOfBirth,
            gender=old_emp.gender,
            mobile=old_emp.phone,  # Use phone as mobile
            position=old_emp.position,
            department=old_emp.department,
            street_address=old_emp.address,
            city=old_emp.city,
            state=old_emp.state,
            postal_code=old_emp.postalCode,
            country=old_emp.country
        )
        
        print(f"Migrated employee: {contact.name}")


def migrate_employee_data_reverse(apps, schema_editor):
    """
    Reverse migration - not implemented as it would be complex
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0003_department_attendance'),
        ('contacts', '0005_add_employee_contact_fields'),
    ]

    operations = [
        # Step 1: Rename the old Employee table to keep data safe
        migrations.RunSQL(
            "ALTER TABLE hr_employee RENAME TO hr_employee_old;",
            reverse_sql="ALTER TABLE hr_employee_old RENAME TO hr_employee;"
        ),
        
        # Step 2: Create new Employee model
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('contact_employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='hr_details', serialize=False, to='contacts.employee')),
                ('join_date', models.DateField(help_text='Join Date (required)')),
                ('employment_type', models.CharField(choices=[('full-time', 'Full Time'), ('part-time', 'Part Time'), ('contract', 'Contract'), ('intern', 'Intern')], help_text='Employment Type (required)', max_length=20)),
                ('salary', models.DecimalField(decimal_places=2, help_text='Salary (required)', max_digits=10)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('On Leave', 'On Leave'), ('Terminated', 'Terminated')], default='Active', help_text='Employment Status', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'hr_employees',
            },
        ),
        
        # Step 3: Migrate data
        migrations.RunPython(
            migrate_employee_data_forward,
            migrate_employee_data_reverse
        ),
        
        # Step 4: Create new Employee records for each ContactEmployee
        migrations.RunSQL(
            """
            INSERT INTO hr_employees (contact_employee_id, join_date, employment_type, salary, status, created_at, updated_at)
            SELECT 
                ce.contact_id,
                COALESCE(oe.joinDate, '2024-01-01') as join_date,
                CASE 
                    WHEN oe.employmentType = 'full-time' THEN 'full-time'
                    WHEN oe.employmentType = 'part-time' THEN 'part-time'
                    WHEN oe.employmentType = 'contract' THEN 'contract'
                    WHEN oe.employmentType = 'intern' THEN 'intern'
                    ELSE 'full-time'
                END as employment_type,
                COALESCE(oe.salary, 0) as salary,
                COALESCE(oe.status, 'Active') as status,
                datetime('now') as created_at,
                datetime('now') as updated_at
            FROM employees ce
            LEFT JOIN hr_employee_old oe ON (
                ce.first_name = oe.firstName AND 
                ce.last_name = oe.lastName AND 
                ce.contact_id IN (
                    SELECT id FROM contacts WHERE email = oe.email
                )
            )
            WHERE ce.contact_id IN (
                SELECT id FROM contacts WHERE contact_type = 'employee'
            );
            """,
            reverse_sql="DELETE FROM hr_employees;"
        ),
        
        # Step 5: Clean up old table (optional - comment out if you want to keep backup)
        # migrations.RunSQL(
        #     "DROP TABLE hr_employee_old;",
        #     reverse_sql="-- Cannot restore dropped table"
        # ),
    ]
