import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Receipt as ReceiptIcon,
  RemoveCircle as IssueIcon,
  TrendingDown as LowStockIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { inventoryService, InventoryItem, StockTransaction } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

// Extended interface for ledger entries with running balance
interface ProductLedgerEntry {
  id: string;
  product_id: number;
  product_name: string;
  product_sku: string;
  warehouse_name: string;
  date: string;
  reference: string;
  description: string;
  receipt_qty: number;
  issue_qty: number;
  balance_qty: number;
  transaction_type: string;
  reference_type: string;
  reference_id: number;
}

// Interface for product with its complete ledger
interface ProductLedger {
  product_id: number;
  product_name: string;
  product_sku: string;
  warehouse_name: string;
  current_balance: number;
  reorder_point: number;
  low_stock_threshold: number;
  total_receipts: number;
  total_issues: number;
  last_transaction_date: string | null;
  entries: ProductLedgerEntry[];
}

const StockLevelsPage: React.FC = () => {
  const [productLedgers, setProductLedgers] = useState<ProductLedger[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [stockTransactions, setStockTransactions] = useState<StockTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState('');
  const [selectedProduct, setSelectedProduct] = useState('');

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadProductLedgers();
  }, []);

  const loadProductLedgers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading stock transactions and inventory for product ledgers...');
      
      // Load both stock transactions and inventory items
      const [transactionsResponse, inventoryResponse] = await Promise.all([
        inventoryService.getStockTransactions({
          search: searchTerm,
          warehouse: selectedWarehouse ? parseInt(selectedWarehouse) : undefined,
          product: selectedProduct ? parseInt(selectedProduct) : undefined,
        }),
        inventoryService.getInventory({
          search: searchTerm,
          warehouse: selectedWarehouse ? parseInt(selectedWarehouse) : undefined,
        })
      ]);
      
      console.log('Stock transactions response:', transactionsResponse);
      console.log('Inventory response:', inventoryResponse);
      
      const transactions = transactionsResponse?.results || transactionsResponse || [];
      const inventory = inventoryResponse?.results || inventoryResponse || [];
      
      setStockTransactions(transactions);
      setInventoryItems(inventory);
      
      // Create product ledgers with inventory info
      const ledgers = createProductLedgers(transactions, inventory);
      setProductLedgers(ledgers);
      
      console.log('Created product ledgers:', ledgers.length);
    } catch (err) {
      console.error('Error loading product ledgers:', err);
      setError(err instanceof Error ? err.message : 'Failed to load product ledgers');
    } finally {
      setLoading(false);
    }
  };

  const createProductLedgers = (transactions: StockTransaction[], inventory: InventoryItem[]): ProductLedger[] => {
    const productGroups: { [key: string]: ProductLedger } = {};
    
    // Sort transactions by date (oldest first for proper balance calculation)
    const sortedTransactions = [...transactions].sort((a, b) => {
      return new Date(a.txn_date).getTime() - new Date(b.txn_date).getTime();
    });
    
    // Initialize product groups with inventory data
    inventory.forEach((item) => {
      const key = `${item.product}-${item.warehouse}`;
      productGroups[key] = {
        product_id: item.product,
        product_name: item.product_name,
        product_sku: item.product_sku,
        warehouse_name: item.warehouse_name,
        current_balance: Number(item.quantity_on_hand || 0),
        reorder_point: Number(item.reorder_point || 0),
        low_stock_threshold: Number(item.reorder_point || 0) * 1.2, // 20% above reorder point
        total_receipts: 0,
        total_issues: 0,
        last_transaction_date: null,
        entries: [],
      };
    });
    
    // Process transactions to build ledger entries
    let runningBalances: { [key: string]: number } = {};
    
    sortedTransactions.forEach((txn) => {
      const key = `${txn.product}-${txn.warehouse}`;
      
      // Initialize product group if not exists (in case there's no inventory record)
      if (!productGroups[key]) {
        productGroups[key] = {
          product_id: txn.product,
          product_name: txn.product_name,
          product_sku: txn.product_sku,
          warehouse_name: txn.warehouse_name,
          current_balance: 0,
          reorder_point: 0,
          low_stock_threshold: 0,
          total_receipts: 0,
          total_issues: 0,
          last_transaction_date: null,
          entries: [],
        };
      }
      
      // Initialize running balance if not exists
      if (!runningBalances[key]) {
        runningBalances[key] = 0;
      }
      
      const receiptQty = Number(txn.quantity) > 0 ? Number(txn.quantity) : 0;
      const issueQty = Number(txn.quantity) < 0 ? Math.abs(Number(txn.quantity)) : 0;
      
      // Update running balance
      runningBalances[key] += receiptQty - issueQty;
      
      // Create ledger entry
      const entry: ProductLedgerEntry = {
        id: `${txn.stock_txn_id}`,
        product_id: txn.product,
        product_name: txn.product_name,
        product_sku: txn.product_sku,
        warehouse_name: txn.warehouse_name,
        date: txn.txn_date,
        reference: `${txn.reference_type}-${txn.reference_id}`,
        description: txn.description || `${txn.transaction_type} transaction`,
        receipt_qty: receiptQty,
        issue_qty: issueQty,
        balance_qty: runningBalances[key],
        transaction_type: txn.transaction_type,
        reference_type: txn.reference_type,
        reference_id: txn.reference_id,
      };
      
      productGroups[key].entries.push(entry);
      productGroups[key].total_receipts += receiptQty;
      productGroups[key].total_issues += issueQty;
      productGroups[key].last_transaction_date = txn.txn_date;
    });
    
    // Update current_balance with the final running balance from transactions
    Object.keys(productGroups).forEach(key => {
      if (runningBalances[key] !== undefined) {
        productGroups[key].current_balance = runningBalances[key];
      }
    });

    // Convert to array and sort alphabetically by product name
    return Object.values(productGroups)
      .filter(ledger => ledger.entries.length > 0 || ledger.current_balance > 0)
      .sort((a, b) => a.product_name.localeCompare(b.product_name));
  };

  const getStockStatusInfo = (ledger: ProductLedger) => {
    const { current_balance, reorder_point, low_stock_threshold } = ledger;
    
    if (current_balance === 0) {
      return { 
        status: 'Out of Stock', 
        color: 'error' as const, 
        icon: <WarningIcon />,
        severity: 'Critical'
      };
    } else if (current_balance <= reorder_point) {
      return { 
        status: 'Reorder Now', 
        color: 'error' as const, 
        icon: <LowStockIcon />,
        severity: 'High'
      };
    } else if (current_balance <= low_stock_threshold) {
      return { 
        status: 'Low Stock', 
        color: 'warning' as const, 
        icon: <LowStockIcon />,
        severity: 'Medium'
      };
    } else {
      return { 
        status: 'In Stock', 
        color: 'success' as const, 
        icon: <InventoryIcon />,
        severity: 'Normal'
      };
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'RECEIPT':
      case 'RETURN':
      case 'OPENING':
        return <ReceiptIcon color="success" fontSize="small" />;
      case 'ISSUE':
      case 'SALE':
      case 'DAMAGE':
        return <IssueIcon color="error" fontSize="small" />;
      default:
        return <InventoryIcon color="primary" fontSize="small" />;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            📊 Stock Ledgers
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Product-wise ledger showing receipts, issues, and running balances
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadProductLedgers}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>📈 Ledger Format:</strong> Each product shows reorder levels and stock status, 
          followed by chronological transaction history with receipts (green), issues (red), and running balance.
        </Typography>
      </Alert>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search products, SKUs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Warehouse</InputLabel>
                <Select
                  label="Warehouse"
                  value={selectedWarehouse}
                  onChange={(e) => setSelectedWarehouse(e.target.value)}
                >
                  <MenuItem value="">All Warehouses</MenuItem>
                  <MenuItem value="1">Main Warehouse</MenuItem>
                  <MenuItem value="2">Branch Warehouse</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Product name or SKU"
                value={selectedProduct}
                onChange={(e) => setSelectedProduct(e.target.value)}
                size="small"
                label="Filter by Product"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                onClick={loadProductLedgers}
                size="small"
              >
                Apply Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Product Ledgers */}
      {loading ? (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
          <Typography variant="body2" sx={{ ml: 2 }}>
            Loading product ledgers...
          </Typography>
        </Box>
      ) : productLedgers.length === 0 ? (
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="center" p={4}>
              <Typography variant="body1" color="text.secondary">
                No stock transactions found. {searchTerm || selectedWarehouse || selectedProduct ? 'Try adjusting your filters.' : 'No product movements recorded yet.'}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      ) : (
        <Box>
          {productLedgers.map((ledger, index) => {
            const stockInfo = getStockStatusInfo(ledger);
            
            return (
              <Card key={`${ledger.product_id}-${ledger.warehouse_name}`} sx={{ mb: 4 }}>
                <CardContent>
                  {/* Product Header */}
                  <Box sx={{ mb: 3 }}>
                    <Grid container alignItems="center" spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="h6" fontWeight="bold" color="primary.main">
                          {ledger.product_name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          SKU: {ledger.product_sku || 'N/A'} | Warehouse: {ledger.warehouse_name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Box display="flex" gap={2} flexWrap="wrap" justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                          <Chip
                            icon={stockInfo.icon}
                            label={stockInfo.status}
                            color={stockInfo.color}
                            variant="outlined"
                          />
                          <Chip
                            label={`Balance: ${ledger.current_balance.toFixed(2)}`}
                            color={ledger.current_balance > 0 ? 'success' : 'error'}
                            variant="filled"
                          />
                        </Box>
                      </Grid>
                    </Grid>
                    
                    {/* Product Stats */}
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                      <Grid container spacing={3}>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Current Balance
                          </Typography>
                          <Typography 
                            variant="body1" 
                            fontWeight="bold"
                            color={ledger.current_balance > 0 ? 'success.main' : 'error.main'}
                          >
                            {ledger.current_balance.toFixed(2)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Reorder Point
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {ledger.reorder_point.toFixed(2)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Total Receipts
                          </Typography>
                          <Typography variant="body1" fontWeight="medium" color="success.main">
                            {ledger.total_receipts.toFixed(2)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Total Issues
                          </Typography>
                          <Typography variant="body1" fontWeight="medium" color="error.main">
                            {ledger.total_issues.toFixed(2)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>

                  <Divider sx={{ mb: 2 }} />

                  {/* Transaction History */}
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    📋 Transaction History
                  </Typography>
                  
                  {ledger.entries.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                      No transactions recorded for this product
                    </Typography>
                  ) : (
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow sx={{ bgcolor: 'grey.50' }}>
                            <TableCell><strong>Date</strong></TableCell>
                            <TableCell><strong>Reference</strong></TableCell>
                            <TableCell><strong>Description</strong></TableCell>
                            <TableCell align="center"><strong>Receipt</strong></TableCell>
                            <TableCell align="center"><strong>Issue</strong></TableCell>
                            <TableCell align="center"><strong>Balance</strong></TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {ledger.entries.map((entry, entryIndex) => (
                            <TableRow key={entry.id} hover>
                              <TableCell>
                                <Typography variant="body2">
                                  {formatDate(entry.date)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Box display="flex" alignItems="center" gap={1}>
                                  {getTransactionIcon(entry.transaction_type)}
                                  <Typography variant="body2">
                                    {entry.reference}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {entry.description}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  color={entry.receipt_qty > 0 ? 'success.main' : 'text.disabled'}
                                  fontWeight={entry.receipt_qty > 0 ? 'medium' : 'normal'}
                                >
                                  {entry.receipt_qty > 0 ? entry.receipt_qty.toFixed(2) : '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  color={entry.issue_qty > 0 ? 'error.main' : 'text.disabled'}
                                  fontWeight={entry.issue_qty > 0 ? 'medium' : 'normal'}
                                >
                                  {entry.issue_qty > 0 ? entry.issue_qty.toFixed(2) : '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  fontWeight="bold"
                                  color={entry.balance_qty > 0 ? 'success.main' : entry.balance_qty === 0 ? 'text.secondary' : 'error.main'}
                                >
                                  {entry.balance_qty.toFixed(2)}
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </Box>
      )}
    </PageContainer>
  );
};

export default StockLevelsPage; 