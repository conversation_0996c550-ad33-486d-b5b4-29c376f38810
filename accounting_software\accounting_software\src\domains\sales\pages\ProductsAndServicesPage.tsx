import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Avatar,
  FormControlLabel,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemAvatar,
  ListItemSecondaryAction,
  Tooltip,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Category as CategoryIcon,
  Business as BusinessIcon,
  LocalOffer as LocalOfferIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Assignment as AssignmentIcon,
  Assessment as AssessmentIcon,
  Store as StoreIcon,
  ShoppingCart as ShoppingCartIcon,
  LocalPharmacy as LocalPharmacyIcon,
  Restaurant as RestaurantIcon,
  Warehouse as WarehouseIcon,
  RequestPage as RequestIcon,
  Info as InfoIcon,
  Lock as LockIcon,
  Receipt as ReceiptIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  PhotoCamera as PhotoCameraIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
  AccountTree as AccountTreeIcon,
  ExpandMore as ExpandMoreIcon,
  Description as DescriptionIcon,
  Settings as SettingsIcon,
  PriceChange as PriceIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import StatCard from '../../../shared/components/StatCard';
import { formatCurrency } from '../../../shared/utils/formatters';
import ProductCategoryModal from '../../../shared/components/ProductCategoryModal';
import { useProductCategories } from '../../../contexts/ProductCategoriesContext';
import { salesTaxService, SalesTaxOption } from '../../../services/sales-tax.service';

// Enhanced interfaces for retail management
interface InventoryStats {
  totalProducts: number;
  activeProducts: number;
  lowStockItems: number;
  outOfStockItems: number;
  nearExpiryItems: number;
  expiredItems: number;
  totalInventoryValue: number;
  totalCategories: number;
  totalBrands: number;
  reorderRequired: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`inventory-tabpanel-${index}`}
      aria-labelledby={`inventory-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

// Categories Tab Content Component (uses context)
const CategoriesTabContent: React.FC<{ onManageCategories: () => void }> = ({ onManageCategories }) => {
  const { categories, loading, loadCategories } = useProductCategories();

  // Load categories when component mounts or when it becomes visible
  useEffect(() => {
    if (categories.length === 0) {
      loadCategories();
    }
  }, [loadCategories]);

  const getDivisionTypeIcon = (type: string) => {
    switch (type) {
      case 'perishable': return '🥬';
      case 'refrigerated': return '🧊';
      case 'frozen': return '❄️';
      case 'controlled-substance': return '💊';
      case 'non-perishable': return '📦';
      default: return '📁';
    }
  };

  const getDivisionTypeColor = (type: string) => {
    switch (type) {
      case 'perishable': return 'success';
      case 'refrigerated': return 'info';
      case 'frozen': return 'primary';
      case 'controlled-substance': return 'warning';
      case 'non-perishable': return 'default';
      default: return 'default';
    }
  };

  return (
    <CardContent>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h6" gutterBottom>
            📂 Product Categories ({categories.length})
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Hierarchical category structure with margin management and tax configuration.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<CategoryIcon />}
          onClick={onManageCategories}
        >
          Manage Categories
        </Button>
      </Box>
      
      {loading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <Typography>Loading categories...</Typography>
        </Box>
      ) : categories.length === 0 ? (
        <Box textAlign="center" py={4}>
          <CategoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No Categories Found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Create your first product category to organize your inventory
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={onManageCategories}
          >
            Create First Category
          </Button>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {categories.map((category) => (
            <Grid item xs={12} sm={6} md={3} key={category.id}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <Typography variant="h6" component="span">
                      {getDivisionTypeIcon(category.divisionType)}
                    </Typography>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {category.name}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Code: {category.code}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary" sx={{ mb: 2, display: 'block' }}>
                    {category.description}
                  </Typography>
                  
                  <Box display="flex" gap={1} flexWrap="wrap" mb={1}>
                    <Chip 
                      size="small" 
                      label={category.divisionType.replace('-', ' ')}
                      color={getDivisionTypeColor(category.divisionType)}
                      variant="outlined"
                    />
                    {category.marginPercentage && (
                      <Chip 
                        size="small" 
                        label={`${category.marginPercentage}% margin`} 
                        color="success" 
                        variant="outlined" 
                      />
                    )}
                  </Box>
                  
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {category.requiresExpiryTracking && (
                      <Chip size="small" label="Expiry Tracking" variant="outlined" />
                    )}
                    {category.requiresBatchTracking && (
                      <Chip size="small" label="Batch Tracking" variant="outlined" />
                    )}
                    {!category.isActive && (
                      <Chip size="small" label="Inactive" color="default" />
                    )}
                  </Box>
                  
                  {(category.subcategoriesCount || category.productsCount) && (
                    <Box mt={1} display="flex" gap={1}>
                      {category.subcategoriesCount && category.subcategoriesCount > 0 && (
                        <Typography variant="caption" color="text.secondary">
                          {category.subcategoriesCount} subcategories
                        </Typography>
                      )}
                      {category.productsCount && category.productsCount > 0 && (
                        <Typography variant="caption" color="text.secondary">
                          {category.productsCount} products
                        </Typography>
                      )}
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </CardContent>
  );
};

const ProductsAndServicesPage: React.FC = () => {
  // State management
  const [currentTab, setCurrentTab] = useState(0);
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(false);
  
  // Menu states
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  
  // Modal states
  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const [requestProductDialogOpen, setRequestProductDialogOpen] = useState(false);
  const [salesEditDialogOpen, setSalesEditDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [salesTaxOptions, setSalesTaxOptions] = useState<SalesTaxOption[]>([]);
  const [loadingTaxes, setLoadingTaxes] = useState(false);

  // Load data functions
  const loadStats = useCallback(async () => {
    try {
      // Load real product statistics from API
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/sales/products/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const products = data.results || data || [];
        
        // Calculate real statistics
        const totalProducts = products.length;
        const activeProducts = products.filter((p: any) => p.status === 'active').length;
        const lowStockItems = products.filter((p: any) => p.track_inventory && p.quantity_on_hand <= p.reorder_point).length;
        const outOfStockItems = products.filter((p: any) => p.track_inventory && p.quantity_on_hand === 0).length;
        const totalInventoryValue = products.reduce((sum: number, p: any) => sum + (p.unit_price * (p.quantity_on_hand || 0)), 0);
        
        setStats({
          totalProducts,
          activeProducts,
          lowStockItems,
          outOfStockItems,
          nearExpiryItems: 0, // Would need expiry tracking
          expiredItems: 0,    // Would need expiry tracking
          totalInventoryValue,
          totalCategories: 25, // Could be calculated from categories API
          totalBrands: 85,     // Could be calculated from brands
          reorderRequired: lowStockItems,
        });
      } else {
        // Fallback to mock data if API fails
        setStats({
          totalProducts: 1250,
          activeProducts: 1180,
          lowStockItems: 45,
          outOfStockItems: 12,
          nearExpiryItems: 23,
          expiredItems: 8,
          totalInventoryValue: 125000,
          totalCategories: 25,
          totalBrands: 85,
          reorderRequired: 67,
        });
      }
    } catch (err) {
      console.error('Error loading inventory stats:', err);
      // Fallback to mock data
      setStats({
        totalProducts: 1250,
        activeProducts: 1180,
        lowStockItems: 45,
        outOfStockItems: 12,
        nearExpiryItems: 23,
        expiredItems: 8,
        totalInventoryValue: 125000,
        totalCategories: 25,
        totalBrands: 85,
        reorderRequired: 67,
      });
    }
  }, []);

  // Load products from API
  const loadProducts = useCallback(async () => {
    setLoadingProducts(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/sales/products/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const productsData = data.results || data || [];
        console.log('✅ Loaded products from API:', productsData);
        setProducts(productsData);
      } else {
        console.warn('⚠️ Failed to load products from API');
        setError('Failed to load products from server');
      }
    } catch (err) {
      console.error('❌ Error loading products:', err);
      setError('Failed to connect to server');
    } finally {
      setLoadingProducts(false);
    }
  }, []);

  // Load sales tax options
  const loadSalesTaxOptions = useCallback(async () => {
    try {
      setLoadingTaxes(true);
      const taxOptions = await salesTaxService.getAllSalesTaxes();
      // Filter for output taxes only (for sales)
      const outputTaxes = taxOptions.filter(tax => tax.tax_type === 'output');
      setSalesTaxOptions(outputTaxes);
    } catch (error) {
      console.error('Error loading sales tax options:', error);
      // Set default options if API fails
      setSalesTaxOptions([
        { id: 1, tax_type: 'output', description: 'Zero Rate GST', rate: 0 },
        { id: 2, tax_type: 'output', description: 'Standard Rate GST', rate: 10 },
        { id: 3, tax_type: 'output', description: 'Reduced Rate GST', rate: 55 },
      ]);
    } finally {
      setLoadingTaxes(false);
    }
  }, []);

  // Load all data on mount
  useEffect(() => {
    loadStats();
    loadProducts();
    loadSalesTaxOptions();
  }, [loadStats, loadProducts, loadSalesTaxOptions]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Handle menu actions
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle request new product
  const handleRequestNewProduct = () => {
    setRequestProductDialogOpen(true);
  };

  // Handle edit sales fields
  const handleEditSalesFields = (product: any) => {
    setSelectedProduct(product);
    setSalesEditDialogOpen(true);
  };

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4">
          🛒 Sales Products & Services (Sales Department)
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Chip 
            label="Sales Department Access" 
            color="primary" 
            variant="outlined"
            icon={<ReceiptIcon />}
          />
          <Button
            variant="outlined"
            startIcon={<AssessmentIcon />}
            size="small"
          >
            Sales Reports
          </Button>
          <Button
            variant="contained"
            startIcon={<RequestIcon />}
            onClick={handleRequestNewProduct}
          >
            Request New Product
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Sales Department Access Notice */}
      <Alert severity="info" sx={{ mb: 2 }}>
        <Typography variant="body2">
          <strong>🛒 Sales Department Access:</strong> You can view all products and edit sales-related fields only 
          (selling price, tax rates, discounts). Product creation and inventory management is handled by the Purchase Department. 
          Use "Request New Product" to submit product requests.
        </Typography>
      </Alert>

      {/* Workflow Notifications */}
      <Alert severity="warning" sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="body2">
              <strong>🔄 Pending Products from Purchase Department:</strong> 3 products are waiting for your sales configuration.
              These products need selling prices, tax rates, and customer pricing policies before they can be activated.
            </Typography>
          </Box>
          <Button
            variant="contained"
            size="small"
            startIcon={<AssignmentIcon />}
            onClick={() => setCurrentTab(4)} // Switch to pending products tab
          >
            Review Pending (3)
          </Button>
        </Box>
      </Alert>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Products"
              value={stats.totalProducts}
              icon={<InventoryIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Low Stock Items"
              value={stats.lowStockItems}
              icon={<WarningIcon />}
              color="warning"
              trend={stats.lowStockItems > 0 ? -stats.lowStockItems : undefined}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Inventory Value"
              value={formatCurrency(stats.totalInventoryValue, 'USD')}
              icon={<TrendingUpIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Reorder Required"
              value={stats.reorderRequired}
              icon={<LocalOfferIcon />}
              color="info"
            />
          </Grid>
        </Grid>
      )}

      {/* Main Content Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange} aria-label="inventory management tabs">
            <Tab 
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <InventoryIcon />
                  Products
                  <Badge badgeContent={stats?.totalProducts || 0} color="primary" />
                </Box>
              } 
            />
            <Tab 
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <CategoryIcon />
                  Divisions
                  <Badge badgeContent={25} color="secondary" />
                </Box>
              } 
            />
            <Tab 
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <CategoryIcon />
                  Categories
                  <Badge badgeContent={stats?.totalCategories || 0} color="secondary" />
                </Box>
              } 
            />
            <Tab 
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <BusinessIcon />
                  Brands
                  <Badge badgeContent={stats?.totalBrands || 0} color="secondary" />
                </Box>
              } 
            />
            <Tab 
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <AssignmentIcon />
                  Pending Products
                  <Badge badgeContent={3} color="warning" />
                </Box>
              } 
            />
          </Tabs>
        </Box>

        {/* Products Tab */}
        <TabPanel value={currentTab} index={0}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  🛍️ Sales Product Catalog (Sales Fields Only)
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  View all products and edit sales-related fields: pricing, tax rates, and discounts.
                </Typography>
              </Box>
              <Chip 
                label="Sales Edit Mode" 
                color="primary" 
                variant="outlined"
                icon={<LockIcon />}
              />
            </Box>
            
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search products..."
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} md={8}>
                <Box display="flex" gap={1} justifyContent="flex-end">
                  <Button
                    variant="outlined"
                    startIcon={<FilterIcon />}
                    size="small"
                  >
                    Filter by Category
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    size="small"
                  >
                    Refresh
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<RequestIcon />}
                    onClick={handleRequestNewProduct}
                  >
                    Request New Product
                  </Button>
                </Box>
              </Grid>
            </Grid>

            {/* Real Product Cards from Database */}
            {loadingProducts ? (
              <Box display="flex" justifyContent="center" py={4}>
                <Typography>Loading products from database...</Typography>
              </Box>
            ) : products.length === 0 ? (
              <Box textAlign="center" py={4}>
                <InventoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  No Products Found
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  No products have been created by the Purchase Department yet.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<RequestIcon />}
                  onClick={handleRequestNewProduct}
                >
                  Request New Product
                </Button>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {products.map((product) => (
                  <Grid item xs={12} sm={6} md={4} key={product.id}>
                    <Card variant="outlined" sx={{ height: '100%' }}>
                      <CardContent>
                        <Box display="flex" alignItems="center" gap={2} mb={2}>
                          <Typography variant="h4">
                            {product.product_type === 'service' ? '🔧' : 
                             product.category?.name?.toLowerCase().includes('food') ? '🍽️' :
                             product.category?.name?.toLowerCase().includes('tech') ? '💻' :
                             product.category?.name?.toLowerCase().includes('dairy') ? '🥛' :
                             product.category?.name?.toLowerCase().includes('grain') ? '🌾' : '📦'}
                          </Typography>
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {product.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              SKU: {product.sku || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                        
                        <Box display="flex" gap={1} mb={2}>
                          <Chip 
                            label={product.category?.name || 'Uncategorized'} 
                            size="small" 
                            variant="outlined" 
                          />
                          <Chip 
                            label={product.track_inventory && product.quantity_on_hand <= product.reorder_point ? 'Low Stock' : 
                                   product.track_inventory && product.quantity_on_hand === 0 ? 'Out of Stock' : 'In Stock'} 
                            size="small" 
                            color={
                              product.track_inventory && product.quantity_on_hand === 0 ? 'error' :
                              product.track_inventory && product.quantity_on_hand <= product.reorder_point ? 'warning' : 'success'
                            }
                          />
                        </Box>
                        
                        {/* Sales-Editable Fields */}
                        <Box sx={{ bgcolor: 'primary.50', p: 1, borderRadius: 1, mb: 2 }}>
                          <Typography variant="caption" color="primary" fontWeight="bold">
                            SALES EDITABLE FIELDS
                          </Typography>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                            <Typography variant="body2">Selling Price:</Typography>
                            <Typography variant="h6" color={product.unit_price > 0 ? "primary" : "warning.main"}>
                              ${product.unit_price || 'Not Set'}
                            </Typography>
                          </Box>
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="body2">Tax Category:</Typography>
                            <Typography variant="body2" color={product.sales_tax_category ? "primary" : "warning.main"}>
                              {product.sales_tax_category || 'Not Set'}
                            </Typography>
                          </Box>
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="body2">Income Account:</Typography>
                            <Typography variant="body2" color={product.income_account ? "primary" : "warning.main"}>
                              {product.income_account || 'Not Set'}
                            </Typography>
                          </Box>
                        </Box>

                        {/* Read-Only Fields */}
                        <Box sx={{ bgcolor: 'grey.50', p: 1, borderRadius: 1, mb: 2 }}>
                          <Typography variant="caption" color="text.secondary" fontWeight="bold">
                            READ-ONLY (Purchase Dept)
                          </Typography>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                            <Typography variant="body2" color="text.secondary">Cost Price:</Typography>
                            <Typography variant="body2" color="text.secondary">
                              ${product.cost_price || 'N/A'}
                            </Typography>
                          </Box>
                          {product.track_inventory && (
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography variant="body2" color="text.secondary">Stock:</Typography>
                              <Typography variant="body2" color="text.secondary">
                                {product.quantity_on_hand || 0} units
                              </Typography>
                            </Box>
                          )}
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="body2" color="text.secondary">Vendor:</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {product.preferred_vendor || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>

                        {/* Action Buttons */}
                        <Box display="flex" gap={1}>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<VisibilityIcon />}
                            fullWidth
                          >
                            View Details
                          </Button>
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<EditIcon />}
                            onClick={() => handleEditSalesFields(product)}
                            color="primary"
                          >
                            Edit Sales Fields
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            <Alert severity="warning" sx={{ mt: 3 }}>
              <Typography variant="body2">
                <strong>⚠️ Sales Department Restrictions:</strong> You can only edit selling price, tax rates, and discount fields. 
                Product creation, cost price, and inventory management requires Purchase Department access.
              </Typography>
            </Alert>
          </CardContent>
        </TabPanel>

        {/* Divisions Tab */}
        <TabPanel value={currentTab} index={1}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🏢 Product Divisions
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Organize products by business type: Perishable, Non-Perishable, Frozen, Refrigerated, and Controlled Substances.
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <ShoppingCartIcon color="warning" />
                      <Typography variant="h6">Fresh Produce</Typography>
                    </Box>
                    <Chip size="small" label="Perishable" color="warning" sx={{ mb: 1 }} />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Fresh fruits and vegetables requiring refrigeration and expiry tracking.
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      <Chip size="small" label="Expiry Tracking" variant="outlined" />
                      <Chip size="small" label="Batch Tracking" variant="outlined" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <StoreIcon color="success" />
                      <Typography variant="h6">Packaged Foods</Typography>
                    </Box>
                    <Chip size="small" label="Non-Perishable" color="success" sx={{ mb: 1 }} />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Canned and packaged food items with extended shelf life.
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      <Chip size="small" label="Expiry Tracking" variant="outlined" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} sm={6} md={4}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={1} mb={2}>
                      <LocalPharmacyIcon color="error" />
                      <Typography variant="h6">Pharmaceuticals</Typography>
                    </Box>
                    <Chip size="small" label="Controlled Substance" color="error" sx={{ mb: 1 }} />
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Regulated pharmaceutical products requiring special handling.
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      <Chip size="small" label="Prescription Required" variant="outlined" />
                      <Chip size="small" label="Batch Tracking" variant="outlined" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </CardContent>
        </TabPanel>

        {/* Categories Tab */}
        <TabPanel value={currentTab} index={2}>
          <CategoriesTabContent onManageCategories={() => setCategoryModalOpen(true)} />
        </TabPanel>

        {/* Brands Tab */}
        <TabPanel value={currentTab} index={3}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🏷️ Product Brands
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              Manage product brands and manufacturers for better organization and supplier tracking.
            </Typography>
            
            <Grid container spacing={2}>
              {['Local Farms', 'Premium Choice', 'Organic Valley', 'Fresh Market', 'Daily Essentials'].map((brand, index) => (
                <Grid item xs={12} sm={6} md={4} key={brand}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <Box 
                          sx={{ 
                            width: 40, 
                            height: 40, 
                            bgcolor: 'grey.200', 
                            borderRadius: 1,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          <BusinessIcon color="disabled" />
                        </Box>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="medium">
                            {brand}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {brand.replace(/\s+/g, '').substring(0, 3).toUpperCase()}
                          </Typography>
                        </Box>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {index % 2 === 0 ? 'Local supplier' : 'Premium brand'} with quality products
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </TabPanel>

        {/* Pending Products Tab */}
        <TabPanel value={currentTab} index={4}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  🔄 Pending Products from Purchase Department
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Products created by Purchase Department waiting for sales configuration.
                </Typography>
              </Box>
              <Chip 
                label="3 Pending Review" 
                color="warning" 
                variant="outlined"
                icon={<AssignmentIcon />}
              />
            </Box>

            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                <strong>🔄 Workflow Process:</strong> These products have been created by the Purchase Department with cost pricing and inventory setup. 
                You need to configure selling prices, tax rates, customer pricing policies, and sales MOQ before they can be activated for sale.
              </Typography>
            </Alert>
            
            {/* Pending Products List */}
            <Grid container spacing={3}>
              {[
                {
                  id: 'PUR-001',
                  name: 'Premium Coffee Beans 1kg',
                  sku: 'COF-PRE-001',
                  category: 'Beverages',
                  costPrice: 450,
                  purchaseMOQ: 20,
                  createdBy: 'Purchase Dept',
                  createdDate: '2024-01-15',
                  status: 'pending_sales_review',
                  aiMOQ: 25,
                  manualMOQ: 20,
                  finalMOQ: 25,
                  workflowNotes: 'Product created with AI MOQ: 25 units. Pending Sales Department review for pricing and tax configuration.'
                },
                {
                  id: 'PUR-002',
                  name: 'Organic Honey 500ml',
                  sku: 'HON-ORG-002',
                  category: 'Natural Products',
                  costPrice: 180,
                  purchaseMOQ: 15,
                  createdBy: 'Purchase Dept',
                  createdDate: '2024-01-14',
                  status: 'pending_sales_review',
                  aiMOQ: 18,
                  manualMOQ: 15,
                  finalMOQ: 18,
                  workflowNotes: 'Product created with AI MOQ: 18 units. Pending Sales Department review for pricing and tax configuration.'
                },
                {
                  id: 'PUR-003',
                  name: 'Artisan Bread Mix 2kg',
                  sku: 'BRD-ART-003',
                  category: 'Bakery Supplies',
                  costPrice: 120,
                  purchaseMOQ: 30,
                  createdBy: 'Purchase Dept',
                  createdDate: '2024-01-13',
                  status: 'pending_sales_review',
                  aiMOQ: 35,
                  manualMOQ: 30,
                  finalMOQ: 35,
                  workflowNotes: 'Product created with AI MOQ: 35 units. Pending Sales Department review for pricing and tax configuration.'
                }
              ].map((product) => (
                <Grid item xs={12} key={product.id}>
                  <Card variant="outlined" sx={{ border: '2px solid', borderColor: 'warning.main' }}>
                    <CardContent>
                      <Box display="flex" justifyContent="between" alignItems="start" mb={2}>
                        <Box sx={{ flex: 1 }}>
                          <Box display="flex" alignItems="center" gap={2} mb={1}>
                            <Typography variant="h6" fontWeight="bold">
                              {product.name}
                            </Typography>
                            <Chip 
                              label="Pending Sales Review" 
                              color="warning" 
                              size="small"
                              icon={<AssignmentIcon />}
                            />
                          </Box>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            SKU: {product.sku} | Category: {product.category} | Created: {product.createdDate}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {product.workflowNotes}
                          </Typography>
                        </Box>
                        <Button
                          variant="contained"
                          color="warning"
                          startIcon={<EditIcon />}
                          onClick={() => handleEditSalesFields(product)}
                        >
                          Configure Sales
                        </Button>
                      </Box>

                      <Grid container spacing={2}>
                        {/* Purchase Department Data (Read-Only) */}
                        <Grid item xs={12} md={6}>
                          <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                              🛒 PURCHASE DEPARTMENT DATA (READ-ONLY)
                            </Typography>
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography variant="body2">Cost Price:</Typography>
                              <Typography variant="body2" fontWeight="medium">${product.costPrice}</Typography>
                            </Box>
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography variant="body2">AI MOQ Recommendation:</Typography>
                              <Typography variant="body2" fontWeight="medium">{product.aiMOQ} units</Typography>
                            </Box>
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography variant="body2">Manual MOQ Input:</Typography>
                              <Typography variant="body2" fontWeight="medium">{product.manualMOQ} units</Typography>
                            </Box>
                            <Box display="flex" justifyContent="space-between">
                              <Typography variant="body2">Final MOQ (Higher Value):</Typography>
                              <Typography variant="body2" fontWeight="bold" color="success.main">
                                {product.finalMOQ} units
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>

                        {/* Sales Configuration Needed */}
                        <Grid item xs={12} md={6}>
                          <Box sx={{ bgcolor: 'warning.50', p: 2, borderRadius: 1 }}>
                            <Typography variant="subtitle2" color="warning.main" gutterBottom>
                              💰 SALES CONFIGURATION NEEDED
                            </Typography>
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography variant="body2">Selling Price:</Typography>
                              <Typography variant="body2" color="warning.main">Not Set</Typography>
                            </Box>
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography variant="body2">Tax Rate (GST/VAT):</Typography>
                              <Typography variant="body2" color="warning.main">Not Set</Typography>
                            </Box>
                            <Box display="flex" justifyContent="space-between" mb={1}>
                              <Typography variant="body2">Customer MOQ:</Typography>
                              <Typography variant="body2" color="warning.main">Not Set</Typography>
                            </Box>
                            <Box display="flex" justifyContent="space-between">
                              <Typography variant="body2">Discount Policy:</Typography>
                              <Typography variant="body2" color="warning.main">Not Set</Typography>
                            </Box>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Workflow Instructions */}
            <Alert severity="success" sx={{ mt: 3 }}>
              <Typography variant="body2">
                <strong>✅ Next Steps:</strong> Click "Configure Sales" on any product to set up selling prices, tax rates, and customer policies. 
                Once configured, the product will be activated and available in your sales catalog.
              </Typography>
            </Alert>
          </CardContent>
        </TabPanel>
      </Card>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <RequestIcon fontSize="small" color="primary" />
          </ListItemIcon>
          <ListItemText>Request Edit</ListItemText>
        </MenuItem>
      </Menu>

      {/* Product Category Modal */}
      <ProductCategoryModal
        open={categoryModalOpen}
        onClose={() => setCategoryModalOpen(false)}
      />

      {/* Request New Product Dialog */}
      <Dialog 
        open={requestProductDialogOpen} 
        onClose={() => setRequestProductDialogOpen(false)} 
        maxWidth={false}
        fullWidth
        PaperProps={{
          sx: {
            width: 'calc(100vw - 1in)',
            height: 'calc(100vh - 1in)',
            maxWidth: 'none',
            maxHeight: 'none',
            margin: '0.5in',
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5" fontWeight="bold">
              🛒 Request New Product (Sales Department)
            </Typography>
            <IconButton onClick={() => setRequestProductDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers sx={{ height: 'calc(100vh - 200px)', overflow: 'auto' }}>
          <Alert severity="info" sx={{ mb: 4 }}>
            <Typography variant="body1">
              <strong>🛒 Sales Department Request:</strong> Fill out this comprehensive form to request a new product. 
              The Purchase Department will review and create the product with proper costing and inventory setup.
            </Typography>
          </Alert>
          
                     <Grid container spacing={4}>
             {/* Basic Product Information */}
             <Grid item xs={12}>
               <Typography variant="h6" color="primary" gutterBottom>
                 📦 Basic Product Information
               </Typography>
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Product Name *"
                 placeholder="e.g., Organic Quinoa 500g"
                 variant="outlined"
                 size="medium"
               />
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Brand Name"
                 placeholder="e.g., Nature's Best"
                 variant="outlined"
                 size="medium"
               />
             </Grid>
             <Grid item xs={12} md={4}>
               <FormControl fullWidth size="medium">
                 <InputLabel>Product Category *</InputLabel>
                 <Select label="Product Category *">
                   <MenuItem value="grains">Grains & Cereals</MenuItem>
                   <MenuItem value="dairy">Dairy Products</MenuItem>
                   <MenuItem value="produce">Fresh Produce</MenuItem>
                   <MenuItem value="beverages">Beverages</MenuItem>
                   <MenuItem value="snacks">Snacks & Confectionery</MenuItem>
                   <MenuItem value="frozen">Frozen Foods</MenuItem>
                   <MenuItem value="bakery">Bakery Items</MenuItem>
                   <MenuItem value="meat">Meat & Poultry</MenuItem>
                   <MenuItem value="seafood">Seafood</MenuItem>
                   <MenuItem value="household">Household Items</MenuItem>
                 </Select>
               </FormControl>
             </Grid>
             <Grid item xs={12} md={4}>
               <FormControl fullWidth size="medium">
                 <InputLabel>Product Type</InputLabel>
                 <Select label="Product Type">
                   <MenuItem value="perishable">Perishable</MenuItem>
                   <MenuItem value="non-perishable">Non-Perishable</MenuItem>
                   <MenuItem value="frozen">Frozen</MenuItem>
                   <MenuItem value="refrigerated">Refrigerated</MenuItem>
                 </Select>
               </FormControl>
             </Grid>
             <Grid item xs={12} md={4}>
               <TextField
                 fullWidth
                 label="Package Size/Weight"
                 placeholder="e.g., 500g, 1L, 12 pieces"
                 variant="outlined"
                 size="medium"
               />
             </Grid>
             
             {/* Product Description */}
             <Grid item xs={12}>
               <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 2 }}>
                 📝 Product Description
               </Typography>
             </Grid>
             <Grid item xs={12}>
               <TextField
                 fullWidth
                 label="Detailed Product Description *"
                 multiline
                 rows={4}
                 placeholder="Provide a comprehensive description including ingredients, features, benefits, usage instructions, etc."
                 variant="outlined"
               />
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Key Features"
                 multiline
                 rows={3}
                 placeholder="• Organic certified&#10;• Gluten-free&#10;• High protein content"
                 variant="outlined"
               />
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Target Customer Segment"
                 multiline
                 rows={3}
                 placeholder="Health-conscious consumers, fitness enthusiasts, families with dietary restrictions, etc."
                 variant="outlined"
               />
             </Grid>

             {/* Pricing Information */}
             <Grid item xs={12}>
               <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 2 }}>
                 💰 Pricing & Tax Information
               </Typography>
             </Grid>
             <Grid item xs={12} md={4}>
               <TextField
                 fullWidth
                 label="Suggested Selling Price *"
                 type="number"
                 InputProps={{
                   startAdornment: <InputAdornment position="start">${}</InputAdornment>,
                 }}
                 variant="outlined"
                 size="medium"
               />
             </Grid>
             <Grid item xs={12} md={4}>
               <FormControl fullWidth size="medium">
                 <InputLabel>Tax Rate *</InputLabel>
                 <Select 
                   label="Tax Rate *" 
                   defaultValue={salesTaxOptions.length > 0 ? salesTaxOptions[0].rate.toString() : "0"}
                   disabled={loadingTaxes}
                 >
                   {salesTaxOptions.map((tax) => (
                     <MenuItem key={tax.id} value={tax.rate.toString()}>
                       {tax.description} ({tax.rate}%)
                     </MenuItem>
                   ))}
                   {salesTaxOptions.length === 0 && (
                     <MenuItem value="0">No Tax (0%)</MenuItem>
                   )}
                 </Select>
               </FormControl>
             </Grid>
             <Grid item xs={12} md={4}>
               <TextField
                 fullWidth
                 label="Competitor Price Range"
                 placeholder="$45 - $55"
                 variant="outlined"
                 size="medium"
               />
             </Grid>

             {/* Market Information */}
             <Grid item xs={12}>
               <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 2 }}>
                 📊 Market & Business Information
               </Typography>
             </Grid>
             <Grid item xs={12}>
               <TextField
                 fullWidth
                 label="Business Justification *"
                 multiline
                 rows={3}
                 placeholder="Why is this product needed? Market demand, customer requests, seasonal trends, competitive advantage, etc."
                 variant="outlined"
               />
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Expected Monthly Sales Volume"
                 type="number"
                 placeholder="e.g., 500 units"
                 variant="outlined"
                 size="medium"
               />
             </Grid>
             <Grid item xs={12} md={6}>
               <FormControl fullWidth size="medium">
                 <InputLabel>Priority Level</InputLabel>
                 <Select label="Priority Level">
                   <MenuItem value="urgent">🔴 Urgent (Launch ASAP)</MenuItem>
                   <MenuItem value="high">🟡 High (Within 2 weeks)</MenuItem>
                   <MenuItem value="medium">🟢 Medium (Within 1 month)</MenuItem>
                   <MenuItem value="low">⚪ Low (When convenient)</MenuItem>
                 </Select>
               </FormControl>
             </Grid>

             {/* Additional Information */}
             <Grid item xs={12}>
               <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 2 }}>
                 📋 Additional Information
               </Typography>
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Preferred Suppliers (if any)"
                 multiline
                 rows={2}
                 placeholder="Suggest any known suppliers or manufacturers"
                 variant="outlined"
               />
             </Grid>
             <Grid item xs={12} md={6}>
               <TextField
                 fullWidth
                 label="Special Requirements"
                 multiline
                 rows={2}
                 placeholder="Storage requirements, certifications needed, etc."
                 variant="outlined"
               />
             </Grid>
             <Grid item xs={12}>
               <TextField
                 fullWidth
                 label="Additional Notes & Comments"
                 multiline
                 rows={3}
                 placeholder="Any other information that would help the Purchase Department in sourcing and setting up this product..."
                 variant="outlined"
               />
             </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={() => setRequestProductDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            startIcon={<RequestIcon />}
            onClick={() => {
              // Handle request submission
              alert('Product request submitted to Purchase Department!');
              setRequestProductDialogOpen(false);
            }}
          >
            Submit Request
          </Button>
        </DialogActions>
      </Dialog>

      {/* Sales Fields Edit Dialog */}
      <Dialog 
        open={salesEditDialogOpen} 
        onClose={() => setSalesEditDialogOpen(false)} 
        maxWidth={false}
        fullWidth
        PaperProps={{
          sx: {
            width: 'calc(100vw - 1in)',
            height: 'calc(100vh - 1in)',
            maxWidth: 'none',
            maxHeight: 'none',
            margin: '0.5in',
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h5" fontWeight="bold">
              🛒 Edit Sales Fields
            </Typography>
            <IconButton onClick={() => setSalesEditDialogOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers sx={{ height: 'calc(100vh - 200px)', overflow: 'auto' }}>
          {selectedProduct && (
            <>
              <Alert severity="info" sx={{ mb: 4 }}>
                <Typography variant="body1">
                  <strong>🛒 Sales Department Access:</strong> You can only edit selling price, tax rates, and discount fields. 
                  Other product details are managed by the Purchase Department.
                </Typography>
              </Alert>
              
              <Box sx={{ mb: 4, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="h5" gutterBottom>
                  {selectedProduct.image} {selectedProduct.name}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  SKU: {selectedProduct.sku} | Category: {selectedProduct.category}
                </Typography>
              </Box>

              <Grid container spacing={4}>
                {/* Sales Editable Fields Section */}
                <Grid item xs={12}>
                  <Typography variant="h6" color="primary" gutterBottom>
                    💰 Sales Editable Fields
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Selling Price *"
                    type="number"
                    defaultValue={selectedProduct.sellingPrice}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">${}</InputAdornment>,
                    }}
                    variant="outlined"
                    size="medium"
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth size="medium">
                    <InputLabel>Tax Rate *</InputLabel>
                    <Select 
                      label="Tax Rate *" 
                      defaultValue={salesTaxOptions.length > 0 ? salesTaxOptions[0].rate.toString() : "0"}
                      disabled={loadingTaxes}
                    >
                      {salesTaxOptions.map((tax) => (
                        <MenuItem key={tax.id} value={tax.rate.toString()}>
                          {tax.description} ({tax.rate}%)
                        </MenuItem>
                      ))}
                      {salesTaxOptions.length === 0 && (
                        <MenuItem value="0">No Tax (0%)</MenuItem>
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Discount Percentage"
                    type="number"
                    defaultValue={0}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">%</InputAdornment>,
                    }}
                    variant="outlined"
                    size="medium"
                  />
                </Grid>

                {/* Additional Sales Fields */}
                <Grid item xs={12}>
                  <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 2 }}>
                    🎯 Sales Configuration
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Minimum Order Quantity"
                    type="number"
                    defaultValue={1}
                    variant="outlined"
                    size="medium"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Maximum Discount Allowed"
                    type="number"
                    defaultValue={10}
                    InputProps={{
                      endAdornment: <InputAdornment position="end">%</InputAdornment>,
                    }}
                    variant="outlined"
                    size="medium"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth size="medium">
                    <InputLabel>Sales Unit</InputLabel>
                    <Select label="Sales Unit" defaultValue="piece">
                      <MenuItem value="piece">Piece</MenuItem>
                      <MenuItem value="kg">Kilogram</MenuItem>
                      <MenuItem value="liter">Liter</MenuItem>
                      <MenuItem value="box">Box</MenuItem>
                      <MenuItem value="pack">Pack</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth size="medium">
                    <InputLabel>Customer Type Pricing</InputLabel>
                    <Select label="Customer Type Pricing" defaultValue="standard">
                      <MenuItem value="standard">Standard Pricing</MenuItem>
                      <MenuItem value="wholesale">Wholesale Pricing</MenuItem>
                      <MenuItem value="retail">Retail Pricing</MenuItem>
                      <MenuItem value="premium">Premium Pricing</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {/* Sales Notes */}
                <Grid item xs={12}>
                  <Typography variant="h6" color="primary" gutterBottom sx={{ mt: 2 }}>
                    📝 Sales Notes & Instructions
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Sales Notes"
                    multiline
                    rows={4}
                    placeholder="Any sales-specific notes, selling points, customer instructions, promotional information, etc."
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Promotional Text"
                    multiline
                    rows={3}
                    placeholder="Special offers, seasonal promotions, bundle deals, etc."
                    variant="outlined"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Customer Benefits"
                    multiline
                    rows={3}
                    placeholder="Key selling points, health benefits, quality features, etc."
                    variant="outlined"
                  />
                </Grid>
              </Grid>

              {/* Read-Only Information */}
              <Box sx={{ mt: 4, p: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  🔒 Read-Only Information (Purchase Department)
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Cost Price:
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      ${selectedProduct.costPrice}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Current Stock:
                    </Typography>
                    <Typography variant="h6" color="text.secondary">
                      {selectedProduct.stock} units
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Profit Margin:
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      {Math.round(((selectedProduct.sellingPrice - selectedProduct.costPrice) / selectedProduct.costPrice) * 100)}%
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Status:
                    </Typography>
                    <Typography variant="h6" color={selectedProduct.status === 'In Stock' ? 'success.main' : 'warning.main'}>
                      {selectedProduct.status}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={() => setSalesEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained" 
            startIcon={<EditIcon />}
            onClick={() => {
              // Handle sales fields update
              alert('Sales fields updated successfully!');
              setSalesEditDialogOpen(false);
            }}
          >
            Update Sales Fields
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default ProductsAndServicesPage;
