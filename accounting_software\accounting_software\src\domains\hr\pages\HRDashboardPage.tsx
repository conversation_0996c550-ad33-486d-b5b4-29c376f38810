import React, { useEffect, useState } from 'react';
import { Grid, Paper, Typography, Box, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import { 
  People as PeopleIcon, 
  WorkOutline as WorkOutlineIcon,
  Event as EventIcon,
  AttachMoney as AttachMoneyIcon 
} from '@mui/icons-material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '100%',
}));

const StatCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '50%',
  width: 48,
  height: 48,
  marginRight: theme.spacing(2),
}));

const HRDashboardPage: React.FC = () => {
  const [employees, setEmployees] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // Fetch employees
        const employeesResponse = await fetch('http://localhost:8000/api/hr/employees/', {
          headers: {
            'Authorization': `Token ${token}`,
          },
        });
        if (!employeesResponse.ok) throw new Error('Failed to fetch employees');
        const employeesData = await employeesResponse.json();
        setEmployees(employeesData.results || employeesData);

        // Fetch departments
        const departmentsResponse = await fetch('http://localhost:8000/api/hr/departments/', {
          headers: {
            'Authorization': `Token ${token}`,
          },
        });
        if (!departmentsResponse.ok) throw new Error('Failed to fetch departments');
        const departmentsData = await departmentsResponse.json();
        setDepartments(departmentsData.results || departmentsData);
      } catch (err: any) {
        setError(err.message || 'Error fetching data');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  // KPIs
  const totalEmployees = employees.length;
  const activeEmployees = employees.filter(e => e.status === 'Active').length;
  const onLeaveEmployees = employees.filter(e => e.status === 'On Leave').length;
  const terminatedEmployees = employees.filter(e => e.status === 'Terminated').length;
  const totalSalary = employees.reduce((sum, e) => sum + (parseFloat(e.salary) || 0), 0);
  const totalDepartments = departments.length;
  const activeDepartments = departments.filter(d => d.is_active).length;

  // By department
  const departmentCounts: Record<string, number> = {};
  employees.forEach(e => {
    if (!departmentCounts[e.department]) departmentCounts[e.department] = 0;
    departmentCounts[e.department]++;
  });
  const departmentData = Object.entries(departmentCounts).map(([name, value]) => ({ name, value }));

  // By employment type
  const employmentTypeCounts: Record<string, number> = {};
  employees.forEach(e => {
    if (!employmentTypeCounts[e.employmentType]) employmentTypeCounts[e.employmentType] = 0;
    employmentTypeCounts[e.employmentType]++;
  });
  const employmentTypeData = Object.entries(employmentTypeCounts).map(([type, value]) => ({ type, value }));

  // Employee count growth by month (last 6 months)
  const monthCounts: Record<string, number> = {};
  employees.forEach(e => {
    if (e.joinDate) {
      const d = new Date(e.joinDate);
      const key = `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2, '0')}`;
      if (!monthCounts[key]) monthCounts[key] = 0;
      monthCounts[key]++;
    }
  });
  const sortedMonths = Object.keys(monthCounts).sort();
  const employeeData = sortedMonths.map(month => ({ month, count: monthCounts[month] }));

  return (
    <>
      <Typography variant="h4" gutterBottom>
        HR Dashboard
      </Typography>
      {loading ? (
        <Box sx={{ textAlign: 'center', my: 4 }}><CircularProgress /></Box>
      ) : error ? (
        <Box sx={{ color: 'error.main', textAlign: 'center', my: 4 }}>{error}</Box>
      ) : (
      <Grid container spacing={3}>
        {/* Key metrics */}
        <Grid item xs={12} md={3}>
          <StatCard elevation={2}>
            <IconWrapper sx={{ backgroundColor: 'rgba(63, 81, 181, 0.1)' }}>
              <PeopleIcon color="primary" />
            </IconWrapper>
            <Box>
              <Typography variant="body2" color="textSecondary">Total Employees</Typography>
              <Typography variant="h5">{totalEmployees}</Typography>
              <Typography variant="caption" color="success.main">Active: {activeEmployees}</Typography>
            </Box>
          </StatCard>
        </Grid>
        <Grid item xs={12} md={3}>
          <StatCard elevation={2}>
            <IconWrapper sx={{ backgroundColor: 'rgba(0, 150, 136, 0.1)' }}>
              <WorkOutlineIcon sx={{ color: 'success.main' }} />
            </IconWrapper>
            <Box>
              <Typography variant="body2" color="textSecondary">On Leave</Typography>
              <Typography variant="h5">{onLeaveEmployees}</Typography>
              <Typography variant="caption" color="warning.main">Terminated: {terminatedEmployees}</Typography>
            </Box>
          </StatCard>
        </Grid>
        <Grid item xs={12} md={3}>
          <StatCard elevation={2}>
            <IconWrapper sx={{ backgroundColor: 'rgba(255, 152, 0, 0.1)' }}>
              <EventIcon sx={{ color: 'warning.main' }} />
            </IconWrapper>
            <Box>
              <Typography variant="body2" color="textSecondary">Departments</Typography>
              <Typography variant="h5">{totalDepartments}</Typography>
              <Typography variant="caption">Active: {activeDepartments}</Typography>
            </Box>
          </StatCard>
        </Grid>
        <Grid item xs={12} md={3}>
          <StatCard elevation={2}>
            <IconWrapper sx={{ backgroundColor: 'rgba(233, 30, 99, 0.1)' }}>
              <AttachMoneyIcon sx={{ color: 'error.main' }} />
            </IconWrapper>
            <Box>
              <Typography variant="body2" color="textSecondary">Salary Budget</Typography>
              <Typography variant="h5">${totalSalary.toLocaleString()}</Typography>
              <Typography variant="caption" color="error.main">Avg: ${totalEmployees ? (totalSalary/totalEmployees).toFixed(2) : 0}</Typography>
            </Box>
          </StatCard>
        </Grid>
        {/* Charts */}
        <Grid item xs={12} md={6}>
          <StyledPaper elevation={2}>
            <Typography variant="h6" gutterBottom>
              Employee Count Growth
            </Typography>
            <Box sx={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={employeeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </StyledPaper>
        </Grid>
        <Grid item xs={12} md={6}>
          <StyledPaper elevation={2}>
            <Typography variant="h6" gutterBottom>
              Employees by Employment Type
            </Typography>
            <Box sx={{ padding: 2 }}>
              {employmentTypeData.map((et) => (
                <Box key={et.type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: 'primary.main', mr: 1 }} />
                  <Typography variant="body2" sx={{ flexGrow: 1 }}>{et.type} ({et.value})</Typography>
                  <Typography variant="body2" fontWeight="medium">
                    {totalEmployees ? Math.round((et.value/totalEmployees)*100) : 0}%
                  </Typography>
                </Box>
              ))}
            </Box>
          </StyledPaper>
        </Grid>
      </Grid>
      )}
    </>
  );
};

export default HRDashboardPage;