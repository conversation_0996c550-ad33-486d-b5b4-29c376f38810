# Generated by Django 4.2.21 on 2025-06-10 22:13

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sales_tax', '0001_initial'),
        ('gl', '0005_add_multi_currency_support'),
    ]

    operations = [
        migrations.AddField(
            model_name='journalentryline',
            name='sales_tax',
            field=models.ForeignKey(blank=True, help_text='Sales tax applied to this line item', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='journal_entry_lines', to='sales_tax.salestax'),
        ),
        migrations.AddField(
            model_name='journalentryline',
            name='sales_tax_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Sales tax amount calculated for this line', max_digits=15),
        ),
        migrations.AddField(
            model_name='journalentryline',
            name='taxable_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount subject to sales tax (before tax calculation)', max_digits=15),
        ),
    ]
