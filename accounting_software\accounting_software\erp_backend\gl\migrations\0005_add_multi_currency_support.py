# Generated by Django 4.2.21 on 2025-06-08 12:25

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gl', '0004_change_default_currency_to_inr'),
    ]

    operations = [
        migrations.AddField(
            model_name='journalentry',
            name='reporting_currency_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total amount converted to reporting currency', max_digits=15),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='reporting_exchange_rate',
            field=models.DecimalField(decimal_places=6, default=Decimal('1.000000'), help_text='Exchange rate from functional currency to reporting currency', max_digits=10),
        ),
        migrations.AlterField(
            model_name='journalentry',
            name='currency',
            field=models.Char<PERSON>ield(default='USD', help_text='Currency of the transaction (ISO 4217 code)', max_length=3),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='journalentry',
            name='exchange_rate',
            field=models.DecimalField(decimal_places=6, default=Decimal('1.000000'), help_text='Exchange rate from transaction currency to functional currency', max_digits=10),
        ),
    ]
