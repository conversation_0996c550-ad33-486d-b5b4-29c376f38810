import React, { useEffect, useState } from 'react';
import {
  <PERSON>, <PERSON>, CardHead<PERSON>, CardContent, <PERSON><PERSON><PERSON>, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Snackbar, Alert, IconButton
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';

// SalesTax type
interface SalesTax {
  id: number;
  tax_type: 'input' | 'output';
  description: string;
  rate: number;
  remarks: string;
  created_by_username?: string;
  created_at?: string;
  updated_at?: string;
}

const API_URL = 'http://localhost:8000/api/sales-tax/';

const SalesTaxPage: React.FC = () => {
  const { user } = useAuth();
  const isSuperUser = user?.is_superuser || false;

  const [taxes, setTaxes] = useState<SalesTax[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  console.log('SalesTaxPage - Current user:', user);
  console.log('SalesTaxPage - Is superuser:', isSuperUser);
  const [openDialog, setOpenDialog] = useState(false);
  const [editing, setEditing] = useState<SalesTax | null>(null);
  const [form, setForm] = useState<Omit<SalesTax, 'id'> & { id?: number }>({ tax_type: 'output', description: '', rate: 0, remarks: '' });
  const [snackbar, setSnackbar] = useState<{ open: boolean; message: string; severity: 'success' | 'error' }>({ open: false, message: '', severity: 'success' });

  // Fetch taxes
  const fetchTaxes = async () => {
    setError(null);
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      console.log('Fetching with token:', token ? 'Present' : 'Missing');
      const res = await fetch(API_URL, {
        headers: { 'Content-Type': 'application/json', ...(token && { 'Authorization': `Token ${token}` }) },
      });
      console.log('API Response status:', res.status);
      if (!res.ok) {
        let errorText = 'Unknown error';
        try {
          const errorData = await res.json();
          errorText = errorData.detail || JSON.stringify(errorData);
        } catch {
          errorText = await res.text();
        }
        console.log('API Error response:', errorText);
        if (res.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to access sales tax data.');
        } else {
          throw new Error(`Failed to fetch sales tax data: ${res.status} - ${errorText}`);
        }
      }
      const data = await res.json();
      console.log('Sales tax data received:', data);
      // Handle paginated response from Django REST Framework
      const taxesArray = data.results || data || [];
      setTaxes(taxesArray);
    } catch (err: any) {
      console.error('Error fetching sales tax data:', err);
      setError(err.message || 'Failed to load sales tax data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { fetchTaxes(); }, []);

  // Handlers
  const handleOpenDialog = (taxType: 'input' | 'output') => {
    setEditing(null);
    setForm({ tax_type: taxType, description: '', rate: 0, remarks: '' });
    setOpenDialog(true);
  };
  const handleEdit = (tax: SalesTax) => {
    setEditing(tax);
    setForm({ ...tax });
    setOpenDialog(true);
  };
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditing(null);
  };
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(f => ({ 
      ...f, 
      [name]: name === 'rate' ? parseFloat(value) || 0 : value 
    }));
  };
  const handleSave = async () => {
    if (!form.description || form.rate <= 0) {
      setSnackbar({ open: true, message: 'Description and Rate (greater than 0) are required.', severity: 'error' });
      return;
    }
    try {
      const token = localStorage.getItem('token');
      const method = editing ? 'PUT' : 'POST';
      const url = editing ? `${API_URL}${editing.id}/` : API_URL;
      const res = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json', ...(token && { 'Authorization': `Token ${token}` }) },
        body: JSON.stringify(form),
      });
      if (!res.ok) {
        let errorText = 'Unknown error';
        try {
          const errorData = await res.json();
          errorText = errorData.detail || JSON.stringify(errorData);
        } catch {
          errorText = await res.text();
        }
        if (res.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to save sales tax data.');
        } else {
          throw new Error(`Failed to save sales tax: ${res.status} - ${errorText}`);
        }
      }
      setSnackbar({ open: true, message: editing ? 'Tax updated.' : 'Tax created.', severity: 'success' });
      setOpenDialog(false);
      fetchTaxes();
    } catch (err: any) {
      setSnackbar({ open: true, message: err.message || 'Failed to save.', severity: 'error' });
    }
  };
  const handleDelete = async (id: number) => {
    if (!window.confirm('Delete this tax entry?')) return;
    try {
      const token = localStorage.getItem('token');
      const res = await fetch(`${API_URL}${id}/`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json', ...(token && { 'Authorization': `Token ${token}` }) },
      });
      if (!res.ok) {
        let errorText = 'Unknown error';
        try {
          const errorData = await res.json();
          errorText = errorData.detail || JSON.stringify(errorData);
        } catch {
          errorText = await res.text();
        }
        if (res.status === 401) {
          throw new Error('Authentication required. Please log in again.');
        } else if (res.status === 403) {
          throw new Error('You do not have permission to delete sales tax data.');
        } else {
          throw new Error(`Failed to delete sales tax: ${res.status} - ${errorText}`);
        }
      }
      setSnackbar({ open: true, message: 'Tax deleted.', severity: 'success' });
      fetchTaxes();
    } catch (err: any) {
      setSnackbar({ open: true, message: err.message || 'Failed to delete.', severity: 'error' });
    }
  };

  // Split taxes by type
  const outputTaxes = Array.isArray(taxes) ? taxes.filter(t => t?.tax_type === 'output') : [];
  const inputTaxes = Array.isArray(taxes) ? taxes.filter(t => t?.tax_type === 'input') : [];

  return (
    <Box sx={{ p: 3 }}>
      <Card>
        <CardHeader title={<Typography variant="h5">Sales Tax Setup</Typography>} />
        <CardContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          {loading && <Typography>Loading sales tax data...</Typography>}
          {!loading && !error && (
            <>
              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>Output Tax</Typography>
          <TableContainer sx={{ mb: 3 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Description</TableCell>
                  <TableCell>Rate (%)</TableCell>
                  <TableCell>Remarks</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {outputTaxes.map(tax => (
                  <TableRow key={tax.id}>
                    <TableCell>{tax.description}</TableCell>
                    <TableCell>{tax.rate}</TableCell>
                    <TableCell>{tax.remarks}</TableCell>
                    <TableCell>
                      {isSuperUser && (
                        <>
                          <IconButton size="small" onClick={() => handleEdit(tax)}><EditIcon /></IconButton>
                          <IconButton size="small" onClick={() => handleDelete(tax.id)}><DeleteIcon /></IconButton>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                {isSuperUser && (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Button startIcon={<AddIcon />} onClick={() => handleOpenDialog('output')} size="small">Add Output Tax</Button>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>Input Tax</Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Description</TableCell>
                  <TableCell>Rate (%)</TableCell>
                  <TableCell>Remarks</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {inputTaxes.map(tax => (
                  <TableRow key={tax.id}>
                    <TableCell>{tax.description}</TableCell>
                    <TableCell>{tax.rate}</TableCell>
                    <TableCell>{tax.remarks}</TableCell>
                    <TableCell>
                      {isSuperUser && (
                        <>
                          <IconButton size="small" onClick={() => handleEdit(tax)}><EditIcon /></IconButton>
                          <IconButton size="small" onClick={() => handleDelete(tax.id)}><DeleteIcon /></IconButton>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
                {isSuperUser && (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Button startIcon={<AddIcon />} onClick={() => handleOpenDialog('input')} size="small">Add Input Tax</Button>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog for Add/Edit */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="xs" fullWidth>
        <DialogTitle>{editing ? 'Edit Sales Tax' : `Add ${form.tax_type === 'output' ? 'Output' : 'Input'} Tax`}</DialogTitle>
        <DialogContent>
          <TextField
            label="Description"
            name="description"
            value={form.description}
            onChange={handleChange}
            fullWidth
            margin="normal"
            required
          />
          <TextField
            label="Rate (%)"
            name="rate"
            value={form.rate}
            onChange={handleChange}
            type="number"
            fullWidth
            margin="normal"
            required
            inputProps={{ step: '0.001', min: 0 }}
          />
          <TextField
            label="Remarks"
            name="remarks"
            value={form.remarks}
            onChange={handleChange}
            fullWidth
            margin="normal"
            multiline
            rows={2}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">{editing ? 'Update' : 'Save'}</Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar(s => ({ ...s, open: false }))}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={() => setSnackbar(s => ({ ...s, open: false }))} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SalesTaxPage; 