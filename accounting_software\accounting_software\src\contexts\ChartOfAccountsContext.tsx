import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Account {
  id: string;
  code: string;
  name: string;
  description?: string;
  type: 'asset' | 'liability' | 'equity' | 'income' | 'expense';
  subtype?: string;
  balance: number;
  isActive: boolean;
  parentId?: string;
}

interface ChartOfAccountsContextType {
  accounts: Account[];
  getAccountById: (id: string) => Account | undefined;
  getAccountsByType: (type: string) => Account[];
}

// Sample data for chart of accounts
const INITIAL_ACCOUNTS: Account[] = [
  // Asset accounts
  {
    id: 'asset-001',
    code: '1001',
    name: 'Cash',
    type: 'asset',
    subtype: 'current',
    balance: 25000,
    isActive: true,
  },
  {
    id: 'asset-002',
    code: '1002',
    name: 'Accounts Receivable',
    type: 'asset',
    subtype: 'current',
    balance: 15000,
    isActive: true,
  },
  {
    id: 'asset-003',
    code: '1003',
    name: 'Inventory',
    type: 'asset',
    subtype: 'inventory',
    balance: 35000,
    isActive: true,
  },
  {
    id: 'asset-004',
    code: '1004',
    name: 'Office Equipment',
    type: 'asset',
    subtype: 'fixed',
    balance: 12000,
    isActive: true,
  },
  
  // Liability accounts
  {
    id: 'liability-001',
    code: '2001',
    name: 'Accounts Payable',
    type: 'liability',
    subtype: 'current',
    balance: 8000,
    isActive: true,
  },
  {
    id: 'liability-002',
    code: '2002',
    name: 'Loans Payable',
    type: 'liability',
    subtype: 'long-term',
    balance: 50000,
    isActive: true,
  },
  
  // Equity accounts
  {
    id: 'equity-001',
    code: '3001',
    name: 'Owner\'s Capital',
    type: 'equity',
    balance: 30000,
    isActive: true,
  },
  {
    id: 'equity-002',
    code: '3002',
    name: 'Retained Earnings',
    type: 'equity',
    balance: 20000,
    isActive: true,
  },
  
  // Income accounts
  {
    id: 'income-001',
    code: '4001',
    name: 'Product Sales',
    type: 'income',
    balance: 75000,
    isActive: true,
  },
  {
    id: 'income-002',
    code: '4002',
    name: 'Service Revenue',
    type: 'income',
    balance: 45000,
    isActive: true,
  },
  {
    id: 'income-003',
    code: '4003',
    name: 'Interest Income',
    type: 'income',
    balance: 1500,
    isActive: true,
  },
  
  // Expense accounts
  {
    id: 'expense-001',
    code: '5001',
    name: 'Cost of Goods Sold',
    type: 'expense',
    balance: 40000,
    isActive: true,
  },
  {
    id: 'expense-002',
    code: '5002',
    name: 'Salaries Expense',
    type: 'expense',
    balance: 35000,
    isActive: true,
  },
  {
    id: 'expense-003',
    code: '5003',
    name: 'Rent Expense',
    type: 'expense',
    balance: 12000,
    isActive: true,
  },
  {
    id: 'expense-004',
    code: '5004',
    name: 'Utilities Expense',
    type: 'expense',
    balance: 5000,
    isActive: true,
  },
];

const ChartOfAccountsContext = createContext<ChartOfAccountsContextType | undefined>(undefined);

export const useChartOfAccounts = (): ChartOfAccountsContextType => {
  const context = useContext(ChartOfAccountsContext);
  if (!context) {
    throw new Error('useChartOfAccounts must be used within a ChartOfAccountsProvider');
  }
  return context;
};

interface ChartOfAccountsProviderProps {
  children: ReactNode;
}

export const ChartOfAccountsProvider: React.FC<ChartOfAccountsProviderProps> = ({ children }) => {
  const [accounts, setAccounts] = useState<Account[]>([]);

  // Load initial data
  useEffect(() => {
    // In a real app, this would fetch from an API
    setAccounts(INITIAL_ACCOUNTS);
  }, []);

  const getAccountById = (id: string) => {
    return accounts.find((account) => account.id === id);
  };

  const getAccountsByType = (type: string) => {
    return accounts.filter((account) => account.type === type);
  };

  const value = {
    accounts,
    getAccountById,
    getAccountsByType,
  };

  return <ChartOfAccountsContext.Provider value={value}>{children}</ChartOfAccountsContext.Provider>;
};

export default ChartOfAccountsContext; 