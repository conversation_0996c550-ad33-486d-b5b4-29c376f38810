# Generated by Django 4.2.21 on 2025-06-25 07:41

from django.db import migrations


def migrate_customer_data_if_needed(apps, schema_editor):
    """Ensure any remaining customer data is handled before deletion"""
    try:
        SalesCustomer = apps.get_model('sales', 'Customer')
        ContactCustomer = apps.get_model('contacts', 'Customer')
        Contact = apps.get_model('contacts', 'Contact')
        
        # Check if there are any sales customers that need migration
        sales_customers = SalesCustomer.objects.all()
        print(f"Found {sales_customers.count()} sales customers to check before deletion")
        
        for sales_customer in sales_customers:
            # Check if this customer already exists in contacts
            try:
                existing_contact_customer = ContactCustomer.objects.get(
                    contact__email=sales_customer.email,
                    contact__name=sales_customer.display_name
                )
                print(f"✅ Customer {sales_customer.display_name} already exists in contacts")
            except ContactCustomer.DoesNotExist:
                print(f"⚠️  Customer {sales_customer.display_name} not found in contacts - creating...")
                
                # Create contact first
                contact = Contact.objects.create(
                    name=sales_customer.display_name,
                    contact_type='customer',
                    email=sales_customer.email,
                    phone=sales_customer.phone,
                    address=f"{sales_customer.billing_street or ''}\n{sales_customer.billing_city or ''}, {sales_customer.billing_state or ''} {sales_customer.billing_postal_code or ''}\n{sales_customer.billing_country or ''}".strip()
                )
                
                # Create customer
                ContactCustomer.objects.create(
                    contact=contact,
                    customer_code=str(sales_customer.customer_id)[:50],
                    credit_limit=sales_customer.credit_limit,
                    payment_terms=sales_customer.payment_terms,
                    customer_category=sales_customer.customer_type,
                    tax_exempt=not sales_customer.taxable,
                    first_name=sales_customer.first_name,
                    last_name=sales_customer.last_name,
                    company_name=sales_customer.company_name,
                    mobile=sales_customer.mobile,
                    billing_street=sales_customer.billing_street,
                    billing_city=sales_customer.billing_city,
                    billing_state=sales_customer.billing_state,
                    billing_postal_code=sales_customer.billing_postal_code,
                    billing_country=sales_customer.billing_country
                )
                print(f"✅ Created customer {sales_customer.display_name} in contacts")
        
        print(f"✅ Customer data migration check complete")
        
    except Exception as e:
        print(f"❌ Error during customer data migration: {e}")
        raise


def reverse_migration(apps, schema_editor):
    """Reverse migration would recreate the Customer model"""
    # This would be complex to implement, so we'll leave it empty
    # In practice, if you need to reverse this migration, 
    # you would need to recreate the model definition
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0009_remove_currency_fields'),
        ('contacts', '0005_add_employee_contact_fields'),  # Ensure contacts app is ready
    ]

    operations = [
        # Step 1: Run data migration function to ensure data is preserved
        migrations.RunPython(
            migrate_customer_data_if_needed,
            reverse_migration,
            hints={'target_db': 'default'}
        ),
        
        # Step 2: Delete the Customer model (this will drop the table)
        migrations.DeleteModel(
            name='Customer',
        ),
    ]
