from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class CustomPageNumberPagination(PageNumberPagination):
    """
    Custom pagination class that can be disabled for dropdown endpoints
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 1000
    
    def paginate_queryset(self, queryset, request, view=None):
        """
        Paginate a queryset if required, either returning a page object,
        or `None` if pagination is not configured for this view.
        """
        # Check if pagination should be disabled
        if request.query_params.get('no_pagination') == 'true':
            return None
            
        return super().paginate_queryset(queryset, request, view)


class DropdownPagination(PageNumberPagination):
    """
    Pagination class for dropdown endpoints - returns all results
    """
    page_size = None  # Disable pagination
    
    def paginate_queryset(self, queryset, request, view=None):
        return None 