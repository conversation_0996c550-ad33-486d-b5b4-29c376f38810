# Generated by Django 4.2.21 on 2025-06-20 00:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gl', '0007_journalentry_source_document_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='detailtype',
            name='balance_sheet_classification',
            field=models.CharField(blank=True, choices=[('CURRENT', 'Current (< 1 year)'), ('NON_CURRENT', 'Non-Current (> 1 year)'), ('NOT_APPLICABLE', 'Not Applicable')], help_text='Balance sheet classification for assets and liabilities', max_length=20),
        ),
    ]
