import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Avatar,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  AlertTitle,
  Stack,
  Tooltip,
  CircularProgress,
  InputAdornment,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  TablePagination,
  Snackbar,
  FormHelperText,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  SwapHoriz as TransferIcon,
  AccountBalance as AccountBalanceIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Schedule as ScheduleIcon,
  CurrencyExchange as CurrencyIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon,
  MonetizationOn as FeeIcon,
  CompareArrows as ArrowIcon,
  AccountBalanceWallet as WalletIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

// Transfer Interfaces
interface CashAccount {
  id: number;
  account_name: string;
  account_number: string;
  currency: string;
  current_balance: number;
  is_active: boolean;
}

interface CashTransfer {
  id: number;
  transfer_number: string;
  transfer_date: string;
  from_account: CashAccount;
  to_account: CashAccount;
  amount: number;
  exchange_rate: number;
  destination_amount: number;
  transfer_fee: number;
  fee_account?: CashAccount;
  description: string;
  reference_number?: string;
  memo?: string;
  status: 'DRAFT' | 'PENDING' | 'COMPLETED' | 'CANCELLED';
  gl_journal_entry?: number;
  created_at: string;
  updated_at: string;
}

interface TransferFormData {
  transfer_date: string;
  from_account_id: number;
  to_account_id: number;
  amount: number;
  exchange_rate: number;
  transfer_fee: number;
  fee_account_id: number | null;
  description: string;
  reference_number: string;
  memo: string;
}

const CashTransfersPage: React.FC = () => {
  const [transfers, setTransfers] = useState<CashTransfer[]>([]);
  const [accounts, setAccounts] = useState<CashAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [dateFrom, setDateFrom] = useState<Dayjs | null>(dayjs().subtract(30, 'day'));
  const [dateTo, setDateTo] = useState<Dayjs | null>(dayjs());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [totalCount, setTotalCount] = useState(0);

  // Dialog states
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedTransfer, setSelectedTransfer] = useState<CashTransfer | null>(null);
  const [formData, setFormData] = useState<TransferFormData>({
    transfer_date: dayjs().format('YYYY-MM-DD'),
    from_account_id: 0,
    to_account_id: 0,
    amount: 0,
    exchange_rate: 1.0,
    transfer_fee: 0,
    fee_account_id: null,
    description: '',
    reference_number: '',
    memo: '',
  });

  // Calculated fields
  const [destinationAmount, setDestinationAmount] = useState(0);
  const [exchangeRateError, setExchangeRateError] = useState('');

  // Snackbar state
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning',
  });

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadTransfers();
    loadAccounts();
  }, [page, rowsPerPage, searchTerm, filterStatus, dateFrom, dateTo]);

  useEffect(() => {
    // Calculate destination amount when amount or exchange rate changes
    setDestinationAmount(formData.amount * formData.exchange_rate);
  }, [formData.amount, formData.exchange_rate]);

  useEffect(() => {
    // Auto-detect exchange rate when accounts change
    const fromAccount = accounts.find(a => a.id === formData.from_account_id);
    const toAccount = accounts.find(a => a.id === formData.to_account_id);
    
    if (fromAccount && toAccount) {
      if (fromAccount.currency === toAccount.currency) {
        setFormData(prev => ({ ...prev, exchange_rate: 1.0 }));
        setExchangeRateError('');
      } else {
        // In real implementation, fetch exchange rate from API
        setExchangeRateError('Please enter the current exchange rate');
      }
    }
  }, [formData.from_account_id, formData.to_account_id, accounts]);

  const loadTransfers = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const params = new URLSearchParams({
        page: (page + 1).toString(),
        page_size: rowsPerPage.toString(),
        search: searchTerm,
        ordering: '-transfer_date,-transfer_number',
      });

      if (filterStatus !== 'all') {
        params.append('status', filterStatus);
      }
      if (dateFrom) {
        params.append('transfer_date__gte', dateFrom.format('YYYY-MM-DD'));
      }
      if (dateTo) {
        params.append('transfer_date__lte', dateTo.format('YYYY-MM-DD'));
      }

      const response = await fetch(`http://localhost:8000/api/cms/transfers/?${params}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load transfers');
      }

      const data = await response.json();
      setTransfers(data.results || []);
      setTotalCount(data.count || 0);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load transfers');
      console.error('Load transfers error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadAccounts = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cms/accounts/?is_active=true', {
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load accounts');
      }

      const data = await response.json();
      setAccounts(data.results || data);
    } catch (err) {
      console.error('Load accounts error:', err);
    }
  };

  const handleCreate = () => {
    setDialogMode('create');
    setSelectedTransfer(null);
    setFormData({
      transfer_date: dayjs().format('YYYY-MM-DD'),
      from_account_id: 0,
      to_account_id: 0,
      amount: 0,
      exchange_rate: 1.0,
      transfer_fee: 0,
      fee_account_id: null,
      description: '',
      reference_number: '',
      memo: '',
    });
    setDestinationAmount(0);
    setExchangeRateError('');
    setOpenDialog(true);
  };

  const handleEdit = (transfer: CashTransfer) => {
    setDialogMode('edit');
    setSelectedTransfer(transfer);
    setFormData({
      transfer_date: transfer.transfer_date,
      from_account_id: transfer.from_account.id,
      to_account_id: transfer.to_account.id,
      amount: transfer.amount,
      exchange_rate: transfer.exchange_rate,
      transfer_fee: transfer.transfer_fee,
      fee_account_id: transfer.fee_account?.id || null,
      description: transfer.description,
      reference_number: transfer.reference_number || '',
      memo: transfer.memo || '',
    });
    setDestinationAmount(transfer.destination_amount);
    setOpenDialog(true);
  };

  const handleView = (transfer: CashTransfer) => {
    setDialogMode('view');
    setSelectedTransfer(transfer);
    setOpenDialog(true);
  };

  const handleSave = async () => {
    try {
      // Validation
      if (formData.from_account_id === formData.to_account_id) {
        setSnackbar({
          open: true,
          message: 'From and To accounts cannot be the same',
          severity: 'error',
        });
        return;
      }

      if (formData.amount <= 0) {
        setSnackbar({
          open: true,
          message: 'Transfer amount must be greater than zero',
          severity: 'error',
        });
        return;
      }

      const token = localStorage.getItem('token');
      const url = dialogMode === 'create' 
        ? 'http://localhost:8000/api/cms/transfers/'
        : `http://localhost:8000/api/cms/transfers/${selectedTransfer?.id}/`;
      
      const method = dialogMode === 'create' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          destination_amount: destinationAmount,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to save transfer');
      }

      setSnackbar({
        open: true,
        message: `Transfer ${dialogMode === 'create' ? 'created' : 'updated'} successfully`,
        severity: 'success',
      });

      setOpenDialog(false);
      loadTransfers();

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to save transfer',
        severity: 'error',
      });
    }
  };

  const handleDelete = async (transfer: CashTransfer) => {
    if (!window.confirm(`Are you sure you want to delete transfer "${transfer.transfer_number}"?`)) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cms/transfers/${transfer.id}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete transfer');
      }

      setSnackbar({
        open: true,
        message: 'Transfer deleted successfully',
        severity: 'success',
      });

      loadTransfers();

    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete transfer',
        severity: 'error',
      });
    }
  };

  const formatCurrency = (amount: number, currency?: string) => {
    if (!currencyInfo) return '0.00';
    
    const currencyCode = currency || currencyInfo.functional_currency;
    const locale = currencyCode === 'INR' ? 'en-IN' : 'en-US';
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'PENDING': return 'warning';
      case 'DRAFT': return 'default';
      case 'CANCELLED': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircleIcon />;
      case 'PENDING': return <ScheduleIcon />;
      case 'DRAFT': return <TimelineIcon />;
      case 'CANCELLED': return <CancelIcon />;
      default: return <TimelineIcon />;
    }
  };

  const fromAccount = accounts.find(a => a.id === formData.from_account_id);
  const toAccount = accounts.find(a => a.id === formData.to_account_id);
  const isDifferentCurrency = fromAccount && toAccount && fromAccount.currency !== toAccount.currency;

  if (loading && transfers.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Cash Transfers...</Typography>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              🔄 Cash Transfers
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Inter-account transfers and currency exchanges
            </Typography>
            {currencyInfo && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                📊 {currencyInfo.company_name} • Base Currency: {currencyInfo.functional_currency} ({currencyInfo.functional_currency_symbol})
              </Typography>
            )}
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreate}
            sx={{ height: 'fit-content' }}
          >
            New Transfer
          </Button>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  placeholder="Search transfers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filterStatus}
                    label="Status"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="all">All Status</MenuItem>
                    <MenuItem value="DRAFT">Draft</MenuItem>
                    <MenuItem value="PENDING">Pending</MenuItem>
                    <MenuItem value="COMPLETED">Completed</MenuItem>
                    <MenuItem value="CANCELLED">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={4}>
                <Stack direction="row" spacing={1}>
                  <DatePicker
                    label="From"
                    value={dateFrom}
                    onChange={setDateFrom}
                    slotProps={{ textField: { size: 'small' } }}
                  />
                  <DatePicker
                    label="To"
                    value={dateTo}
                    onChange={setDateTo}
                    slotProps={{ textField: { size: 'small' } }}
                  />
                </Stack>
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={loadTransfers}
                  size="small"
                >
                  Refresh
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Transfers Table */}
        <Card>
          <CardContent>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <AlertTitle>Error</AlertTitle>
                {error}
              </Alert>
            )}

            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Transfer</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>From Account</TableCell>
                    <TableCell align="center">Transfer</TableCell>
                    <TableCell>To Account</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell align="center">Status</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transfers.map((transfer) => (
                    <TableRow key={transfer.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar 
                            sx={{ 
                              width: 36, 
                              height: 36, 
                              mr: 2, 
                              bgcolor: 'primary.light' 
                            }}
                          >
                            <TransferIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {transfer.transfer_number}
                            </Typography>
                            {transfer.reference_number && (
                              <Typography variant="caption" color="text.secondary">
                                Ref: {transfer.reference_number}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {dayjs(transfer.transfer_date).format('MMM DD, YYYY')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {dayjs(transfer.created_at).format('h:mm A')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ width: 28, height: 28, mr: 1, bgcolor: 'error.light' }}>
                            <TrendingDownIcon fontSize="small" />
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {transfer.from_account.account_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {transfer.from_account.account_number}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell align="center">
                        <Box display="flex" flexDirection="column" alignItems="center">
                          <ArrowIcon color="primary" />
                          <Typography variant="caption" color="text.secondary">
                            {formatCurrency(transfer.amount, transfer.from_account.currency)}
                          </Typography>
                          {transfer.exchange_rate !== 1 && (
                            <Typography variant="caption" color="warning.main">
                              @ {transfer.exchange_rate}
                            </Typography>
                          )}
                          {transfer.transfer_fee > 0 && (
                            <Typography variant="caption" color="text.secondary">
                              Fee: {formatCurrency(transfer.transfer_fee)}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ width: 28, height: 28, mr: 1, bgcolor: 'success.light' }}>
                            <TrendingUpIcon fontSize="small" />
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {transfer.to_account.account_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {transfer.to_account.account_number}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body1" fontWeight="medium">
                          {formatCurrency(transfer.destination_amount, transfer.to_account.currency)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {transfer.description}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          size="small"
                          label={transfer.status}
                          color={getStatusColor(transfer.status) as any}
                          icon={getStatusIcon(transfer.status)}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Stack direction="row" spacing={1} justifyContent="center">
                          <Tooltip title="View Details">
                            <IconButton size="small" onClick={() => handleView(transfer)}>
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          {transfer.status === 'DRAFT' && (
                            <Tooltip title="Edit Transfer">
                              <IconButton size="small" onClick={() => handleEdit(transfer)}>
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                          {transfer.status === 'DRAFT' && (
                            <Tooltip title="Delete Transfer">
                              <IconButton 
                                size="small" 
                                onClick={() => handleDelete(transfer)}
                                color="error"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[10, 25, 50, 100]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
            />
          </CardContent>
        </Card>

        {/* Transfer Dialog */}
        <Dialog 
          open={openDialog} 
          onClose={() => setOpenDialog(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box display="flex" alignItems="center">
              <Avatar sx={{ mr: 2, bgcolor: 'primary.light' }}>
                <TransferIcon />
              </Avatar>
              {dialogMode === 'create' && 'Create Cash Transfer'}
              {dialogMode === 'edit' && 'Edit Cash Transfer'}
              {dialogMode === 'view' && 'Transfer Details'}
            </Box>
          </DialogTitle>
          <DialogContent dividers>
            {dialogMode === 'view' && selectedTransfer ? (
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Transfer Summary
                      </Typography>
                      <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ bgcolor: 'error.light', mr: 2 }}>
                            <WalletIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="body1" fontWeight="medium">
                              {selectedTransfer.from_account.account_name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {selectedTransfer.from_account.account_number}
                            </Typography>
                          </Box>
                        </Box>
                        <Box textAlign="center">
                          <ArrowIcon color="primary" sx={{ fontSize: 40 }} />
                          <Typography variant="h6" color="primary">
                            {formatCurrency(selectedTransfer.amount, selectedTransfer.from_account.currency)}
                          </Typography>
                          {selectedTransfer.exchange_rate !== 1 && (
                            <Typography variant="body2" color="warning.main">
                              Rate: {selectedTransfer.exchange_rate}
                            </Typography>
                          )}
                        </Box>
                        <Box display="flex" alignItems="center">
                          <Box textAlign="right" mr={2}>
                            <Typography variant="body1" fontWeight="medium">
                              {selectedTransfer.to_account.account_name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {selectedTransfer.to_account.account_number}
                            </Typography>
                          </Box>
                          <Avatar sx={{ bgcolor: 'success.light' }}>
                            <WalletIcon />
                          </Avatar>
                        </Box>
                      </Box>
                      <Divider sx={{ my: 2 }} />
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Destination Amount
                          </Typography>
                          <Typography variant="h6">
                            {formatCurrency(selectedTransfer.destination_amount, selectedTransfer.to_account.currency)}
                          </Typography>
                        </Grid>
                        {selectedTransfer.transfer_fee > 0 && (
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Transfer Fee
                            </Typography>
                            <Typography variant="h6" color="warning.main">
                              {formatCurrency(selectedTransfer.transfer_fee)}
                            </Typography>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6}>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="Transfer Number" 
                        secondary={selectedTransfer.transfer_number} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Date" 
                        secondary={dayjs(selectedTransfer.transfer_date).format('MMMM DD, YYYY')} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Status" 
                        secondary={selectedTransfer.status} 
                      />
                    </ListItem>
                  </List>
                </Grid>
                <Grid item xs={12} md={6}>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="Description" 
                        secondary={selectedTransfer.description} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Reference" 
                        secondary={selectedTransfer.reference_number || 'N/A'} 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText 
                        primary="Memo" 
                        secondary={selectedTransfer.memo || 'N/A'} 
                      />
                    </ListItem>
                  </List>
                </Grid>
              </Grid>
            ) : (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Transfer Date"
                    value={dayjs(formData.transfer_date)}
                    onChange={(newValue) => setFormData({...formData, transfer_date: newValue ? newValue.format('YYYY-MM-DD') : ''})}
                    slotProps={{ textField: { fullWidth: true, margin: 'normal', required: true } }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Transfer Amount"
                    type="number"
                    value={formData.amount}
                    onChange={(e) => setFormData({...formData, amount: Number(e.target.value)})}
                    margin="normal"
                    required
                    InputProps={{
                      startAdornment: <InputAdornment position="start">
                        {fromAccount ? (fromAccount.currency === 'USD' ? '$' : fromAccount.currency) : '$'}
                      </InputAdornment>,
                    }}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Transfer Accounts
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal" required>
                    <InputLabel>From Account</InputLabel>
                    <Select
                      value={formData.from_account_id}
                      label="From Account"
                      onChange={(e) => setFormData({...formData, from_account_id: Number(e.target.value)})}
                    >
                      {accounts.map((account) => (
                        <MenuItem key={account.id} value={account.id}>
                          {account.account_name} ({account.currency}) - {formatCurrency(account.current_balance, account.currency)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth margin="normal" required>
                    <InputLabel>To Account</InputLabel>
                    <Select
                      value={formData.to_account_id}
                      label="To Account"
                      onChange={(e) => setFormData({...formData, to_account_id: Number(e.target.value)})}
                    >
                      {accounts
                        .filter(account => account.id !== formData.from_account_id)
                        .map((account) => (
                        <MenuItem key={account.id} value={account.id}>
                          {account.account_name} ({account.currency}) - {formatCurrency(account.current_balance, account.currency)}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {isDifferentCurrency && (
                  <>
                    <Grid item xs={12}>
                      <Alert severity="info">
                        <AlertTitle>Currency Conversion Required</AlertTitle>
                        This transfer involves different currencies. Please specify the exchange rate.
                      </Alert>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label={`Exchange Rate (${fromAccount?.currency} to ${toAccount?.currency})`}
                        type="number"
                        value={formData.exchange_rate}
                        onChange={(e) => setFormData({...formData, exchange_rate: Number(e.target.value)})}
                        margin="normal"
                        required
                        error={!!exchangeRateError}
                        helperText={exchangeRateError}
                        inputProps={{ step: 0.0001 }}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        label={`Destination Amount (${toAccount?.currency})`}
                        value={destinationAmount.toFixed(2)}
                        margin="normal"
                        InputProps={{
                          readOnly: true,
                          startAdornment: <InputAdornment position="start">
                            {toAccount ? (toAccount.currency === 'USD' ? '$' : toAccount.currency) : '$'}
                          </InputAdornment>,
                        }}
                        helperText="Automatically calculated based on exchange rate"
                      />
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    Transfer Details
                  </Typography>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    margin="normal"
                    required
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Reference Number"
                    value={formData.reference_number}
                    onChange={(e) => setFormData({...formData, reference_number: e.target.value})}
                    margin="normal"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Transfer Fee"
                    type="number"
                    value={formData.transfer_fee}
                    onChange={(e) => setFormData({...formData, transfer_fee: Number(e.target.value)})}
                    margin="normal"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">$</InputAdornment>,
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Memo"
                    multiline
                    rows={3}
                    value={formData.memo}
                    onChange={(e) => setFormData({...formData, memo: e.target.value})}
                    margin="normal"
                  />
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>
              {dialogMode === 'view' ? 'Close' : 'Cancel'}
            </Button>
            {dialogMode !== 'view' && (
              <Button variant="contained" onClick={handleSave}>
                {dialogMode === 'create' ? 'Create Transfer' : 'Update Transfer'}
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={() => setSnackbar({ ...snackbar, open: false })}
        >
          <Alert
            onClose={() => setSnackbar({ ...snackbar, open: false })}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    </LocalizationProvider>
  );
};

export default CashTransfersPage; 