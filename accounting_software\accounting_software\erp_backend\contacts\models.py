from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid


class Contact(models.Model):
    """Centralized contact model - base for all contacts (customers, vendors, employees)"""
    
    CONTACT_TYPE_CHOICES = [
        ('customer', 'Customer'),
        ('vendor', 'Vendor'),
        ('employee', 'Employee'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=255, help_text="Full name or company name")
    contact_type = models.CharField(max_length=20, choices=CONTACT_TYPE_CHOICES)
    email = models.EmailField(blank=True, null=True)
    phone = models.CharField(max_length=50, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'contacts'
        ordering = ['name']
        indexes = [
            models.Index(fields=['contact_type']),
            models.Index(fields=['name']),
            models.Index(fields=['email']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_contact_type_display()})"


class Customer(models.Model):
    """Customer-specific information linked to Contact"""
    contact = models.OneToOneField(Contact, on_delete=models.CASCADE, primary_key=True)
    
    # Customer-specific fields
    customer_code = models.CharField(max_length=50, blank=True, null=True)
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    payment_terms = models.CharField(max_length=100, blank=True, null=True)
    customer_category = models.CharField(max_length=100, blank=True, null=True)
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    tax_exempt = models.BooleanField(default=False)
    
    # Additional contact fields
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    company_name = models.CharField(max_length=200, blank=True, null=True)
    mobile = models.CharField(max_length=20, blank=True, null=True)
    
    # Billing Address
    billing_street = models.TextField(blank=True, null=True)
    billing_city = models.CharField(max_length=100, blank=True, null=True)
    billing_state = models.CharField(max_length=100, blank=True, null=True)
    billing_postal_code = models.CharField(max_length=20, blank=True, null=True)
    billing_country = models.CharField(max_length=100, blank=True, null=True)
    
    # Shipping Address
    shipping_same_as_billing = models.BooleanField(default=True)
    shipping_street = models.TextField(blank=True, null=True)
    shipping_city = models.CharField(max_length=100, blank=True, null=True)
    shipping_state = models.CharField(max_length=100, blank=True, null=True)
    shipping_postal_code = models.CharField(max_length=20, blank=True, null=True)
    shipping_country = models.CharField(max_length=100, blank=True, null=True)
    
    class Meta:
        db_table = 'customers'
    
    def __str__(self):
        return f"Customer: {self.contact.name}"


class Vendor(models.Model):
    """Vendor-specific information linked to Contact"""
    contact = models.OneToOneField(Contact, on_delete=models.CASCADE, primary_key=True)
    
    # Vendor-specific fields
    vendor_code = models.CharField(max_length=50, blank=True, null=True)
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    payment_terms = models.CharField(max_length=100, blank=True, null=True)
    vendor_category = models.CharField(max_length=100, blank=True, null=True)
    lead_time_days = models.IntegerField(default=0)
    minimum_order_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    preferred_vendor = models.BooleanField(default=False)
    bank_details = models.TextField(blank=True, null=True)
    
    # Additional contact fields that don't fit in the basic Contact model
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    company_name = models.CharField(max_length=255, blank=True, null=True)
    mobile = models.CharField(max_length=50, blank=True, null=True)
    
    # Billing Address
    billing_street = models.TextField(blank=True, null=True)
    billing_city = models.CharField(max_length=100, blank=True, null=True)
    billing_state = models.CharField(max_length=100, blank=True, null=True)
    billing_postal_code = models.CharField(max_length=20, blank=True, null=True)
    billing_country = models.CharField(max_length=100, default='India')
    
    # Shipping Address
    shipping_same_as_billing = models.BooleanField(default=True)
    shipping_street = models.TextField(blank=True, null=True)
    shipping_city = models.CharField(max_length=100, blank=True, null=True)
    shipping_state = models.CharField(max_length=100, blank=True, null=True)
    shipping_postal_code = models.CharField(max_length=20, blank=True, null=True)
    shipping_country = models.CharField(max_length=100, blank=True, null=True)
    
    class Meta:
        db_table = 'vendors'
    
    def __str__(self):
        return f"Vendor: {self.contact.name}"


class Employee(models.Model):
    """Employee-specific information linked to Contact"""
    contact = models.OneToOneField(Contact, on_delete=models.CASCADE, primary_key=True)
    
    # Personal Information (contact-related fields)
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    gender = models.CharField(max_length=20, blank=True, null=True, 
                             help_text="Gender (male, female, other, prefer-not-to-say)")
    mobile = models.CharField(max_length=20, blank=True, null=True)
    
    # Employment Details (basic info that fits in contacts)
    employee_id = models.CharField(max_length=50, unique=True, blank=True, null=True)
    position = models.CharField(max_length=100, blank=True, null=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    
    # Address Information
    street_address = models.TextField(blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        db_table = 'employees'

    def __str__(self):
        return f"Employee: {self.contact.name} - {self.position or 'No Position'}"


# AuditLog model temporarily removed due to SQLite JSON compatibility issues
# Will be implemented later with proper database support 