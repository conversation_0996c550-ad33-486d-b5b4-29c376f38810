import React, { useState } from 'react';
import { 
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  TextField,
  Box,
  Chip,
  IconButton,
  Grid,
  InputAdornment
} from '@mui/material';
import { 
  Search as SearchIcon, 
  Add as AddIcon, 
  Description as DescriptionIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';

// Sample job openings data
const initialJobOpenings = [
  { id: 1, position: "Frontend Developer", department: "Engineering", status: "Open", applicants: 12, posted: "2024-03-15" },
  { id: 2, position: "Senior Accountant", department: "Finance", status: "Open", applicants: 8, posted: "2024-03-20" },
  { id: 3, position: "HR Assistant", department: "Human Resources", status: "Closed", applicants: 15, posted: "2024-02-10" },
  { id: 4, position: "Sales Representative", department: "Sales", status: "Open", applicants: 10, posted: "2024-04-01" },
  { id: 5, position: "Marketing Coordinator", department: "Marketing", status: "On Hold", applicants: 6, posted: "2024-03-25" },
];

const RecruitmentPage: React.FC = () => {
  const [jobOpenings] = useState(initialJobOpenings);
  const [searchTerm, setSearchTerm] = useState('');
  
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };
  
  const filteredJobs = jobOpenings.filter(job => 
    job.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch(status) {
      case 'Open': return 'success';
      case 'Closed': return 'default';
      case 'On Hold': return 'warning';
      default: return 'default';
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Recruitment</Typography>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
        >
          Add Job Opening
        </Button>
      </Box>

      <Box sx={{ display: 'flex', mb: 3, gap: 2 }}>
        <TextField
          placeholder="Search job openings..."
          variant="outlined"
          fullWidth
          size="small"
          value={searchTerm}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
        <Button 
          variant="outlined" 
          startIcon={<FilterListIcon />}
        >
          Filter
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
            <Typography variant="subtitle2" fontWeight="medium">Open Positions</Typography>
            <Typography variant="h4">3</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, bgcolor: 'success.light', color: 'success.contrastText' }}>
            <Typography variant="subtitle2" fontWeight="medium">Total Applicants</Typography>
            <Typography variant="h4">51</Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, bgcolor: 'secondary.light', color: 'secondary.contrastText' }}>
            <Typography variant="subtitle2" fontWeight="medium">Interviews Scheduled</Typography>
            <Typography variant="h4">8</Typography>
          </Paper>
        </Grid>
      </Grid>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Position</TableCell>
              <TableCell>Department</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Applicants</TableCell>
              <TableCell>Posted Date</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredJobs.map((job) => (
              <TableRow key={job.id}>
                <TableCell component="th" scope="row">
                  {job.position}
                </TableCell>
                <TableCell>{job.department}</TableCell>
                <TableCell>
                  <Chip 
                    label={job.status} 
                    color={getStatusColor(job.status) as "default" | "primary" | "secondary" | "error" | "info" | "success" | "warning"}
                    size="small"
                  />
                </TableCell>
                <TableCell>{job.applicants}</TableCell>
                <TableCell>{new Date(job.posted).toLocaleDateString()}</TableCell>
                <TableCell align="right">
                  <IconButton size="small">
                    <DescriptionIcon fontSize="small" />
                  </IconButton>
                  <IconButton size="small">
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default RecruitmentPage;