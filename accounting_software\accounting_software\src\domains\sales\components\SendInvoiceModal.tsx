import React, { useState, useRef } from 'react';
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  <PERSON>alogActions, 
  Button, 
  TextField, 
  Typography, 
  Box, 
  IconButton, 
  Divider,
  CircularProgress,
  Tabs,
  Tab,
  Alert,
  Snackbar
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import EmailIcon from '@mui/icons-material/Email';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { Invoice } from '../../../shared/types/invoice.types';
import { pdfService } from '../../../services/PDFService';
import { emailService } from '../../../services/EmailService';
import { useCompany } from '../../../contexts/CompanyContext';
import InvoicePDFPreview from './InvoicePDFPreview';

interface SendInvoiceModalProps {
  open: boolean;
  onClose: () => void;
  invoice: Invoice;
}

const SendInvoiceModal: React.FC<SendInvoiceModalProps> = ({ open, onClose, invoice }) => {
  const { companyInfo } = useCompany();
  const [activeTab, setActiveTab] = useState(0);
  const [recipientEmail, setRecipientEmail] = useState(invoice.customer.email || '');
  const [subject, setSubject] = useState(`Invoice #${invoice.invoiceNumber} from ${companyInfo.companyName}`);
  const [message, setMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleSendEmail = async () => {
    if (!recipientEmail) {
      setError('Recipient email is required');
      return;
    }

    try {
      setSending(true);
      setError(null);

      // Generate PDF
      const pdfBlob = pdfService.generateInvoicePDF(invoice, companyInfo);
      
      // Send email
      const emailSent = await emailService.sendInvoiceEmail(
        invoice,
        recipientEmail,
        pdfBlob,
        message
      );

      if (emailSent) {
        setSuccess(true);
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError('Failed to send email. Please try again.');
      }
    } catch (err) {
      setError('An error occurred while sending the email');
      console.error(err);
    } finally {
      setSending(false);
    }
  };

  const handleDownloadPDF = async () => {
    try {
      // Generate PDF from the preview component
      if (previewRef.current) {
        const pdfBlob = await pdfService.generatePDFFromElement(
          previewRef.current,
          `Invoice_${invoice.invoiceNumber}.pdf`
        );
        pdfService.saveBlob(pdfBlob, `Invoice_${invoice.invoiceNumber}.pdf`);
      } else {
        // Fallback to direct PDF generation
        const pdfBlob = pdfService.generateInvoicePDF(invoice, companyInfo);
        pdfService.saveBlob(pdfBlob, `Invoice_${invoice.invoiceNumber}.pdf`);
      }
    } catch (err) {
      setError('An error occurred while generating the PDF');
      console.error(err);
    }
  };

  return (
    <>
      <Dialog 
        open={open} 
        onClose={!sending ? onClose : undefined} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Send Invoice #{invoice.invoiceNumber}</Typography>
            {!sending && (
              <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        </DialogTitle>
        
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange} 
          aria-label="invoice tabs"
          centered
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab icon={<EmailIcon />} label="Email" />
          <Tab icon={<PictureAsPdfIcon />} label="PDF Preview" />
        </Tabs>
        
        <DialogContent dividers>
          {activeTab === 0 ? (
            <Box sx={{ pt: 2 }}>
              <TextField
                label="To"
                fullWidth
                margin="normal"
                value={recipientEmail}
                onChange={(e) => setRecipientEmail(e.target.value)}
                disabled={sending}
                required
                error={!recipientEmail}
                helperText={!recipientEmail ? 'Recipient email is required' : ''}
              />
              
              <TextField
                label="Subject"
                fullWidth
                margin="normal"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                disabled={sending}
              />
              
              <TextField
                label="Message (optional)"
                fullWidth
                margin="normal"
                multiline
                rows={6}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                disabled={sending}
                placeholder="Add a personal message to your customer..."
              />
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  The following will be included:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>
                  <PictureAsPdfIcon fontSize="small" color="primary" />
                  <Typography variant="body2">
                    Invoice_{invoice.invoiceNumber}.pdf
                  </Typography>
                </Box>
              </Box>
            </Box>
          ) : (
            <Box sx={{ mt: 2 }} ref={previewRef}>
              <InvoicePDFPreview invoice={invoice} />
            </Box>
          )}
        </DialogContent>
        
        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
          {activeTab === 0 ? (
            <>
              <Button 
                onClick={onClose} 
                disabled={sending}
              >
                Cancel
              </Button>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleSendEmail}
                disabled={sending || !recipientEmail}
                startIcon={sending ? <CircularProgress size={20} /> : <EmailIcon />}
              >
                {sending ? 'Sending...' : 'Send Email'}
              </Button>
            </>
          ) : (
            <>
              <Button onClick={onClose}>
                Cancel
              </Button>
              <Button 
                variant="contained" 
                color="primary" 
                onClick={handleDownloadPDF}
                startIcon={<PictureAsPdfIcon />}
              >
                Download PDF
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
      
      <Snackbar 
        open={success} 
        autoHideDuration={3000} 
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" sx={{ width: '100%' }}>
          Invoice sent successfully!
        </Alert>
      </Snackbar>
      
      <Snackbar 
        open={!!error} 
        autoHideDuration={5000} 
        onClose={() => setError(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
    </>
  );
};

export default SendInvoiceModal; 