import api from './api';

export interface ProductPricing {
  id: number;
  product_id: string;
  name: string;
  sku?: string;
  unit_price: number;
  cost_price?: number;
  minimum_selling_price: number;
  price_effective_date?: string;
  price_last_updated_by?: number;
  price_last_updated_at?: string;
  margin_amount: number;
  margin_percentage: number;
  markup_percentage: number;
  current_average_cost: number;
}

export interface PricingStats {
  total_products: number;
  no_cost_price_count: number;
  negative_margin_count: number;
  average_margin_percentage: number;
  recent_price_updates_30_days: number;
}

export interface MarginAnalysis {
  negative: number;
  '0_to_10': number;
  '10_to_25': number;
  '25_to_50': number;
  above_50: number;
}

export interface PricingFilters {
  category?: number;
  product_type?: string;
  min_margin?: number;
  max_margin?: number;
  search?: string;
  ordering?: string;
}

class ProductPricingService {
  private baseUrl = '/sales/product-pricing';

  async getAll(filters?: PricingFilters): Promise<{ results: ProductPricing[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await api.get(`${this.baseUrl}/?${params.toString()}`);
    return response.data;
  }

  async getById(id: number): Promise<ProductPricing> {
    const response = await api.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async updatePrice(id: number, priceData: {
    unit_price: number;
    minimum_selling_price?: number;
    price_effective_date?: string;
  }): Promise<ProductPricing> {
    const response = await api.patch(`${this.baseUrl}/${id}/`, priceData);
    return response.data;
  }

  async getPricingStats(): Promise<PricingStats> {
    const response = await api.get(`${this.baseUrl}/pricing_stats/`);
    return response.data;
  }

  async getMarginAnalysis(): Promise<MarginAnalysis> {
    const response = await api.get(`${this.baseUrl}/margin_analysis/`);
    return response.data;
  }

  // Helper method to calculate margin percentage
  calculateMarginPercentage(sellingPrice: number, costPrice: number): number {
    if (costPrice <= 0) return 0;
    return ((sellingPrice - costPrice) / costPrice) * 100;
  }

  // Helper method to calculate markup percentage
  calculateMarkupPercentage(sellingPrice: number, costPrice: number): number {
    if (costPrice <= 0) return 0;
    return ((sellingPrice - costPrice) / sellingPrice) * 100;
  }

  // Helper method to calculate margin amount
  calculateMarginAmount(sellingPrice: number, costPrice: number): number {
    return sellingPrice - costPrice;
  }

  // Helper method to validate minimum selling price
  validateMinimumPrice(sellingPrice: number, minimumPrice: number): boolean {
    return sellingPrice >= minimumPrice;
  }

  // Helper method to get margin status
  getMarginStatus(marginPercentage: number): 'excellent' | 'good' | 'fair' | 'poor' | 'negative' {
    if (marginPercentage < 0) return 'negative';
    if (marginPercentage < 10) return 'poor';
    if (marginPercentage < 25) return 'fair';
    if (marginPercentage < 50) return 'good';
    return 'excellent';
  }

  // Helper method to format currency
  formatCurrency(amount: number, currency: string = 'INR'): string {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  }

  // Helper method to format percentage
  formatPercentage(percentage: number): string {
    return `${percentage.toFixed(2)}%`;
  }
}

export default new ProductPricingService(); 