import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Alert,
  CircularProgress,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Edit as EditIcon,
  PostAdd as PostIcon,
  ArrowBack as ArrowBackIcon,
  Undo as UndoIcon,
  Business as BusinessIcon,
  Warehouse as WarehouseIcon,
  CalendarToday as CalendarIcon,
  ThumbUp as ApproveIcon,
  LocalShipping as ShippingIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { grnReturnService, GRNReturn } from '../services/grn-return.service';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const GRNReturnDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [grnReturn, setGrnReturn] = useState<GRNReturn | null>(null);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [markReturnedDialogOpen, setMarkReturnedDialogOpen] = useState(false);
  const [postDialogOpen, setPostDialogOpen] = useState(false);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    if (id) {
      loadGRNReturn();
    }
  }, [id]);

  const loadGRNReturn = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const grnReturnData = await grnReturnService.getGRNReturn(parseInt(id));
      setGrnReturn(grnReturnData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRN Return');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigate(`/dashboard/inventory/grn-returns/${id}/edit`);
  };

  const handleApprove = async () => {
    if (!grnReturn?.grn_return_id) return;
    
    try {
      await grnReturnService.approveGRNReturn(grnReturn.grn_return_id);
      setApproveDialogOpen(false);
      loadGRNReturn(); // Reload to get updated status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to approve GRN Return');
    }
  };

  const handleMarkAsReturned = async () => {
    if (!grnReturn?.grn_return_id) return;
    
    try {
      await grnReturnService.markAsReturned(grnReturn.grn_return_id);
      setMarkReturnedDialogOpen(false);
      loadGRNReturn(); // Reload to get updated status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark GRN Return as returned');
    }
  };

  const handlePostToInventory = async () => {
    if (!grnReturn?.grn_return_id) return;
    
    try {
      await grnReturnService.postToInventory(grnReturn.grn_return_id);
      setPostDialogOpen(false);
      loadGRNReturn(); // Reload to get updated status
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post GRN Return to inventory');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      DRAFT: { color: 'default' as const, label: 'Draft' },
      APPROVED: { color: 'warning' as const, label: 'Approved' },
      RETURNED: { color: 'info' as const, label: 'Returned' },
      POSTED: { color: 'success' as const, label: 'Posted' },
      CANCELLED: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
    return <Chip label={config.label} color={config.color} />;
  };

  const getReturnReasonChip = (reason: string) => {
    const reasonConfig = {
      DEFECTIVE: { color: 'error' as const, label: 'Defective' },
      WRONG_ITEM: { color: 'warning' as const, label: 'Wrong Item' },
      EXCESS_QTY: { color: 'info' as const, label: 'Excess Qty' },
      QUALITY_ISSUE: { color: 'error' as const, label: 'Quality Issue' },
      NOT_ORDERED: { color: 'warning' as const, label: 'Not Ordered' },
      EXPIRED: { color: 'error' as const, label: 'Expired' },
      OTHER: { color: 'default' as const, label: 'Other' },
    };
    
    const config = reasonConfig[reason as keyof typeof reasonConfig] || reasonConfig.OTHER;
    return <Chip label={config.label} color={config.color} size="small" variant="outlined" />;
  };

  const formatCurrencyAmount = (amount: number): string => {
    const symbol = currencyInfo?.functional_currency_symbol || '₹';
    return `${symbol}${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}`;
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Loading return details...</Typography>
        </Box>
      </PageContainer>
    );
  }

  if (!grnReturn) {
    return (
      <PageContainer>
        <Alert severity="error">GRN Return not found</Alert>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <UndoIcon color="primary" />
          <Box>
            <Typography variant="h5" fontWeight="bold">
              Return {grnReturn.grn_return_number}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Good Return Note Details
            </Typography>
          </Box>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/inventory/grn-returns')}
          >
            Back to List
          </Button>
          {grnReturn.status === 'DRAFT' && (
            <>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={handleEdit}
              >
                Edit
              </Button>
              <Button
                variant="contained"
                color="warning"
                startIcon={<ApproveIcon />}
                onClick={() => setApproveDialogOpen(true)}
              >
                Approve
              </Button>
            </>
          )}
          {grnReturn.status === 'APPROVED' && (
            <Button
              variant="contained"
              color="info"
              startIcon={<ShippingIcon />}
              onClick={() => setMarkReturnedDialogOpen(true)}
            >
              Mark as Returned
            </Button>
          )}
          {grnReturn.status === 'RETURNED' && (
            <Button
              variant="contained"
              color="success"
              startIcon={<PostIcon />}
              onClick={() => setPostDialogOpen(true)}
            >
              Post to Inventory
            </Button>
          )}
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Return Information */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <ReceiptIcon color="primary" />
                <Typography variant="h6">Return Information</Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Return Number
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {grnReturn.grn_return_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Original GRN
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {grnReturn.original_grn_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Return Date
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(grnReturn.return_date)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                  <Box mt={0.5}>
                    {getStatusChip(grnReturn.status)}
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Primary Return Reason
                  </Typography>
                  <Box mt={0.5}>
                    {getReturnReasonChip(grnReturn.return_reason)}
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Returned By
                  </Typography>
                  <Typography variant="body1">
                    {grnReturn.returned_by_name || 'N/A'}
                  </Typography>
                </Grid>
              </Grid>

              {grnReturn.notes && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    Return Notes
                  </Typography>
                  <Typography variant="body1">
                    {grnReturn.notes}
                  </Typography>
                </>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Vendor & Warehouse Information */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <BusinessIcon color="primary" />
                <Typography variant="h6">Vendor Information</Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Vendor Name
              </Typography>
              <Typography variant="body1" fontWeight="medium" mb={2}>
                {grnReturn.vendor_name}
              </Typography>

              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <WarehouseIcon color="primary" />
                <Typography variant="h6">Warehouse</Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Location
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {grnReturn.warehouse_name}
              </Typography>
            </CardContent>
          </Card>

          {/* Financial Summary */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" mb={2}>Financial Summary</Typography>
              
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Quantity:
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {Number(grnReturn.total_quantity || 0).toFixed(2)}
                </Typography>
              </Box>
              
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2" color="text.secondary">
                  Total Value:
                </Typography>
                <Typography variant="body2" fontWeight="medium" color="error.main">
                  {formatCurrencyAmount(Number(grnReturn.total_value || 0))}
                </Typography>
              </Box>

              {grnReturn.expected_credit_amount && (
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Expected Credit:
                  </Typography>
                  <Typography variant="body2" fontWeight="medium" color="success.main">
                    {formatCurrencyAmount(Number(grnReturn.expected_credit_amount))}
                  </Typography>
                </Box>
              )}

              {grnReturn.actual_credit_amount && (
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="body2" color="text.secondary">
                    Actual Credit:
                  </Typography>
                  <Typography variant="body2" fontWeight="medium" color="success.main">
                    {formatCurrencyAmount(Number(grnReturn.actual_credit_amount))}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Return Items */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" mb={2}>Return Items</Typography>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Product</TableCell>
                      <TableCell align="right">Received Qty</TableCell>
                      <TableCell align="right">Returned Qty</TableCell>
                      <TableCell align="right">Unit Cost</TableCell>
                      <TableCell align="right">Return Value</TableCell>
                      <TableCell>Return Reason</TableCell>
                      <TableCell>Batch/Notes</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {grnReturn.items?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {item.product_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              SKU: {item.product_sku}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            {Number(item.quantity_received).toFixed(2)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium">
                            {Number(item.quantity_returned).toFixed(2)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            {formatCurrencyAmount(Number(item.unit_cost))}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium" color="error.main">
                            {formatCurrencyAmount(
                              Number(item.quantity_returned) * Number(item.unit_cost)
                            )}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {getReturnReasonChip(item.return_reason)}
                        </TableCell>
                        <TableCell>
                          <Box>
                            {item.batch_number && (
                              <Typography variant="caption" display="block">
                                Batch: {item.batch_number}
                              </Typography>
                            )}
                            {item.condition_notes && (
                              <Typography variant="caption" color="text.secondary">
                                {item.condition_notes}
                              </Typography>
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Audit Information */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" mb={2}>Audit Information</Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Created At
                  </Typography>
                  <Typography variant="body2">
                    {grnReturn.created_at ? formatDate(grnReturn.created_at) : 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Updated At
                  </Typography>
                  <Typography variant="body2">
                    {grnReturn.updated_at ? formatDate(grnReturn.updated_at) : 'N/A'}
                  </Typography>
                </Grid>
                {grnReturn.approved_at && (
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Approved At
                    </Typography>
                    <Typography variant="body2">
                      {formatDate(grnReturn.approved_at)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      By: {grnReturn.approved_by_name || 'N/A'}
                    </Typography>
                  </Grid>
                )}
                {grnReturn.posted_at && (
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Posted At
                    </Typography>
                    <Typography variant="body2">
                      {formatDate(grnReturn.posted_at)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      By: {grnReturn.posted_by_name || 'N/A'}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Approve Confirmation Dialog */}
      <Dialog open={approveDialogOpen} onClose={() => setApproveDialogOpen(false)}>
        <DialogTitle>Approve Return Note</DialogTitle>
        <DialogContent>
          Are you sure you want to approve return note {grnReturn.grn_return_number}?
          Once approved, the return can be processed for shipment back to the vendor.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApproveDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleApprove} color="primary" variant="contained">
            Approve
          </Button>
        </DialogActions>
      </Dialog>

      {/* Mark as Returned Confirmation Dialog */}
      <Dialog open={markReturnedDialogOpen} onClose={() => setMarkReturnedDialogOpen(false)}>
        <DialogTitle>Mark as Returned</DialogTitle>
        <DialogContent>
          Are you sure you want to mark return note {grnReturn.grn_return_number} as returned to vendor?
          This indicates that the goods have been physically shipped back to the vendor.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMarkReturnedDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleMarkAsReturned} color="primary" variant="contained">
            Mark as Returned
          </Button>
        </DialogActions>
      </Dialog>

      {/* Post to Inventory Confirmation Dialog */}
      <Dialog open={postDialogOpen} onClose={() => setPostDialogOpen(false)}>
        <DialogTitle>Post to Inventory</DialogTitle>
        <DialogContent>
          Are you sure you want to post return note {grnReturn.grn_return_number} to inventory?
          This will:
          <ul>
            <li>Reduce inventory quantities for returned items</li>
            <li>Create stock transactions for audit trail</li>
            <li>Generate journal entries for accounting</li>
          </ul>
          This action cannot be undone.
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPostDialogOpen(false)}>Cancel</Button>
          <Button onClick={handlePostToInventory} color="primary" variant="contained">
            Post to Inventory
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default GRNReturnDetailPage; 