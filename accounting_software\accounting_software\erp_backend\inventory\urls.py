from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    GoodsReceiptNoteViewSet, InventoryViewSet, StockTransactionViewSet,
    WarehouseViewSet, UnitsOfMeasureViewSet, GoodsReturnNoteViewSet,
    InventoryTransferViewSet
)

router = DefaultRouter()
router.register(r'grns', GoodsReceiptNoteViewSet, basename='goodsreceiptnote')
router.register(r'grn-returns', GoodsReturnNoteViewSet, basename='goodsreturnnote')
router.register(r'inventory', InventoryViewSet, basename='inventory')
router.register(r'stock-transactions', StockTransactionViewSet, basename='stocktransaction')
router.register(r'warehouses', WarehouseViewSet, basename='warehouse')
router.register(r'units-of-measure', UnitsOfMeasureViewSet, basename='unitsofmeasure')
router.register(r'transfers', InventoryTransferViewSet, basename='inventorytransfer')

urlpatterns = [
    path('', include(router.urls)),
] 