import React from 'react';
import { Box, styled } from '@mui/material';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import Topbar from './Topbar';

// Get the sidebar width from the Sidebar component
const SIDEBAR_WIDTH = 275;

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(2),
  paddingTop: theme.spacing(2),
  width: '100%',
  boxSizing: 'border-box',
  overflow: 'auto',
  height: '100%',
}));

const ContentWrapper = styled(Box)({
  display: 'flex',
  height: 'calc(100vh - 64px)',
  width: '100%',
});

const SidebarWrapper = styled(Box)({
  width: SIDEBAR_WIDTH,
  flexShrink: 0,
  height: '100%',
});

const DashboardLayout: React.FC = () => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <Topbar />
      <ContentWrapper>
        <SidebarWrapper>
          <Sidebar />
        </SidebarWrapper>
        <MainContent>
          <Outlet />
        </MainContent>
      </ContentWrapper>
    </Box>
  );
};

export default DashboardLayout;