import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Autocomplete,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add,
  Delete,
  Save,
  Preview,
  Timeline,
  AccountBalance,
  TrendingUp,
  CalendarToday,
  Download,
  Upload,
  Refresh,
  Assessment,
} from '@mui/icons-material';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';

// Sample accounts data (will come from GL module)
const sampleAccounts = [
  { id: 1, account_number: '4000', account_name: 'Revenue - Product Sales', account_type: 'Revenue' },
  { id: 2, account_number: '4100', account_name: 'Revenue - Service Income', account_type: 'Revenue' },
  { id: 3, account_number: '5000', account_name: 'Cost of Goods Sold', account_type: 'Expense' },
  { id: 4, account_number: '6000', account_name: 'Salaries and Wages', account_type: 'Expense' },
  { id: 5, account_number: '6100', account_name: 'Office Rent', account_type: 'Expense' },
  { id: 6, account_number: '6200', account_name: 'Marketing Expenses', account_type: 'Expense' },
  { id: 7, account_number: '6300', account_name: 'Utilities', account_type: 'Expense' },
  { id: 8, account_number: '1000', account_name: 'Cash and Cash Equivalents', account_type: 'Asset' },
];

interface BudgetLine {
  id: string;
  account_id: number | null;
  account_name: string;
  account_type: string;
  jan: number;
  feb: number;
  mar: number;
  apr: number;
  may: number;
  jun: number;
  jul: number;
  aug: number;
  sep: number;
  oct: number;
  nov: number;
  dec: number;
  total: number;
  notes: string;
}

const BudgetPlanningForm: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [budgetName, setBudgetName] = useState('');
  const [budgetYear, setBudgetYear] = useState(new Date().getFullYear());
  const [budgetType, setBudgetType] = useState('annual');
  const [budgetLines, setBudgetLines] = useState<BudgetLine[]>([]);

  const months = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
  const monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Calculate totals for each month
  const calculateMonthlyTotals = () => {
    const totals = {
      jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
      jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0, total: 0
    };

    budgetLines.forEach(line => {
      totals.jan += line.jan;
      totals.feb += line.feb;
      totals.mar += line.mar;
      totals.apr += line.apr;
      totals.may += line.may;
      totals.jun += line.jun;
      totals.jul += line.jul;
      totals.aug += line.aug;
      totals.sep += line.sep;
      totals.oct += line.oct;
      totals.nov += line.nov;
      totals.dec += line.dec;
      totals.total += line.total;
    });

    return totals;
  };

  // Calculate totals by account type
  const calculateAccountTypeTotals = () => {
    const typeTotals = { Revenue: 0, Expense: 0, Asset: 0 };
    
    budgetLines.forEach(line => {
      if (typeTotals.hasOwnProperty(line.account_type)) {
        typeTotals[line.account_type as keyof typeof typeTotals] += line.total;
      }
    });

    return typeTotals;
  };

  // Add new budget line
  const addBudgetLine = () => {
    const newLine: BudgetLine = {
      id: `line_${Date.now()}`,
      account_id: null,
      account_name: '',
      account_type: '',
      jan: 0, feb: 0, mar: 0, apr: 0, may: 0, jun: 0,
      jul: 0, aug: 0, sep: 0, oct: 0, nov: 0, dec: 0,
      total: 0,
      notes: ''
    };
    setBudgetLines([...budgetLines, newLine]);
  };

  // Remove budget line
  const removeBudgetLine = (lineId: string) => {
    setBudgetLines(budgetLines.filter(line => line.id !== lineId));
  };

  // Update budget line
  const updateBudgetLine = (lineId: string, field: string, value: any) => {
    setBudgetLines(budgetLines.map(line => {
      if (line.id === lineId) {
        const updatedLine = { ...line, [field]: value };
        
        // If a month value changed, recalculate total
        if (months.includes(field)) {
          updatedLine.total = months.reduce((sum, month) => sum + (updatedLine[month as keyof BudgetLine] as number || 0), 0);
        }
        
        return updatedLine;
      }
      return line;
    }));
  };

  // Handle account selection
  const handleAccountSelect = (lineId: string, account: any) => {
    if (account) {
      updateBudgetLine(lineId, 'account_id', account.id);
      updateBudgetLine(lineId, 'account_name', account.account_name);
      updateBudgetLine(lineId, 'account_type', account.account_type);
    } else {
      updateBudgetLine(lineId, 'account_id', null);
      updateBudgetLine(lineId, 'account_name', '');
      updateBudgetLine(lineId, 'account_type', '');
    }
  };

  const monthlyTotals = calculateMonthlyTotals();
  const accountTypeTotals = calculateAccountTypeTotals();

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: '#f5f7fa', minHeight: '100vh' }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2c3e50' }}>
            Budget Planning
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Create and manage your annual budget plans
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button variant="outlined" startIcon={<Upload />}>
            Import
          </Button>
          <Button variant="outlined" startIcon={<Download />}>
            Export
          </Button>
          <Button variant="outlined" startIcon={<Preview />}>
            Preview
          </Button>
          <Button variant="contained" startIcon={<Save />} sx={{ background: 'linear-gradient(45deg, #4CAF50 30%, #45a049 90%)' }}>
            Save Budget
          </Button>
        </Box>
      </Box>

      {/* Budget Info Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            Budget Information
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Budget Name"
                value={budgetName}
                onChange={(e) => setBudgetName(e.target.value)}
                placeholder="e.g., Annual Budget 2024"
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Budget Year</InputLabel>
                <Select
                  value={budgetYear}
                  onChange={(e) => setBudgetYear(e.target.value as number)}
                  label="Budget Year"
                >
                  {Array.from({ length: 5 }, (_, i) => {
                    const year = new Date().getFullYear() + i;
                    return (
                      <MenuItem key={year} value={year}>
                        {year}
                      </MenuItem>
                    );
                  })}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Budget Type</InputLabel>
                <Select
                  value={budgetType}
                  onChange={(e) => setBudgetType(e.target.value)}
                  label="Budget Type"
                >
                  <MenuItem value="annual">Annual Budget</MenuItem>
                  <MenuItem value="quarterly">Quarterly Budget</MenuItem>
                  <MenuItem value="monthly">Monthly Budget</MenuItem>
                  <MenuItem value="rolling">Rolling Forecast</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)}>
          <Tab icon={<AccountBalance />} label="Budget Lines" />
          <Tab icon={<TrendingUp />} label="Summary" />
          <Tab icon={<Timeline />} label="Trends" />
        </Tabs>
      </Paper>

      {/* Budget Lines Tab */}
      {selectedTab === 0 && (
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                Budget Line Items
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={addBudgetLine}
                size="small"
              >
                Add Line
              </Button>
            </Box>

            {budgetLines.length === 0 ? (
              <Box textAlign="center" py={4}>
                <Typography variant="body1" color="text.secondary">
                  No budget lines created yet. Click "Add Line" to start building your budget.
                </Typography>
              </Box>
            ) : (
              <TableContainer component={Paper} variant="outlined">
                <Table size="small" sx={{ minWidth: 1400 }}>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                      <TableCell sx={{ fontWeight: 'bold', minWidth: 200 }}>Account</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', minWidth: 80 }}>Type</TableCell>
                      {monthLabels.map(month => (
                        <TableCell key={month} align="right" sx={{ fontWeight: 'bold', minWidth: 90 }}>
                          {month}
                        </TableCell>
                      ))}
                      <TableCell align="right" sx={{ fontWeight: 'bold', minWidth: 100, backgroundColor: '#e3f2fd' }}>
                        Total
                      </TableCell>
                      <TableCell align="center" sx={{ fontWeight: 'bold', minWidth: 80 }}>
                        Actions
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {budgetLines.map((line, index) => (
                      <TableRow 
                        key={line.id}
                        sx={{
                          '&:nth-of-type(even)': { backgroundColor: '#f9f9f9' },
                          '&:hover': { backgroundColor: '#e3f2fd' }
                        }}
                      >
                        <TableCell>
                          <Autocomplete
                            size="small"
                            options={sampleAccounts}
                            getOptionLabel={(option) => `${option.account_number} - ${option.account_name}`}
                            value={sampleAccounts.find(acc => acc.id === line.account_id) || null}
                            onChange={(event, newValue) => handleAccountSelect(line.id, newValue)}
                            renderInput={(params) => (
                              <TextField {...params} placeholder="Select Account" variant="outlined" />
                            )}
                            sx={{ minWidth: 250 }}
                          />
                        </TableCell>
                        <TableCell>
                          {line.account_type && (
                            <Chip 
                              label={line.account_type} 
                              size="small"
                              color={
                                line.account_type === 'Revenue' ? 'success' :
                                line.account_type === 'Expense' ? 'error' :
                                line.account_type === 'Asset' ? 'primary' : 'default'
                              }
                            />
                          )}
                        </TableCell>
                        {months.map(month => (
                          <TableCell key={month} align="right">
                            <FormattedCurrencyInput
                              size="small"
                              value={line[month as keyof BudgetLine] as number}
                              onChange={(e) => updateBudgetLine(line.id, month, parseFloat(e.target.value) || 0)}
                              currencySymbol="$"
                              sx={{ width: 85 }}
                            />
                          </TableCell>
                        ))}
                        <TableCell align="right" sx={{ backgroundColor: '#e3f2fd', fontWeight: 'bold' }}>
                          ${line.total.toLocaleString()}
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" gap={0.5}>
                            <IconButton 
                              size="small" 
                              onClick={() => updateBudgetLine(line.id, 'total', line.total)}
                              title="Spread total evenly"
                              color="primary"
                            >
                              <Timeline fontSize="small" />
                            </IconButton>
                            <IconButton 
                              size="small" 
                              onClick={() => removeBudgetLine(line.id)}
                              color="error"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      )}

      {/* Summary Tab */}
      {selectedTab === 1 && (
        <Grid container spacing={3}>
          <Grid item xs={12} lg={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Budget Summary by Account Type
                </Typography>
                <Box>
                  {Object.entries(accountTypeTotals).map(([type, total]) => (
                    <Box key={type} display="flex" justifyContent="space-between" alignItems="center" py={1}>
                      <Box display="flex" alignItems="center">
                        <Chip 
                          label={type} 
                          size="small"
                          color={
                            type === 'Revenue' ? 'success' :
                            type === 'Expense' ? 'error' :
                            type === 'Asset' ? 'primary' : 'default'
                          }
                          sx={{ mr: 2 }}
                        />
                      </Box>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        ${total.toLocaleString()}
                      </Typography>
                    </Box>
                  ))}
                  <Divider sx={{ my: 2 }} />
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Net Income (Revenue - Expenses)
                    </Typography>
                    <Typography 
                      variant="h6" 
                      sx={{ 
                        fontWeight: 'bold',
                        color: (accountTypeTotals['Revenue'] || 0) - (accountTypeTotals['Expense'] || 0) >= 0 ? 'success.main' : 'error.main'
                      }}
                    >
                      ${((accountTypeTotals['Revenue'] || 0) - (accountTypeTotals['Expense'] || 0)).toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} lg={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                  Budget Statistics
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: '#e8f5e8', borderRadius: 2 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                        {budgetLines.filter(line => line.account_type === 'Revenue').length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Revenue Accounts
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: '#ffebee', borderRadius: 2 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#f44336' }}>
                        {budgetLines.filter(line => line.account_type === 'Expense').length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Expense Accounts
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: '#e3f2fd', borderRadius: 2 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2196f3' }}>
                        {budgetLines.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Lines
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={2} sx={{ backgroundColor: '#f3e5f5', borderRadius: 2 }}>
                      <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#9c27b0' }}>
                        {budgetYear}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Budget Year
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Trends Tab */}
      {selectedTab === 2 && (
        <Card>
          <CardContent>
            <Typography variant="h5" sx={{ textAlign: 'center', py: 4, color: '#666' }}>
              📈 Budget Trends & Analysis
              <br />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Visual charts showing monthly trends, variance analysis, and budget performance will be displayed here
              </Typography>
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default BudgetPlanningForm;