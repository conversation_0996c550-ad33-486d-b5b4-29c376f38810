import React from 'react';
import { Box, Typography } from '@mui/material';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { useTheme } from '@mui/material/styles';

const SalesChart: React.FC = () => {
  const theme = useTheme();

  const data = [
    { month: 'Jan', sales: 4000, target: 4500 },
    { month: 'Feb', sales: 5000, target: 4500 },
    { month: 'Mar', sales: 4500, target: 4500 },
    { month: 'Apr', sales: 6000, target: 4500 },
    { month: 'May', sales: 5500, target: 4500 },
    { month: 'Jun', sales: 7000, target: 4500 },
  ];

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
        Sales Performance
      </Typography>
      <Box sx={{ width: '100%', height: 300 }}>
        <ResponsiveContainer>
          <AreaChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Area
              type="monotone"
              dataKey="sales"
              stroke={theme.palette.primary.main}
              fill={theme.palette.primary.light}
              fillOpacity={0.3}
            />
            <Area
              type="monotone"
              dataKey="target"
              stroke={theme.palette.secondary.main}
              fill={theme.palette.secondary.light}
              fillOpacity={0.3}
            />
          </AreaChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default SalesChart;