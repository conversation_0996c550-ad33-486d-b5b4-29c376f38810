<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Customers API</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .section { 
            margin-bottom: 20px; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
            margin-right: 10px; 
        }
        button:hover { background: #0056b3; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto; 
            white-space: pre-wrap; 
        }
        input { 
            width: 100%; 
            padding: 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            margin-bottom: 10px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Customer API Debug Tool</h1>
        
        <div class="section info">
            <h3>📋 Current Status</h3>
            <p><strong>Backend URL:</strong> http://localhost:8000</p>
            <p><strong>API Endpoint:</strong> /api/contacts/customers/</p>
            <p><strong>Current Token:</strong> <span id="currentToken">Checking...</span></p>
        </div>

        <div class="section">
            <h3>🔑 Authentication Token</h3>
            <input type="text" id="tokenInput" placeholder="Enter authentication token" value="e15bc01f831c5111f413f534ef82288744cb7d41">
            <button onclick="setToken()">💾 Set Token in localStorage</button>
            <button onclick="clearToken()">🗑️ Clear Token</button>
        </div>

        <div class="section">
            <h3>🌐 API Tests</h3>
            <button onclick="testCustomersAPI()">👥 Test Customers API</button>
            <button onclick="testStatsAPI()">📊 Test Stats API</button>
            <button onclick="testHealthCheck()">🏥 Test Server Health</button>
        </div>

        <div class="section" id="results">
            <h3>📝 Results</h3>
            <div id="output">Click a test button to see results...</div>
        </div>
    </div>

    <script>
        // Display current token
        function updateTokenDisplay() {
            const token = localStorage.getItem('token');
            document.getElementById('currentToken').textContent = token || 'No token found';
        }

        // Set token in localStorage
        function setToken() {
            const token = document.getElementById('tokenInput').value;
            if (token) {
                localStorage.setItem('token', token);
                log('✅ Token saved to localStorage', 'success');
                updateTokenDisplay();
            } else {
                log('❌ Please enter a token', 'error');
            }
        }

        // Clear token from localStorage
        function clearToken() {
            localStorage.removeItem('token');
            log('🗑️ Token cleared from localStorage', 'info');
            updateTokenDisplay();
        }

        // Logging function
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            
            output.innerHTML = `
                <div class="${className}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
                ${output.innerHTML}
            `;
        }

        // Test customers API
        async function testCustomersAPI() {
            log('🔄 Testing Customers API...', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ No authentication token found. Please set a token first.', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8000/api/contacts/customers/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Customers API Success: Found ${data.count} customers`, 'success');
                    log(`📋 First customer: ${data.results[0]?.display_name || 'None'}`, 'info');
                    log(`🔍 Full Response:\n${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    const errorData = await response.text();
                    log(`❌ Customers API Error (${response.status}): ${errorData}`, 'error');
                }
            } catch (error) {
                log(`❌ Network Error: ${error.message}`, 'error');
            }
        }

        // Test stats API
        async function testStatsAPI() {
            log('🔄 Testing Stats API...', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ No authentication token found. Please set a token first.', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:8000/api/contacts/customers/stats/', {
                    headers: {
                        'Authorization': `Token ${token}`,
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Stats API Success`, 'success');
                    log(`📊 Stats:\n${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    const errorData = await response.text();
                    log(`❌ Stats API Error (${response.status}): ${errorData}`, 'error');
                }
            } catch (error) {
                log(`❌ Network Error: ${error.message}`, 'error');
            }
        }

        // Test server health
        async function testHealthCheck() {
            log('🔄 Testing Server Health...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/admin/', {
                    method: 'GET',
                });

                if (response.status === 200 || response.status === 302) {
                    log(`✅ Server is running (Status: ${response.status})`, 'success');
                } else {
                    log(`⚠️ Server responded with status: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Server Health Check Failed: ${error.message}`, 'error');
                log(`💡 Make sure Django server is running on port 8000`, 'info');
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateTokenDisplay();
            log('🚀 Debug tool initialized', 'info');
            log('💡 Set the authentication token and test the APIs', 'info');
        });
    </script>
</body>
</html> 