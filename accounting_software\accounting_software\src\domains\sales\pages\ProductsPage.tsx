import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Grid,
  <PERSON>,
  Card,
  CardContent,
  Button,
} from '@mui/material';
import { 
  Inventory, 
  AttachMoney, 
  Category as CategoryIcon,
  LocalShipping,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  Tooltip as RechartsTooltip, ResponsiveContainer,
  <PERSON><PERSON>hart, Pie, Cell, Legend
} from 'recharts';
import StatCard from '../../../shared/components/StatCard';
import DataTable, { RowAction, commonRowActions } from '../../../shared/components/DataTable';
import { PageContainer } from '../../../layouts/components/PageComponents';
import { useProducts } from '../../../contexts/ProductContext';
import { Product, PRODUCT_CATEGORIES } from '../../../shared/types/product.types';
import { useTheme } from '@mui/material/styles';
import ProductFormModal from '../components/ProductFormModal';

const ProductsPage: React.FC = () => {
  const theme = useTheme();
  const { products, getCategoryName, deleteProduct, updateProduct } = useProducts();
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isProductFormOpen, setIsProductFormOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Calculate product statistics
  const productStats = {
    totalProducts: products.filter(p => p.type === 'product').length,
    totalServices: products.filter(p => p.type === 'service').length,
    totalValue: products.reduce((sum, product) => {
      if (product.type === 'product' && product.trackInventory) {
        return sum + (product.costPrice * (product.inventoryQuantity || 0));
      }
      return sum;
    }, 0),
    lowStockCount: products.filter(p => 
      p.type === 'product' && 
      p.trackInventory && 
      p.lowStockAlert && 
      (p.inventoryQuantity || 0) <= (p.lowStockThreshold || 0)
    ).length,
    categoryCounts: Object.entries(
      products.reduce((acc, product) => {
        const category = product.category;
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    ).map(([category, count]) => ({
      name: getCategoryName(category),
      value: count,
    })),
  };

  // Handle edit product
  const handleEditProduct = useCallback((product: Product) => {
    setSelectedProduct(product);
    setIsProductFormOpen(true);
  }, []);

  // Handle delete product
  const handleDeleteProduct = useCallback((product: Product) => {
    if (window.confirm(`Are you sure you want to delete ${product.name}?`)) {
      try {
        deleteProduct(product.id);
      } catch (error) {
        console.error('Error deleting product:', error);
      }
    }
  }, [deleteProduct]);

  // Handle view product
  const handleViewProduct = useCallback((product: Product) => {
    // In a real app, you would navigate to a product details page
    console.log('View product details:', product);
  }, []);

  // Custom row actions for the data table
  const productRowActions: RowAction[] = [
    commonRowActions.edit(handleEditProduct),
    commonRowActions.delete(handleDeleteProduct),
    {
      icon: <VisibilityIcon fontSize="small" color="info" />,
      label: 'View Details',
      tooltip: 'View product details',
      onClick: handleViewProduct,
    },
  ];

  // Colors for the pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  // Handle close product form
  const handleCloseProductForm = useCallback(() => {
    setIsProductFormOpen(false);
    setSelectedProduct(null);
  }, []);

  // Get unique categories for the searchable dropdown
  const categoryOptions = PRODUCT_CATEGORIES.map(category => ({
    value: category.value,
    label: category.label
  }));

  // Handle cell change for searchable dropdown
  const handleCellChange = (row: Product, field: string, value: any) => {
    if (field === 'category') {
      // Update the product with the new category
      const updatedProduct = { ...row, category: value };
      updateProduct(updatedProduct);
    }
  };

  return (
    <PageContainer
      title="Products & Services"
      actions={
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setIsProductFormOpen(true)}
        >
          Add Product/Service
        </Button>
      }
    >
      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Products"
            value={productStats.totalProducts}
            icon={<Inventory />}
            trend={+5}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Services"
            value={productStats.totalServices}
            icon={<CategoryIcon />}
            trend={+8}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Inventory Value"
            value={`$${productStats.totalValue.toLocaleString()}`}
            icon={<AttachMoney />}
            trend={+12}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Low Stock Items"
            value={productStats.lowStockCount}
            icon={<LocalShipping />}
            trend={-2}
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sales by Product Category
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={productStats.categoryCounts}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value" fill={theme.palette.primary.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Product Distribution
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'Products', value: productStats.totalProducts },
                        { name: 'Services', value: productStats.totalServices },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {[
                        { name: 'Products', value: productStats.totalProducts },
                        { name: 'Services', value: productStats.totalServices },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Products Table */}
      <Box sx={{ mt: 4 }}>
        <DataTable
          title="Product & Service List"
          columns={[
            { field: 'name', headerName: 'Name', flex: 1 },
            { 
              field: 'type', 
              headerName: 'Type', 
              flex: 0.5,
              valueGetter: ({ row }) => row.type.charAt(0).toUpperCase() + row.type.slice(1)
            },
            { 
              field: 'category', 
              headerName: 'Category', 
              flex: 0.8,
              valueGetter: ({ row }) => getCategoryName(row.category),
              searchable: true,
              options: categoryOptions,
              optionLabel: 'label',
              optionValue: 'value'
            },
            { 
              field: 'salesPrice', 
              headerName: 'Sales Price', 
              flex: 0.5,
              type: 'currency',
              align: 'right',
              currencyCode: ({ row }) => row.currency || 'USD'
            },
            { 
              field: 'status', 
              headerName: 'Status', 
              flex: 0.5,
              renderCell: ({ row }) => (
                <Chip 
                  label={row.status === 'active' ? 'Active' : 'Inactive'} 
                  color={row.status === 'active' ? 'success' : 'error'}
                  size="small"
                />
              )
            },
            { 
              field: 'inventory', 
              headerName: 'Inventory', 
              flex: 0.5,
              renderCell: ({ row }) => {
                if (row.type !== 'product' || !row.trackInventory) {
                  return <Typography variant="body2">N/A</Typography>;
                }
                
                const isLowStock = row.lowStockAlert && 
                  (row.inventoryQuantity || 0) <= (row.lowStockThreshold || 0);
                
                return (
                  <Chip 
                    label={`${row.inventoryQuantity || 0} in stock`}
                    color={isLowStock ? 'warning' : 'default'}
                    size="small"
                  />
                );
              }
            },
          ]}
          rows={products}
          rowActions={productRowActions}
          onSelectionChange={setSelectedProducts}
          onCellChange={handleCellChange}
          loading={isLoading}
          pageSize={10}
          stickyHeader
          initialState={{
            sorting: { field: 'name', sort: 'asc' },
          }}
        />
      </Box>

      {/* Product Form Modal */}
      <ProductFormModal 
        open={isProductFormOpen} 
        onClose={handleCloseProductForm} 
        product={selectedProduct}
      />
    </PageContainer>
  );
};

export default ProductsPage; 