import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  LinearProgress,
} from '@mui/material';

const TopCustomers: React.FC = () => {
  const customers = [
    {
      name: 'Tech Solutions Ltd',
      revenue: 25000,
      progress: 85,
    },
    {
      name: 'Global Traders Inc',
      revenue: 18000,
      progress: 70,
    },
    {
      name: 'ABC Corporation',
      revenue: 15000,
      progress: 60,
    },
    {
      name: 'XYZ Industries',
      revenue: 12000,
      progress: 45,
    },
  ];

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
        Top Customers
      </Typography>
      <List>
        {customers.map((customer, index) => (
          <ListItem key={index}>
            <ListItemAvatar>
              <Avatar sx={{ bgcolor: `primary.${index % 2 ? 'light' : 'main'}` }}>
                {customer.name.charAt(0)}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={customer.name}
              secondary={
                <Box sx={{ display: 'flex', flexDirection: 'column', mt: 1 }}>
                  <Typography variant="body2" color="textSecondary">
                    ${customer.revenue.toLocaleString()}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={customer.progress}
                    sx={{ mt: 1 }}
                  />
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );
};

export default TopCustomers;