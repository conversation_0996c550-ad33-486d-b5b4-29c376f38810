import api from './api';

export interface SalesOrderLineItem {
  id?: number;
  product?: number;
  product_name?: string;
  product_sku?: string;
  description: string;
  quantity: number;
  unit_of_measure: string;
  unit_price: number;
  discount_percent: number;
  line_total: number;
  quantity_delivered: number;
  quantity_pending: number;
  taxable: boolean;
  tax_rate: number;
  tax_amount: number;
  notes?: string;
  line_order: number;
}

export interface SalesOrder {
  id?: number;
  so_id?: string;
  so_number?: string;
  customer: number;
  customer_name?: string;
  customer_email?: string;
  so_date: string;
  expected_delivery_date?: string;
  sales_rep_name?: string;
  sales_rep_email?: string;
  sales_rep_phone?: string;
  subtotal: number;
  discount_percent: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  amount_invoiced: number;
  balance_due: number;
  currency: string;
  status: 'draft' | 'pending' | 'confirmed' | 'partial' | 'delivered' | 'invoiced' | 'closed' | 'cancelled';
  payment_terms?: string;
  customer_po_number?: string;
  memo?: string;
  notes?: string;
  ship_to_address?: string;
  shipping_method?: string;
  email_sent: boolean;
  email_sent_date?: string;
  acknowledged_date?: string;
  line_items?: SalesOrderLineItem[];
  created_at?: string;
  updated_at?: string;
  created_by?: number;
}

export interface SalesOrderStats {
  total_orders: number;
  total_value: number;
  status_counts: Record<string, number>;
  pending_delivery_value: number;
  recent_orders_30_days: number;
}

export interface SalesOrderFilters {
  status?: string;
  customer?: number;
  currency?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
  ordering?: string;
}

class SalesOrderService {
  private baseUrl = '/sales/sales-orders';

  async getAll(filters?: SalesOrderFilters): Promise<{ results: SalesOrder[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await api.get(`${this.baseUrl}/?${params.toString()}`);
    return response.data;
  }

  async getById(id: number): Promise<SalesOrder> {
    const response = await api.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  async create(salesOrder: Partial<SalesOrder>): Promise<SalesOrder> {
    const response = await api.post(`${this.baseUrl}/`, salesOrder);
    return response.data;
  }

  async update(id: number, salesOrder: Partial<SalesOrder>): Promise<SalesOrder> {
    const response = await api.put(`${this.baseUrl}/${id}/`, salesOrder);
    return response.data;
  }

  async partialUpdate(id: number, salesOrder: Partial<SalesOrder>): Promise<SalesOrder> {
    const response = await api.patch(`${this.baseUrl}/${id}/`, salesOrder);
    return response.data;
  }

  async delete(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/${id}/`);
  }

  async getStats(): Promise<SalesOrderStats> {
    const response = await api.get(`${this.baseUrl}/stats/`);
    return response.data;
  }

  async getPendingDelivery(): Promise<SalesOrder[]> {
    const response = await api.get(`${this.baseUrl}/pending_delivery/`);
    return response.data;
  }

  async confirm(id: number): Promise<{ status: string }> {
    const response = await api.post(`${this.baseUrl}/${id}/confirm/`);
    return response.data;
  }

  async cancel(id: number): Promise<{ status: string }> {
    const response = await api.post(`${this.baseUrl}/${id}/cancel/`);
    return response.data;
  }

  async getDeliveryNotes(id: number): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/${id}/delivery_notes/`);
    return response.data;
  }

  // Helper method to calculate line totals
  calculateLineTotal(item: Partial<SalesOrderLineItem>): number {
    const subtotal = (item.quantity || 0) * (item.unit_price || 0);
    const discount = subtotal * ((item.discount_percent || 0) / 100);
    return subtotal - discount;
  }

  // Helper method to calculate tax amount
  calculateTaxAmount(item: Partial<SalesOrderLineItem>): number {
    if (!item.taxable) return 0;
    const lineTotal = this.calculateLineTotal(item);
    return lineTotal * ((item.tax_rate || 0) / 100);
  }

  // Helper method to calculate order totals
  calculateOrderTotals(lineItems: SalesOrderLineItem[]): {
    subtotal: number;
    tax_amount: number;
    total_amount: number;
  } {
    const subtotal = lineItems.reduce((sum, item) => sum + item.line_total, 0);
    const tax_amount = lineItems.reduce((sum, item) => sum + item.tax_amount, 0);
    const total_amount = subtotal + tax_amount;

    return { subtotal, tax_amount, total_amount };
  }
}

export default new SalesOrderService(); 