import { CurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

/**
 * Enhanced currency formatting utility for CMS
 * Provides consistent currency formatting across all CMS components
 */

export interface CurrencyOption {
  code: string;
  name: string;
  symbol: string;
}

/**
 * Format currency amount with proper locale support
 */
export const formatCurrency = (
  amount: number,
  currencyCode?: string,
  currencyInfo?: CurrencyInfo | null,
  options?: {
    showSymbol?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  }
): string => {
  if (!currencyInfo) return '0.00';
  
  const currency = currencyCode || currencyInfo.functional_currency;
  const locale = currency === 'INR' ? 'en-IN' : currency === 'EUR' ? 'de-DE' : 'en-US';
  
  const formatOptions: Intl.NumberFormatOptions = {
    style: options?.showSymbol !== false ? 'currency' : 'decimal',
    currency: currency,
    minimumFractionDigits: options?.minimumFractionDigits ?? 2,
    maximumFractionDigits: options?.maximumFractionDigits ?? 2,
  };

  try {
    return new Intl.NumberFormat(locale, formatOptions).format(amount);
  } catch (error) {
    // Fallback to USD if currency is not supported
    return new Intl.NumberFormat('en-US', {
      ...formatOptions,
      currency: 'USD'
    }).format(amount);
  }
};

/**
 * Format currency with color based on amount (positive/negative)
 */
export const formatCurrencyWithColor = (
  amount: number,
  currencyCode?: string,
  currencyInfo?: CurrencyInfo | null
): { formattedAmount: string; color: string } => {
  const formattedAmount = formatCurrency(amount, currencyCode, currencyInfo);
  const color = amount < 0 ? 'error.main' : amount > 0 ? 'success.main' : 'text.primary';
  
  return { formattedAmount, color };
};

/**
 * Get currency symbol from currency code
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  const symbols: Record<string, string> = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'INR': '₹',
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'Fr',
    'CNY': '¥',
    'SEK': 'kr',
    'NZD': 'NZ$',
  };
  
  return symbols[currencyCode] || currencyCode;
};

/**
 * Get available currencies for selection
 */
export const getAvailableCurrencies = (currencyInfo?: CurrencyInfo | null): CurrencyOption[] => {
  if (!currencyInfo?.available_currencies) {
    // Default currencies if not available from API
    return [
      { code: 'USD', name: 'US Dollar', symbol: '$' },
      { code: 'EUR', name: 'Euro', symbol: '€' },
      { code: 'GBP', name: 'British Pound', symbol: '£' },
      { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
      { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    ];
  }
  
  return currencyInfo.available_currencies;
};

/**
 * Get functional currency info
 */
export const getFunctionalCurrency = (currencyInfo?: CurrencyInfo | null): CurrencyOption => {
  if (!currencyInfo) {
    return { code: 'USD', name: 'US Dollar', symbol: '$' };
  }
  
  return {
    code: currencyInfo.functional_currency,
    name: currencyInfo.available_currencies?.find(c => c.code === currencyInfo.functional_currency)?.name || currencyInfo.functional_currency,
    symbol: currencyInfo.functional_currency_symbol
  };
};

/**
 * Get reporting currency info
 */
export const getReportingCurrency = (currencyInfo?: CurrencyInfo | null): CurrencyOption => {
  if (!currencyInfo) {
    return { code: 'USD', name: 'US Dollar', symbol: '$' };
  }
  
  return {
    code: currencyInfo.reporting_currency,
    name: currencyInfo.available_currencies?.find(c => c.code === currencyInfo.reporting_currency)?.name || currencyInfo.reporting_currency,
    symbol: currencyInfo.reporting_currency_symbol
  };
};

/**
 * Format amount for input display (without currency symbol)
 */
export const formatAmountForInput = (amount: number): string => {
  return amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

/**
 * Parse amount from input string
 */
export const parseAmountFromInput = (value: string): number => {
  // Remove currency symbols, commas, and spaces
  const cleanValue = value.replace(/[^0-9.-]/g, '');
  const parsed = parseFloat(cleanValue);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Validate currency code
 */
export const isValidCurrencyCode = (code: string, currencyInfo?: CurrencyInfo | null): boolean => {
  if (!currencyInfo?.available_currencies) {
    // Basic validation for common currencies
    const commonCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'INR', 'CAD', 'AUD', 'CHF', 'CNY'];
    return commonCurrencies.includes(code);
  }
  
  return currencyInfo.available_currencies.some(currency => currency.code === code);
};

/**
 * Get exchange rate display (placeholder for future implementation)
 */
export const formatExchangeRate = (rate: number, fromCurrency: string, toCurrency: string): string => {
  return `1 ${fromCurrency} = ${rate.toFixed(4)} ${toCurrency}`;
};

/**
 * Check if two currencies are the same
 */
export const isSameCurrency = (currency1: string, currency2: string): boolean => {
  return currency1.toUpperCase() === currency2.toUpperCase();
};

/**
 * Get currency precision (decimal places) for specific currencies
 */
export const getCurrencyPrecision = (currencyCode: string): number => {
  const noPrecisionCurrencies = ['JPY', 'KRW', 'VND', 'CLP']; // Currencies without decimal places
  return noPrecisionCurrencies.includes(currencyCode) ? 0 : 2;
};

export default {
  formatCurrency,
  formatCurrencyWithColor,
  getCurrencySymbol,
  getAvailableCurrencies,
  getFunctionalCurrency,
  getReportingCurrency,
  formatAmountForInput,
  parseAmountFromInput,
  isValidCurrencyCode,
  formatExchangeRate,
  isSameCurrency,
  getCurrencyPrecision,
}; 