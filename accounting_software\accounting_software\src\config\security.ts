/**
 * Frontend Security Configuration
 * Centralizes all security-related settings and utilities
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
} as const;

// File Upload Security
export const FILE_UPLOAD_CONFIG = {
  MAX_SIZE: 20 * 1024 * 1024, // 20MB
  MAX_FILES: 5,
  ALLOWED_EXTENSIONS: [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
    '.txt', '.csv', '.zip', '.rar'
  ],
  ALLOWED_MIME_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/svg+xml',
    'text/plain',
    'text/csv',
    'application/zip',
    'application/x-rar-compressed'
  ],
  DANGEROUS_EXTENSIONS: [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs',
    '.js', '.jar', '.app', '.deb', '.pkg', '.dmg', '.msi',
    '.php', '.asp', '.aspx', '.jsp', '.cgi', '.pl', '.py',
    '.sh', '.bash', '.ps1', '.psm1'
  ]
} as const;

// Authentication Security
export const AUTH_CONFIG = {
  TOKEN_KEY: 'authToken',
  REFRESH_TOKEN_KEY: 'refreshToken',
  SESSION_TIMEOUT: 3600000, // 1 hour in milliseconds
  INACTIVITY_TIMEOUT: 900000, // 15 minutes in milliseconds
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 300000, // 5 minutes in milliseconds
} as const;

// Input Validation Patterns
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[\+]?[1-9][\d]{0,15}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$/,
  ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
  NUMERIC: /^[0-9]+$/,
  DECIMAL: /^\d+(\.\d{1,2})?$/,
  DATE: /^\d{4}-\d{2}-\d{2}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
} as const;

// Content Security Policy
export const CSP_CONFIG = {
  'default-src': "'self'",
  'script-src': "'self' 'unsafe-inline'",
  'style-src': "'self' 'unsafe-inline' https://fonts.googleapis.com",
  'img-src': "'self' data: https:",
  'font-src': "'self' https://fonts.gstatic.com",
  'connect-src': `'self' ${API_CONFIG.BASE_URL}`,
  'frame-ancestors': "'none'",
  'base-uri': "'self'",
  'form-action': "'self'"
} as const;

// Security Headers
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
} as const;

// Rate Limiting
export const RATE_LIMIT_CONFIG = {
  API_CALLS_PER_MINUTE: 60,
  LOGIN_ATTEMPTS_PER_MINUTE: 5,
  FILE_UPLOADS_PER_HOUR: 20
} as const;

/**
 * File Security Validator
 */
export class FileSecurityValidator {
  static validateFile(file: File): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check file size
    if (file.size > FILE_UPLOAD_CONFIG.MAX_SIZE) {
      errors.push(`File size exceeds maximum allowed size of ${this.formatFileSize(FILE_UPLOAD_CONFIG.MAX_SIZE)}`);
    }

    // Check file extension
    const extension = this.getFileExtension(file.name);
    if (FILE_UPLOAD_CONFIG.DANGEROUS_EXTENSIONS.includes(extension)) {
      errors.push(`File extension '${extension}' is not allowed for security reasons`);
    }

    if (!FILE_UPLOAD_CONFIG.ALLOWED_EXTENSIONS.includes(extension)) {
      errors.push(`File extension '${extension}' is not supported`);
    }

    // Check MIME type
    if (!FILE_UPLOAD_CONFIG.ALLOWED_MIME_TYPES.includes(file.type)) {
      errors.push(`File type '${file.type}' is not allowed`);
    }

    // Check filename for suspicious patterns
    if (this.hasSuspiciousFilename(file.name)) {
      errors.push('Filename contains suspicious characters');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  static getFileExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    return lastDot === -1 ? '' : filename.substring(lastDot).toLowerCase();
  }

  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  static hasSuspiciousFilename(filename: string): boolean {
    const suspiciousPatterns = [
      /\.\./,           // Directory traversal
      /[<>:"|?*]/,      // Invalid filename characters
      /^\./,            // Hidden files
      /\s+$/,           // Trailing whitespace
      /^con$|^prn$|^aux$|^nul$/i,  // Windows reserved names
    ];

    return suspiciousPatterns.some(pattern => pattern.test(filename));
  }
}

/**
 * Input Sanitizer
 */
export class InputSanitizer {
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential XSS characters
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .substring(0, 1000); // Limit length
  }

  static sanitizeNumber(input: string | number): number | null {
    const num = typeof input === 'string' ? parseFloat(input) : input;
    return isNaN(num) ? null : num;
  }

  static sanitizeEmail(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    const sanitized = input.trim().toLowerCase();
    return VALIDATION_PATTERNS.EMAIL.test(sanitized) ? sanitized : '';
  }

  static sanitizeFilename(filename: string): string {
    if (!filename || typeof filename !== 'string') return '';
    
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special characters
      .replace(/\.+/g, '.') // Multiple dots to single dot
      .replace(/^\./, '') // Remove leading dot
      .substring(0, 255); // Limit length
  }
}

/**
 * Session Security Manager
 */
export class SessionManager {
  private static instance: SessionManager;
  private lastActivity: number = Date.now();
  private inactivityTimer: NodeJS.Timeout | null = null;

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  startInactivityTimer(): void {
    this.resetInactivityTimer();
    
    // Set up automatic logout on inactivity
    this.inactivityTimer = setTimeout(() => {
      this.handleInactivity();
    }, AUTH_CONFIG.INACTIVITY_TIMEOUT);
  }

  resetInactivityTimer(): void {
    this.lastActivity = Date.now();
    
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }
  }

  private handleInactivity(): void {
    // Clear tokens and redirect to login
    localStorage.removeItem(AUTH_CONFIG.TOKEN_KEY);
    localStorage.removeItem(AUTH_CONFIG.REFRESH_TOKEN_KEY);
    
    // Show inactivity message
    alert('Session expired due to inactivity. Please log in again.');
    
    // Redirect to login page
    window.location.href = '/login';
  }

  isSessionValid(): boolean {
    const token = localStorage.getItem(AUTH_CONFIG.TOKEN_KEY);
    if (!token) return false;

    // Check if session has expired
    const lastActivity = this.lastActivity;
    const now = Date.now();
    
    return (now - lastActivity) < AUTH_CONFIG.SESSION_TIMEOUT;
  }
}

/**
 * Security Event Logger
 */
export class SecurityLogger {
  static log(event: string, details: Record<string, any> = {}): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.getSessionId()
    };

    // Log to console in development
    if (import.meta.env.DEV) {
      console.warn('Security Event:', logEntry);
    }

    // In production, you might want to send this to a logging service
    this.sendToLoggingService(logEntry);
  }

  private static getSessionId(): string {
    return sessionStorage.getItem('sessionId') || 'anonymous';
  }

  private static sendToLoggingService(logEntry: Record<string, any>): void {
    // Implementation depends on your logging service
    // For now, just store in localStorage for debugging
    const logs = JSON.parse(localStorage.getItem('securityLogs') || '[]');
    logs.push(logEntry);
    
    // Keep only last 100 logs
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    localStorage.setItem('securityLogs', JSON.stringify(logs));
  }
}

/**
 * Initialize security measures
 */
export function initializeSecurity(): void {
  // Start session monitoring
  const sessionManager = SessionManager.getInstance();
  sessionManager.startInactivityTimer();

  // Set up activity listeners
  const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
  events.forEach(event => {
    document.addEventListener(event, () => {
      sessionManager.resetInactivityTimer();
    }, true);
  });

  // Log security initialization
  SecurityLogger.log('security_initialized', {
    userAgent: navigator.userAgent,
    screen: {
      width: screen.width,
      height: screen.height
    }
  });
}

export {
  API_CONFIG,
  FILE_UPLOAD_CONFIG,
  AUTH_CONFIG,
  VALIDATION_PATTERNS,
  CSP_CONFIG,
  SECURITY_HEADERS,
  RATE_LIMIT_CONFIG
}; 