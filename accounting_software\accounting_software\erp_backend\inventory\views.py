from django.shortcuts import render
from django.db import models
from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    StockTransaction, Warehouse, 
    GoodsReceiptNote, GoodsReceiptNoteItem, 
    GoodsReturnNote, GoodsReturnNoteItem, Inventory,
    InventoryTransfer, UnitsOfMeasure
)
from .serializers import (
    StockTransactionSerializer, WarehouseSerializer,
    InventorySerializer, GoodsReceiptNoteSerializer, GoodsReceiptNoteItemSerializer,
    GoodsReceiptNoteCreateSerializer, GoodsReturnNoteSerializer, GoodsReturnNoteItemSerializer,
    InventoryTransferSerializer, InventoryTransferCreateSerializer,
    UnitsOfMeasureSerializer, GoodsReturnNoteCreateSerializer
)
from purchase.models import PurchaseOrder
from gl.models import JournalEntry
from sales.models import Product  # Import Product from sales app
from .pagination import InventoryPagination, NoPagination


class WarehouseViewSet(viewsets.ModelViewSet):
    """ViewSet for managing warehouses"""
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'code', 'address']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class UnitsOfMeasureViewSet(viewsets.ModelViewSet):
    """ViewSet for managing units of measure"""
    queryset = UnitsOfMeasure.objects.all()
    serializer_class = UnitsOfMeasureSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['unit_type', 'is_active']
    search_fields = ['unit_name', 'abbreviation']
    ordering_fields = ['unit_name', 'created_at']
    ordering = ['unit_name']


class GoodsReceiptNoteViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Goods Receipt Notes"""
    queryset = GoodsReceiptNote.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'warehouse', 'purchase_order']
    search_fields = ['grn_number', 'purchase_order__po_number', 'purchase_order__vendor__display_name']
    ordering_fields = ['receipt_date', 'created_at', 'grn_number']
    ordering = ['-receipt_date', '-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return GoodsReceiptNoteCreateSerializer
        return GoodsReceiptNoteSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(receipt_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(receipt_date__lte=end_date)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get GRN statistics"""
        queryset = self.get_queryset()
        
        total_grns = queryset.count()
        draft_grns = queryset.filter(status='DRAFT').count()
        received_grns = queryset.filter(status='RECEIVED').count()
        posted_grns = queryset.filter(status='POSTED').count()
        
        # Get recent GRNs (last 30 days)
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_grns = queryset.filter(receipt_date__gte=thirty_days_ago).count()
        
        # Calculate total value
        total_value = queryset.aggregate(
            total=Sum('total_value')
        )['total'] or 0
        
        return Response({
            'total_grns': total_grns,
            'draft_grns': draft_grns,
            'received_grns': received_grns,
            'posted_grns': posted_grns,
            'recent_grns': recent_grns,
            'total_value': float(total_value),
        })

    @action(detail=True, methods=['post'])
    def post_to_inventory(self, request, pk=None):
        """Post GRN to inventory"""
        grn = self.get_object()
        
        if grn.status != 'RECEIVED':
            return Response(
                {'error': 'GRN must be in RECEIVED status to post to inventory'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Create stock transactions and update inventory for each item
            for item in grn.items.all():
                if item.condition == 'GOOD' and item.quantity_received > 0:
                    # Create stock transaction
                    item.create_stock_transaction()
                    
                    # Update inventory
                    item.update_inventory()
                    
                    # Journal entry will be created when bill is generated manually
                    # item.create_journal_entry()
                    
                    # Update PO line item received quantity
                    po_line_item = grn.purchase_order.line_items.filter(product=item.product).first()
                    if po_line_item:
                        po_line_item.quantity_received += item.quantity_received
                        po_line_item.save()
            
            # Update PO status based on received quantities
            po = grn.purchase_order
            total_ordered = sum(line.quantity for line in po.line_items.all())
            total_received = sum(line.quantity_received for line in po.line_items.all())
            
            if total_received >= total_ordered:
                po.status = 'received'
            elif total_received > 0:
                po.status = 'partial'
            po.save()
            
            # Update GRN status
            grn.status = 'POSTED'
            grn.posted_at = timezone.now()
            grn.posted_by = request.user
            grn.save()
            
            return Response({'message': 'GRN posted to inventory successfully'})
            
        except Exception as e:
            return Response(
                {'error': f'Failed to post GRN to inventory: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def pending_pos(self, request):
        """Get Purchase Orders pending receipt with proper validation"""
        try:
            from django.db.models import Sum, F, Case, When, DecimalField
            
            # Get POs that are in valid status for GRN creation
            pending_pos = PurchaseOrder.objects.filter(
                status__in=['sent', 'acknowledged', 'partial']
            ).select_related('vendor').prefetch_related('line_items__product')
            
            po_data = []
            for po in pending_pos:
                # Check if this PO has any existing GRNs
                existing_grns = GoodsReceiptNote.objects.filter(
                    purchase_order=po,
                    status__in=['DRAFT', 'RECEIVED', 'POSTED']  # Exclude cancelled GRNs
                )
                
                # Calculate total quantities ordered vs received
                line_items = po.line_items.all()
                total_ordered = sum(item.quantity for item in line_items)
                
                # Calculate actually received quantities from POSTED GRNs only
                # Don't count DRAFT or RECEIVED GRNs as they haven't been posted to inventory yet
                posted_grns = existing_grns.filter(status='POSTED')
                total_received = 0
                
                for grn in posted_grns:
                    for grn_item in grn.items.all():
                        if grn_item.product and grn_item.condition == 'GOOD':
                            total_received += grn_item.quantity_received
                
                remaining_quantity = max(0, total_ordered - total_received)  # Ensure never negative
                
                # Determine if PO is eligible for GRN creation
                can_create_grn = False
                grn_status = ""
                
                if not existing_grns.exists():
                    # No GRNs exist - can create new GRN
                    can_create_grn = True
                    grn_status = "Ready for first receipt"
                elif remaining_quantity > 0:
                    # Partial receipt - can create additional GRN for remaining items
                    can_create_grn = True
                    grn_status = f"Partially received - {remaining_quantity} units remaining"
                else:
                    # Fully received - cannot create new GRN
                    can_create_grn = False
                    grn_status = "Fully received - complete"
                
                # Only include POs that can have GRNs created
                if can_create_grn:
                    # Calculate per-product remaining quantities
                    line_items_data = []
                    for line_item in line_items:
                        # Calculate received quantity for this specific product from posted GRNs
                        product_received = 0
                        for grn in posted_grns:
                            for grn_item in grn.items.filter(product=line_item.product, condition='GOOD'):
                                product_received += grn_item.quantity_received
                        
                        product_remaining = max(0, line_item.quantity - product_received)
                        
                        line_items_data.append({
                            'id': line_item.id,
                            'product_id': line_item.product.id if line_item.product else None,
                            'product_name': line_item.product.name if line_item.product else line_item.description,
                            'product_sku': line_item.product.sku if line_item.product else '',
                            'description': line_item.description,
                            'quantity_ordered': float(line_item.quantity),
                            'quantity_received': float(product_received),
                            'quantity_remaining': float(product_remaining),
                            'unit_price': float(line_item.unit_price),
                            'unit_of_measure': line_item.unit_of_measure,
                            'taxable': line_item.taxable,
                            'tax_rate': float(line_item.tax_rate),
                            'has_remaining': product_remaining > 0,
                        })
                    
                    # Safe vendor name extraction
                    vendor_name = "Unknown Vendor"
                    if po.vendor:
                        if hasattr(po.vendor, 'display_name') and po.vendor.display_name:
                            vendor_name = po.vendor.display_name
                        elif hasattr(po.vendor, 'name') and po.vendor.name:
                            vendor_name = po.vendor.name
                        else:
                            vendor_name = str(po.vendor)

                    po_data.append({
                        'po_id': po.id,
                        'po_number': po.po_number,
                        'vendor_name': vendor_name,
                        'po_date': po.po_date,
                        'total_amount': float(po.total_amount) if po.total_amount else 0.0,
                        'status': po.status,
                        'line_items_count': po.line_items.count(),
                        'total_ordered': float(total_ordered),
                        'total_received': float(total_received),
                        'remaining_quantity': float(remaining_quantity),
                        'grn_status': grn_status,
                        'existing_grns_count': existing_grns.count(),
                        'can_create_grn': can_create_grn,
                        'line_items': line_items_data,  # Include detailed line item data
                    })
            
            return Response(po_data)
        
        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in pending_pos endpoint: {str(e)}", exc_info=True)
            
            return Response(
                {'error': f'Failed to fetch pending purchase orders: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class InventoryViewSet(viewsets.ModelViewSet):
    """ViewSet for managing inventory"""
    queryset = Inventory.objects.all()
    serializer_class = InventorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['warehouse', 'product']
    search_fields = ['product__name', 'product__sku', 'warehouse__name']
    ordering_fields = ['product__name', 'quantity_on_hand', 'last_transaction_date']
    ordering = ['product__name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by low stock
        low_stock = self.request.query_params.get('low_stock')
        if low_stock == 'true':
            from django.db import models as django_models
            queryset = queryset.filter(
                quantity_on_hand__lte=django_models.F('reorder_point')
            )
        
        # Filter by out of stock
        out_of_stock = self.request.query_params.get('out_of_stock')
        if out_of_stock == 'true':
            queryset = queryset.filter(quantity_on_hand=0)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get inventory statistics"""
        queryset = self.get_queryset()
        
        from django.db import models as django_models
        
        total_items = queryset.count()
        low_stock_items = queryset.filter(
            quantity_on_hand__lte=django_models.F('reorder_point')
        ).count()
        out_of_stock_items = queryset.filter(quantity_on_hand=0).count()
        
        # Calculate total inventory value
        total_value = 0
        for item in queryset:
            total_value += item.quantity_on_hand * item.average_cost
        
        return Response({
            'total_items': total_items,
            'low_stock_items': low_stock_items,
            'out_of_stock_items': out_of_stock_items,
            'total_value': float(total_value),
        })


class StockTransactionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for viewing stock transactions"""
    queryset = StockTransaction.objects.all()
    serializer_class = StockTransactionSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = InventoryPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['transaction_type', 'reference_type', 'warehouse', 'product']
    search_fields = ['product__name', 'product__sku', 'description', 'batch_number']
    ordering_fields = ['txn_date', 'created_at']
    ordering = ['-txn_date', '-created_at']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(txn_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(txn_date__lte=end_date)
        
        return queryset

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get stock transaction summary"""
        queryset = self.get_queryset()
        
        # Group by transaction type
        summary = {}
        transaction_types = [
            ('RECEIPT', 'Goods Receipt'),
            ('ISSUE', 'Stock Issue'),
            ('TRANSFER', 'Warehouse Transfer'),
            ('ADJUSTMENT', 'Stock Adjustment'),
            ('SALE', 'Sale Transaction'),
            ('RETURN', 'Return Transaction'),
            ('DAMAGE', 'Damage/Loss'),
            ('OPENING', 'Opening Balance'),
        ]
        for txn_type, _ in transaction_types:
            txn_count = queryset.filter(transaction_type=txn_type).count()
            txn_value = queryset.filter(transaction_type=txn_type).aggregate(
                total=Sum('total_cost')
            )['total'] or 0
            
            summary[txn_type] = {
                'count': txn_count,
                'total_value': float(txn_value)
            }
        
        return Response(summary)


class GoodsReturnNoteViewSet(viewsets.ModelViewSet):
    """ViewSet for managing Goods Return Notes"""
    queryset = GoodsReturnNote.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'warehouse', 'vendor', 'return_reason']
    search_fields = ['grn_return_number', 'vendor__name', 'vendor__display_name', 'original_grn__grn_number']
    ordering_fields = ['return_date', 'created_at', 'grn_return_number']
    ordering = ['-return_date', '-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return GoodsReturnNoteCreateSerializer
        return GoodsReturnNoteSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(return_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(return_date__lte=end_date)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get Good Return Note statistics"""
        queryset = self.get_queryset()
        
        total_returns = queryset.count()
        draft_returns = queryset.filter(status='DRAFT').count()
        approved_returns = queryset.filter(status='APPROVED').count()
        returned_returns = queryset.filter(status='RETURNED').count()
        posted_returns = queryset.filter(status='POSTED').count()
        
        # Get recent returns (last 30 days)
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_returns = queryset.filter(return_date__gte=thirty_days_ago).count()
        
        # Calculate total value
        total_value = queryset.aggregate(
            total=Sum('total_value')
        )['total'] or 0
        
        # Calculate expected credit amount
        expected_credit = queryset.aggregate(
            total=Sum('expected_credit_amount')
        )['total'] or 0
        
        return Response({
            'total_returns': total_returns,
            'draft_returns': draft_returns,
            'approved_returns': approved_returns,
            'returned_returns': returned_returns,
            'posted_returns': posted_returns,
            'recent_returns': recent_returns,
            'total_value': float(total_value),
            'expected_credit': float(expected_credit),
        })

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a Good Return Note"""
        return_note = self.get_object()
        
        if return_note.status != 'DRAFT':
            return Response(
                {'error': 'Only DRAFT returns can be approved'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            return_note.status = 'APPROVED'
            return_note.approved_at = timezone.now()
            return_note.approved_by = request.user
            return_note.save()
            
            return Response({'message': 'Return note approved successfully'})
            
        except Exception as e:
            return Response(
                {'error': f'Failed to approve return note: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def mark_returned(self, request, pk=None):
        """Mark a Good Return Note as returned to vendor"""
        return_note = self.get_object()
        
        if return_note.status != 'APPROVED':
            return Response(
                {'error': 'Return note must be APPROVED to mark as returned'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            return_note.status = 'RETURNED'
            return_note.save()
            
            return Response({'message': 'Return note marked as returned successfully'})
            
        except Exception as e:
            return Response(
                {'error': f'Failed to mark return note as returned: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def post_to_inventory(self, request, pk=None):
        """Post Good Return Note to inventory"""
        return_note = self.get_object()
        
        if return_note.status != 'RETURNED':
            return Response(
                {'error': 'Return note must be RETURNED to post to inventory'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Create stock transactions and update inventory for each item
            for item in return_note.items.all():
                if item.quantity_returned > 0:
                    # Create stock transaction (negative quantity for return)
                    item.create_stock_transaction()
                    
                    # Update inventory (reduce quantities)
                    item.update_inventory()
                    
                    # Journal entry will be created when bill is generated manually
                    # item.create_journal_entry()
            
            # Update return note status
            return_note.status = 'POSTED'
            return_note.posted_at = timezone.now()
            return_note.posted_by = request.user
            return_note.save()
            
            return Response({'message': 'Return note posted to inventory successfully'})
            
        except Exception as e:
            return Response(
                {'error': f'Failed to post return note to inventory: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def returnable_grns(self, request):
        """Get GRNs that have items available for return"""
        try:
            print("🔍 DEBUG: Starting returnable_grns endpoint")
            
            # Get all GRNs first to see what we have
            all_grns = GoodsReceiptNote.objects.all()
            print(f"🔍 DEBUG: Total GRNs in database: {all_grns.count()}")
            
            # If no GRNs at all, return helpful message
            if all_grns.count() == 0:
                return Response({
                    'message': 'No GRNs found in database. Please create some GRNs first.',
                    'debug_info': {
                        'total_grns': 0,
                        'purchase_orders': PurchaseOrder.objects.count(),
                        'help': 'Create Purchase Orders first, then create GRNs from those POs.'
                    }
                })
            
            # Show status breakdown
            status_breakdown = {}
            for status in ['DRAFT', 'RECEIVED', 'POSTED', 'CANCELLED']:
                count = GoodsReceiptNote.objects.filter(status=status).count()
                status_breakdown[status] = count
                print(f"🔍 DEBUG: {status}: {count}")
            
            # Get GRNs that could potentially be returnable (relaxed criteria for debugging)
            returnable_grns = GoodsReceiptNote.objects.filter(
                status__in=['DRAFT', 'RECEIVED', 'POSTED']  # Temporarily include DRAFT for testing
            ).select_related(
                'purchase_order',
                'purchase_order__vendor',
                'warehouse'
            ).prefetch_related('items__product')
            
            print(f"🔍 DEBUG: Found {returnable_grns.count()} GRNs with correct status")

            returnable_grns_data = []
            grns_with_issues = []

            for grn in returnable_grns:
                print(f"🔍 DEBUG: Processing GRN {grn.grn_number}")
                issues = []
                
                # Check vendor
                if not grn.purchase_order or not grn.purchase_order.vendor:
                    issues.append('No vendor (missing purchase order or vendor)')
                
                # Check warehouse
                if not grn.warehouse:
                    issues.append('No warehouse assigned')
                
                # Check items - get items with proper product info
                grn_items = grn.items.select_related('product').all()
                print(f"🔍 DEBUG: GRN {grn.grn_number} has {grn_items.count()} items")
                
                if grn_items.count() == 0:
                    issues.append('No items in GRN')
                
                # Get returnable items (items that haven't been fully returned)
                returnable_items = []
                for item in grn_items:
                    if not item.product:
                        issues.append(f'Item {item.grn_item_id} has no product assigned')
                        continue
                        
                    # Calculate returned quantity for this item
                    returned_qty = GoodsReturnNoteItem.objects.filter(
                        original_grn_item=item
                    ).aggregate(
                        total_returned=models.Sum('quantity_returned')
                    )['total_returned'] or 0
                    
                    available_qty = item.quantity_received - returned_qty
                    
                    if available_qty > 0:
                        returnable_items.append({
                            'grn_item_id': item.grn_item_id,
                            'product': item.product.id,
                            'product_name': item.product.name,
                            'product_sku': item.product.sku or '',
                            'quantity': item.quantity_received,
                            'returned_quantity': returned_qty,
                            'available_quantity': available_qty,
                            'unit_price': str(item.unit_cost),
                            'batch_number': item.batch_number or '',
                            'condition': item.condition or 'GOOD',
                        })
                
                if len(returnable_items) == 0 and len(issues) == 0:
                    issues.append('All items have been fully returned')
                
                # If there are issues, add to issues list
                if issues:
                    grns_with_issues.append({
                        'grn_id': grn.grn_id,
                        'grn_number': grn.grn_number,
                        'status': grn.status,
                        'vendor_name': grn.purchase_order.vendor.name if grn.purchase_order and grn.purchase_order.vendor else 'No Vendor',
                        'po_number': grn.purchase_order.po_number if grn.purchase_order else 'No PO',
                        'warehouse_name': grn.warehouse.name if grn.warehouse else 'No Warehouse',
                        'issues': issues,
                        'returnable_items': returnable_items,
                    })
                    print(f"🔍 DEBUG: GRN {grn.grn_number} has issues: {issues}")
                else:
                    # No issues - add to returnable list
                    returnable_grns_data.append({
                        'grn_id': grn.grn_id,
                        'grn_number': grn.grn_number,
                        'vendor_id': grn.purchase_order.vendor.id if grn.purchase_order and grn.purchase_order.vendor else None,
                        'vendor_name': grn.purchase_order.vendor.name if grn.purchase_order and grn.purchase_order.vendor else 'Unknown Vendor',
                        'po_number': grn.purchase_order.po_number if grn.purchase_order else 'No PO',
                        'warehouse_id': grn.warehouse.warehouse_id if grn.warehouse else None,
                        'warehouse_name': grn.warehouse.name if grn.warehouse else 'No Warehouse',
                        'receipt_date': grn.receipt_date.isoformat() if grn.receipt_date else None,
                        'status': grn.status,
                        'total_returnable_qty': sum(item['available_quantity'] for item in returnable_items),
                        'total_returnable_value': float(sum(float(item['available_quantity']) * float(item['unit_price']) for item in returnable_items)),
                        'returnable_items': returnable_items,
                        'existing_returns_count': 0,  # Could be calculated if needed
                    })
                    print(f"🔍 DEBUG: GRN {grn.grn_number} is returnable with {len(returnable_items)} items")
            
            print(f"🔍 DEBUG: Final returnable GRNs count: {len(returnable_grns_data)}")
            
            # Return comprehensive debug information
            response_data = {
                'returnable_grns': returnable_grns_data,
                'debug_info': {
                    'total_grns_in_db': all_grns.count(),
                    'status_breakdown': status_breakdown,
                    'grns_with_issues': grns_with_issues,
                    'help': 'Check debug_info.grns_with_issues to see what problems prevent GRNs from being returnable'
                }
            }
            
            # If no returnable GRNs, return the list anyway for debugging
            if len(returnable_grns_data) == 0:
                response_data['message'] = 'No returnable GRNs found. Check debug_info for details.'
            
            return Response(response_data)
            
        except Exception as e:
            import traceback
            print('🔍 DEBUG: Error in returnable_grns:', str(e))
            print('🔍 DEBUG: Traceback:', traceback.format_exc())
            return Response(
                {
                    'error': f'Failed to get returnable GRNs: {str(e)}',
                    'traceback': traceback.format_exc()
                },
                status=500
            )


class InventoryTransferViewSet(viewsets.ModelViewSet):
    """ViewSet for managing inventory transfers"""
    queryset = InventoryTransfer.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'from_warehouse', 'to_warehouse']
    search_fields = ['transfer_number', 'notes']
    ordering_fields = ['transfer_date', 'created_at']
    ordering = ['-transfer_date', '-created_at']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return InventoryTransferCreateSerializer
        return InventoryTransferSerializer
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """Complete an inventory transfer"""
        transfer = self.get_object()
        
        if transfer.status != 'PENDING':
            return Response(
                {'error': 'Only pending transfers can be completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            transfer.complete_transfer(request.user)
            serializer = self.get_serializer(transfer)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def set_pending(self, request, pk=None):
        """Set transfer status to pending"""
        transfer = self.get_object()
        
        if transfer.status != 'DRAFT':
            return Response(
                {'error': 'Only draft transfers can be set to pending'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        transfer.status = 'PENDING'
        transfer.save()
        
        serializer = self.get_serializer(transfer)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get transfer statistics"""
        total_transfers = self.get_queryset().count()
        draft_transfers = self.get_queryset().filter(status='DRAFT').count()
        pending_transfers = self.get_queryset().filter(status='PENDING').count()
        completed_transfers = self.get_queryset().filter(status='COMPLETED').count()
        
        # Recent transfers (last 30 days)
        recent_date = timezone.now() - timedelta(days=30)
        recent_transfers = self.get_queryset().filter(created_at__gte=recent_date).count()
        
        return Response({
            'total_transfers': total_transfers,
            'draft_transfers': draft_transfers,
            'pending_transfers': pending_transfers,
            'completed_transfers': completed_transfers,
            'recent_transfers': recent_transfers,
        }) 