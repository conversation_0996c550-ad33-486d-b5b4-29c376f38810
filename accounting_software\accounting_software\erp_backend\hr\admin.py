# HR Admin Configuration
from django.contrib import admin
from .models import Employee, Department

@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('get_full_name', 'get_email', 'get_department', 'get_position', 'status', 'join_date')
    list_filter = ('status', 'employment_type', 'contact_employee__department')
    search_fields = ('contact_employee__first_name', 'contact_employee__last_name', 
                    'contact_employee__contact__email', 'contact_employee__department', 
                    'contact_employee__position')
    ordering = ('contact_employee__last_name', 'contact_employee__first_name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Employment Details', {
            'fields': ('contact_employee', 'join_date', 'employment_type', 'salary', 'status')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_full_name(self, obj):
        return obj.full_name
    get_full_name.short_description = 'Full Name'
    
    def get_email(self, obj):
        return obj.email
    get_email.short_description = 'Email'
    
    def get_department(self, obj):
        return obj.contact_employee.department
    get_department.short_description = 'Department'
    
    def get_position(self, obj):
        return obj.contact_employee.position
    get_position.short_description = 'Position'

@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'manager', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'code', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'description', 'manager', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ) 