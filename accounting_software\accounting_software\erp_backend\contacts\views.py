from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import (
    Contact, Customer, Vendor, Employee
)
from .serializers import (
    ContactSerializer, CustomerSerializer, VendorSerializer, EmployeeSerializer
)


class ContactViewSet(viewsets.ModelViewSet):
    """ViewSet for managing all contacts"""
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['contact_type']
    search_fields = ['name', 'email', 'phone']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class CustomerViewSet(viewsets.ModelViewSet):
    """ViewSet for managing customers"""
    queryset = Customer.objects.select_related('contact').all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['customer_category', 'payment_terms', 'tax_exempt']
    search_fields = ['contact__name', 'contact__email', 'customer_code', 'company_name']
    ordering_fields = ['contact__name', 'customer_code', 'contact__created_at']
    ordering = ['contact__name']

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get customer statistics"""
        total_customers = self.get_queryset().count()

        # Count customers by tax status
        tax_exempt_customers = self.get_queryset().filter(tax_exempt=True).count()
        taxable_customers = total_customers - tax_exempt_customers

        # Get recent customers (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_customers = self.get_queryset().filter(
            contact__created_at__gte=thirty_days_ago
        ).count()

        # Count by customer category
        categories = self.get_queryset().values('customer_category').annotate(
            count=Count('customer_category')
        ).order_by('-count')

        return Response({
            'total_customers': total_customers,
            'taxable_customers': taxable_customers,
            'tax_exempt_customers': tax_exempt_customers,
            'new_customers_30_days': new_customers,
            'categories': list(categories),
        })


class VendorViewSet(viewsets.ModelViewSet):
    """ViewSet for managing vendors"""
    queryset = Vendor.objects.select_related('contact').all()
    serializer_class = VendorSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['vendor_category', 'payment_terms', 'preferred_vendor']
    search_fields = ['contact__name', 'contact__email', 'vendor_code', 'company_name']
    ordering_fields = ['contact__name', 'vendor_code', 'contact__created_at']
    ordering = ['contact__name']


class EmployeeViewSet(viewsets.ModelViewSet):
    """ViewSet for managing employees"""
    queryset = Employee.objects.select_related('contact').all()
    serializer_class = EmployeeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['department', 'position']
    search_fields = ['contact__name', 'contact__email', 'employee_id', 'position', 'department']
    ordering_fields = ['contact__name', 'employee_id', 'position', 'department']
    ordering = ['contact__name'] 