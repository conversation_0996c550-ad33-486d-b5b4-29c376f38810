import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Grid,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  Payment as PaymentIcon,
  MoreVert as MoreVertIcon,
  AttachMoney as MoneyIcon,
  Receipt as ReceiptIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Inventory as InventoryIcon,
  Warehouse as WarehouseIcon,
  PostAdd as PostAddIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { invoiceService, Invoice, InvoiceFilters, InvoiceStats, Warehouse } from '../../../services/invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';

const InvoicesPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State management
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [stats, setStats] = useState<InvoiceStats | null>(null);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<InvoiceFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [postingDialogOpen, setPostingDialogOpen] = useState(false);
  const [postingLoading, setPostingLoading] = useState(false);

  // Load invoices
  const loadInvoices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const currentFilters = { ...filters };
      if (searchTerm) currentFilters.search = searchTerm;
      
      const response = await invoiceService.getInvoices(currentFilters);
      setInvoices(response.results || []);
      
    } catch (err) {
      console.error('Error loading invoices:', err);
      setError(err instanceof Error ? err.message : 'Failed to load invoices');
    } finally {
      setLoading(false);
    }
  }, [filters, searchTerm]);

  // Load statistics
  const loadStats = useCallback(async () => {
    try {
      const statsData = await invoiceService.getInvoiceStats();
      setStats(statsData);
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  }, []);

  // Load warehouses
  const loadWarehouses = useCallback(async () => {
    try {
      const warehousesData = await invoiceService.getWarehouses();
      setWarehouses(warehousesData);
    } catch (err) {
      console.error('Error loading warehouses:', err);
    }
  }, []);

  // Effects
  useEffect(() => {
    loadInvoices();
  }, [loadInvoices]);

  useEffect(() => {
    loadStats();
    loadWarehouses();
  }, [loadStats, loadWarehouses]);

  // Handlers
  const handleCreateInvoice = () => {
    navigate('/dashboard/sales/invoices/create');
  };

  const handleViewInvoice = (invoice: Invoice) => {
    navigate(`/dashboard/sales/invoices/${invoice.id}`);
  };

  const handleEditInvoice = (invoice: Invoice) => {
    navigate(`/dashboard/sales/invoices/${invoice.id}/edit`);
  };

  const handleDeleteInvoice = async (invoice: Invoice) => {
    if (!window.confirm(`Are you sure you want to delete invoice ${invoice.invoice_number}?`)) {
        return;
      }
      
    try {
      await invoiceService.deleteInvoice(invoice.id!);
      await loadInvoices();
      await loadStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete invoice');
    }
  };

  const handleSendInvoice = async (invoice: Invoice) => {
    try {
      await invoiceService.sendInvoice(invoice.id!);
      await loadInvoices();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invoice');
    }
  };

  const handleMarkAsPaid = async (invoice: Invoice) => {
    try {
      await invoiceService.markAsPaid(invoice.id!);
      await loadInvoices();
      await loadStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark invoice as paid');
    }
  };

  const handlePostToInventory = async (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setPostingDialogOpen(true);
  };

  const confirmPostToInventory = async () => {
    if (!selectedInvoice) return;
    
    try {
      setPostingLoading(true);
      await invoiceService.postToInventory(selectedInvoice.id!);
      await loadInvoices();
      await loadStats();
      setPostingDialogOpen(false);
      setSelectedInvoice(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post invoice to inventory');
    } finally {
      setPostingLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, invoice: Invoice) => {
    setMenuAnchor(event.currentTarget);
    setSelectedInvoice(invoice);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedInvoice(null);
  };

  const handleFilterChange = (field: keyof InvoiceFilters, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
  };

  // Helper functions
  const getStatusColor = (status: string) => {
    const colors = {
      draft: 'default',
      sent: 'info',
      paid: 'success',
      overdue: 'error',
      cancelled: 'default',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircleIcon fontSize="small" />;
      case 'overdue': return <WarningIcon fontSize="small" />;
      default: return undefined;
    }
  };

  const getInventoryPostingStatus = (invoice: Invoice) => {
    if (invoice.inventory_posted) {
      return (
        <Chip 
          size="small"
          label="Posted"
          color="success"
          icon={<CheckCircleIcon />}
        />
      );
    } else if (invoice.status === 'paid') {
      return (
        <Chip 
          size="small"
          label="Ready to Post"
          color="warning"
          icon={<WarningIcon />}
        />
      );
    } else {
      return (
        <Chip 
          size="small"
          label="Not Posted"
          color="default"
        />
      );
    }
  };

  if (loading && invoices.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading Invoices...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Invoices</Typography>
          <Button
            variant="contained"
            color="primary"
          onClick={handleCreateInvoice}
            startIcon={<AddIcon />}
          >
            Create Invoice
          </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Total Revenue"
              value={formatCurrency(stats.total_revenue)}
              icon={<MoneyIcon />}
              color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Outstanding"
              value={formatCurrency(stats.outstanding_amount)}
              icon={<ReceiptIcon />}
              color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Total Invoices"
              value={stats.total_invoices}
              icon={<ReceiptIcon />}
              color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Overdue"
              value={stats.overdue_count}
            icon={<WarningIcon />}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <StatCard
            title="Posted to Inventory"
            value={stats.inventory_posted_count || 0}
            icon={<InventoryIcon />}
            color="primary"
          />
        </Grid>
      </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
            <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search invoices..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="sent">Sent</MenuItem>
                  <MenuItem value="paid">Paid</MenuItem>
                  <MenuItem value="overdue">Overdue</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Warehouse</InputLabel>
                <Select
                  value={filters.warehouse || ''}
                  onChange={(e) => handleFilterChange('warehouse', e.target.value)}
                  label="Warehouse"
                >
                  <MenuItem value="">All Warehouses</MenuItem>
                  {warehouses.map((warehouse) => (
                    <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                      {warehouse.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Inventory Status</InputLabel>
                <Select
                  value={filters.inventory_posted !== undefined ? filters.inventory_posted.toString() : ''}
                  onChange={(e) => handleFilterChange('inventory_posted', e.target.value === '' ? undefined : e.target.value === 'true')}
                  label="Inventory Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">Posted</MenuItem>
                  <MenuItem value="false">Not Posted</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={1.5}>
              <DatePicker
                label="From Date"
                value={filters.date_from ? dayjs(filters.date_from) : null}
                onChange={(date) => handleFilterChange('date_from', date?.format('YYYY-MM-DD'))}
                slotProps={{ textField: { fullWidth: true, size: 'small' } }}
              />
            </Grid>
            <Grid item xs={12} md={1.5}>
              <DatePicker
                label="To Date"
                value={filters.date_to ? dayjs(filters.date_to) : null}
                onChange={(date) => handleFilterChange('date_to', date?.format('YYYY-MM-DD'))}
                slotProps={{ textField: { fullWidth: true, size: 'small' } }}
              />
        </Grid>
          </Grid>
          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            <Button
              variant="outlined"
              onClick={clearFilters}
              startIcon={<FilterIcon />}
            >
              Clear
            </Button>
            <Button
              variant="outlined"
              onClick={loadInvoices}
              startIcon={<RefreshIcon />}
            >
              Refresh
            </Button>
          </Box>
            </CardContent>
          </Card>

      {/* Invoices Table */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Invoice #</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Warehouse</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Inventory</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoices.map((invoice) => (
                  <TableRow key={invoice.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {invoice.invoice_number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {invoice.customer_details?.display_name || `Customer ${invoice.customer}`}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <WarehouseIcon fontSize="small" color="action" />
                        {invoice.warehouse_name || 'N/A'}
                      </Box>
                    </TableCell>
                    <TableCell>
                      {dayjs(invoice.invoice_date).format('MMM D, YYYY')}
                    </TableCell>
                    <TableCell>
                      {dayjs(invoice.due_date).format('MMM D, YYYY')}
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(invoice.total_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                <Chip 
                  size="small"
                        label={invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                        color={getStatusColor(invoice.status) as any}
                        icon={getStatusIcon(invoice.status)}
                      />
                    </TableCell>
                    <TableCell>
                      {getInventoryPostingStatus(invoice)}
                    </TableCell>
                    <TableCell align="center">
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, invoice)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
                {invoices.length === 0 && !loading && (
                  <TableRow>
                    <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                      <Typography color="text.secondary">
                        No invoices found. Create your first invoice to get started.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { handleViewInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><VisibilityIcon fontSize="small" /></ListItemIcon>
          <ListItemText>View</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleEditInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><EditIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { handleSendInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><EmailIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Send</ListItemText>
        </MenuItem>
        {selectedInvoice?.status !== 'paid' && (
          <MenuItem onClick={() => { handleMarkAsPaid(selectedInvoice!); handleMenuClose(); }}>
            <ListItemIcon><PaymentIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Mark as Paid</ListItemText>
          </MenuItem>
        )}
        {selectedInvoice && !selectedInvoice.inventory_posted && selectedInvoice.status === 'paid' && (
          <MenuItem onClick={() => { handlePostToInventory(selectedInvoice!); handleMenuClose(); }}>
            <ListItemIcon><PostAddIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Post to Inventory</ListItemText>
          </MenuItem>
        )}
        <MenuItem onClick={() => { handleDeleteInvoice(selectedInvoice!); handleMenuClose(); }}>
          <ListItemIcon><DeleteIcon fontSize="small" /></ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Post to Inventory Confirmation Dialog */}
      <Dialog open={postingDialogOpen} onClose={() => setPostingDialogOpen(false)}>
        <DialogTitle>Post Invoice to Inventory</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to post invoice {selectedInvoice?.invoice_number} to inventory?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            This will create stock transactions and update inventory levels. This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPostingDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={confirmPostToInventory} 
            variant="contained" 
            disabled={postingLoading}
            startIcon={postingLoading ? <CircularProgress size={16} /> : <PostAddIcon />}
          >
            {postingLoading ? 'Posting...' : 'Post to Inventory'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InvoicesPage;