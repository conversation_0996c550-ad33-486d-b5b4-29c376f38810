import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Box,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { usePaymentTerms, PaymentTerm } from '../../../contexts/PaymentTermsContext';

interface PaymentTermsModalProps {
  open: boolean;
  onClose: () => void;
}

interface PaymentTermFormData {
  name: string;
  code: string;
  days: number;
  description: string;
}

const PaymentTermsModal: React.FC<PaymentTermsModalProps> = ({ open, onClose }) => {
  const { paymentTerms, loading, error, addPaymentTerm, updatePaymentTerm, deletePaymentTerm, setDefaultPaymentTerm } = usePaymentTerms();
  const [isAdding, setIsAdding] = useState(false);
  const [editingTerm, setEditingTerm] = useState<PaymentTerm | null>(null);
  const [formData, setFormData] = useState<PaymentTermFormData>({
    name: '',
    code: '',
    days: 0,
    description: '',
  });
  const [submitting, setSubmitting] = useState(false);

  const handleAddNew = () => {
    setFormData({ name: '', code: '', days: 0, description: '' });
    setEditingTerm(null);
    setIsAdding(true);
  };

  const handleEdit = (term: PaymentTerm) => {
    setFormData({
      name: term.name,
      code: term.code,
      days: term.days,
      description: term.description || '',
    });
    setEditingTerm(term);
    setIsAdding(true);
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingTerm(null);
    setFormData({ name: '', code: '', days: 0, description: '' });
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      if (editingTerm) {
        await updatePaymentTerm(editingTerm.id, formData);
      } else {
        await addPaymentTerm({
          ...formData,
          is_default: false,
          is_active: true,
        });
      }
      
      handleCancel();
    } catch (err) {
      console.error('Error saving payment term:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (term: PaymentTerm) => {
    if (window.confirm(`Are you sure you want to delete "${term.name}"?`)) {
      try {
        await deletePaymentTerm(term.id);
      } catch (err) {
        console.error('Error deleting payment term:', err);
      }
    }
  };

  const handleSetDefault = async (term: PaymentTerm) => {
    try {
      await setDefaultPaymentTerm(term.id);
    } catch (err) {
      console.error('Error setting default payment term:', err);
    }
  };

  const generateCode = (name: string) => {
    return name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');
  };

  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      code: generateCode(name),
    }));
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">Manage Payment Terms</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Add/Edit Form */}
        {isAdding && (
          <Box sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              {editingTerm ? 'Edit Payment Term' : 'Add New Payment Term'}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Name"
                  value={formData.name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="e.g., Net 30"
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Code"
                  value={formData.code}
                  onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                  placeholder="e.g., net_30"
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Days"
                  type="number"
                  value={formData.days}
                  onChange={(e) => setFormData(prev => ({ ...prev, days: parseInt(e.target.value) || 0 }))}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Optional description"
                  size="small"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    onClick={handleSubmit}
                    disabled={submitting || !formData.name || !formData.code}
                    startIcon={submitting ? <CircularProgress size={16} /> : null}
                    size="small"
                  >
                    {submitting ? 'Saving...' : (editingTerm ? 'Update' : 'Add')}
                  </Button>
                  <Button variant="outlined" onClick={handleCancel} size="small">
                    Cancel
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Payment Terms List */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">
            Payment Terms ({paymentTerms.length})
          </Typography>
          {!isAdding && (
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddNew}
              size="small"
            >
              Add New
            </Button>
          )}
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List dense>
            {paymentTerms.map((term, index) => (
              <React.Fragment key={term.id}>
                <ListItem>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" fontWeight="medium">
                          {term.name}
                        </Typography>
                        {term.is_default && (
                          <Chip
                            label="Default"
                            size="small"
                            color="primary"
                            icon={<StarIcon />}
                          />
                        )}
                        <Chip
                          label={`${term.days} days`}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Code: {term.code}
                        </Typography>
                        {term.description && (
                          <Typography variant="body2" color="text.secondary">
                            {term.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      {!term.is_default && (
                        <IconButton
                          size="small"
                          onClick={() => handleSetDefault(term)}
                          title="Set as default"
                        >
                          <StarBorderIcon fontSize="small" />
                        </IconButton>
                      )}
                      <IconButton
                        size="small"
                        onClick={() => handleEdit(term)}
                        title="Edit"
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      {!term.is_default && (
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(term)}
                          title="Delete"
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      )}
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < paymentTerms.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}

        {paymentTerms.length === 0 && !loading && (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Typography color="text.secondary">
              No payment terms found. Add your first payment term above.
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentTermsModal; 