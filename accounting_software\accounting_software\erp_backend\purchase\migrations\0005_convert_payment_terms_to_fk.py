# Generated migration to convert payment_terms from CharField to ForeignKey

from django.db import migrations, models
import django.db.models.deletion


def convert_payment_terms_to_fk(apps, schema_editor):
    """
    Convert existing payment_terms string values to foreign key references
    """
    Vendor = apps.get_model('purchase', 'Vendor')
    PaymentTerm = apps.get_model('purchase', 'PaymentTerm')
    
    # Create mapping from string values to PaymentTerm objects
    payment_term_mapping = {}
    for term in PaymentTerm.objects.all():
        payment_term_mapping[term.code] = term
    
    # Get default payment term or create one
    default_term = PaymentTerm.objects.filter(is_default=True).first()
    if not default_term:
        default_term = PaymentTerm.objects.filter(code='net_30').first()
    if not default_term:
        # Create a default payment term if none exists
        User = apps.get_model('auth', 'User')
        admin_user = User.objects.filter(is_superuser=True).first()
        default_term = PaymentTerm.objects.create(
            name='Net 30',
            code='net_30',
            days=30,
            description='Payment due within 30 days',
            is_default=True,
            is_active=True,
            created_by=admin_user
        )
        payment_term_mapping['net_30'] = default_term
    
    # Update vendors with proper foreign key references
    for vendor in Vendor.objects.all():
        if hasattr(vendor, 'payment_terms') and vendor.payment_terms:
            # Try to find matching payment term
            payment_term = payment_term_mapping.get(vendor.payment_terms)
            if not payment_term:
                # Use default if no match found
                payment_term = default_term
            
            # Set the foreign key (we'll add the field in the next operation)
            vendor.payment_terms_fk = payment_term
            vendor.save()


def reverse_payment_terms_conversion(apps, schema_editor):
    """
    Reverse the conversion by setting string values from foreign key references
    """
    Vendor = apps.get_model('purchase', 'Vendor')
    
    for vendor in Vendor.objects.all():
        if hasattr(vendor, 'payment_terms_fk') and vendor.payment_terms_fk:
            vendor.payment_terms = vendor.payment_terms_fk.code
            vendor.save()


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0004_add_buyer_and_notes_fields'),
    ]

    operations = [
        # First, add the new foreign key field (nullable)
        migrations.AddField(
            model_name='vendor',
            name='payment_terms_fk',
            field=models.ForeignKey(blank=True, help_text='Payment terms for this vendor', null=True, on_delete=django.db.models.deletion.SET_NULL, to='purchase.paymentterm'),
        ),
        
        # Run the data migration to populate the foreign key field
        migrations.RunPython(convert_payment_terms_to_fk, reverse_payment_terms_conversion),
        
        # Remove the old CharField
        migrations.RemoveField(
            model_name='vendor',
            name='payment_terms',
        ),
        
        # Rename the foreign key field to the original name
        migrations.RenameField(
            model_name='vendor',
            old_name='payment_terms_fk',
            new_name='payment_terms',
        ),
    ] 