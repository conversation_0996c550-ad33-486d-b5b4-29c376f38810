import React, { useState } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Tabs,
  Tab,
  Button,
  IconButton,
  Chip,
  LinearProgress,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  BarChart,
  <PERSON><PERSON>hart,
  ShowChart,
  Add,
  FilterList,
  Download,
  Refresh,
  Warning,
  CheckCircle,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart as RechartsBarChart,
  Bar,
  Pie<PERSON>hart as RechartsPieChart,
  Cell,
  Pie,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Sample data for demonstrations
const monthlyData = [
  { month: 'Jan', planned: 45000, actual: 42000, variance: -3000 },
  { month: 'Feb', planned: 52000, actual: 48000, variance: -4000 },
  { month: 'Mar', planned: 48000, actual: 51000, variance: 3000 },
  { month: 'Apr', planned: 61000, actual: 58000, variance: -3000 },
  { month: 'May', planned: 55000, actual: 62000, variance: 7000 },
  { month: 'Jun', planned: 67000, actual: 65000, variance: -2000 },
];

const revenueByCategory = [
  { name: 'Product Sales', value: 450000, color: '#0088FE' },
  { name: 'Services', value: 320000, color: '#00C49F' },
  { name: 'Consulting', value: 180000, color: '#FFBB28' },
  { name: 'Licensing', value: 120000, color: '#FF8042' },
];

const kpiData = [
  { name: 'Revenue Growth', current: 12.5, target: 15.0, status: 'warning' },
  { name: 'Gross Margin', current: 68.2, target: 65.0, status: 'success' },
  { name: 'EBITDA Margin', current: 23.8, target: 25.0, status: 'warning' },
  { name: 'Cash Conversion', current: 85.4, target: 90.0, status: 'warning' },
];

const FPADashboard: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  // KPI Card Component
  const KPICard = ({ title, value, target, trend, status }: any) => (
    <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h6" sx={{ fontSize: '0.9rem', opacity: 0.9 }}>
              {title}
            </Typography>
            <Typography variant="h4" sx={{ fontWeight: 'bold', mt: 1 }}>
              {typeof value === 'number' ? `$${value.toLocaleString()}` : value}
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5, opacity: 0.8 }}>
              Target: {typeof target === 'number' ? `$${target.toLocaleString()}` : target}
            </Typography>
          </Box>
          <Box textAlign="center">
            {trend > 0 ? (
              <TrendingUp sx={{ fontSize: 40, color: '#4caf50' }} />
            ) : (
              <TrendingDown sx={{ fontSize: 40, color: '#f44336' }} />
            )}
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              {trend > 0 ? '+' : ''}{trend}%
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  // Variance Card Component
  const VarianceCard = ({ title, planned, actual, variance, percentage }: any) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          {title}
        </Typography>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="body2" color="text.secondary">Planned</Typography>
            <Typography variant="h6">${planned.toLocaleString()}</Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary">Actual</Typography>
            <Typography variant="h6">${actual.toLocaleString()}</Typography>
          </Box>
        </Box>
        <Divider sx={{ my: 1 }} />
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">Variance</Typography>
          <Box display="flex" alignItems="center">
            <Chip
              label={`${variance > 0 ? '+' : ''}$${variance.toLocaleString()}`}
              color={variance > 0 ? 'success' : 'error'}
              size="small"
              sx={{ mr: 1 }}
            />
            <Typography variant="body2" color={variance > 0 ? 'success.main' : 'error.main'}>
              {percentage > 0 ? '+' : ''}{percentage}%
            </Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: '#f5f7fa', minHeight: '100vh' }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#2c3e50' }}>
            Financial Planning & Analysis
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Plan, Analyze, and Optimize Your Financial Performance
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button variant="outlined" startIcon={<FilterList />}>
            Filter
          </Button>
          <Button variant="outlined" startIcon={<Download />}>
            Export
          </Button>
          <Button variant="contained" startIcon={<Add />} sx={{ background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)' }}>
            New Plan
          </Button>
        </Box>
      </Box>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab icon={<Assessment />} label="Dashboard" />
          <Tab icon={<Timeline />} label="Budget Planning" />
          <Tab icon={<BarChart />} label="Variance Analysis" />
          <Tab icon={<PieChart />} label="Financial Models" />
          <Tab icon={<ShowChart />} label="Forecasting" />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {selectedTab === 0 && (
        <Box>
          {/* KPI Cards */}
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} sm={6} md={3}>
              <KPICard
                title="Total Revenue"
                value={1250000}
                target={1200000}
                trend={8.5}
                status="success"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KPICard
                title="Operating Expenses"
                value={850000}
                target={900000}
                trend={-5.2}
                status="success"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KPICard
                title="Net Income"
                value={400000}
                target={300000}
                trend={15.8}
                status="success"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <KPICard
                title="Cash Flow"
                value={320000}
                target={350000}
                trend={-8.1}
                status="warning"
              />
            </Grid>
          </Grid>

          {/* Charts Row */}
          <Grid container spacing={3} mb={3}>
            {/* Monthly Performance */}
            <Grid item xs={12} lg={8}>
              <Card>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      Monthly Performance - Plan vs Actual
                    </Typography>
                    <IconButton>
                      <Refresh />
                    </IconButton>
                  </Box>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="planned" stroke="#2196F3" strokeWidth={3} name="Planned" />
                      <Line type="monotone" dataKey="actual" stroke="#4CAF50" strokeWidth={3} name="Actual" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Revenue Breakdown */}
            <Grid item xs={12} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                    Revenue by Category
                  </Typography>
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={revenueByCategory}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {revenueByCategory.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />
                      <Legend />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Variance Analysis */}
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} md={3}>
              <VarianceCard
                title="Q1 Revenue"
                planned={300000}
                actual={315000}
                variance={15000}
                percentage={5.0}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <VarianceCard
                title="Q1 Expenses"
                planned={200000}
                actual={185000}
                variance={-15000}
                percentage={-7.5}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <VarianceCard
                title="Q1 Profit"
                planned={100000}
                actual={130000}
                variance={30000}
                percentage={30.0}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <VarianceCard
                title="Cash Position"
                planned={500000}
                actual={480000}
                variance={-20000}
                percentage={-4.0}
              />
            </Grid>
          </Grid>

          {/* KPI Performance Table */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                Key Performance Indicators
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>KPI</TableCell>
                      <TableCell align="right">Current</TableCell>
                      <TableCell align="right">Target</TableCell>
                      <TableCell align="right">Progress</TableCell>
                      <TableCell align="center">Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {kpiData.map((kpi) => (
                      <TableRow key={kpi.name}>
                        <TableCell>{kpi.name}</TableCell>
                        <TableCell align="right">{kpi.current}%</TableCell>
                        <TableCell align="right">{kpi.target}%</TableCell>
                        <TableCell align="right" sx={{ width: 200 }}>
                          <Box display="flex" alignItems="center">
                            <LinearProgress
                              variant="determinate"
                              value={(kpi.current / kpi.target) * 100}
                              sx={{ flexGrow: 1, mr: 1, height: 8, borderRadius: 4 }}
                              color={kpi.status === 'success' ? 'success' : 'warning'}
                            />
                            <Typography variant="body2">
                              {Math.round((kpi.current / kpi.target) * 100)}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          {kpi.status === 'success' ? (
                            <CheckCircle color="success" />
                          ) : (
                            <Warning color="warning" />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Box>
      )}

      {selectedTab === 1 && (
        <Card>
          <CardContent>
            <Typography variant="h5" sx={{ textAlign: 'center', py: 4, color: '#666' }}>
              📊 Budget Planning Module
              <br />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Create and manage annual budgets, quarterly forecasts, and departmental allocations
              </Typography>
            </Typography>
          </CardContent>
        </Card>
      )}

      {selectedTab === 2 && (
        <Card>
          <CardContent>
            <Typography variant="h5" sx={{ textAlign: 'center', py: 4, color: '#666' }}>
              📈 Variance Analysis Module
              <br />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Analyze differences between planned and actual results with detailed drill-down capabilities
              </Typography>
            </Typography>
          </CardContent>
        </Card>
      )}

      {selectedTab === 3 && (
        <Card>
          <CardContent>
            <Typography variant="h5" sx={{ textAlign: 'center', py: 4, color: '#666' }}>
              🏗️ Financial Models Module
              <br />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Build sophisticated financial models for scenario planning and sensitivity analysis
              </Typography>
            </Typography>
          </CardContent>
        </Card>
      )}

      {selectedTab === 4 && (
        <Card>
          <CardContent>
            <Typography variant="h5" sx={{ textAlign: 'center', py: 4, color: '#666' }}>
              🔮 Forecasting Module
              <br />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Generate rolling forecasts with AI-powered predictions and trend analysis
              </Typography>
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default FPADashboard;