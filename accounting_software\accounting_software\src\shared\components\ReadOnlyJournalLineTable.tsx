﻿import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  Divider,
  Card,
  CardContent,
} from '@mui/material';

export interface ReadOnlyJournalLineItem {
  id: string;
  account_name: string;
  description: string;
  memo: string;
  quantity: number;
  unit_rate: number;
  net_amount: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
}

interface ReadOnlyJournalLineTableProps {
  lines: ReadOnlyJournalLineItem[];
  currencySymbol?: string;
  productColumnLabel?: string;
  skuColumnLabel?: string;
  qtyRateColumnLabel?: string;
  netAmountColumnLabel?: string;
  taxColumnLabel?: string;
  totalColumnLabel?: string;
  showTotals?: boolean;
}

const ReadOnlyJournalLineTable: React.FC<ReadOnlyJournalLineTableProps> = ({
  lines,
  currencySymbol = '$',
  productColumnLabel = 'Product',
  skuColumnLabel = 'SKU',
  qtyRateColumnLabel = 'Qty & Rate',
  netAmountColumnLabel = 'Net Amount',
  taxColumnLabel = 'Tax',
  totalColumnLabel = 'Total',
  showTotals = true,
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencySymbol.replace('$', 'USD').replace('₹', 'INR'),
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Calculate totals
  const totalNetAmount = lines.reduce((sum, line) => sum + line.net_amount, 0);
  const totalTaxAmount = lines.reduce((sum, line) => sum + line.tax_amount, 0);
  const grandTotal = lines.reduce((sum, line) => sum + line.total_amount, 0);

  return (
    <Box>
      <TableContainer 
        component={Paper} 
        variant="outlined"
        sx={{ 
          width: '100%',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          '& .MuiTableCell-root': {
            padding: '8px 12px',
            border: '1px solid #e8e8e8',
            fontSize: '0.875rem',
          },
          '& .MuiTableCell-head': {
            backgroundColor: '#f8f9fa',
            fontWeight: 600,
            color: '#2c3e50',
            borderBottom: '2px solid #dee2e6',
            textAlign: 'center',
          },
          '& .MuiTableRow-root:nth-of-type(odd)': {
            backgroundColor: '#f9f9f9',
          },
          '& .MuiTableRow-root:hover': {
            backgroundColor: '#f1f3f4',
          },
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ width: '5%', textAlign: 'center' }}>#</TableCell>
              <TableCell sx={{ width: '25%', textAlign: 'center' }}>{productColumnLabel}</TableCell>
              <TableCell sx={{ width: '15%', textAlign: 'center' }}>{skuColumnLabel}</TableCell>
              <TableCell sx={{ width: '20%', textAlign: 'center' }}>{qtyRateColumnLabel}</TableCell>
              <TableCell sx={{ width: '15%', textAlign: 'center' }}>{netAmountColumnLabel}</TableCell>
              <TableCell sx={{ width: '10%', textAlign: 'center' }}>{taxColumnLabel}</TableCell>
              <TableCell sx={{ width: '10%', textAlign: 'center' }}>{totalColumnLabel}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {lines.map((line, index) => (
              <TableRow key={line.id}>
                <TableCell sx={{ textAlign: 'center', fontWeight: 'medium' }}>
                  {index + 1}
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 500, color: 'text.primary' }}>
                    {line.account_name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {line.description}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {line.memo}
                  </Typography>
                </TableCell>
                <TableCell sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    {formatCurrency(line.net_amount)}
                  </Typography>
                </TableCell>
                <TableCell sx={{ textAlign: 'center' }}>
                  <Box>
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                      {line.tax_rate > 0 ? `${line.tax_rate}%` : '-'}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'medium', fontSize: '0.75rem' }}>
                      {line.tax_amount > 0 ? formatCurrency(line.tax_amount) : '-'}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {formatCurrency(line.total_amount)}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Totals Section */}
      {showTotals && (
        <Card sx={{ mt: 2, backgroundColor: '#f8f9fa', border: '1px solid #e9ecef' }}>
          <CardContent sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Box sx={{ minWidth: 300 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    Net Amount:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    {formatCurrency(totalNetAmount)}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    Total Tax:
                  </Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                    {formatCurrency(totalTaxAmount)}
                  </Typography>
                </Box>
                
                <Divider sx={{ my: 1 }} />
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    Grand Total:
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {formatCurrency(grandTotal)}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ReadOnlyJournalLineTable;
