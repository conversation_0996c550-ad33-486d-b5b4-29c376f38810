from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from .views import (
    ContactViewSet, CustomerViewSet, VendorViewSet, EmployeeViewSet
)

router = DefaultRouter()
router.register(r'contacts', ContactViewSet)
router.register(r'customers', CustomerViewSet)
router.register(r'vendors', VendorViewSet)
router.register(r'employees', EmployeeViewSet)

urlpatterns = [
    path('', include(router.urls)),
] 