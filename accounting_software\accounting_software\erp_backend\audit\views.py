"""
Audit Trail API Views

Provides secure access to audit trail data for:
- Compliance reporting
- Security monitoring
- User activity analysis
- Administrative oversight

All views require appropriate permissions and log access attempts.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timed<PERSON>ta
from django.contrib.auth import get_user_model

from .models import AuditLog, UserSession
from .serializers import (
    AuditLogSerializer, UserSessionSerializer, 
    AuditSummarySerializer, ComplianceReportSerializer
)

User = get_user_model()

class IsAdminOrSuperUser(permissions.BasePermission):
    """
    Custom permission to only allow admin users or superusers to access audit data.
    """
    
    def has_permission(self, request, view):
        return (
            request.user and 
            request.user.is_authenticated and 
            (request.user.is_staff or request.user.is_superuser)
        )

class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing audit logs.
    
    Provides read-only access to audit trail data with:
    - Advanced filtering capabilities
    - Search functionality
    - Compliance reporting features
    - Security event monitoring
    
    Access is restricted to admin users and superusers only.
    """
    
    queryset = AuditLog.objects.select_related('user', 'content_type').order_by('-timestamp')
    serializer_class = AuditLogSerializer
    permission_classes = [IsAdminOrSuperUser]
    
    filterset_fields = [
        'action', 'module', 'risk_level', 'is_financial', 
        'user', 'requires_approval', 'is_approved'
    ]
    search_fields = ['description', 'user__username', 'ip_address']
    ordering_fields = ['timestamp', 'action', 'risk_level']
    ordering = ['-timestamp']
    
    def list(self, request, *args, **kwargs):
        """Log audit trail access."""
        # Log that someone accessed the audit trail
        AuditLog.objects.create(
            user=request.user,
            action='AUDIT_REVIEW',
            description=f"User '{request.user.username}' accessed audit trail",
            module='AUDIT_SYSTEM',
            risk_level='MEDIUM',
            is_financial=False,
        )
        
        return super().list(request, *args, **kwargs)
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get audit trail summary statistics."""
        now = timezone.now()
        today = now.date()
        week_ago = today - timedelta(days=7)
        
        # Basic statistics
        stats = {
            'total_logs': AuditLog.objects.count(),
            'today_logs': AuditLog.objects.filter(timestamp__date=today).count(),
            'week_logs': AuditLog.objects.filter(timestamp__date__gte=week_ago).count(),
            'financial_logs': AuditLog.objects.filter(is_financial=True).count(),
            'high_risk_logs': AuditLog.objects.filter(risk_level__in=['HIGH', 'CRITICAL']).count(),
            'active_sessions': UserSession.objects.filter(is_active=True).count(),
            'failed_logins_today': AuditLog.objects.filter(
                action='LOGIN_FAILED',
                timestamp__date=today
            ).count(),
        }
        
        # User activity breakdown
        user_activity = dict(
            AuditLog.objects.filter(user__isnull=False)
            .values('user__username')
            .annotate(count=Count('id'))
            .values_list('user__username', 'count')[:10]
        )
        
        # Module activity breakdown
        module_activity = dict(
            AuditLog.objects.values('module')
            .annotate(count=Count('id'))
            .values_list('module', 'count')
        )
        
        # Action breakdown
        action_breakdown = dict(
            AuditLog.objects.values('action')
            .annotate(count=Count('id'))
            .values_list('action', 'count')
        )
        
        # Risk level breakdown
        risk_level_breakdown = dict(
            AuditLog.objects.values('risk_level')
            .annotate(count=Count('id'))
            .values_list('risk_level', 'count')
        )
        
        stats.update({
            'user_activity': user_activity,
            'module_activity': module_activity,
            'action_breakdown': action_breakdown,
            'risk_level_breakdown': risk_level_breakdown,
        })
        
        serializer = AuditSummarySerializer(stats)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def generate_report(self, request):
        """Generate compliance report."""
        serializer = ComplianceReportSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        data = serializer.validated_data
        
        # Build query based on report type and filters
        queryset = AuditLog.objects.filter(
            timestamp__gte=data['date_from'],
            timestamp__lte=data['date_to']
        )
        
        # Apply filters
        if data.get('user_id'):
            queryset = queryset.filter(user_id=data['user_id'])
        if data.get('module'):
            queryset = queryset.filter(module=data['module'])
        if data.get('risk_level'):
            queryset = queryset.filter(risk_level=data['risk_level'])
        if data.get('is_financial') is not None:
            queryset = queryset.filter(is_financial=data['is_financial'])
        
        # Apply report type specific filters
        report_type = data['report_type']
        if report_type == 'SECURITY_EVENTS':
            queryset = queryset.filter(
                Q(action='LOGIN_FAILED') | 
                Q(risk_level__in=['HIGH', 'CRITICAL'])
            )
        elif report_type == 'FINANCIAL_CHANGES':
            queryset = queryset.filter(is_financial=True)
        elif report_type == 'FAILED_LOGINS':
            queryset = queryset.filter(action='LOGIN_FAILED')
        
        # Generate report data
        logs = queryset.order_by('-timestamp')
        serialized_logs = AuditLogSerializer(logs, many=True).data
        
        # Log report generation
        AuditLog.objects.create(
            user=request.user,
            action='GENERATE_REPORT',
            description=f"Generated {report_type} compliance report",
            module='AUDIT_SYSTEM',
            risk_level='MEDIUM',
            is_financial=False,
        )
        
        return Response({
            'report_type': report_type,
            'date_range': {
                'from': data['date_from'],
                'to': data['date_to']
            },
            'filters': {k: v for k, v in data.items() if k not in ['report_type', 'date_from', 'date_to']},
            'record_count': logs.count(),
            'generated_by': request.user.username,
            'generated_at': timezone.now(),
            'data': serialized_logs
        })
    
    @action(detail=False, methods=['get'])
    def financial_activity(self, request):
        """Get financial activity audit logs."""
        financial_logs = self.queryset.filter(is_financial=True)
        
        # Apply date filter if provided
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        
        if date_from:
            financial_logs = financial_logs.filter(timestamp__gte=date_from)
        if date_to:
            financial_logs = financial_logs.filter(timestamp__lte=date_to)
        
        page = self.paginate_queryset(financial_logs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(financial_logs, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def security_events(self, request):
        """Get security-related audit logs."""
        security_logs = self.queryset.filter(
            Q(action='LOGIN_FAILED') |
            Q(risk_level__in=['HIGH', 'CRITICAL']) |
            Q(action__in=['PERMISSION_CHANGE', 'DELETE'])
        )
        
        page = self.paginate_queryset(security_logs)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(security_logs, many=True)
        return Response(serializer.data)

class UserSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for viewing user sessions.
    
    Provides session tracking and monitoring capabilities.
    """
    
    queryset = UserSession.objects.select_related('user').order_by('-login_time')
    serializer_class = UserSessionSerializer
    permission_classes = [IsAdminOrSuperUser]
    
    filterset_fields = ['user', 'is_active', 'logout_reason']
    search_fields = ['user__username', 'ip_address']
    ordering_fields = ['login_time', 'logout_time']
    ordering = ['-login_time']
    
    @action(detail=False, methods=['get'])
    def active_sessions(self, request):
        """Get currently active user sessions."""
        active_sessions = self.queryset.filter(is_active=True)
        serializer = self.get_serializer(active_sessions, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def concurrent_sessions(self, request):
        """Get users with multiple concurrent sessions."""
        concurrent_users = (
            UserSession.objects.filter(is_active=True)
            .values('user')
            .annotate(session_count=Count('id'))
            .filter(session_count__gt=1)
        )
        
        result = []
        for item in concurrent_users:
            user = User.objects.get(id=item['user'])
            sessions = UserSession.objects.filter(user=user, is_active=True)
            result.append({
                'user': user.username,
                'session_count': item['session_count'],
                'sessions': UserSessionSerializer(sessions, many=True).data
            })
        
        return Response(result) 