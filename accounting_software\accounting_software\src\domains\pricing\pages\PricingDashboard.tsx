import React from 'react';
import { Box, Grid, Paper, Typography, Card, CardContent } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import PricingCalculator from '../components/PricingCalculator';

const PricingDashboard: React.FC = () => {
  const navigate = useNavigate();

  const modules = [
    {
      title: 'Price Lists',
      description: 'Manage product price lists and pricing tiers',
      path: '/dashboard/pricing/price-lists',
      color: '#1976d2'
    },
    {
      title: 'Discount Rules',
      description: 'Configure discount rules and promotional pricing',
      path: '/dashboard/pricing/discount-rules',
      color: '#388e3c'
    },
    {
      title: 'Price Calculator',
      description: 'Calculate prices with discounts and rules applied',
      path: '/dashboard/pricing/calculator',
      color: '#f57c00'
    },
    {
      title: 'Customer Pricing',
      description: 'Manage customer-specific pricing arrangements',
      path: '/dashboard/pricing/customer-pricing',
      color: '#7b1fa2'
    },
    {
      title: 'Cost Management',
      description: 'Manage product costs and costing methods',
      path: '/dashboard/pricing/cost-management',
      color: '#d32f2f'
    },
    {
      title: 'Margin Analysis',
      description: 'Analyze profit margins and cost vs sale price',
      path: '/dashboard/pricing/margin-analysis',
      color: '#2e7d32'
    }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Pricing Management
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Manage product pricing, discount rules, and customer-specific pricing arrangements
      </Typography>

      <Grid container spacing={3}>
        {modules.map((module, index) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
            <Card 
              sx={{ 
                cursor: 'pointer',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: 4
                }
              }}
              onClick={() => navigate(module.path)}
            >
              <CardContent>
                <Box
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: 2,
                    backgroundColor: module.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 2
                  }}
                >
                  <Typography variant="h6" color="white">
                    {module.title.charAt(0)}
                  </Typography>
                </Box>
                <Typography variant="h6" gutterBottom>
                  {module.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {module.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Price Calculator */}
      <Box sx={{ mt: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            Quick Price Calculator
          </Typography>
          <PricingCalculator />
        </Paper>
      </Box>
    </Box>
  );
};

export default PricingDashboard;
