"""
Django management command to clear all transactional data from the database
while preserving master data like Chart of Accounts, Products, etc.

This is useful for testing and starting fresh with clean data.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User


class Command(BaseCommand):
    help = 'Clear all transactional data from the database while preserving master data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to delete all transactional data',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will delete ALL transactional data from the database!\n'
                    'This includes:\n'
                    '- All Journal Entries and Journal Entry Lines\n'
                    '- All Cash Payments and Cash Receipts\n' 
                    '- All GRN Returns and GRN Return Items\n'
                    '- All GRNs and GRN Items\n'
                    '- All Purchase Orders and PO Line Items\n'
                    '- All Vendors\n'
                    '- All Vendor Invoices\n'
                    '- All Stock Transactions\n'
                    '- All Inventory levels (will be reset to zero)\n\n'
                    'Master data like Chart of Accounts, Products, Companies, etc. will be preserved.\n\n'
                    'To confirm this action, run the command with --confirm flag:\n'
                    'python manage.py clear_transaction_data --confirm'
                )
            )
            return

        self.stdout.write('Starting to clear transactional data...')
        
        try:
            with transaction.atomic():
                # Import all the models we need to clear
                from gl.models import JournalEntry, JournalEntryLine
                from cms.models import CashTransaction, CashTransactionJournal
                from inventory.models import (
                    GoodsReturnNote, GoodsReturnNoteItem,
                    GoodsReceiptNote, GoodsReceiptNoteItem,
                    StockTransaction, Inventory, VendorInvoice
                )
                from purchase.models import PurchaseOrder, PurchaseOrderLineItem, Vendor
                
                # Clear data in order to respect foreign key constraints
                
                # 1. Clear Journal Entry Lines first (child records)
                journal_lines_count = JournalEntryLine.objects.count()
                JournalEntryLine.objects.all().delete()
                self.stdout.write(f'✓ Deleted {journal_lines_count} Journal Entry Lines')
                
                # 2. Clear Journal Entries
                journal_entries_count = JournalEntry.objects.count()
                JournalEntry.objects.all().delete()
                self.stdout.write(f'✓ Deleted {journal_entries_count} Journal Entries')
                
                # 3. Clear Cash Transaction Journals
                cash_txn_journals_count = CashTransactionJournal.objects.count()
                CashTransactionJournal.objects.all().delete()
                self.stdout.write(f'✓ Deleted {cash_txn_journals_count} Cash Transaction Journals')
                
                # 4. Clear Cash Transactions
                cash_transactions_count = CashTransaction.objects.count()
                CashTransaction.objects.all().delete()
                self.stdout.write(f'✓ Deleted {cash_transactions_count} Cash Transactions')
                
                # 5. Clear GRN Return Items
                grn_return_items_count = GoodsReturnNoteItem.objects.count()
                GoodsReturnNoteItem.objects.all().delete()
                self.stdout.write(f'✓ Deleted {grn_return_items_count} GRN Return Items')
                
                # 6. Clear GRN Returns
                grn_returns_count = GoodsReturnNote.objects.count()
                GoodsReturnNote.objects.all().delete()
                self.stdout.write(f'✓ Deleted {grn_returns_count} GRN Returns')
                
                # 7. Clear Vendor Invoices
                vendor_invoices_count = VendorInvoice.objects.count()
                VendorInvoice.objects.all().delete()
                self.stdout.write(f'✓ Deleted {vendor_invoices_count} Vendor Invoices')
                
                # 8. Clear GRN Items
                grn_items_count = GoodsReceiptNoteItem.objects.count()
                GoodsReceiptNoteItem.objects.all().delete()
                self.stdout.write(f'✓ Deleted {grn_items_count} GRN Items')
                
                # 9. Clear GRNs
                grns_count = GoodsReceiptNote.objects.count()
                GoodsReceiptNote.objects.all().delete()
                self.stdout.write(f'✓ Deleted {grns_count} GRNs')
                
                # 10. Clear Purchase Order Line Items
                po_items_count = PurchaseOrderLineItem.objects.count()
                PurchaseOrderLineItem.objects.all().delete()
                self.stdout.write(f'✓ Deleted {po_items_count} Purchase Order Line Items')
                
                # 11. Clear Purchase Orders
                pos_count = PurchaseOrder.objects.count()
                PurchaseOrder.objects.all().delete()
                self.stdout.write(f'✓ Deleted {pos_count} Purchase Orders')
                
                # 12. Clear Stock Transactions
                stock_txns_count = StockTransaction.objects.count()
                StockTransaction.objects.all().delete()
                self.stdout.write(f'✓ Deleted {stock_txns_count} Stock Transactions')
                
                # 13. Reset Inventory levels to zero (but keep the inventory records)
                inventory_count = Inventory.objects.count()
                Inventory.objects.all().update(
                    quantity_on_hand=0,
                    quantity_reserved=0,
                    quantity_on_order=0,
                    average_cost=0,
                    last_cost=0
                )
                self.stdout.write(f'✓ Reset {inventory_count} Inventory levels to zero')
                
                # 14. Clear Vendors
                vendors_count = Vendor.objects.count()
                Vendor.objects.all().delete()
                self.stdout.write(f'✓ Deleted {vendors_count} Vendors')
                
                self.stdout.write(
                    self.style.SUCCESS(
                        '\n🎉 Successfully cleared all transactional data!\n'
                        'The database is now clean and ready for fresh testing data.\n'
                        'Master data like Chart of Accounts, Products, Companies, and Users have been preserved.'
                    )
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error clearing transactional data: {str(e)}')
            )
            raise 