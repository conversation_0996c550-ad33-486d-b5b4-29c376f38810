# Generated by Django 4.2.21 on 2025-05-31 23:23

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('firstName', models.Char<PERSON><PERSON>(help_text='First Name (required)', max_length=100)),
                ('lastName', models.CharField(help_text='Last Name (required)', max_length=100)),
                ('email', models.EmailField(help_text='Email (required)', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Phone (optional)', max_length=20, null=True)),
                ('dateOfBirth', models.DateField(blank=True, help_text='Date of Birth (optional)', null=True)),
                ('gender', models.Char<PERSON>ield(blank=True, help_text='Gender (optional) (male, female, other, prefer-not-to-say)', max_length=20, null=True)),
                ('position', models.CharField(help_text='Position (required)', max_length=100)),
                ('department', models.CharField(help_text='Department (required)', max_length=100)),
                ('joinDate', models.DateField(help_text='Join Date (required)')),
                ('employmentType', models.CharField(help_text='Employment Type (required) (full-time, part-time, contract, intern)', max_length=20)),
                ('salary', models.DecimalField(decimal_places=2, help_text='Salary (required)', max_digits=10)),
                ('status', models.CharField(help_text='Status (required) (Active, On Leave, Terminated)', max_length=20)),
                ('address', models.TextField(blank=True, help_text='Street Address (optional)', null=True)),
                ('city', models.CharField(blank=True, help_text='City (optional)', max_length=100, null=True)),
                ('state', models.CharField(blank=True, help_text='State/Province (optional)', max_length=100, null=True)),
                ('postalCode', models.CharField(blank=True, help_text='Postal Code (optional)', max_length=20, null=True)),
                ('country', models.CharField(blank=True, help_text='Country (optional)', max_length=100, null=True)),
            ],
        ),
    ]
