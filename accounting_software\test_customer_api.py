#!/usr/bin/env python3
"""
Test script to debug customer creation API
"""
import requests
import json

# Test data similar to what frontend sends
test_customer_data = {
    "displayName": "John Doe Business",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "companyName": "Doe Enterprises",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "mobile": "******-987-6543",
    "billingAddress": {
        "street": "123 Business Ave",
        "city": "Business City",
        "state": "Business State",
        "postalCode": "12345",
        "country": "US"
    },
    "shippingAddress": {
        "sameAsBilling": True,
        "street": "",
        "city": "",
        "state": "",
        "postalCode": "",
        "country": "US"
    },
    "paymentTerms": "net_30",
    "creditLimit": 10000,
    "customerCategory": "business",
    "discountPercentage": 10,
    "taxExempt": False,
    "notes": "Test customer from frontend",
    "is_active": True
}

def test_customer_creation():
    """Test customer creation API"""
    url = "http://localhost:8000/api/contacts/customers/"
    
    # Use the token from debug file
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"

    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print("Testing customer creation API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_customer_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_customer_data, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("✅ SUCCESS!")
            print(f"Response Data: {json.dumps(response.json(), indent=2)}")
        else:
            print("❌ FAILED!")
            print(f"Response Text: {response.text}")

            # Try to parse as JSON for better error details
            try:
                error_data = response.json()
                print(f"Error Details: {json.dumps(error_data, indent=2)}")

                # Print specific field errors
                if isinstance(error_data, dict):
                    for field, errors in error_data.items():
                        if isinstance(errors, list):
                            print(f"  {field}: {', '.join(errors)}")
                        else:
                            print(f"  {field}: {errors}")
            except:
                print("Could not parse error response as JSON")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

def test_customer_list():
    """Test customer list API with pagination"""
    base_url = "http://localhost:8000/api/contacts/customers/"

    # Use the token from debug file
    token = "e15bc01f831c5111f413f534ef82288744cb7d41"

    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }

    print("\nTesting customer list API with pagination...")

    # Test first page with explicit page_size
    test_url = f"{base_url}?page=1&page_size=10"
    print(f"URL: {test_url}")
    try:
        response = requests.get(test_url, headers=headers)

        print(f"\nResponse Status: {response.status_code}")

        if response.status_code == 200:
            print("✅ SUCCESS!")
            data = response.json()
            print(f"Total customers: {data.get('count', 0)}")
            print(f"Results on this page: {len(data.get('results', []))}")
            print(f"Next page: {data.get('next', 'None')}")
            print(f"Previous page: {data.get('previous', 'None')}")
            if data.get('results'):
                print(f"First customer: {data['results'][0].get('display_name', 'N/A')}")

            # Test second page if available
            if data.get('next'):
                print(f"\nTesting second page...")
                response2 = requests.get(data['next'], headers=headers)
                if response2.status_code == 200:
                    data2 = response2.json()
                    print(f"✅ Page 2 SUCCESS!")
                    print(f"Results on page 2: {len(data2.get('results', []))}")
                    if data2.get('results'):
                        print(f"First customer on page 2: {data2['results'][0].get('display_name', 'N/A')}")
        else:
            print("❌ FAILED!")
            print(f"Response Text: {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("Customer API Test Script")
    print("=" * 50)
    
    # Test list first (should work)
    test_customer_list()
    
    # Test creation (this is what's failing)
    test_customer_creation()
