from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
# Note: Customer endpoints moved to /api/contacts/customers/ - use contacts module instead
router.register(r'categories', views.ProductCategoryViewSet, basename='productcategory')
router.register(r'products', views.ProductViewSet, basename='product')
router.register(r'invoices', views.InvoiceViewSet, basename='invoice')
router.register(r'payments', views.PaymentViewSet, basename='payment')
router.register(r'estimates', views.EstimateViewSet, basename='estimate')
router.register(r'payment-terms', views.PaymentTermViewSet, basename='paymentterm')

# New Sales Order and Delivery Note endpoints
router.register(r'sales-orders', views.SalesOrderViewSet, basename='salesorder')
router.register(r'delivery-notes', views.DeliveryNoteViewSet, basename='deliverynote')

# Sales Price Authority endpoint
router.register(r'product-pricing', views.ProductPricingViewSet, basename='productpricing')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
] 