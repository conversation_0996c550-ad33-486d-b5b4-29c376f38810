import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Product, ProductFormData, PRODUCT_CATEGORIES } from '../shared/types/product.types';
import api from '../services/api';

// Sample data for initial products
const INITIAL_PRODUCTS: Product[] = [
  {
    id: '1',
    name: 'Laptop Computer',
    sku: 'TECH-001',
    description: 'High-performance laptop for business use',
    type: 'product',
    category: 'electronics',
    salesPrice: 1200,
    costPrice: 900,
    currency: 'USD',
    trackInventory: true,
    inventoryQuantity: 15,
    lowStockAlert: true,
    lowStockThreshold: 5,
    status: 'active',
    createdAt: '2023-01-15T10:30:00Z',
    updatedAt: '2023-01-15T10:30:00Z',
    totalSales: 12500,
    lastSoldDate: '2023-05-20T14:30:00Z',
    incomeAccountId: 'income-001',
    expenseAccountId: 'expense-001',
    inventoryAccountId: 'asset-001',
    averageRating: 4.7,
    reviewCount: 28,
  },
  {
    id: '2',
    name: 'Office Chair',
    sku: 'FURN-002',
    description: 'Ergonomic office chair with lumbar support',
    type: 'product',
    category: 'furniture',
    salesPrice: 250,
    costPrice: 150,
    currency: 'USD',
    trackInventory: true,
    inventoryQuantity: 8,
    lowStockAlert: true,
    lowStockThreshold: 10,
    status: 'active',
    createdAt: '2023-02-10T09:15:00Z',
    updatedAt: '2023-02-10T09:15:00Z',
    totalSales: 3750,
    lastSoldDate: '2023-05-15T11:45:00Z',
    incomeAccountId: 'income-001',
    expenseAccountId: 'expense-001',
    inventoryAccountId: 'asset-001',
    averageRating: 4.2,
    reviewCount: 15,
  },
  {
    id: '3',
    name: 'Accounting Software Setup',
    sku: 'SERV-001',
    description: 'Professional setup and configuration of accounting software',
    type: 'service',
    category: 'software',
    salesPrice: 500,
    costPrice: 300,
    currency: 'USD',
    status: 'active',
    createdAt: '2023-03-05T14:20:00Z',
    updatedAt: '2023-03-05T14:20:00Z',
    totalSales: 4500,
    lastSoldDate: '2023-05-10T16:30:00Z',
    incomeAccountId: 'income-002',
    expenseAccountId: 'expense-002',
    averageRating: 4.9,
    reviewCount: 12,
  },
  {
    id: '4',
    name: 'Business Consulting',
    sku: 'SERV-002',
    description: 'Strategic business consulting services',
    type: 'service',
    category: 'consulting',
    salesPrice: 1500,
    costPrice: 1000,
    currency: 'USD',
    status: 'active',
    createdAt: '2023-04-12T11:00:00Z',
    updatedAt: '2023-04-12T11:00:00Z',
    totalSales: 9000,
    lastSoldDate: '2023-05-05T10:15:00Z',
    incomeAccountId: 'income-002',
    expenseAccountId: 'expense-002',
    averageRating: 4.8,
    reviewCount: 9,
  },
  {
    id: '5',
    name: 'Wireless Headphones',
    sku: 'TECH-002',
    description: 'Noise-cancelling wireless headphones',
    type: 'product',
    category: 'electronics',
    salesPrice: 180,
    costPrice: 100,
    currency: 'USD',
    trackInventory: true,
    inventoryQuantity: 3,
    lowStockAlert: true,
    lowStockThreshold: 5,
    status: 'active',
    createdAt: '2023-05-01T15:45:00Z',
    updatedAt: '2023-05-01T15:45:00Z',
    totalSales: 5400,
    lastSoldDate: '2023-05-18T09:30:00Z',
    incomeAccountId: 'income-001',
    expenseAccountId: 'expense-001',
    inventoryAccountId: 'asset-001',
    averageRating: 4.5,
    reviewCount: 32,
  },
];

interface ProductContextType {
  products: Product[];
  loading: boolean;
  error: string | null;
  addProduct: (product: ProductFormData) => Promise<void>;
  updateProduct: (product: Product) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProductById: (id: string) => Product | undefined;
  getCategoryName: (categoryValue: string) => string;
  filterProducts: (filters: ProductFilters) => Product[];
  refreshProducts: () => Promise<void>;
}

interface ProductFilters {
  search?: string;
  category?: string;
  type?: string;
  status?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
}

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const useProducts = (): ProductContextType => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};

interface ProductProviderProps {
  children: ReactNode;
}

export const ProductProvider: React.FC<ProductProviderProps> = ({ children }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch products from API
  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get('/sales/products/');
      const apiProducts = response.data.results || response.data;
      
      // Transform API data to match frontend Product interface
      const transformedProducts: Product[] = apiProducts.map((apiProduct: any) => ({
        id: apiProduct.id.toString(),
        name: apiProduct.name,
        sku: apiProduct.sku,
        description: apiProduct.description || '',
        type: apiProduct.product_type === 'product' ? 'product' : 'service', // Map API field
        category: apiProduct.category || 'general',
        salesPrice: parseFloat(apiProduct.unit_price) || 0,
        costPrice: parseFloat(apiProduct.cost_price) || 0,
        currency: 'USD', // Default currency
        status: apiProduct.status === 'active' ? 'active' : 'inactive',
        trackInventory: apiProduct.track_inventory || false,
        inventoryQuantity: apiProduct.quantity_on_hand || 0,
        lowStockAlert: apiProduct.reorder_point > 0,
        lowStockThreshold: apiProduct.reorder_point || 5,
        createdAt: apiProduct.created_at || new Date().toISOString(),
        updatedAt: apiProduct.updated_at || new Date().toISOString(),
        totalSales: 0, // Not available from API
        incomeAccountId: apiProduct.income_account_gl?.toString() || '',
        expenseAccountId: apiProduct.expense_account_gl?.toString() || '',
        inventoryAccountId: apiProduct.inventory_asset_account_gl?.toString() || '',
      }));
      
      setProducts(transformedProducts);
      console.log('✅ Products loaded from API:', transformedProducts.length);
    } catch (err: any) {
      console.error('❌ Error fetching products:', err);
      setError(err.message || 'Failed to fetch products');
      // Fallback to empty array on error
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  // Load products on mount
  useEffect(() => {
    fetchProducts();
  }, []);

  const addProduct = async (productData: ProductFormData): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      // Transform frontend data to API format
      const apiData = {
        name: productData.name,
        sku: productData.sku,
        description: productData.description,
        product_type: productData.type === 'product' ? 'product' : 'service',
        unit_price: productData.salesPrice,
        cost_price: productData.costPrice,
        minimum_selling_price: productData.costPrice * 1.1, // 10% markup as minimum
        status: productData.status,
        track_inventory: productData.trackInventory || false,
        quantity_on_hand: productData.inventoryQuantity || 0,
        reorder_point: productData.lowStockThreshold || 5,
        taxable: true, // Default
        income_account_gl: productData.incomeAccountId ? parseInt(productData.incomeAccountId) : null,
        expense_account_gl: productData.expenseAccountId ? parseInt(productData.expenseAccountId) : null,
        inventory_asset_account_gl: productData.inventoryAccountId ? parseInt(productData.inventoryAccountId) : null,
      };
      
      console.log('🚀 Creating product with API data:', apiData);
      
      const response = await api.post('/sales/products/', apiData);
      console.log('✅ Product created successfully:', response.data);
      
      // Refresh products list to get the latest data
      await fetchProducts();
      
    } catch (err: any) {
      console.error('❌ Error creating product:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to create product';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateProduct = async (product: Product): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      // Transform frontend data to API format
      const apiData = {
        name: product.name,
        sku: product.sku,
        description: product.description,
        product_type: product.type === 'product' ? 'product' : 'service',
        unit_price: product.salesPrice,
        cost_price: product.costPrice,
        minimum_selling_price: product.costPrice * 1.1, // 10% markup as minimum
        status: product.status,
        track_inventory: product.trackInventory || false,
        quantity_on_hand: product.inventoryQuantity || 0,
        reorder_point: product.lowStockThreshold || 5,
        income_account_gl: product.incomeAccountId ? parseInt(product.incomeAccountId) : null,
        expense_account_gl: product.expenseAccountId ? parseInt(product.expenseAccountId) : null,
        inventory_asset_account_gl: product.inventoryAccountId ? parseInt(product.inventoryAccountId) : null,
      };
      
      console.log('🔄 Updating product with API data:', { id: product.id, data: apiData });
      
      const response = await api.patch(`/sales/products/${product.id}/`, apiData);
      console.log('✅ Product updated successfully:', response.data);
      
      // Refresh products list to get the latest data
      await fetchProducts();
      
    } catch (err: any) {
      console.error('❌ Error updating product:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to update product';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (id: string): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      await api.delete(`/sales/products/${id}/`);
      console.log('✅ Product deleted successfully:', id);
      
      // Refresh products list
      await fetchProducts();
      
    } catch (err: any) {
      console.error('❌ Error deleting product:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to delete product';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const refreshProducts = async (): Promise<void> => {
    await fetchProducts();
  };

  const getProductById = (id: string) => {
    return products.find((product) => product.id === id);
  };

  const getCategoryName = (categoryValue: string) => {
    const category = PRODUCT_CATEGORIES.find((cat) => cat.value === categoryValue);
    return category ? category.label : 'Unknown Category';
  };

  const filterProducts = (filters: ProductFilters) => {
    return products.filter((product) => {
      // Search filter
      if (filters.search && !product.name.toLowerCase().includes(filters.search.toLowerCase()) &&
          !product.sku.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Category filter
      if (filters.category && product.category !== filters.category) {
        return false;
      }

      // Type filter
      if (filters.type && product.type !== filters.type) {
        return false;
      }

      // Status filter
      if (filters.status && product.status !== filters.status) {
        return false;
      }

      // Price range filter
      if (filters.minPrice !== undefined && product.salesPrice < filters.minPrice) {
        return false;
      }
      if (filters.maxPrice !== undefined && product.salesPrice > filters.maxPrice) {
        return false;
      }

      // In stock filter
      if (filters.inStock && 
          (product.type !== 'product' || 
           !product.trackInventory || 
           (product.inventoryQuantity !== undefined && product.inventoryQuantity <= 0))) {
        return false;
      }

      return true;
    });
  };

  const value = {
    products,
    loading,
    error,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    getCategoryName,
    filterProducts,
    refreshProducts,
  };

  return <ProductContext.Provider value={value}>{children}</ProductContext.Provider>;
};

export default ProductContext; 