#!/usr/bin/env python3
"""
Debug test script to check API connectivity
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000/api"
TOKEN = "e0d1048e1f14689c6eba6616845a074bad2167bc"

def test_basic_connectivity():
    """Test basic API connectivity"""
    print("Testing basic API connectivity...")
    
    try:
        # Test basic GET request to root API
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"Root API Status: {response.status_code}")
        print(f"Root API Response: {response.text[:200]}")
        
        # Test authenticated request
        response = requests.get(
            f"{BASE_URL}/sales/invoices/",
            headers={"Authorization": f"Token {TOKEN}"},
            timeout=5
        )
        print(f"Invoice API Status: {response.status_code}")
        print(f"Invoice API Response: {response.text[:200]}")
        
        return True
        
    except requests.exceptions.ConnectionError as e:
        print(f"Connection Error: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print(f"Timeout Error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected Error: {e}")
        return False

def test_invoice_creation():
    """Test invoice creation with minimal data"""
    print("\nTesting invoice creation...")
    
    # Minimal invoice data
    invoice_data = {
        "customer": 51,
        "invoice_date": "2025-06-29",
        "due_date": "2025-07-29",
        "status": "draft",
        "subtotal": 100.0,
        "tax_amount": 10.0,
        "total_amount": 110.0,
        "line_items": []  # Empty line items for now
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/sales/invoices/",
            json=invoice_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Token {TOKEN}"
            },
            timeout=10
        )
        
        print(f"POST Status: {response.status_code}")
        print(f"POST Response Headers: {dict(response.headers)}")
        print(f"POST Response: {response.text}")
        
        return response.status_code == 201
        
    except Exception as e:
        print(f"POST Error: {e}")
        return False

if __name__ == "__main__":
    print("Starting API debug tests...")
    
    # Test connectivity first
    if not test_basic_connectivity():
        print("❌ Basic connectivity failed!")
        sys.exit(1)
    
    # Test invoice creation
    if test_invoice_creation():
        print("✅ Invoice creation test passed!")
    else:
        print("❌ Invoice creation test failed!")
