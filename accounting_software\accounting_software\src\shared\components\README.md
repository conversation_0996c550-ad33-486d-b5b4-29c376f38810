# DataTable Component

The DataTable component is a powerful and flexible table component that provides various features for displaying and interacting with tabular data.

## Features

- Sorting
- Pagination
- Searching
- Row selection
- Row actions
- Expandable rows
- Column visibility control
- Searchable dropdowns for columns

## Searchable Dropdown Feature

The DataTable component now supports searchable dropdowns for columns, making it easier to search and select values from a predefined list of options.

### How to Use Searchable Dropdowns

To enable a searchable dropdown for a column, you need to:

1. Set the `searchable` property to `true` in the column definition.
2. Provide an array of options using the `options` property.
3. Specify which property to display using the `optionLabel` property (if options are objects).
4. Specify which property to use as the value using the `optionValue` property (if options are objects).
5. Implement the `onCellChange` callback to handle changes to the cell value.

### Example

```tsx
// Define options for the dropdown
const categoryOptions = PRODUCT_CATEGORIES.map(category => ({
  value: category.value,
  label: category.label
}));

// Handle cell change
const handleCellChange = (row: Product, field: string, value: any) => {
  if (field === 'category') {
    // Update the product with the new category
    const updatedProduct = { ...row, category: value };
    updateProduct(updatedProduct);
  }
};

// Use in DataTable
<DataTable
  columns={[
    // ... other columns ...
    { 
      field: 'category', 
      headerName: 'Category', 
      flex: 0.8,
      valueGetter: ({ row }) => getCategoryName(row.category),
      searchable: true, // Enable searchable dropdown
      options: categoryOptions, // Provide options for the dropdown
      optionLabel: 'label', // Specify which property to display
      optionValue: 'value' // Specify which property to use as the value
    },
    // ... other columns ...
  ]}
  rows={products}
  onCellChange={handleCellChange} // Add the cell change handler
  // ... other props ...
/>
```

### Options Format

The `options` property can be an array of strings or an array of objects. If you provide an array of objects, you need to specify which property to display using the `optionLabel` property and which property to use as the value using the `optionValue` property.

```tsx
// Array of strings
const options = ['Option 1', 'Option 2', 'Option 3'];

// Array of objects
const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
];
```

### Handling Changes

When a user selects a value from the dropdown, the `onCellChange` callback is called with the row, field, and new value. You can use this callback to update the data in your application.

```tsx
const handleCellChange = (row: YourRowType, field: string, value: any) => {
  if (field === 'yourField') {
    // Update the row with the new value
    const updatedRow = { ...row, [field]: value };
    updateYourData(updatedRow);
  }
};
```

## Other Features

For information on other features of the DataTable component, please refer to the component's documentation. 