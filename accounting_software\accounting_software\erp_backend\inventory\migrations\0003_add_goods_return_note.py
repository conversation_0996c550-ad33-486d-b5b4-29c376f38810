# Generated by Django 4.2.21 on 2025-06-18 00:54

import datetime
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0006_remove_product_expense_account_and_more'),
        ('purchase', '0004_add_buyer_and_notes_fields'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0002_make_product_nullable_in_grn_items'),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsReturnNote',
            fields=[
                ('grn_return_id', models.AutoField(primary_key=True, serialize=False)),
                ('grn_return_number', models.CharField(help_text='GRN Return reference number', max_length=50, unique=True)),
                ('return_date', models.DateField(default=datetime.date.today)),
                ('return_reason', models.CharField(choices=[('DEFECTIVE', 'Defective/Damaged'), ('WRONG_ITEM', 'Wrong Item Delivered'), ('EXCESS_QTY', 'Excess Quantity'), ('QUALITY_ISSUE', 'Quality Issue'), ('NOT_ORDERED', 'Not Ordered'), ('EXPIRED', 'Expired'), ('OTHER', 'Other')], help_text='Reason for return', max_length=50)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('APPROVED', 'Approved'), ('RETURNED', 'Returned to Vendor'), ('POSTED', 'Posted to Inventory'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Return notes and comments')),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('expected_credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Expected credit from vendor', max_digits=15)),
                ('actual_credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Actual credit received from vendor', max_digits=15)),
                ('credit_received_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='grn_returns_approved', to=settings.AUTH_USER_MODEL)),
                ('original_grn', models.ForeignKey(help_text='Original GRN being returned', on_delete=django.db.models.deletion.CASCADE, related_name='return_notes', to='inventory.goodsreceiptnote')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='grn_returns_posted', to=settings.AUTH_USER_MODEL)),
                ('returned_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='goods_returned', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(help_text='Vendor to return goods to', on_delete=django.db.models.deletion.CASCADE, related_name='goods_returns', to='purchase.vendor')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='goods_returns', to='inventory.warehouse')),
            ],
            options={
                'db_table': 'grn_returns',
                'ordering': ['-return_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoodsReturnNoteItem',
            fields=[
                ('grn_return_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity_received', models.DecimalField(decimal_places=4, help_text='Quantity originally received', max_digits=15)),
                ('quantity_returned', models.DecimalField(decimal_places=4, help_text='Quantity being returned', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, help_text='Original cost per unit', max_digits=15)),
                ('return_value', models.DecimalField(decimal_places=2, help_text='Total return value for this line item', max_digits=15)),
                ('return_reason', models.CharField(choices=[('DEFECTIVE', 'Defective/Damaged'), ('WRONG_ITEM', 'Wrong Item Delivered'), ('EXCESS_QTY', 'Excess Quantity'), ('QUALITY_ISSUE', 'Quality Issue'), ('NOT_ORDERED', 'Not Ordered'), ('EXPIRED', 'Expired'), ('OTHER', 'Other')], help_text='Specific reason for this item return', max_length=50)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('condition_notes', models.TextField(blank=True, help_text='Notes about item condition')),
                ('photos_attached', models.BooleanField(default=False, help_text='Whether photos are attached for evidence')),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('grn_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.goodsreturnnote')),
                ('original_grn_item', models.ForeignKey(help_text='Original GRN item being returned', on_delete=django.db.models.deletion.CASCADE, related_name='return_items', to='inventory.goodsreceiptnoteitem')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='grn_return_items', to='sales.product')),
            ],
            options={
                'db_table': 'grn_return_items',
                'ordering': ['line_order'],
                'unique_together': {('grn_return', 'product', 'original_grn_item')},
            },
        ),
    ]
