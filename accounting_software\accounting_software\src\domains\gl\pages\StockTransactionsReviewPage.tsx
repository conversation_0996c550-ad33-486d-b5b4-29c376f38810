import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  AccountBalance as AccountBalanceIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Receipt as ReceiptIcon,
  RemoveCircle as IssueIcon,
  TrendingUp as PriceUpIcon,
  TrendingDown as PriceDownIcon,
  AttachMoney as MoneyIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { inventoryService, InventoryItem, StockTransaction } from '../../inventory/services/inventory.service';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';

// Extended interface for cost ledger entries with pricing information
interface ProductCostLedgerEntry {
  id: string;
  product_id: number;
  product_name: string;
  product_sku: string;
  warehouse_name: string;
  date: string;
  reference: string;
  description: string;
  receipt_qty: number;
  issue_qty: number;
  balance_qty: number;
  unit_cost: number;
  receipt_value: number;
  issue_value: number;
  balance_value: number;
  transaction_type: string;
  reference_type: string;
  reference_id: number;
}

// Interface for product with complete cost ledger
interface ProductCostLedger {
  product_id: number;
  product_name: string;
  product_sku: string;
  warehouse_name: string;
  current_balance_qty: number;
  current_balance_value: number;
  average_cost: number;
  latest_cost: number;
  highest_cost: number;
  lowest_cost: number;
  reorder_point: number;
  total_receipts_qty: number;
  total_receipts_value: number;
  total_issues_qty: number;
  total_issues_value: number;
  last_transaction_date: string | null;
  entries: ProductCostLedgerEntry[];
}

const StockTransactionsReviewPage: React.FC = () => {
  const [productCostLedgers, setProductCostLedgers] = useState<ProductCostLedger[]>([]);
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [stockTransactions, setStockTransactions] = useState<StockTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState('');
  const [selectedProduct, setSelectedProduct] = useState('');
  const [totalPortfolioValue, setTotalPortfolioValue] = useState(0);

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadProductCostLedgers();
  }, []);

  const loadProductCostLedgers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Loading cost ledgers for GL review...');
      
      // Load both stock transactions and inventory items
      const [transactionsResponse, inventoryResponse] = await Promise.all([
        inventoryService.getStockTransactions({
          search: searchTerm,
          warehouse: selectedWarehouse ? parseInt(selectedWarehouse) : undefined,
          product: selectedProduct ? parseInt(selectedProduct) : undefined,
        }),
        inventoryService.getInventory({
          search: searchTerm,
          warehouse: selectedWarehouse ? parseInt(selectedWarehouse) : undefined,
        })
      ]);
      
      console.log('Stock transactions response:', transactionsResponse);
      console.log('Inventory response:', inventoryResponse);
      
      const transactions = transactionsResponse?.results || transactionsResponse || [];
      const inventory = inventoryResponse?.results || inventoryResponse || [];
      
      setStockTransactions(transactions);
      setInventoryItems(inventory);
      
      // Create cost ledgers with financial focus
      const ledgers = createProductCostLedgers(transactions, inventory);
      setProductCostLedgers(ledgers);
      
      // Calculate total portfolio value
      const totalValue = ledgers.reduce((sum, ledger) => sum + ledger.current_balance_value, 0);
      setTotalPortfolioValue(totalValue);
      
      console.log('Created product cost ledgers:', ledgers.length, 'Total Value:', totalValue);
    } catch (err) {
      console.error('Error loading product cost ledgers:', err);
      setError(err instanceof Error ? err.message : 'Failed to load product cost ledgers');
    } finally {
      setLoading(false);
    }
  };

  const createProductCostLedgers = (transactions: StockTransaction[], inventory: InventoryItem[]): ProductCostLedger[] => {
    const productGroups: { [key: string]: ProductCostLedger } = {};
    
    // Sort transactions by date (oldest first for proper cost calculation)
    const sortedTransactions = [...transactions].sort((a, b) => {
      return new Date(a.txn_date).getTime() - new Date(b.txn_date).getTime();
    });
    
    // Initialize product groups with inventory data
    inventory.forEach((item) => {
      const key = `${item.product}-${item.warehouse}`;
      productGroups[key] = {
        product_id: item.product,
        product_name: item.product_name,
        product_sku: item.product_sku,
        warehouse_name: item.warehouse_name,
        current_balance_qty: Number(item.quantity_on_hand || 0),
        current_balance_value: 0,
        average_cost: 0,
        latest_cost: 0,
        highest_cost: 0,
        lowest_cost: 0,
        reorder_point: Number(item.reorder_point || 0),
        total_receipts_qty: 0,
        total_receipts_value: 0,
        total_issues_qty: 0,
        total_issues_value: 0,
        last_transaction_date: null,
        entries: [],
      };
    });
    
    // Process transactions to build cost ledger entries
    let runningBalances: { [key: string]: { qty: number; value: number } } = {};
    
    sortedTransactions.forEach((txn) => {
      const key = `${txn.product}-${txn.warehouse}`;
      
      // Initialize product group if not exists
      if (!productGroups[key]) {
        productGroups[key] = {
          product_id: txn.product,
          product_name: txn.product_name,
          product_sku: txn.product_sku,
          warehouse_name: txn.warehouse_name,
          current_balance_qty: 0,
          current_balance_value: 0,
          average_cost: 0,
          latest_cost: 0,
          highest_cost: 0,
          lowest_cost: 0,
          reorder_point: 0,
          total_receipts_qty: 0,
          total_receipts_value: 0,
          total_issues_qty: 0,
          total_issues_value: 0,
          last_transaction_date: null,
          entries: [],
        };
      }
      
      // Initialize running balance if not exists
      if (!runningBalances[key]) {
        runningBalances[key] = { qty: 0, value: 0 };
      }
      
      const receiptQty = Number(txn.quantity) > 0 ? Number(txn.quantity) : 0;
      const issueQty = Number(txn.quantity) < 0 ? Math.abs(Number(txn.quantity)) : 0;
      const unitCost = Number(txn.unit_cost || 0);
      const receiptValue = receiptQty * unitCost;
      const issueValue = issueQty * unitCost;
      
      // Update running balance
      runningBalances[key].qty += receiptQty - issueQty;
      runningBalances[key].value += receiptValue - issueValue;
      
      // Create cost ledger entry
      const entry: ProductCostLedgerEntry = {
        id: `${txn.stock_txn_id}`,
        product_id: txn.product,
        product_name: txn.product_name,
        product_sku: txn.product_sku,
        warehouse_name: txn.warehouse_name,
        date: txn.txn_date,
        reference: `${txn.reference_type}-${txn.reference_id}`,
        description: txn.description || `${txn.transaction_type} transaction`,
        receipt_qty: receiptQty,
        issue_qty: issueQty,
        balance_qty: runningBalances[key].qty,
        unit_cost: unitCost,
        receipt_value: receiptValue,
        issue_value: issueValue,
        balance_value: runningBalances[key].value,
        transaction_type: txn.transaction_type,
        reference_type: txn.reference_type,
        reference_id: txn.reference_id,
      };
      
      productGroups[key].entries.push(entry);
      productGroups[key].total_receipts_qty += receiptQty;
      productGroups[key].total_receipts_value += receiptValue;
      productGroups[key].total_issues_qty += issueQty;
      productGroups[key].total_issues_value += issueValue;
      productGroups[key].last_transaction_date = txn.txn_date;
      productGroups[key].latest_cost = unitCost;
      
      // Track highest and lowest costs
      if (unitCost > 0) {
        if (productGroups[key].highest_cost === 0 || unitCost > productGroups[key].highest_cost) {
          productGroups[key].highest_cost = unitCost;
        }
        if (productGroups[key].lowest_cost === 0 || unitCost < productGroups[key].lowest_cost) {
          productGroups[key].lowest_cost = unitCost;
        }
      }
    });
    
    // Calculate final values and average costs
    Object.values(productGroups).forEach(ledger => {
      ledger.current_balance_value = runningBalances[`${ledger.product_id}-${ledger.warehouse_name.replace(' ', '')}`]?.value || 0;
      ledger.average_cost = ledger.total_receipts_qty > 0 ? ledger.total_receipts_value / ledger.total_receipts_qty : 0;
      
      // If we don't have cost data from transactions, use inventory value estimate
      if (ledger.current_balance_value === 0 && ledger.current_balance_qty > 0) {
        ledger.current_balance_value = ledger.current_balance_qty * ledger.average_cost;
      }
    });
    
    // Convert to array and sort alphabetically by product name
    return Object.values(productGroups)
      .filter(ledger => ledger.entries.length > 0 || ledger.current_balance_qty > 0)
      .sort((a, b) => a.product_name.localeCompare(b.product_name));
  };

  const getCostTrendInfo = (ledger: ProductCostLedger) => {
    const { latest_cost, average_cost, highest_cost, lowest_cost } = ledger;
    
    if (latest_cost > average_cost) {
      return { 
        trend: 'UP', 
        color: 'error' as const, 
        icon: <PriceUpIcon />,
        message: 'Cost Rising'
      };
    } else if (latest_cost < average_cost) {
      return { 
        trend: 'DOWN', 
        color: 'success' as const, 
        icon: <PriceDownIcon />,
        message: 'Cost Falling'
      };
    } else {
      return { 
        trend: 'STABLE', 
        color: 'info' as const, 
        icon: <MoneyIcon />,
        message: 'Cost Stable'
      };
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'RECEIPT':
      case 'RETURN':
      case 'OPENING':
        return <ReceiptIcon color="success" fontSize="small" />;
      case 'ISSUE':
      case 'SALE':
      case 'DAMAGE':
        return <IssueIcon color="error" fontSize="small" />;
      default:
        return <InventoryIcon color="primary" fontSize="small" />;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            💰 Stock Cost Ledgers - GL Review
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Financial review of inventory with cost analysis and valuation ({currencyInfo?.functional_currency_symbol || '$'})
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadProductCostLedgers}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Portfolio Summary */}
      <Card sx={{ mb: 3, bgcolor: 'primary.dark', color: 'white' }}>
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h6" fontWeight="bold">
                📊 Total Inventory Portfolio Value
              </Typography>
              <Typography variant="h4" fontWeight="bold" sx={{ mt: 1 }}>
                {formatCurrency(totalPortfolioValue)}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box display="flex" gap={2}>
                <Box textAlign="center">
                  <Typography variant="body2">Products</Typography>
                  <Typography variant="h6" fontWeight="bold">
                    {productCostLedgers.length}
                  </Typography>
                </Box>
                <Box textAlign="center">
                  <Typography variant="body2">Avg. Value</Typography>
                  <Typography variant="h6" fontWeight="bold">
                    {formatCurrency(productCostLedgers.length > 0 ? totalPortfolioValue / productCostLedgers.length : 0)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>💵 Cost Ledger Format:</strong> Each product shows cost analysis (latest, average, high/low), 
          current inventory value, and transaction history with unit costs and running financial balance.
        </Typography>
      </Alert>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search products, SKUs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Warehouse</InputLabel>
                <Select
                  label="Warehouse"
                  value={selectedWarehouse}
                  onChange={(e) => setSelectedWarehouse(e.target.value)}
                >
                  <MenuItem value="">All Warehouses</MenuItem>
                  <MenuItem value="1">Main Warehouse</MenuItem>
                  <MenuItem value="2">Branch Warehouse</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Product name or SKU"
                value={selectedProduct}
                onChange={(e) => setSelectedProduct(e.target.value)}
                size="small"
                label="Filter by Product"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="contained"
                onClick={loadProductCostLedgers}
                size="small"
              >
                Apply Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Product Cost Ledgers */}
      {loading ? (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
          <Typography variant="body2" sx={{ ml: 2 }}>
            Loading cost ledgers...
          </Typography>
        </Box>
      ) : productCostLedgers.length === 0 ? (
        <Card>
          <CardContent>
            <Box display="flex" justifyContent="center" p={4}>
              <Typography variant="body1" color="text.secondary">
                No cost data found. {searchTerm || selectedWarehouse || selectedProduct ? 'Try adjusting your filters.' : 'No financial transactions recorded yet.'}
              </Typography>
            </Box>
          </CardContent>
        </Card>
      ) : (
        <Box>
          {productCostLedgers.map((ledger, index) => {
            const costTrend = getCostTrendInfo(ledger);
            
            return (
              <Card key={`${ledger.product_id}-${ledger.warehouse_name}`} sx={{ mb: 4 }}>
                <CardContent>
                  {/* Product Header */}
                  <Box sx={{ mb: 3 }}>
                    <Grid container alignItems="center" spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="h6" fontWeight="bold" color="primary.main">
                          {ledger.product_name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          SKU: {ledger.product_sku || 'N/A'} | Warehouse: {ledger.warehouse_name}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Box display="flex" gap={2} flexWrap="wrap" justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                          <Chip
                            icon={costTrend.icon}
                            label={costTrend.message}
                            color={costTrend.color}
                            variant="outlined"
                          />
                          <Chip
                            label={`Value: ${formatCurrency(ledger.current_balance_value)}`}
                            color={ledger.current_balance_value > 0 ? 'success' : 'default'}
                            variant="filled"
                          />
                        </Box>
                      </Grid>
                    </Grid>
                    
                    {/* Cost Analysis */}
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                      <Grid container spacing={3}>
                        <Grid item xs={6} md={2}>
                          <Typography variant="caption" color="text.secondary">
                            Current Value
                          </Typography>
                          <Typography 
                            variant="body1" 
                            fontWeight="bold"
                            color="primary.main"
                          >
                            {formatCurrency(ledger.current_balance_value)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={2}>
                          <Typography variant="caption" color="text.secondary">
                            Balance Qty
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {ledger.current_balance_qty.toFixed(2)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={2}>
                          <Typography variant="caption" color="text.secondary">
                            Latest Cost
                          </Typography>
                          <Typography variant="body1" fontWeight="medium" color="info.main">
                            {formatCurrency(ledger.latest_cost)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={2}>
                          <Typography variant="caption" color="text.secondary">
                            Average Cost
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {formatCurrency(ledger.average_cost)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={2}>
                          <Typography variant="caption" color="text.secondary">
                            Highest Cost
                          </Typography>
                          <Typography variant="body1" fontWeight="medium" color="error.main">
                            {formatCurrency(ledger.highest_cost)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={2}>
                          <Typography variant="caption" color="text.secondary">
                            Lowest Cost
                          </Typography>
                          <Typography variant="body1" fontWeight="medium" color="success.main">
                            {formatCurrency(ledger.lowest_cost)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>

                    {/* Value Summary */}
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                      <Grid container spacing={3}>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Total Receipts Value
                          </Typography>
                          <Typography variant="body1" fontWeight="bold" color="success.main">
                            {formatCurrency(ledger.total_receipts_value)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Total Issues Value
                          </Typography>
                          <Typography variant="body1" fontWeight="bold" color="error.main">
                            {formatCurrency(ledger.total_issues_value)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Net Value Movement
                          </Typography>
                          <Typography 
                            variant="body1" 
                            fontWeight="bold"
                            color={ledger.total_receipts_value - ledger.total_issues_value >= 0 ? 'success.main' : 'error.main'}
                          >
                            {formatCurrency(ledger.total_receipts_value - ledger.total_issues_value)}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} md={3}>
                          <Typography variant="caption" color="text.secondary">
                            Inventory Turnover
                          </Typography>
                          <Typography variant="body1" fontWeight="medium">
                            {ledger.current_balance_value > 0 
                              ? (ledger.total_issues_value / ledger.current_balance_value).toFixed(2) + 'x'
                              : 'N/A'
                            }
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>

                  <Divider sx={{ mb: 2 }} />

                  {/* Cost Transaction History */}
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    💰 Cost Transaction History
                  </Typography>
                  
                  {ledger.entries.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'center' }}>
                      No cost transactions recorded for this product
                    </Typography>
                  ) : (
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow sx={{ bgcolor: 'grey.50' }}>
                            <TableCell><strong>Date</strong></TableCell>
                            <TableCell><strong>Reference</strong></TableCell>
                            <TableCell><strong>Description</strong></TableCell>
                            <TableCell align="center"><strong>Unit Cost</strong></TableCell>
                            <TableCell align="center"><strong>Receipt Qty</strong></TableCell>
                            <TableCell align="center"><strong>Issue Qty</strong></TableCell>
                            <TableCell align="center"><strong>Qty Balance</strong></TableCell>
                            <TableCell align="center"><strong>Receipt Value</strong></TableCell>
                            <TableCell align="center"><strong>Issue Value</strong></TableCell>
                            <TableCell align="center"><strong>Balance Value</strong></TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {ledger.entries.map((entry, entryIndex) => (
                            <TableRow key={entry.id} hover>
                              <TableCell>
                                <Typography variant="body2">
                                  {formatDate(entry.date)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Box display="flex" alignItems="center" gap={1}>
                                  {getTransactionIcon(entry.transaction_type)}
                                  <Typography variant="body2">
                                    {entry.reference}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {entry.description}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  fontWeight="medium"
                                  color="info.main"
                                >
                                  {formatCurrency(entry.unit_cost)}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  color={entry.receipt_qty > 0 ? 'success.main' : 'text.disabled'}
                                  fontWeight={entry.receipt_qty > 0 ? 'medium' : 'normal'}
                                >
                                  {entry.receipt_qty > 0 ? entry.receipt_qty.toFixed(2) : '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  color={entry.issue_qty > 0 ? 'error.main' : 'text.disabled'}
                                  fontWeight={entry.issue_qty > 0 ? 'medium' : 'normal'}
                                >
                                  {entry.issue_qty > 0 ? entry.issue_qty.toFixed(2) : '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  fontWeight="bold"
                                  color={entry.balance_qty > 0 ? 'success.main' : entry.balance_qty === 0 ? 'text.secondary' : 'error.main'}
                                >
                                  {entry.balance_qty.toFixed(2)}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  color={entry.receipt_value > 0 ? 'success.main' : 'text.disabled'}
                                  fontWeight={entry.receipt_value > 0 ? 'bold' : 'normal'}
                                >
                                  {entry.receipt_value > 0 ? formatCurrency(entry.receipt_value) : '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  color={entry.issue_value > 0 ? 'error.main' : 'text.disabled'}
                                  fontWeight={entry.issue_value > 0 ? 'bold' : 'normal'}
                                >
                                  {entry.issue_value > 0 ? formatCurrency(entry.issue_value) : '-'}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <Typography 
                                  variant="body2" 
                                  fontWeight="bold"
                                  color={entry.balance_value > 0 ? 'primary.main' : entry.balance_value === 0 ? 'text.secondary' : 'error.main'}
                                >
                                  {formatCurrency(entry.balance_value)}
                                </Typography>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </Box>
      )}
    </PageContainer>
  );
};

export default StockTransactionsReviewPage;