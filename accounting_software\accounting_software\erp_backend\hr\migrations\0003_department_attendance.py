# Generated by Django 4.2.21 on 2025-06-04 22:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0002_payrollrun_payslip'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(help_text='Department Name (required)', max_length=100)),
                ('code', models.CharField(help_text='Department Code (required, unique)', max_length=20, unique=True)),
                ('description', models.TextField(blank=True, help_text='Department Description (optional)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True, help_text='Whether the department is active')),
                ('manager', models.ForeignKey(blank=True, help_text='Department Manager (optional)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.employee')),
                ('parent_department', models.ForeignKey(blank=True, help_text='Parent Department (optional)', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_departments', to='hr.department')),
            ],
            options={
                'verbose_name': 'Department',
                'verbose_name_plural': 'Departments',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('Present', 'Present'), ('Absent', 'Absent'), ('Leave', 'Leave'), ('Holiday', 'Holiday'), ('Half Day', 'Half Day')], max_length=20)),
                ('check_in_time', models.TimeField(blank=True, null=True)),
                ('check_out_time', models.TimeField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance', to='hr.employee')),
            ],
            options={
                'ordering': ['date', 'employee__lastName', 'employee__firstName'],
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
