# Generated by Django 4.2.21 on 2025-06-06 18:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(help_text='Django session key', max_length=40, unique=True)),
                ('login_time', models.DateTimeField(default=django.utils.timezone.now, help_text='When user logged in')),
                ('logout_time', models.DateTimeField(blank=True, help_text='When user logged out', null=True)),
                ('last_activity', models.DateTimeField(default=django.utils.timezone.now, help_text='Last activity timestamp')),
                ('ip_address', models.GenericIPAddressField(help_text='IP address of the session')),
                ('user_agent', models.TextField(help_text='Browser/application user agent')),
                ('is_active', models.BooleanField(default=True, help_text='Whether session is still active')),
                ('logout_reason', models.CharField(blank=True, choices=[('USER', 'User Logout'), ('TIMEOUT', 'Session Timeout'), ('ADMIN', 'Admin Termination'), ('SECURITY', 'Security Violation')], help_text='Reason for session termination', max_length=50, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Session',
                'verbose_name_plural': 'User Sessions',
                'ordering': ['-login_time'],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('audit_id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this audit record', unique=True)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now, help_text='Exact timestamp when action occurred')),
                ('action', models.CharField(choices=[('CREATE', 'Create Record'), ('UPDATE', 'Update Record'), ('DELETE', 'Delete Record'), ('VIEW', 'View Record'), ('LOGIN', 'User Login'), ('LOGOUT', 'User Logout'), ('LOGIN_FAILED', 'Failed Login Attempt'), ('PASSWORD_CHANGE', 'Password Changed'), ('POST_JOURNAL', 'Post Journal Entry'), ('REVERSE_JOURNAL', 'Reverse Journal Entry'), ('CLOSE_PERIOD', 'Close Accounting Period'), ('REOPEN_PERIOD', 'Reopen Accounting Period'), ('GENERATE_REPORT', 'Generate Financial Report'), ('EXPORT_DATA', 'Export Data'), ('IMPORT_DATA', 'Import Data'), ('PERMISSION_CHANGE', 'Change User Permissions'), ('BACKUP_CREATE', 'Create System Backup'), ('RESTORE_DATA', 'Restore from Backup'), ('DATA_ACCESS', 'Access Sensitive Data'), ('AUDIT_REVIEW', 'Audit Trail Review'), ('COMPLIANCE_CHECK', 'Compliance Verification')], help_text='Type of action performed', max_length=30)),
                ('description', models.TextField(help_text='Human-readable description of the action')),
                ('object_id', models.PositiveIntegerField(blank=True, help_text='ID of the affected object', null=True)),
                ('old_values', models.TextField(blank=True, help_text='Values before the change (JSON format)', null=True)),
                ('new_values', models.TextField(blank=True, help_text='Values after the change (JSON format)', null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the user', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser/application user agent', null=True)),
                ('session_key', models.CharField(blank=True, help_text='User session identifier', max_length=40, null=True)),
                ('module', models.CharField(help_text='Application module (GL, AR, AP, etc.)', max_length=50)),
                ('risk_level', models.CharField(choices=[('LOW', 'Low Risk'), ('MEDIUM', 'Medium Risk'), ('HIGH', 'High Risk'), ('CRITICAL', 'Critical Risk')], default='LOW', help_text='Risk level of this action', max_length=10)),
                ('metadata', models.TextField(blank=True, help_text='Additional context data (JSON format)', null=True)),
                ('is_financial', models.BooleanField(default=False, help_text='Whether this affects financial data')),
                ('requires_approval', models.BooleanField(default=False, help_text='Whether this action requires approval')),
                ('is_approved', models.BooleanField(default=False, help_text='Whether this action has been approved')),
                ('approved_at', models.DateTimeField(blank=True, help_text='When this action was approved', null=True)),
                ('approved_by', models.ForeignKey(blank=True, help_text='User who approved this action', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_audit_logs', to=settings.AUTH_USER_MODEL)),
                ('content_type', models.ForeignKey(blank=True, help_text='Type of object that was affected', null=True, on_delete=django.db.models.deletion.SET_NULL, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(blank=True, help_text='User who performed the action', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Audit Log Entry',
                'verbose_name_plural': 'Audit Log Entries',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['timestamp'], name='audit_audit_timesta_19e18a_idx'), models.Index(fields=['user'], name='audit_audit_user_id_292c79_idx'), models.Index(fields=['action'], name='audit_audit_action_86e815_idx'), models.Index(fields=['module'], name='audit_audit_module_e8d32b_idx'), models.Index(fields=['is_financial'], name='audit_audit_is_fina_e477e4_idx'), models.Index(fields=['content_type', 'object_id'], name='audit_audit_content_4c2ead_idx')],
            },
        ),
    ]
