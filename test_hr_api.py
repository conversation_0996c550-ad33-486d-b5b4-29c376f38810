#!/usr/bin/env python3
"""
Simple test script to verify the HR employee API is working after fixes
"""
import requests
import json

def test_hr_api():
    base_url = "http://localhost:8000/api"
    
    # Login to get token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # Get authentication token
        print("🔐 Attempting authentication...")
        login_response = requests.post("http://localhost:8000/api-token-auth/", json=login_data)
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {'Authorization': f'Token {token}'}
            print("✅ Authentication successful")
        else:
            print(f"❌ Authentication failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return
        
        # Test HR employees endpoint
        print("\n📋 Testing HR Employees API...")
        employees_response = requests.get(f"{base_url}/hr/employees/", headers=headers)
        print(f"Status: {employees_response.status_code}")
        
        if employees_response.status_code == 200:
            employees_data = employees_response.json()
            results = employees_data.get('results', employees_data)
            print(f"✅ HR Employees API working - Found {len(results)} employees")
            
            # Print first employee if exists
            if results:
                first_employee = results[0]
                print(f"\n📝 Sample employee data:")
                print(f"  - ID: {first_employee.get('contact_employee')}")
                print(f"  - Name: {first_employee.get('full_name')}")
                print(f"  - Email: {first_employee.get('email')}")
                print(f"  - Position: {first_employee.get('position')}")
                print(f"  - Department: {first_employee.get('department')}")
                print(f"  - Status: {first_employee.get('status')}")
        else:
            print(f"❌ HR Employees API error: {employees_response.status_code}")
            print(f"Response: {employees_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Django server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_hr_api() 