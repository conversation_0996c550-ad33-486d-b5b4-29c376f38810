import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Autocomplete,
  Box,
  Typography,
  Chip,
} from '@mui/material';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import { getAvailableCurrencies, getFunctionalCurrency, getCurrencySymbol } from '../utils/currencyUtils';

interface CurrencySelectorProps {
  value: string;
  onChange: (currency: string) => void;
  label?: string;
  variant?: 'select' | 'autocomplete';
  size?: 'small' | 'medium';
  fullWidth?: boolean;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
  showFunctionalCurrencyChip?: boolean;
}

/**
 * Reusable currency selector component for CMS
 * Supports both Select and Autocomplete variants
 */
const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  value,
  onChange,
  label = 'Currency',
  variant = 'select',
  size = 'medium',
  fullWidth = true,
  required = false,
  disabled = false,
  error = false,
  helperText,
  showFunctionalCurrencyChip = true,
}) => {
  const { currencyInfo } = useCurrencyInfo();
  const availableCurrencies = getAvailableCurrencies(currencyInfo);
  const functionalCurrency = getFunctionalCurrency(currencyInfo);

  if (variant === 'autocomplete') {
    return (
      <Box>
        <Autocomplete
          value={availableCurrencies.find(currency => currency.code === value) || null}
          onChange={(_, newValue) => {
            onChange(newValue ? newValue.code : functionalCurrency.code);
          }}
          options={availableCurrencies}
          getOptionLabel={(option) => `${option.name} (${option.code})`}
          renderOption={(props, option) => (
            <Box component="li" {...props}>
              <Box display="flex" alignItems="center" width="100%">
                <Typography variant="body2" sx={{ mr: 1 }}>
                  {option.symbol}
                </Typography>
                <Box>
                  <Typography variant="body2" fontWeight="medium">
                    {option.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {option.code}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
          renderInput={(params) => (
            <TextField
              {...params}
              label={label}
              required={required}
              error={error}
              helperText={helperText}
              size={size}
              fullWidth={fullWidth}
              InputProps={{
                ...params.InputProps,
                startAdornment: value ? (
                  <Typography variant="body2" sx={{ mr: 1, color: 'text.secondary' }}>
                    {getCurrencySymbol(value)}
                  </Typography>
                ) : null,
              }}
            />
          )}
          disabled={disabled}
          size={size}
        />
        {showFunctionalCurrencyChip && (
          <Box mt={1}>
            <Chip
              size="small"
              label={`Default: ${functionalCurrency.code} (${functionalCurrency.symbol})`}
              variant="outlined"
              color="primary"
            />
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box>
      <FormControl fullWidth={fullWidth} size={size} required={required} error={error} disabled={disabled}>
        <InputLabel>{label}</InputLabel>
        <Select
          value={value}
          label={label}
          onChange={(e) => onChange(e.target.value)}
        >
          {availableCurrencies.map((currency) => (
            <MenuItem key={currency.code} value={currency.code}>
              <Box display="flex" alignItems="center">
                <Typography variant="body2" sx={{ mr: 2, minWidth: 24 }}>
                  {currency.symbol}
                </Typography>
                <Box>
                  <Typography variant="body2">
                    {currency.name} ({currency.code})
                  </Typography>
                </Box>
              </Box>
            </MenuItem>
          ))}
        </Select>
        {helperText && (
          <Typography variant="caption" color={error ? 'error' : 'text.secondary'} sx={{ mt: 0.5, ml: 1.5 }}>
            {helperText}
          </Typography>
        )}
      </FormControl>
      {showFunctionalCurrencyChip && (
        <Box mt={1}>
          <Chip
            size="small"
            label={`Default: ${functionalCurrency.code} (${functionalCurrency.symbol})`}
            variant="outlined"
            color="primary"
          />
        </Box>
      )}
    </Box>
  );
};

export default CurrencySelector; 