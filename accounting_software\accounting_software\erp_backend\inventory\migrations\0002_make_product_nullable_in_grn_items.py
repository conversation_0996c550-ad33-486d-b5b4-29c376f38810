# Generated by Django 4.2.21 on 2025-06-17 00:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0005_product_expense_account_gl_product_income_account_gl_and_more'),
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='goodsreceiptnoteitem',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='goodsreceiptnoteitem',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='grn_items', to='sales.product'),
        ),
    ]
