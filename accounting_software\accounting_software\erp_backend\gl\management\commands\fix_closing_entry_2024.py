"""
Django management command to fix the existing CLOSE-2024 entry
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Sum
from gl.models import JournalEntry, JournalEntryLine, Account
from account.models import Company
from decimal import Decimal


class Command(BaseCommand):
    help = 'Fix the existing CLOSE-2024 entry to include all revenue and expense accounts'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🔧 Fixing CLOSE-2024 Entry')
        )
        
        # Find the existing closing entry
        closing_entry = JournalEntry.objects.filter(entry_number='CLOSE-2024').first()
        if not closing_entry:
            self.stdout.write(
                self.style.ERROR('❌ CLOSE-2024 entry not found')
            )
            return
        
        self.stdout.write(f'📝 Found entry: {closing_entry.entry_number}')
        
        # Get company and fiscal year dates
        company = Company.objects.first()
        fy_start = company.fiscal_year_start.replace(year=2024)
        fy_end = company.fiscal_year_end.replace(year=2024)
        if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
           (company.fiscal_year_end.month == company.fiscal_year_start.month and 
            company.fiscal_year_end.day < company.fiscal_year_start.day):
            fy_end = fy_end.replace(year=2025)
        
        # Get all Income Statement accounts with activity
        income_accounts = Account.objects.filter(
            account_type__financial_statement='INCOME_STATEMENT',
            is_active=True,
            is_header_account=False
        ).exclude(account_number='39999')
        
        # Calculate account balances
        account_balances = {}
        total_revenue = Decimal('0.00')
        total_expenses = Decimal('0.00')
        
        for account in income_accounts:
            debits = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__status='POSTED',
                journal_entry__transaction_date__gte=fy_start,
                journal_entry__transaction_date__lte=fy_end
            ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
            
            credits = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__status='POSTED',
                journal_entry__transaction_date__gte=fy_start,
                journal_entry__transaction_date__lte=fy_end
            ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
            
            if account.account_type.type == 'REVENUE':
                balance = credits - debits
                if balance != 0:
                    account_balances[account] = balance
                    total_revenue += balance
            elif account.account_type.type == 'EXPENSE':
                balance = debits - credits
                if balance != 0:
                    account_balances[account] = balance
                    total_expenses += balance
        
        net_income = total_revenue - total_expenses
        
        self.stdout.write(f'💰 Total Revenue: {total_revenue:,.2f}')
        self.stdout.write(f'💸 Total Expenses: {total_expenses:,.2f}')
        self.stdout.write(f'📊 Net Income: {net_income:,.2f}')
        
        # Find Retained Earnings account
        retained_earnings = Account.objects.filter(
            account_name__icontains='retained earnings'
        ).first()
        
        if not retained_earnings:
            self.stdout.write(
                self.style.ERROR('❌ Retained Earnings account not found')
            )
            return
        
        with transaction.atomic():
            # Delete existing journal lines
            closing_entry.journal_lines.all().delete()
            self.stdout.write('🗑️  Deleted existing journal lines')
            
            line_number = 1
            
            # Close revenue accounts (DEBIT revenue to zero them out)
            for account, balance in account_balances.items():
                if account.account_type.type == 'REVENUE':
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        account=account,
                        debit_amount=balance,
                        credit_amount=Decimal('0.00'),
                        description=f'Close {account.account_name}',
                        line_number=line_number
                    )
                    line_number += 1
                    self.stdout.write(f'   Dr. {account.account_number}: {balance:,.2f}')
            
            # Close expense accounts (CREDIT expense to zero them out)
            for account, balance in account_balances.items():
                if account.account_type.type == 'EXPENSE':
                    JournalEntryLine.objects.create(
                        journal_entry=closing_entry,
                        account=account,
                        debit_amount=Decimal('0.00'),
                        credit_amount=balance,
                        description=f'Close {account.account_name}',
                        line_number=line_number
                    )
                    line_number += 1
                    self.stdout.write(f'   Cr. {account.account_number}: {balance:,.2f}')
            
            # Credit Retained Earnings with net income
            JournalEntryLine.objects.create(
                journal_entry=closing_entry,
                account=retained_earnings,
                debit_amount=Decimal('0.00'),
                credit_amount=net_income,
                description='Net Income to Retained Earnings',
                line_number=line_number
            )
            self.stdout.write(f'   Cr. {retained_earnings.account_number}: {net_income:,.2f}')
            
            # Update entry description
            closing_entry.description = f'Year-end closing FY2024 - Net Income {net_income:,.2f}'
            closing_entry.save()
            
            # Verify the entry balances
            total_debits = closing_entry.journal_lines.aggregate(
                total=Sum('debit_amount'))['total'] or Decimal('0.00')
            total_credits = closing_entry.journal_lines.aggregate(
                total=Sum('credit_amount'))['total'] or Decimal('0.00')
            
            self.stdout.write(f'\\n🧮 VERIFICATION:')
            self.stdout.write(f'   Total Debits: {total_debits:,.2f}')
            self.stdout.write(f'   Total Credits: {total_credits:,.2f}')
            
            if abs(total_debits - total_credits) < Decimal('0.01'):
                self.stdout.write('   ✅ Entry is balanced!')
            else:
                self.stdout.write('   ❌ Entry is not balanced!')
                return
        
        self.stdout.write(f'\\n✅ Successfully fixed CLOSE-2024 entry')
        self.stdout.write(f'💰 Net Income of {net_income:,.2f} now closed to Retained Earnings')
        self.stdout.write(f'🏦 Balance Sheet should now be balanced!') 