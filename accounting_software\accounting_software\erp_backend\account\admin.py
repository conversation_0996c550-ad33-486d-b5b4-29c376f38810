from django.contrib import admin
from .models import Company, UserRole, UserPermission

# Register your models here.

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    """
    Django Admin configuration for Company model.
    This allows superusers to manage companies through the Django admin interface.
    Perfect for accountants who prefer a traditional admin panel interface.
    """
    
    # Fields to display in the company list view
    list_display = [
        'name',                    # Company name
        'registration_number',     # Registration number
        'functional_currency',     # Primary business currency
        'reporting_currency',      # Financial reporting currency
        'sales_tax_registered',    # Whether registered for sales tax
        'created_at',             # When company was created
        'updated_at'              # When company was last modified
    ]
    
    # Fields that can be searched in the admin interface
    search_fields = [
        'name',                   # Search by company name
        'registration_number',    # Search by registration number
        'tax_id',                # Search by tax ID
        'email'                  # Search by email address
    ]
    
    # Filters available in the right sidebar
    list_filter = [
        'functional_currency',     # Filter by functional currency
        'reporting_currency',      # Filter by reporting currency
        'sales_tax_registered',    # Filter by tax registration status
        'created_at',             # Filter by creation date
        'updated_at'              # Filter by last update date
    ]
    
    # Fields that are read-only (cannot be edited)
    readonly_fields = [
        'created_at',             # Creation timestamp
        'updated_at'              # Last update timestamp
    ]
    
    # How fields are organized on the edit page
    fieldsets = (
        # Basic Company Information Section
        ('Company Information', {
            'fields': (
                'name',
                'registration_number', 
                'tax_id'
            ),
            'description': 'Basic legal information about the company'
        }),
        
        # Contact Information Section
        ('Contact Details', {
            'fields': (
                'address',
                'phone',
                'email'
            ),
            'description': 'How to contact the company'
        }),
        
        # Currency Settings Section (NEW)
        ('Currency Settings', {
            'fields': (
                'functional_currency',
                'reporting_currency'
            ),
            'description': 'Currency settings for business operations and financial reporting'
        }),
        
        # Financial Year Settings Section
        ('Financial Year Configuration', {
            'fields': (
                ('accounting_year_start', 'accounting_year_end'),
                ('fiscal_year_start', 'fiscal_year_end')
            ),
            'description': 'Define the company\'s accounting and fiscal year periods'
        }),
        
        # Tax Registration Section
        ('Tax Registration', {
            'fields': (
                'sales_tax_registered',
                'sales_tax_number'
            ),
            'description': 'Sales tax and VAT registration information'
        }),
        
        # System Information Section
        ('System Information', {
            'fields': (
                'created_at',
                'updated_at'
            ),
            'description': 'System-generated timestamps (read-only)',
            'classes': ('collapse',)  # This section is collapsed by default
        })
    )
    
    # Set the ordering for the list view
    ordering = ['name']  # Sort companies alphabetically by name
    
    # Number of companies to show per page
    list_per_page = 25
    
    # Custom actions that can be performed on selected companies
    actions = ['export_company_data']
    
    def export_company_data(self, request, queryset):
        """
        Custom admin action to export company data.
        This allows accountants to export company information for reporting.
        """
        # This is a placeholder - you can implement CSV/Excel export here
        self.message_user(request, f"Exported {queryset.count()} companies")
    export_company_data.short_description = "Export selected companies"

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """
    Django Admin configuration for User Roles.
    Allows management of user roles and permissions.
    """
    list_display = ['user', 'role', 'created_at', 'updated_at']
    list_filter = ['role', 'created_at']
    search_fields = ['user__username', 'user__email']
    ordering = ['user__username']

@admin.register(UserPermission)
class UserPermissionAdmin(admin.ModelAdmin):
    """
    Django Admin configuration for User Permissions.
    Allows fine-grained permission management.
    """
    list_display = ['user', 'module', 'access_level', 'created_at']
    list_filter = ['module', 'access_level', 'created_at']
    search_fields = ['user__username', 'user__email']
    ordering = ['user__username', 'module']
