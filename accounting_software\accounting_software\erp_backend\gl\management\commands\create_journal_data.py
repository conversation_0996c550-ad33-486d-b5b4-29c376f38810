from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import date, timedelta

from gl.models import VoucherType, Project, Product, JournalEntry, JournalEntryLine, Account
from sales_tax.models import SalesTax
from tds.models import TDS

User = get_user_model()

class Command(BaseCommand):
    help = 'Create sample data for Enhanced Journal Entry System'

    def handle(self, *args, **options):
        self.stdout.write("Creating sample data for Enhanced Journal Entry System...")
        self.stdout.write("=" * 60)
        
        try:
            self.create_voucher_types()
            self.stdout.write("")
            
            self.create_projects()
            self.stdout.write("")
            
            self.create_products()
            self.stdout.write("")
            
            self.stdout.write("=" * 60)
            self.stdout.write(self.style.SUCCESS("✅ Sample data creation completed successfully!"))
            self.stdout.write("\nYou can now:")
            self.stdout.write("- Access voucher types at /api/gl/voucher-types/")
            self.stdout.write("- Access projects at /api/gl/projects/")
            self.stdout.write("- Access products at /api/gl/products/")
            self.stdout.write("- Access enhanced journal entries at /api/gl/enhanced-journal-entries/")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error creating sample data: {str(e)}"))
            import traceback
            traceback.print_exc()

    def create_voucher_types(self):
        """Create standard voucher types"""
        self.stdout.write("Creating voucher types...")
        
        voucher_types = [
            {
                'voucher_code': 'JV',
                'voucher_name': 'Journal Voucher - General Entry',
                'description': 'General journal entries for adjustments, corrections, and other transactions',
                'number_prefix': 'JV',
                'next_number': 1
            },
            {
                'voucher_code': 'PV',
                'voucher_name': 'Payment Voucher - Payments Made',
                'description': 'All payments made by the company including expenses, vendor payments, etc.',
                'number_prefix': 'PV',
                'next_number': 1
            },
            {
                'voucher_code': 'RV',
                'voucher_name': 'Receipt Voucher - Receipts Received',
                'description': 'All receipts received by the company including sales, customer payments, etc.',
                'number_prefix': 'RV',
                'next_number': 1
            },
            {
                'voucher_code': 'BV',
                'voucher_name': 'Bank Voucher - Bank Transactions',
                'description': 'Bank-related transactions including transfers, bank charges, interest, etc.',
                'number_prefix': 'BV',
                'next_number': 1
            },
            {
                'voucher_code': 'CV',
                'voucher_name': 'Cash Voucher - Cash Transactions',
                'description': 'Cash transactions including petty cash, cash sales, cash expenses, etc.',
                'number_prefix': 'CV',
                'next_number': 1
            }
        ]
        
        for vt_data in voucher_types:
            voucher_type, created = VoucherType.objects.get_or_create(
                voucher_code=vt_data['voucher_code'],
                defaults=vt_data
            )
            if created:
                self.stdout.write(f"  ✓ Created voucher type: {voucher_type.voucher_name}")
            else:
                self.stdout.write(f"  - Voucher type already exists: {voucher_type.voucher_name}")

    def create_projects(self):
        """Create sample projects"""
        self.stdout.write("Creating sample projects...")
        
        # Get a user for created_by field
        user = User.objects.first()
        if not user:
            self.stdout.write("  ⚠ No users found. Please create a user first.")
            return
        
        projects = [
            {
                'project_code': 'PROJ001',
                'project_name': 'Website Development',
                'description': 'Company website redesign and development project',
                'start_date': date.today() - timedelta(days=30),
                'end_date': date.today() + timedelta(days=60),
                'created_by': user
            },
            {
                'project_code': 'PROJ002',
                'project_name': 'Office Renovation',
                'description': 'Office space renovation and interior design',
                'start_date': date.today() - timedelta(days=15),
                'end_date': date.today() + timedelta(days=45),
                'created_by': user
            },
            {
                'project_code': 'PROJ003',
                'project_name': 'Marketing Campaign Q1',
                'description': 'First quarter marketing and advertising campaign',
                'start_date': date.today(),
                'end_date': date.today() + timedelta(days=90),
                'created_by': user
            },
            {
                'project_code': 'PROJ004',
                'project_name': 'Software Implementation',
                'description': 'ERP software implementation and training',
                'start_date': date.today() - timedelta(days=10),
                'end_date': date.today() + timedelta(days=120),
                'created_by': user
            }
        ]
        
        for proj_data in projects:
            project, created = Project.objects.get_or_create(
                project_code=proj_data['project_code'],
                defaults=proj_data
            )
            if created:
                self.stdout.write(f"  ✓ Created project: {project.project_name}")
            else:
                self.stdout.write(f"  - Project already exists: {project.project_name}")

    def create_products(self):
        """Create sample products and services"""
        self.stdout.write("Creating sample products/services...")
        
        # Get a user for created_by field
        user = User.objects.first()
        if not user:
            self.stdout.write("  ⚠ No users found. Please create a user first.")
            return
        
        products = [
            {
                'product_code': 'SERV001',
                'product_name': 'Consulting Services',
                'description': 'Professional consulting and advisory services',
                'product_type': 'SERVICE',
                'unit_price': Decimal('150.00'),
                'created_by': user
            },
            {
                'product_code': 'SERV002',
                'product_name': 'Software Development',
                'description': 'Custom software development services',
                'product_type': 'SERVICE',
                'unit_price': Decimal('200.00'),
                'created_by': user
            },
            {
                'product_code': 'PROD001',
                'product_name': 'Office Supplies',
                'description': 'General office supplies and stationery',
                'product_type': 'PRODUCT',
                'unit_price': Decimal('25.00'),
                'created_by': user
            },
            {
                'product_code': 'PROD002',
                'product_name': 'Computer Equipment',
                'description': 'Computers, laptops, and IT equipment',
                'product_type': 'PRODUCT',
                'unit_price': Decimal('1200.00'),
                'created_by': user
            },
            {
                'product_code': 'DIGI001',
                'product_name': 'Software License',
                'description': 'Software licenses and digital products',
                'product_type': 'DIGITAL',
                'unit_price': Decimal('500.00'),
                'created_by': user
            }
        ]
        
        for prod_data in products:
            product, created = Product.objects.get_or_create(
                product_code=prod_data['product_code'],
                defaults=prod_data
            )
            if created:
                self.stdout.write(f"  ✓ Created product: {product.product_name}")
            else:
                self.stdout.write(f"  - Product already exists: {product.product_name}") 