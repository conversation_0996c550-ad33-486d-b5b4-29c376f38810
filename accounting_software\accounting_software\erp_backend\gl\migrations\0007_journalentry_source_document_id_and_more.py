# Generated by Django 4.2.21 on 2025-06-16 18:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gl', '0006_add_sales_tax_to_journal_entry_line'),
    ]

    operations = [
        migrations.AddField(
            model_name='journalentry',
            name='source_document_id',
            field=models.CharField(blank=True, help_text='ID of the source document (GRN number, PO number, etc.)', max_length=50),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='source_document_reference',
            field=models.CharField(blank=True, help_text='Additional reference information for the source document', max_length=100),
        ),
        migrations.AddField(
            model_name='journalentry',
            name='source_document_type',
            field=models.CharField(blank=True, choices=[('GRN', 'Goods Receipt Note'), ('PO', 'Purchase Order'), ('INVOICE', 'Invoice'), ('BILL', 'Bill'), ('PAYMENT', 'Payment'), ('RECEIPT', 'Receipt'), ('MANUAL', 'Manual Entry')], help_text='Type of source document that generated this journal entry', max_length=20),
        ),
    ]
