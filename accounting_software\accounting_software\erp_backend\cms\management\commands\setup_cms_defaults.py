"""
Django management command to set up default Cash Management System data.

This command creates default cash account types and sample data to make
the CMS immediately usable for demonstration and testing.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from cms.models import CashAccountType, CashAccount
from decimal import Decimal


class Command(BaseCommand):
    help = 'Set up default Cash Management System data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if data already exists',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Setting up Cash Management System defaults...')
        )

        # Create default cash account types
        account_types_data = [
            {
                'code': 'BANK_CHECKING',
                'name': 'Bank Checking Account',
                'description': 'Regular checking accounts for daily operations',
                'requires_bank_details': True,
                'allows_overdraft': False,
                'requires_reconciliation': True,
                'is_investment_account': False,
                'sort_order': 1,
            },
            {
                'code': 'BANK_SAVINGS',
                'name': 'Bank Savings Account',
                'description': 'Savings accounts for earning interest',
                'requires_bank_details': True,
                'allows_overdraft': False,
                'requires_reconciliation': True,
                'is_investment_account': False,
                'sort_order': 2,
            },
            {
                'code': 'BANK_CREDIT_LINE',
                'name': 'Bank Credit Line',
                'description': 'Line of credit facilities',
                'requires_bank_details': True,
                'allows_overdraft': True,
                'requires_reconciliation': True,
                'is_investment_account': False,
                'sort_order': 3,
            },
            {
                'code': 'PETTY_CASH',
                'name': 'Petty Cash Fund',
                'description': 'Small cash funds for office expenses',
                'requires_bank_details': False,
                'allows_overdraft': False,
                'requires_reconciliation': False,
                'is_investment_account': False,
                'sort_order': 4,
            },
            {
                'code': 'CASH_ON_HAND',
                'name': 'Cash on Hand',
                'description': 'Physical cash kept on premises',
                'requires_bank_details': False,
                'allows_overdraft': False,
                'requires_reconciliation': False,
                'is_investment_account': False,
                'sort_order': 5,
            },
            {
                'code': 'INVESTMENT',
                'name': 'Investment Account',
                'description': 'Short-term investment and money market accounts',
                'requires_bank_details': True,
                'allows_overdraft': False,
                'requires_reconciliation': True,
                'is_investment_account': True,
                'sort_order': 6,
            },
            {
                'code': 'ESCROW',
                'name': 'Escrow/Trust Account',
                'description': 'Client funds held in trust',
                'requires_bank_details': True,
                'allows_overdraft': False,
                'requires_reconciliation': True,
                'is_investment_account': False,
                'sort_order': 7,
            },
            {
                'code': 'FOREIGN_CURRENCY',
                'name': 'Foreign Currency Account',
                'description': 'Accounts denominated in foreign currencies',
                'requires_bank_details': True,
                'allows_overdraft': False,
                'requires_reconciliation': True,
                'is_investment_account': False,
                'sort_order': 8,
            },
        ]

        created_types = 0
        for type_data in account_types_data:
            account_type, created = CashAccountType.objects.get_or_create(
                code=type_data['code'],
                defaults=type_data
            )
            if created or options['force']:
                if not created and options['force']:
                    for key, value in type_data.items():
                        setattr(account_type, key, value)
                    account_type.save()
                    self.stdout.write(f"  Updated: {account_type.name}")
                else:
                    self.stdout.write(f"  Created: {account_type.name}")
                created_types += 1
            else:
                self.stdout.write(f"  Exists: {account_type.name}")

        self.stdout.write(
            self.style.SUCCESS(f'✓ Created/updated {created_types} cash account types')
        )

        # Create sample cash accounts if user exists
        if User.objects.exists():
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.first()

            # Get account types
            checking_type = CashAccountType.objects.get(code='BANK_CHECKING')
            savings_type = CashAccountType.objects.get(code='BANK_SAVINGS')
            petty_cash_type = CashAccountType.objects.get(code='PETTY_CASH')

            sample_accounts_data = [
                {
                    'account_name': 'Main Checking Account',
                    'account_number': 'CHK-001',
                    'account_type': checking_type,
                    'bank_name': 'First National Bank',
                    'bank_branch': 'Downtown Branch',
                    'bank_routing_number': '*********',
                    'currency': 'USD',
                    'opening_balance': Decimal('50000.00'),
                    'is_active': True,
                    'is_default': True,
                    'created_by': admin_user,
                },
                {
                    'account_name': 'Business Savings',
                    'account_number': 'SAV-001',
                    'account_type': savings_type,
                    'bank_name': 'First National Bank',
                    'bank_branch': 'Downtown Branch',
                    'bank_routing_number': '*********',
                    'currency': 'USD',
                    'opening_balance': Decimal('100000.00'),
                    'interest_rate': Decimal('2.5'),
                    'is_active': True,
                    'is_default': False,
                    'created_by': admin_user,
                },
                {
                    'account_name': 'Office Petty Cash',
                    'account_number': 'PC-001',
                    'account_type': petty_cash_type,
                    'currency': 'USD',
                    'opening_balance': Decimal('500.00'),
                    'is_active': True,
                    'is_default': False,
                    'created_by': admin_user,
                    'description': 'Small expenses and office supplies',
                },
            ]

            created_accounts = 0
            for account_data in sample_accounts_data:
                account, created = CashAccount.objects.get_or_create(
                    account_number=account_data['account_number'],
                    defaults=account_data
                )
                if created or options['force']:
                    if not created and options['force']:
                        for key, value in account_data.items():
                            setattr(account, key, value)
                        account.save()
                        self.stdout.write(f"  Updated: {account.account_name}")
                    else:
                        self.stdout.write(f"  Created: {account.account_name}")
                    created_accounts += 1
                else:
                    self.stdout.write(f"  Exists: {account.account_name}")

            self.stdout.write(
                self.style.SUCCESS(f'✓ Created/updated {created_accounts} sample cash accounts')
            )
        else:
            self.stdout.write(
                self.style.WARNING('! No users found. Sample accounts not created.')
            )

        self.stdout.write(
            self.style.SUCCESS('🎉 Cash Management System setup completed successfully!')
        )
        self.stdout.write(
            self.style.SUCCESS('You can now access the CMS in Django Admin at /admin/')
        ) 