"""
Audit Trail URL Configuration

Defines API endpoints for audit trail access and reporting.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router for audit API endpoints
router = DefaultRouter()
router.register(r'logs', views.AuditLogViewSet, basename='auditlog')
router.register(r'sessions', views.UserSessionViewSet, basename='usersession')

app_name = 'audit'

urlpatterns = [
    path('api/', include(router.urls)),
] 