from django.db import models
from django.conf import settings

class TDS(models.Model):
    TDS_TYPE_CHOICES = [
        ('payment_deduction', 'Tax Deducted at Source while Making Payment'),
        ('receipt_deduction', 'Tax Deducted at Source while Receiving Payment'),
    ]
    
    tds_type = models.CharField(max_length=20, choices=TDS_TYPE_CHOICES)
    description = models.CharField(max_length=255)
    rate = models.DecimalField(max_digits=6, decimal_places=3)
    section = models.CharField(max_length=50, blank=True, help_text="Income Tax Section (e.g., 194A, 194C, etc.)")
    threshold_limit = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True, help_text="Minimum amount above which TDS applies")
    remarks = models.TextField(blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'TDS'
        verbose_name_plural = 'TDS'
        ordering = ['tds_type', 'description']

    def __str__(self):
        return f"{self.get_tds_type_display()} - {self.description} ({self.rate}%)"
    
    @property
    def is_liability(self):
        """Returns True if this TDS type creates a liability for the company"""
        return self.tds_type == 'payment_deduction'
    
    @property
    def is_asset(self):
        """Returns True if this TDS type creates an advance tax asset for the company"""
        return self.tds_type == 'receipt_deduction'
