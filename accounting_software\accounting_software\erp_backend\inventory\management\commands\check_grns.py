from django.core.management.base import BaseCommand
from inventory.models import GoodsReceiptNote, GoodsReturnNote
from purchase.models import PurchaseOrder


class Command(BaseCommand):
    help = 'Check GRN data for debugging'

    def handle(self, *args, **options):
        self.stdout.write("=== GRN DATA CHECK ===")
        
        # Check all GRNs
        all_grns = GoodsReceiptNote.objects.all()
        self.stdout.write(f"Total GRNs in database: {all_grns.count()}")
        
        if all_grns.count() == 0:
            self.stdout.write(self.style.WARNING("No GRNs found in database!"))
            
            # Check if we have Purchase Orders
            pos = PurchaseOrder.objects.all()
            self.stdout.write(f"Total Purchase Orders: {pos.count()}")
            
            if pos.count() > 0:
                self.stdout.write("Purchase Orders exist but no GRNs. You need to create GRNs first.")
                for po in pos[:3]:
                    self.stdout.write(f"  PO: {po.po_number} - Status: {po.status}")
            else:
                self.stdout.write("No Purchase Orders found either. Create POs first, then GRNs.")
            return
        
        # Show GRN status breakdown
        self.stdout.write("\n=== GRN STATUS BREAKDOWN ===")
        statuses = ['DRAFT', 'RECEIVED', 'POSTED', 'CANCELLED']
        for status in statuses:
            count = GoodsReceiptNote.objects.filter(status=status).count()
            self.stdout.write(f"{status}: {count}")
        
        # Show first few GRNs
        self.stdout.write("\n=== FIRST 5 GRNs ===")
        for grn in all_grns[:5]:
            self.stdout.write(f"\nGRN: {grn.grn_number}")
            self.stdout.write(f"  Status: {grn.status}")
            self.stdout.write(f"  Items: {grn.items.count()}")
            self.stdout.write(f"  PO: {grn.purchase_order.po_number if grn.purchase_order else 'None'}")
            self.stdout.write(f"  Vendor: {grn.purchase_order.vendor.name if grn.purchase_order and grn.purchase_order.vendor else 'None'}")
            self.stdout.write(f"  Warehouse: {grn.warehouse.name if grn.warehouse else 'None'}")
            
            # Check items
            for item in grn.items.all():
                self.stdout.write(f"    Item: {item.product_name} - Qty: {item.quantity_received}")
        
        # Check existing returns
        returns = GoodsReturnNote.objects.all()
        self.stdout.write(f"\n=== EXISTING RETURNS ===")
        self.stdout.write(f"Total Returns: {returns.count()}")
        
        # Check which GRNs should be returnable
        self.stdout.write(f"\n=== RETURNABLE GRNs CHECK ===")
        returnable_grns = GoodsReceiptNote.objects.filter(
            status__in=['DRAFT', 'RECEIVED', 'POSTED']
        )
        self.stdout.write(f"GRNs with DRAFT/RECEIVED/POSTED status: {returnable_grns.count()}")
        
        for grn in returnable_grns:
            has_po = bool(grn.purchase_order)
            has_vendor = bool(grn.purchase_order and grn.purchase_order.vendor) if has_po else False
            has_warehouse = bool(grn.warehouse)
            has_items = grn.items.count() > 0
            
            self.stdout.write(f"GRN {grn.grn_number}: PO={has_po}, Vendor={has_vendor}, Warehouse={has_warehouse}, Items={has_items}")
            
            if has_po and has_vendor and has_warehouse and has_items:
                self.stdout.write(f"  ✅ {grn.grn_number} should be returnable!")
            else:
                self.stdout.write(f"  ❌ {grn.grn_number} missing requirements") 