import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Card,
  CardContent,
  Grid,
  Paper,
} from '@mui/material';
import {
  Receipt as ReceiptIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';

// Import PO types and service from purchase domain
interface PurchaseOrder {
  id: number;
  po_number: string;
  vendor_name: string;
  po_date: string;
  expected_date?: string;
  status: string;
  subtotal: number;
  discount_amount: number;
  tax_amount: number;
  total_amount: number;
  currency: string;
  payment_terms: string;
  reference_number?: string;
  memo?: string;
  created_at: string;
  line_items?: any[];
}

interface PurchaseOrderStats {
  total_pos: number;
  draft_pos: number;
  sent_pos: number;
  acknowledged_pos: number;
  received_pos: number;
  total_value: number;
  pending_value: number;
}

const PurchaseOrdersReviewPage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [stats, setStats] = useState<PurchaseOrderStats | null>(null);
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadPurchaseOrders();
    loadStats();
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadPurchaseOrders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem('token');
      const queryParams = new URLSearchParams();
      
      queryParams.append('page', (page + 1).toString());
      if (searchTerm) queryParams.append('search', searchTerm);
      if (statusFilter) queryParams.append('status', statusFilter);

      const response = await fetch(`http://localhost:8000/api/purchase/purchase-orders/?${queryParams}`, {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load purchase orders');
      }

      const data = await response.json();
      setPurchaseOrders(data.results || []);
      setTotalCount(data.count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load purchase orders');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadStats = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/purchase/purchase-orders/stats/', {
        headers: {
          'Authorization': `Token ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load PO statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Failed to load PO stats:', err);
    }
  };

  const handleViewPO = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setViewDialogOpen(true);
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      draft: { color: 'default' as const, label: 'Draft' },
      sent: { color: 'primary' as const, label: 'Sent' },
      acknowledged: { color: 'info' as const, label: 'Acknowledged' },
      partial: { color: 'warning' as const, label: 'Partially Received' },
      received: { color: 'success' as const, label: 'Received' },
      cancelled: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const columns = [
    {
      field: 'po_number',
      headerName: 'PO Number',
      flex: 1,
      renderCell: ({ row }: { row: PurchaseOrder }) => (
        <Typography variant="body2" fontWeight="medium">
          {row.po_number}
        </Typography>
      ),
    },
    {
      field: 'vendor_name',
      headerName: 'Vendor',
      flex: 1.5,
    },
    {
      field: 'po_date',
      headerName: 'PO Date',
      flex: 1,
      renderCell: ({ row }: { row: PurchaseOrder }) => formatDate(row.po_date),
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      renderCell: ({ row }: { row: PurchaseOrder }) => getStatusChip(row.status),
    },
    {
      field: 'subtotal',
      headerName: 'Subtotal',
      flex: 1,
      renderCell: ({ row }: { row: PurchaseOrder }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'medium' }}>
          {formatCurrency(row.subtotal || 0)}
        </Typography>
      ),
    },
    {
      field: 'tax_amount',
      headerName: 'Tax Amount',
      flex: 1,
      renderCell: ({ row }: { row: PurchaseOrder }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', color: 'primary.main' }}>
          {formatCurrency(row.tax_amount || 0)}
        </Typography>
      ),
    },
    {
      field: 'total_amount',
      headerName: 'Total Amount',
      flex: 1.2,
      renderCell: ({ row }: { row: PurchaseOrder }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'bold' }}>
          {formatCurrency(row.total_amount || 0)}
        </Typography>
      ),
    },
    {
      field: 'payment_terms',
      headerName: 'Payment Terms',
      flex: 1,
    },
  ];

  const rowActions = [
    {
      icon: <ViewIcon />,
      label: 'View Details',
      onClick: (row: PurchaseOrder) => handleViewPO(row),
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold" display="flex" alignItems="center" gap={1}>
            <ReceiptIcon />
            Purchase Orders Review
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Financial review of purchase orders for accounting purposes
          </Typography>
        </Box>
      </PageHeader>

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <BusinessIcon color="primary" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total POs</Typography>
                    <Typography variant="h6" fontWeight="bold">{stats.total_pos}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <CalendarIcon color="warning" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Pending</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {stats.draft_pos + stats.sent_pos + stats.acknowledged_pos}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <AccountBalanceIcon color="success" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Total Value</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {formatCurrency(stats.total_value)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={1}>
                  <TrendingUpIcon color="info" />
                  <Box>
                    <Typography variant="body2" color="text.secondary">Pending Value</Typography>
                    <Typography variant="h6" fontWeight="bold">
                      {formatCurrency(stats.pending_value)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <DataTable
        columns={columns}
        rows={purchaseOrders}
        loading={loading}
        totalCount={totalCount}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        rowActions={rowActions}
        serverSide={true}
        showToolbar={true}
        title="Purchase Orders"
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
      />

      {/* View PO Dialog */}
      <Dialog 
        open={viewDialogOpen} 
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" display="flex" alignItems="center" gap={1}>
            <ReceiptIcon />
            Purchase Order Details - {selectedPO?.po_number}
          </Typography>
        </DialogTitle>
        <DialogContent>
          {selectedPO && (
            <Box sx={{ mt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>Basic Information</Typography>
                    <Typography variant="body2" color="text.secondary">Vendor</Typography>
                    <Typography variant="body1" fontWeight="medium" gutterBottom>
                      {selectedPO.vendor_name}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">PO Date</Typography>
                    <Typography variant="body1" gutterBottom>
                      {formatDate(selectedPO.po_date)}
                    </Typography>
                    
                    <Typography variant="body2" color="text.secondary">Status</Typography>
                    <Box sx={{ mb: 2 }}>
                      {getStatusChip(selectedPO.status)}
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary">Payment Terms</Typography>
                    <Typography variant="body1">
                      {selectedPO.payment_terms}
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>Financial Summary</Typography>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Subtotal:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(selectedPO.subtotal || 0)}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Discount:</Typography>
                      <Typography variant="body2" color="error.main">
                        -{formatCurrency(selectedPO.discount_amount || 0)}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" mb={1}>
                      <Typography variant="body2" color="text.secondary">Tax Amount:</Typography>
                      <Typography variant="body2" color="primary.main">
                        {formatCurrency(selectedPO.tax_amount || 0)}
                      </Typography>
                    </Box>
                    
                    <Box display="flex" justifyContent="space-between" sx={{ pt: 1, borderTop: 1, borderColor: 'divider' }}>
                      <Typography variant="h6">Total Amount:</Typography>
                      <Typography variant="h6" fontWeight="bold">
                        {formatCurrency(selectedPO.total_amount || 0)}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">Currency:</Typography>
                      <Typography variant="body1">{selectedPO.currency}</Typography>
                    </Box>
                  </Paper>
                </Grid>
                
                {selectedPO.memo && (
                  <Grid item xs={12}>
                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>Memo</Typography>
                      <Typography variant="body1">{selectedPO.memo}</Typography>
                    </Paper>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default PurchaseOrdersReviewPage; 