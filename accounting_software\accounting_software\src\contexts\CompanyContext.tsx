import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSnackbar } from 'notistack';

// Interface for company information stored in the frontend
export interface CompanyInfo {
  id?: string;                    // Company ID from backend
  companyName: string;           // Legal company name
  address: string;               // Business address
  city: string;                  // City location
  state: string;                 // State/Province
  postalCode: string;           // Postal/ZIP code
  country: string;              // Country name
  phone: string;                // Business phone number
  email: string;                // Business email address
  website: string;              // Company website URL
  taxId: string;                // Tax identification number
  functionalCurrency: string;   // Primary business currency (NEW)
  reportingCurrency: string;    // Financial reporting currency (NEW)
  registrationNumber?: string;  // Company registration number
  salesTaxRegistered?: boolean; // Sales tax registration status
  salesTaxNumber?: string;      // Sales tax number if registered
  accountingYearStart?: string; // Accounting year start date
  accountingYearEnd?: string;   // Accounting year end date
  fiscalYearStart?: string;     // Fiscal year start date
  fiscalYearEnd?: string;       // Fiscal year end date
  logo?: string;                // Company logo URL
  createdAt?: string;           // When company was created
  updatedAt?: string;           // When company was last updated
}

// Context interface with all available functions
interface CompanyContextType {
  companyInfo: CompanyInfo | null;           // Current company data (null if not loaded)
  loading: boolean;                          // Loading state for API calls
  error: string | null;                      // Error message if any
  companyExists: boolean;                    // Whether a company exists in system
  updateCompanyInfo: (info: Partial<CompanyInfo>) => Promise<void>;  // Update company
  fetchCompanyInfo: () => Promise<void>;     // Fetch latest company data
  checkCompanyExists: () => Promise<boolean>; // Check if company exists
  clearError: () => void;                    // Clear error state
}

// Create the context
const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

// Hook to use the company context
export const useCompany = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (!context) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};

interface CompanyProviderProps {
  children: ReactNode;
}

// Company provider component that manages all company-related state
export const CompanyProvider: React.FC<CompanyProviderProps> = ({ children }) => {
  // State management for company information
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [companyExists, setCompanyExists] = useState<boolean>(false);
  
  // Snackbar for user notifications
  const { enqueueSnackbar } = useSnackbar();

  // Helper function to get authentication token
  const getAuthToken = () => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }
    return token;
  };

  // Helper function to make authenticated API calls
  const apiCall = async (url: string, options: RequestInit = {}) => {
    const token = getAuthToken();
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`,
      ...options.headers,
    };

    const response = await fetch(`http://localhost:8000${url}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  };

  // Function to check if a company exists in the system
  const checkCompanyExists = async (): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      
      // Call the backend API to check if company exists
      const data = await apiCall('/api/account/company/current/');
      setCompanyExists(true);
      return true;
    } catch (err) {
      console.error('Error checking company existence:', err);
      setCompanyExists(false);
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch current company information from backend
  const fetchCompanyInfo = async (): Promise<void> => {
    console.log('CompanyContext: fetchCompanyInfo called');
    try {
      setLoading(true);
      setError(null);
      
      // Call the backend API to get current company
      console.log('CompanyContext: Making API call to /api/account/company/current/');
      const data = await apiCall('/api/account/company/current/');
      console.log('CompanyContext: Received company data:', data);
      
      // Transform backend data to frontend format
      const transformedData: CompanyInfo = {
        id: data.id,
        companyName: data.name || '',
        address: data.address || '',
        city: 'Metropolis', // Default values for missing fields
        state: 'NY',
        postalCode: '10001',
        country: 'United States',
        phone: data.phone || '',
        email: data.email || '',
        website: 'www.company.com',
        taxId: data.tax_id || '',
        functionalCurrency: data.functional_currency || 'USD',
        reportingCurrency: data.reporting_currency || 'USD',
        registrationNumber: data.registration_number || '',
        salesTaxRegistered: data.sales_tax_registered || false,
        salesTaxNumber: data.sales_tax_number || '',
        accountingYearStart: data.accounting_year_start || '',
        accountingYearEnd: data.accounting_year_end || '',
        fiscalYearStart: data.fiscal_year_start || '',
        fiscalYearEnd: data.fiscal_year_end || '',
        logo: data.logo,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
      };
      
      console.log('CompanyContext: Setting company info:', transformedData);
      setCompanyInfo(transformedData);
      setCompanyExists(true);
      
    } catch (err) {
      console.error('CompanyContext: Error fetching company info:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch company information');
      setCompanyInfo(null);
      setCompanyExists(false);
    } finally {
      setLoading(false);
      console.log('CompanyContext: fetchCompanyInfo completed');
    }
  };

  // Function to update company information on the backend
  const updateCompanyInfo = async (info: Partial<CompanyInfo>): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      if (!companyInfo?.id) {
        throw new Error('No company ID found. Cannot update company.');
      }
      
      // Transform frontend data to backend format
      const backendData = {
        name: info.companyName || companyInfo.companyName,
        address: info.address || companyInfo.address,
        phone: info.phone || companyInfo.phone,
        email: info.email || companyInfo.email,
        tax_id: info.taxId || companyInfo.taxId,
        functional_currency: info.functionalCurrency || companyInfo.functionalCurrency,
        reporting_currency: info.reportingCurrency || companyInfo.reportingCurrency,
        registration_number: info.registrationNumber || companyInfo.registrationNumber,
        sales_tax_registered: info.salesTaxRegistered !== undefined ? info.salesTaxRegistered : companyInfo.salesTaxRegistered,
        sales_tax_number: info.salesTaxNumber || companyInfo.salesTaxNumber,
        accounting_year_start: info.accountingYearStart || companyInfo.accountingYearStart,
        accounting_year_end: info.accountingYearEnd || companyInfo.accountingYearEnd,
        fiscal_year_start: info.fiscalYearStart || companyInfo.fiscalYearStart,
        fiscal_year_end: info.fiscalYearEnd || companyInfo.fiscalYearEnd,
      };
      
      console.log('Updating company with data:', backendData);
      
      // Call the backend API to update company
      const updatedData = await apiCall(`/api/account/companies/${companyInfo.id}/`, {
        method: 'PATCH',
        body: JSON.stringify(backendData),
      });
      
      console.log('Company update successful:', updatedData);
      
      // Refresh company info from backend to get latest data
      await fetchCompanyInfo();
      
      // Show success message
      enqueueSnackbar('Company information updated successfully!', { 
        variant: 'success',
        autoHideDuration: 3000,
      });
      
    } catch (err) {
      console.error('Error updating company info:', err);
      
      let errorMessage = 'Failed to update company information';
      
      if (err instanceof Error) {
        if (err.message.includes('403') || err.message.includes('Permission denied')) {
          errorMessage = 'You do not have permission to edit company information. Please contact an administrator.';
        } else if (err.message.includes('401') || err.message.includes('Unauthorized')) {
          errorMessage = 'Your session has expired. Please log in again.';
        } else {
          errorMessage = err.message;
        }
      }
      
      setError(errorMessage);
      
      // Show error message
      enqueueSnackbar(errorMessage, { 
        variant: 'error',
        autoHideDuration: 5000,
      });
      
      throw err; // Re-throw to allow component to handle if needed
    } finally {
      setLoading(false);
    }
  };

  // Function to clear error state
  const clearError = () => {
    setError(null);
  };

  // Load company information when component mounts
  useEffect(() => {
    const loadCompanyData = async () => {
      try {
        // First check if company exists and fetch its information
        const exists = await checkCompanyExists();
        if (exists) {
          await fetchCompanyInfo();
        }
      } catch (err) {
        console.error('Error loading initial company data:', err);
      }
    };

    loadCompanyData();
  }, []); // Only run once when component mounts

  // Provide context value to children components
  const contextValue: CompanyContextType = {
    companyInfo,
    loading,
    error,
    companyExists,
    updateCompanyInfo,
    fetchCompanyInfo,
    checkCompanyExists,
    clearError,
  };

  return (
    <CompanyContext.Provider value={contextValue}>
      {children}
    </CompanyContext.Provider>
  );
};

export default CompanyProvider;