# Generated by Django 4.2.21 on 2025-06-09 22:00

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('gl', '0005_add_multi_currency_support'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BankStatement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('statement_number', models.CharField(help_text='Bank statement number or identifier', max_length=50)),
                ('statement_date', models.DateField(help_text='Statement ending date')),
                ('period_from', models.DateField(help_text='Statement period start date')),
                ('period_to', models.DateField(help_text='Statement period end date')),
                ('opening_balance', models.DecimalField(decimal_places=2, help_text='Opening balance on statement', max_digits=15)),
                ('closing_balance', models.DecimalField(decimal_places=2, help_text='Closing balance on statement', max_digits=15)),
                ('import_file', models.CharField(blank=True, help_text='Original file name if imported', max_length=500)),
                ('import_format', models.CharField(blank=True, help_text='Format of imported file (CSV, QIF, OFX, etc.)', max_length=20)),
                ('is_reconciled', models.BooleanField(default=False, help_text='Whether this statement has been reconciled')),
                ('reconciled_at', models.DateTimeField(blank=True, help_text='Timestamp when reconciliation was completed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Bank Statement',
                'verbose_name_plural': 'Bank Statements',
                'ordering': ['-statement_date'],
            },
        ),
        migrations.CreateModel(
            name='CashAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_name', models.CharField(help_text='Descriptive name for the cash account', max_length=200)),
                ('account_number', models.CharField(help_text='Unique identifier for the account (can be bank account number or internal code)', max_length=50, unique=True)),
                ('bank_name', models.CharField(blank=True, help_text='Name of the financial institution', max_length=200)),
                ('bank_branch', models.CharField(blank=True, help_text='Bank branch or location', max_length=200)),
                ('bank_routing_number', models.CharField(blank=True, help_text='Bank routing or sort code', max_length=20)),
                ('bank_swift_code', models.CharField(blank=True, help_text='SWIFT/BIC code for international transfers', max_length=20)),
                ('bank_iban', models.CharField(blank=True, help_text='International Bank Account Number', max_length=34)),
                ('currency', models.CharField(default='USD', help_text='Primary currency for this account (ISO 4217 code)', max_length=3)),
                ('opening_balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Opening balance when account was created', max_digits=15)),
                ('opening_balance_date', models.DateField(default=django.utils.timezone.now, help_text='Date of the opening balance')),
                ('current_balance', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Current calculated balance (updated automatically)', max_digits=15)),
                ('credit_limit', models.DecimalField(blank=True, decimal_places=2, help_text='Credit limit for credit line accounts', max_digits=15, null=True)),
                ('interest_rate', models.DecimalField(blank=True, decimal_places=4, help_text='Interest rate for savings or credit accounts (annual %)', max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('description', models.TextField(blank=True, help_text='Additional notes about the account')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the account is currently active')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default cash account for transactions')),
                ('requires_approval', models.BooleanField(default=False, help_text='Whether transactions require approval')),
                ('last_reconciled_date', models.DateField(blank=True, help_text='Date of last successful reconciliation', null=True)),
                ('last_reconciled_balance', models.DecimalField(blank=True, decimal_places=2, help_text='Balance as of last reconciliation', max_digits=15, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account_manager', models.ForeignKey(blank=True, help_text='User responsible for managing this account', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_cash_accounts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Cash Account',
                'verbose_name_plural': 'Cash Accounts',
                'ordering': ['account_name'],
            },
        ),
        migrations.CreateModel(
            name='CashAccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(choices=[('BANK_CHECKING', 'Bank Checking Account'), ('BANK_SAVINGS', 'Bank Savings Account'), ('BANK_CREDIT_LINE', 'Bank Credit Line'), ('PETTY_CASH', 'Petty Cash Fund'), ('CASH_ON_HAND', 'Cash on Hand'), ('INVESTMENT', 'Investment Account'), ('ESCROW', 'Escrow/Trust Account'), ('FOREIGN_CURRENCY', 'Foreign Currency Account')], help_text='Unique code for the cash account type', max_length=20, unique=True)),
                ('name', models.CharField(help_text='Display name for the cash account type', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the account type')),
                ('requires_bank_details', models.BooleanField(default=True, help_text='Whether this account type requires banking information')),
                ('allows_overdraft', models.BooleanField(default=False, help_text='Whether negative balances are allowed')),
                ('requires_reconciliation', models.BooleanField(default=True, help_text='Whether this account needs bank reconciliation')),
                ('is_investment_account', models.BooleanField(default=False, help_text='Whether this is an investment/trading account')),
                ('sort_order', models.PositiveIntegerField(default=1, help_text='Order for displaying account types')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this account type is available for use')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Cash Account Type',
                'verbose_name_plural': 'Cash Account Types',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CashTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_number', models.CharField(help_text='Unique transfer number for tracking', max_length=50, unique=True)),
                ('transfer_date', models.DateField(help_text='Date when the transfer occurred')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Transfer amount in source account currency', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('exchange_rate', models.DecimalField(decimal_places=6, default=Decimal('1.000000'), help_text='Exchange rate if currencies differ', max_digits=10)),
                ('destination_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Amount in destination currency (calculated)', max_digits=15, null=True)),
                ('description', models.CharField(help_text='Description of the transfer', max_length=500)),
                ('reference_number', models.CharField(blank=True, help_text='Transfer reference number', max_length=100)),
                ('memo', models.TextField(blank=True, help_text='Additional notes about the transfer')),
                ('transfer_fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Fee charged for the transfer', max_digits=15)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending'), ('POSTED', 'Posted'), ('CANCELLED', 'Cancelled')], default='DRAFT', help_text='Current status of the transfer', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('posted_at', models.DateTimeField(blank=True, help_text='Timestamp when transfer was posted', null=True)),
                ('created_by', models.ForeignKey(help_text='User who created this transfer', on_delete=django.db.models.deletion.PROTECT, related_name='created_cash_transfers', to=settings.AUTH_USER_MODEL)),
                ('fee_account', models.ForeignKey(blank=True, help_text='Account to debit transfer fees', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transfer_fees', to='cms.cashaccount')),
                ('from_account', models.ForeignKey(help_text='Source cash account', on_delete=django.db.models.deletion.PROTECT, related_name='cash_transfers_from', to='cms.cashaccount')),
                ('gl_journal_entry', models.ForeignKey(blank=True, help_text='Related journal entry in General Ledger', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cash_transfers', to='gl.journalentry')),
                ('posted_by', models.ForeignKey(blank=True, help_text='User who posted this transfer', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_cash_transfers', to=settings.AUTH_USER_MODEL)),
                ('to_account', models.ForeignKey(help_text='Destination cash account', on_delete=django.db.models.deletion.PROTECT, related_name='cash_transfers_to', to='cms.cashaccount')),
            ],
            options={
                'verbose_name': 'Cash Transfer',
                'verbose_name_plural': 'Cash Transfers',
                'ordering': ['-transfer_date', '-transfer_number'],
            },
        ),
        migrations.CreateModel(
            name='CashTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(help_text='Unique transaction number for tracking', max_length=50, unique=True)),
                ('transaction_date', models.DateField(help_text='Date when the transaction occurred')),
                ('transaction_type', models.CharField(choices=[('RECEIPT', 'Cash Receipt'), ('PAYMENT', 'Cash Payment'), ('ADJUSTMENT', 'Balance Adjustment'), ('FEE', 'Bank Fee/Charge'), ('INTEREST', 'Interest Received/Paid'), ('EXCHANGE', 'Currency Exchange')], help_text='Type of cash transaction', max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Transaction amount (always positive)', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('currency', models.CharField(help_text='Transaction currency (ISO 4217 code)', max_length=3)),
                ('exchange_rate', models.DecimalField(decimal_places=6, default=Decimal('1.000000'), help_text='Exchange rate to account currency', max_digits=10)),
                ('description', models.CharField(help_text='Description of the transaction', max_length=500)),
                ('reference_number', models.CharField(blank=True, help_text='External reference (check number, wire reference, etc.)', max_length=100)),
                ('memo', models.TextField(blank=True, help_text='Additional notes about the transaction')),
                ('customer', models.CharField(blank=True, help_text='Customer name for receipts', max_length=200)),
                ('vendor', models.CharField(blank=True, help_text='Vendor name for payments', max_length=200)),
                ('employee', models.CharField(blank=True, help_text='Employee name for reimbursements', max_length=200)),
                ('bank_reference', models.CharField(blank=True, help_text='Bank transaction reference or confirmation number', max_length=100)),
                ('cleared_date', models.DateField(blank=True, help_text='Date when transaction cleared the bank', null=True)),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('POSTED', 'Posted'), ('CANCELLED', 'Cancelled')], default='DRAFT', help_text='Current status of the transaction', max_length=20)),
                ('requires_approval', models.BooleanField(default=False, help_text='Whether this transaction needs approval')),
                ('approved_at', models.DateTimeField(blank=True, help_text='Timestamp when transaction was approved', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('posted_at', models.DateTimeField(blank=True, help_text='Timestamp when transaction was posted', null=True)),
                ('approved_by', models.ForeignKey(blank=True, help_text='User who approved this transaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_cash_transactions', to=settings.AUTH_USER_MODEL)),
                ('cash_account', models.ForeignKey(help_text='Cash account for this transaction', on_delete=django.db.models.deletion.PROTECT, related_name='cash_transactions', to='cms.cashaccount')),
                ('created_by', models.ForeignKey(help_text='User who created this transaction', on_delete=django.db.models.deletion.PROTECT, related_name='created_cash_transactions', to=settings.AUTH_USER_MODEL)),
                ('gl_journal_entry', models.ForeignKey(blank=True, help_text='Related journal entry in General Ledger', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cash_transactions', to='gl.journalentry')),
                ('posted_by', models.ForeignKey(blank=True, help_text='User who posted this transaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_cash_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Cash Transaction',
                'verbose_name_plural': 'Cash Transactions',
                'ordering': ['-transaction_date', '-transaction_number'],
            },
        ),
        migrations.CreateModel(
            name='CashFlowForecast',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('forecast_name', models.CharField(help_text='Name or description of the forecast', max_length=200)),
                ('forecast_type', models.CharField(choices=[('BUDGET', 'Annual Budget'), ('FORECAST', 'Rolling Forecast'), ('SCENARIO', 'Scenario Planning')], help_text='Type of cash flow forecast', max_length=20)),
                ('period_from', models.DateField(help_text='Forecast period start date')),
                ('period_to', models.DateField(help_text='Forecast period end date')),
                ('projected_receipts', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Projected cash receipts for the period', max_digits=15)),
                ('projected_payments', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Projected cash payments for the period', max_digits=15)),
                ('net_cash_flow', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Net projected cash flow (receipts - payments)', max_digits=15)),
                ('confidence_level', models.DecimalField(decimal_places=0, default=80, help_text='Confidence level in percentage (0-100)', max_digits=3, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('assumptions', models.TextField(blank=True, help_text='Key assumptions underlying this forecast')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this forecast is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cash_account', models.ForeignKey(help_text='Cash account this forecast applies to', on_delete=django.db.models.deletion.CASCADE, related_name='cash_forecasts', to='cms.cashaccount')),
                ('created_by', models.ForeignKey(help_text='User who created this forecast', on_delete=django.db.models.deletion.PROTECT, related_name='created_forecasts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Cash Flow Forecast',
                'verbose_name_plural': 'Cash Flow Forecasts',
                'ordering': ['-period_from'],
            },
        ),
        migrations.AddField(
            model_name='cashaccount',
            name='account_type',
            field=models.ForeignKey(help_text='Type of cash account (checking, savings, petty cash, etc.)', on_delete=django.db.models.deletion.PROTECT, related_name='cash_accounts', to='cms.cashaccounttype'),
        ),
        migrations.AddField(
            model_name='cashaccount',
            name='created_by',
            field=models.ForeignKey(help_text='User who created this account', on_delete=django.db.models.deletion.PROTECT, related_name='created_cash_accounts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='cashaccount',
            name='gl_account',
            field=models.ForeignKey(blank=True, help_text='Linked General Ledger account for automatic posting', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='cash_accounts', to='gl.account'),
        ),
        migrations.CreateModel(
            name='BankStatementEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_date', models.DateField(help_text='Date of the bank transaction')),
                ('entry_type', models.CharField(choices=[('DEBIT', 'Debit (Money Out)'), ('CREDIT', 'Credit (Money In)')], help_text='Type of bank entry (debit or credit)', max_length=10)),
                ('amount', models.DecimalField(decimal_places=2, help_text='Transaction amount (always positive)', max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))])),
                ('description', models.CharField(help_text='Bank transaction description', max_length=500)),
                ('reference_number', models.CharField(blank=True, help_text='Bank reference or check number', max_length=100)),
                ('bank_code', models.CharField(blank=True, help_text='Bank transaction code', max_length=20)),
                ('is_matched', models.BooleanField(default=False, help_text='Whether this entry has been matched to a cash transaction')),
                ('matched_at', models.DateTimeField(blank=True, help_text='Timestamp when matching was performed', null=True)),
                ('running_balance', models.DecimalField(blank=True, decimal_places=2, help_text='Running balance after this transaction', max_digits=15, null=True)),
                ('bank_statement', models.ForeignKey(help_text='Bank statement this entry belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='entries', to='cms.bankstatement')),
                ('matched_by', models.ForeignKey(blank=True, help_text='User who performed the matching', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='matched_bank_entries', to=settings.AUTH_USER_MODEL)),
                ('matched_transaction', models.ForeignKey(blank=True, help_text='Matched cash transaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bank_entries', to='cms.cashtransaction')),
            ],
            options={
                'verbose_name': 'Bank Statement Entry',
                'verbose_name_plural': 'Bank Statement Entries',
                'ordering': ['bank_statement', 'entry_date', 'id'],
            },
        ),
        migrations.AddField(
            model_name='bankstatement',
            name='cash_account',
            field=models.ForeignKey(help_text='Cash account this statement belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='bank_statements', to='cms.cashaccount'),
        ),
        migrations.AddField(
            model_name='bankstatement',
            name='imported_by',
            field=models.ForeignKey(help_text='User who imported this statement', on_delete=django.db.models.deletion.PROTECT, related_name='imported_statements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='bankstatement',
            name='reconciled_by',
            field=models.ForeignKey(blank=True, help_text='User who performed the reconciliation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reconciled_statements', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='cashtransaction',
            index=models.Index(fields=['cash_account', 'transaction_date'], name='cms_cashtra_cash_ac_408623_idx'),
        ),
        migrations.AddIndex(
            model_name='cashtransaction',
            index=models.Index(fields=['status', 'transaction_date'], name='cms_cashtra_status_420ddf_idx'),
        ),
        migrations.AddIndex(
            model_name='cashtransaction',
            index=models.Index(fields=['customer'], name='cms_cashtra_custome_270051_idx'),
        ),
        migrations.AddIndex(
            model_name='cashtransaction',
            index=models.Index(fields=['vendor'], name='cms_cashtra_vendor_e54319_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='bankstatement',
            unique_together={('cash_account', 'statement_number')},
        ),
    ]
