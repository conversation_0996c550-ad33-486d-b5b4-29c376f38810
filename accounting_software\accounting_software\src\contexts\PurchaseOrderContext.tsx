import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { PurchaseOrder, PurchaseOrderFormData } from '../shared/types/purchase.types';

interface PurchaseOrderContextType {
  purchaseOrders: PurchaseOrder[];
  addPurchaseOrder: (poData: PurchaseOrderFormData) => PurchaseOrder;
  updatePurchaseOrder: (po: PurchaseOrder) => void;
  deletePurchaseOrder: (poId: string) => void;
  getPurchaseOrderById: (poId: string) => PurchaseOrder | undefined;
  isLoading: boolean;
  error: string | null;
}

const PurchaseOrderContext = createContext<PurchaseOrderContextType | undefined>(undefined);

// Mock data for purchase orders
const mockPurchaseOrders: PurchaseOrder[] = [
  {
    id: 'PO-001',
    poNumber: 'PO-001',
    vendorId: 'V001',
    vendorName: 'Office Supplies Co.',
    date: '2024-03-20',
    expectedDate: '2024-04-05',
    status: 'pending',
    currency: 'USD',
    subtotal: 2380.95,
    taxTotal: 119.05,
    total: 2500.00,
    notes: 'Office supplies for Q2',
    lineItems: [
      {
        id: 'LI001',
        productId: 'P001',
        description: 'Paper (A4, 500 sheets)',
        quantity: 20,
        unitPrice: 4.99,
        taxRate: 5,
        taxAmount: 5.00,
        subtotal: 99.80,
        total: 104.80,
      },
      {
        id: 'LI002',
        productId: 'P002',
        description: 'Ink Cartridges (Black)',
        quantity: 10,
        unitPrice: 24.99,
        taxRate: 5,
        taxAmount: 12.50,
        subtotal: 249.90,
        total: 262.40,
      },
      {
        id: 'LI003',
        productId: 'P016',
        description: 'Office Furniture Set',
        quantity: 2,
        unitPrice: 999.99,
        taxRate: 5,
        taxAmount: 100.00,
        subtotal: 1999.98,
        total: 2099.98,
      },
    ],
    createdAt: '2024-03-20T09:30:00Z',
    updatedAt: '2024-03-20T09:30:00Z',
  },
  {
    id: 'PO-002',
    poNumber: 'PO-002',
    vendorId: 'V002',
    vendorName: 'Tech Hardware Inc.',
    date: '2024-03-18',
    expectedDate: '2024-04-10',
    status: 'approved',
    currency: 'USD',
    subtotal: 5476.19,
    taxTotal: 274.31,
    total: 5750.50,
    notes: 'IT equipment for new hires',
    lineItems: [
      {
        id: 'LI004',
        productId: 'P003',
        description: 'Laptop (Business Model)',
        quantity: 3,
        unitPrice: 1200.00,
        taxRate: 5,
        taxAmount: 180.00,
        subtotal: 3600.00,
        total: 3780.00,
      },
      {
        id: 'LI005',
        productId: 'P004',
        description: 'External Monitor (24")',
        quantity: 5,
        unitPrice: 295.00,
        taxRate: 5,
        taxAmount: 73.75,
        subtotal: 1475.00,
        total: 1548.75,
      },
      {
        id: 'LI006',
        productId: 'P017',
        description: 'Wireless Keyboard and Mouse Set',
        quantity: 3,
        unitPrice: 89.99,
        taxRate: 5,
        taxAmount: 13.50,
        subtotal: 269.97,
        total: 283.47,
      },
    ],
    createdAt: '2024-03-18T14:15:00Z',
    updatedAt: '2024-03-18T14:15:00Z',
    approvedBy: 'John Manager',
    approvedDate: '2024-03-19T10:30:00Z',
  },
  {
    id: 'PO-003',
    poNumber: 'PO-003',
    vendorId: 'V003',
    vendorName: 'Furniture Depot',
    date: '2024-03-15',
    expectedDate: '2024-04-15',
    status: 'received',
    currency: 'USD',
    subtotal: 7809.52,
    taxTotal: 390.48,
    total: 8200.00,
    notes: 'Office furniture for new office space',
    lineItems: [
      {
        id: 'LI007',
        productId: 'P005',
        description: 'Office Desk (Standard)',
        quantity: 8,
        unitPrice: 350.00,
        taxRate: 5,
        taxAmount: 140.00,
        subtotal: 2800.00,
        total: 2940.00,
      },
      {
        id: 'LI008',
        productId: 'P006',
        description: 'Ergonomic Chair',
        quantity: 8,
        unitPrice: 450.00,
        taxRate: 5,
        taxAmount: 180.00,
        subtotal: 3600.00,
        total: 3780.00,
      },
      {
        id: 'LI009',
        productId: 'P007',
        description: 'Filing Cabinet',
        quantity: 4,
        unitPrice: 275.00,
        taxRate: 5,
        taxAmount: 55.00,
        subtotal: 1100.00,
        total: 1155.00,
      },
    ],
    createdAt: '2024-03-15T11:45:00Z',
    updatedAt: '2024-03-15T11:45:00Z',
    approvedBy: 'Jane Director',
    approvedDate: '2024-03-16T09:20:00Z',
    receivedDate: '2024-03-30T14:30:00Z',
  },
  {
    id: 'PO-004',
    poNumber: 'PO-004',
    vendorId: 'V004',
    vendorName: 'Cleaning Services LLC',
    date: '2024-03-12',
    expectedDate: '2024-03-25',
    status: 'cancelled',
    currency: 'USD',
    subtotal: 1191.19,
    taxTotal: 59.56,
    total: 1250.75,
    notes: 'Deep cleaning services - cancelled due to rescheduling',
    lineItems: [
      {
        id: 'LI010',
        productId: 'P018',
        description: 'Deep Cleaning Service',
        quantity: 1,
        unitPrice: 999.99,
        taxRate: 5,
        taxAmount: 50.00,
        subtotal: 999.99,
        total: 1049.99,
      },
      {
        id: 'LI011',
        productId: 'P019',
        description: 'Carpet Cleaning',
        quantity: 1,
        unitPrice: 199.99,
        taxRate: 5,
        taxAmount: 10.00,
        subtotal: 199.99,
        total: 209.99,
      },
    ],
    createdAt: '2024-03-12T10:30:00Z',
    updatedAt: '2024-03-12T10:30:00Z',
    approvedBy: 'John Manager',
    approvedDate: '2024-03-13T09:15:00Z',
  },
  {
    id: 'PO-005',
    poNumber: 'PO-005',
    vendorId: 'V005',
    vendorName: 'Marketing Agency',
    date: '2024-03-10',
    expectedDate: '2024-04-01',
    status: 'pending',
    currency: 'USD',
    subtotal: 3333.33,
    taxTotal: 166.67,
    total: 3500.00,
    notes: 'Marketing materials for trade show',
    lineItems: [
      {
        id: 'LI012',
        productId: 'P020',
        description: 'Brochure Design and Printing (500 copies)',
        quantity: 1,
        unitPrice: 1500.00,
        taxRate: 5,
        taxAmount: 75.00,
        subtotal: 1500.00,
        total: 1575.00,
      },
      {
        id: 'LI013',
        productId: 'P021',
        description: 'Banner Design and Printing',
        quantity: 3,
        unitPrice: 350.00,
        taxRate: 5,
        taxAmount: 52.50,
        subtotal: 1050.00,
        total: 1102.50,
      },
      {
        id: 'LI014',
        productId: 'P022',
        description: 'Promotional Items (Pens, USB drives)',
        quantity: 1,
        unitPrice: 800.00,
        taxRate: 5,
        taxAmount: 40.00,
        subtotal: 800.00,
        total: 840.00,
      },
    ],
    createdAt: '2024-03-10T13:45:00Z',
    updatedAt: '2024-03-10T13:45:00Z',
  },
];

export const PurchaseOrderProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>(mockPurchaseOrders);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // In a real app, you would fetch purchase orders from an API
  useEffect(() => {
    // Simulating API call
    setIsLoading(true);
    try {
      // In a real app, this would be an API call
      // For now, we're using mock data
      setPurchaseOrders(mockPurchaseOrders);
      setIsLoading(false);
    } catch (err) {
      setError('Failed to load purchase orders');
      setIsLoading(false);
    }
  }, []);

  const addPurchaseOrder = (poData: PurchaseOrderFormData): PurchaseOrder => {
    const now = new Date().toISOString();
    const newPO: PurchaseOrder = {
      ...poData,
      id: `PO-${uuidv4().substring(0, 8)}`,
      createdAt: now,
      updatedAt: now,
    };

    setPurchaseOrders((prevPOs) => [...prevPOs, newPO]);
    return newPO;
  };

  const updatePurchaseOrder = (updatedPO: PurchaseOrder) => {
    setPurchaseOrders((prevPOs) =>
      prevPOs.map((po) =>
        po.id === updatedPO.id
          ? { ...updatedPO, updatedAt: new Date().toISOString() }
          : po
      )
    );
  };

  const deletePurchaseOrder = (poId: string) => {
    setPurchaseOrders((prevPOs) => prevPOs.filter((po) => po.id !== poId));
  };

  const getPurchaseOrderById = (poId: string) => {
    return purchaseOrders.find((po) => po.id === poId);
  };

  const value = {
    purchaseOrders,
    addPurchaseOrder,
    updatePurchaseOrder,
    deletePurchaseOrder,
    getPurchaseOrderById,
    isLoading,
    error,
  };

  return <PurchaseOrderContext.Provider value={value}>{children}</PurchaseOrderContext.Provider>;
};

export const usePurchaseOrders = (): PurchaseOrderContextType => {
  const context = useContext(PurchaseOrderContext);
  if (context === undefined) {
    throw new Error('usePurchaseOrders must be used within a PurchaseOrderProvider');
  }
  return context;
}; 