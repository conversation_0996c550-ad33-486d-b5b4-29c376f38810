"""
Django management command to remove the "For the Period Retained Earnings" account
since we're implementing QuickBooks-style Balance Sheet presentation.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from gl.models import Account


class Command(BaseCommand):
    help = 'Remove the "For the Period Retained Earnings" account (QuickBooks-style implementation)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm the removal of the account',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will remove the "For the Period Retained Earnings" account.\n'
                    'Add --confirm to proceed.'
                )
            )
            return

        try:
            with transaction.atomic():
                # Find the account
                account = Account.objects.filter(account_number='36200').first()
                
                if not account:
                    self.stdout.write(
                        self.style.WARNING('Account 36200 "For the Period Retained Earnings" not found.')
                    )
                    return

                # Check if account has any transactions
                journal_lines_count = account.journal_lines.count()
                
                if journal_lines_count > 0:
                    self.stdout.write(
                        self.style.ERROR(
                            f'Cannot delete account {account.account_number} - {account.account_name}. '
                            f'It has {journal_lines_count} journal line(s) associated with it.\n'
                            'Please move or delete the transactions first.'
                        )
                    )
                    return

                # Safe to delete
                account_info = f'{account.account_number} - {account.account_name}'
                account.delete()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ Successfully removed account: {account_info}\n'
                        'The Balance Sheet will now use QuickBooks-style "Profit for the year" presentation.'
                    )
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error removing account: {str(e)}')
            ) 