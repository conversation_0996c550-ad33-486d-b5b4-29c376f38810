"""
Audit Trail Middleware for Automatic User Action Logging

This middleware automatically captures:
- User actions and requests
- IP addresses and user agents
- Session information
- Request timing and performance
- Security events

Required for SOX compliance and security auditing.
"""

import time
import json
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from django.utils import timezone
from .models import AuditLog, UserSession

class AuditTrailMiddleware(MiddlewareMixin):
    """
    Middleware to capture all user requests for audit trail.
    
    This middleware logs every request to provide complete audit coverage:
    - All API calls and page views
    - User identification and session tracking
    - IP address and browser information
    - Request timing and response codes
    - Financial data access tracking
    """
    
    def process_request(self, request):
        """Capture request start time and user context."""
        request.audit_start_time = time.time()
        request.audit_ip = AuditTrailMiddleware.get_client_ip(request)
        request.audit_user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Safely get session key - avoid touching session during admin login
        request.audit_session_key = None
        if not '/admin/login/' in request.path_info:
            try:
                # Only access session if it already exists, don't create it
                if hasattr(request, 'session') and hasattr(request.session, 'session_key'):
                    request.audit_session_key = request.session.session_key
            except (AttributeError, Exception):
                # Ignore any session access errors during request processing
                pass
    
    def process_response(self, request, response):
        """Log the completed request and update user session."""
        # Update user session tracking after authentication is complete
        self.update_user_session(request)
        
        # Only log significant requests (not static files, health checks, etc.)
        if self.should_audit_request(request):
            self.log_request(request, response)
        
        return response
    
    def update_user_session(self, request):
        """Update user session tracking - called after authentication."""
        try:
            # Skip session tracking during admin login process to prevent conflicts
            if '/admin/login/' in request.path_info:
                return
                
            # Only proceed if user is authenticated and session exists
            if (hasattr(request, 'user') and 
                request.user.is_authenticated and 
                hasattr(request, 'session')):
                
                # Get session key safely
                session_key = None
                try:
                    session_key = request.session.session_key
                except (AttributeError, Exception):
                    # If we can't get session key, skip session tracking
                    return
                
                if session_key:
                    user_session, created = UserSession.objects.get_or_create(
                        user=request.user,
                        session_key=session_key,
                        defaults={
                            'is_active': True,
                            'last_activity': timezone.now(),
                            'ip_address': self.get_client_ip(request),
                            'user_agent': request.META.get('HTTP_USER_AGENT', '')[:500]
                        }
                    )
                    
                    if not created:
                        user_session.last_activity = timezone.now()
                        user_session.is_active = True
                        user_session.save(update_fields=['last_activity', 'is_active'])
                    
        except Exception as e:
            # Don't let session tracking break the application
            import logging
            logger = logging.getLogger('audit')
            logger.warning(f"Failed to update user session: {str(e)}")
    
    def should_audit_request(self, request):
        """Determine if this request should be audited."""
        path = request.path_info.lower()
        
        # Skip static files and health checks
        skip_paths = [
            '/static/', '/media/', '/favicon.ico', '/health/',
            '/admin/jsi18n/', '/__debug__/'
        ]
        
        for skip_path in skip_paths:
            if path.startswith(skip_path):
                return False
        
        # Always audit API calls and admin actions
        audit_paths = ['/api/', '/admin/']
        for audit_path in audit_paths:
            if path.startswith(audit_path):
                return True
        
        # Audit authenticated user actions
        if hasattr(request, 'user') and request.user.is_authenticated:
            return True
        
        return False
    
    def log_request(self, request, response):
        """Create audit log entry for the request."""
        try:
            # Determine action type based on HTTP method and path
            action = self.determine_action(request, response)
            
            # Determine module based on URL path
            module = self.determine_module(request)
            
            # Determine risk level
            risk_level = self.determine_risk_level(request, action)
            
            # Check if this affects financial data
            is_financial = self.is_financial_action(request)
            
            # Create description
            description = self.create_description(request, response, action)
            
            # Safely get user for audit log
            audit_user = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                audit_user = request.user
            
            # Safely get session key
            session_key = getattr(request, 'audit_session_key', None)
            if not session_key and hasattr(request, 'session'):
                session_key = getattr(request.session, 'session_key', None)
            
            # Create audit log entry
            audit_log = AuditLog.objects.create(
                user=audit_user,
                action=action,
                description=description,
                ip_address=request.audit_ip,
                user_agent=request.audit_user_agent,
                session_key=session_key,
                module=module,
                risk_level=risk_level,
                is_financial=is_financial,
            )
            
            # Set metadata using the helper method
            audit_log.set_metadata({
                'path': request.path_info,
                'method': request.method,
                'status_code': response.status_code,
                'response_time': getattr(request, 'audit_start_time', None) and 
                               time.time() - request.audit_start_time,
                'content_length': len(response.content) if hasattr(response, 'content') else 0,
                'query_params': dict(request.GET) if request.GET else None,
            })
            audit_log.save()
            
        except Exception as e:
            # Don't let audit logging break the application
            import logging
            logger = logging.getLogger('audit')
            logger.error(f"Failed to create audit log: {str(e)}")
    
    def determine_action(self, request, response):
        """Determine the type of action based on request."""
        method = request.method.upper()
        path = request.path_info.lower()
        status = response.status_code
        
        if method == 'POST':
            if 'login' in path:
                return 'LOGIN' if status == 200 else 'LOGIN_FAILED'
            elif 'logout' in path:
                return 'LOGOUT'
            else:
                return 'CREATE'
        elif method == 'PUT' or method == 'PATCH':
            return 'UPDATE'
        elif method == 'DELETE':
            return 'DELETE'
        elif method == 'GET':
            if 'report' in path or 'export' in path:
                return 'GENERATE_REPORT'
            else:
                return 'VIEW'
        else:
            return 'DATA_ACCESS'
    
    def determine_module(self, request):
        """Determine which module this request belongs to."""
        path = request.path_info.lower()
        
        if '/api/gl/' in path or '/gl/' in path:
            return 'GENERAL_LEDGER'
        elif '/api/ar/' in path or '/ar/' in path:
            return 'ACCOUNTS_RECEIVABLE'
        elif '/api/ap/' in path or '/ap/' in path:
            return 'ACCOUNTS_PAYABLE'
        elif '/api/sales/' in path or '/sales/' in path:
            return 'SALES'
        elif '/api/purchases/' in path or '/purchases/' in path:
            return 'PURCHASES'
        elif '/api/inventory/' in path or '/inventory/' in path:
            return 'INVENTORY'
        elif '/api/hr/' in path or '/hr/' in path:
            return 'HUMAN_RESOURCES'
        elif '/admin/' in path:
            return 'ADMINISTRATION'
        elif '/api/account/' in path or '/users/' in path:
            return 'USER_MANAGEMENT'
        else:
            return 'SYSTEM'
    
    def determine_risk_level(self, request, action):
        """Determine risk level of the action."""
        path = request.path_info.lower()
        
        # Critical actions
        if action in ['POST_JOURNAL', 'REVERSE_JOURNAL', 'DELETE', 'PERMISSION_CHANGE']:
            return 'CRITICAL'
        
        # High risk paths
        high_risk_paths = ['/admin/', '/api/gl/', '/api/users/']
        for risk_path in high_risk_paths:
            if risk_path in path:
                return 'HIGH'
        
        # Medium risk for financial modules
        if any(module in path for module in ['/api/sales/', '/api/purchases/', '/api/ar/', '/api/ap/']):
            return 'MEDIUM'
        
        return 'LOW'
    
    def is_financial_action(self, request):
        """Check if this action affects financial data."""
        financial_paths = [
            '/api/gl/', '/api/ar/', '/api/ap/', '/api/sales/', '/api/purchases/',
            '/api/journal', '/api/account', '/api/invoice', '/api/payment'
        ]
        
        path = request.path_info.lower()
        return any(fin_path in path for fin_path in financial_paths)
    
    def create_description(self, request, response, action):
        """Create human-readable description of the action."""
        # Safely get user
        user = None
        if hasattr(request, 'user') and request.user.is_authenticated:
            user = request.user
            
        path = request.path_info
        method = request.method
        
        user_str = f"User '{user.username}'" if user else "Anonymous user"
        
        if action == 'LOGIN':
            return f"{user_str} logged into the system"
        elif action == 'LOGOUT':
            return f"{user_str} logged out of the system"
        elif action == 'LOGIN_FAILED':
            return f"Failed login attempt for user at {path}"
        elif action == 'CREATE':
            return f"{user_str} created new record via {method} {path}"
        elif action == 'UPDATE':
            return f"{user_str} updated record via {method} {path}"
        elif action == 'DELETE':
            return f"{user_str} deleted record via {method} {path}"
        elif action == 'VIEW':
            return f"{user_str} accessed {path}"
        elif action == 'GENERATE_REPORT':
            return f"{user_str} generated report from {path}"
        else:
            return f"{user_str} performed {action} on {path}"
    
    @staticmethod
    def get_client_ip(request):
        """Get the client's IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

# Signal handlers for authentication events
@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login."""
    try:
        # Get session key safely
        session_key = None
        try:
            session_key = request.session.session_key
        except (AttributeError, Exception):
            # If session key is not available, continue without it
            pass
        
        # Create user session record only if we have a session key
        if session_key:
            UserSession.objects.create(
                user=user,
                session_key=session_key,
                ip_address=AuditTrailMiddleware.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                login_time=timezone.now(),
                is_active=True
            )
        
        # Create audit log
        audit_log = AuditLog.objects.create(
            user=user,
            action='LOGIN',
            description=f"User '{user.username}' successfully logged in",
            ip_address=AuditTrailMiddleware.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            session_key=session_key,
            module='AUTHENTICATION',
            risk_level='MEDIUM',
            is_financial=False,
        )
        
        audit_log.set_metadata({
            'login_method': 'WEB',
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        })
        audit_log.save()
        
    except Exception as e:
        # Don't let audit logging break the login process
        import logging
        logger = logging.getLogger('audit')
        logger.error(f"Failed to log user login: {str(e)}")

@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout."""
    try:
        # Get session key safely
        session_key = None
        if request:
            try:
                session_key = request.session.session_key
            except (AttributeError, Exception):
                pass
        
        # Update user session if we have a session key
        if session_key:
            try:
                user_session = UserSession.objects.get(
                    user=user,
                    session_key=session_key,
                    is_active=True
                )
                user_session.logout_time = timezone.now()
                user_session.logout_reason = 'USER'
                user_session.is_active = False
                user_session.save()
            except UserSession.DoesNotExist:
                pass
        
        # Create audit log
        AuditLog.objects.create(
            user=user,
            action='LOGOUT',
            description=f"User '{user.username}' logged out",
            ip_address=AuditTrailMiddleware.get_client_ip(request) if request else None,
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
            session_key=session_key,
            module='AUTHENTICATION',
            risk_level='LOW',
            is_financial=False
        )
        
    except Exception as e:
        # Don't let audit logging break the logout process
        import logging
        logger = logging.getLogger('audit')
        logger.error(f"Failed to log user logout: {str(e)}")

@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts."""
    try:
        username = credentials.get('username', 'Unknown')
        
        # Get session key safely
        session_key = None
        try:
            session_key = request.session.session_key
        except (AttributeError, Exception):
            pass
        
        audit_log = AuditLog.objects.create(
            user=None,  # Failed login, so no user object
            action='LOGIN_FAILED',
            description=f"Failed login attempt for username '{username}'",
            ip_address=AuditTrailMiddleware.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            session_key=session_key,
            module='AUTHENTICATION',
            risk_level='HIGH',  # Failed logins are high risk
            is_financial=False,
        )
        
        audit_log.set_metadata({
            'attempted_username': username,
            'failure_reason': 'INVALID_CREDENTIALS',
        })
        audit_log.save()
        
    except Exception as e:
        # Don't let audit logging break the authentication process
        import logging
        logger = logging.getLogger('audit')
        logger.error(f"Failed to log failed login: {str(e)}") 