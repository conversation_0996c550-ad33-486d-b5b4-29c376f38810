#!/usr/bin/env python3
"""
Simple test script to verify the employee API is working
"""
import requests
import json

# Test the employee API
def test_employee_api():
    base_url = "http://localhost:8000/api"
    
    # Login to get token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # Get authentication token
        login_response = requests.post(f"{base_url}/api-token-auth/", json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            headers = {'Authorization': f'Token {token}'}
            print("✅ Authentication successful")
        else:
            print("❌ Authentication failed")
            return
        
        # Test employees endpoint
        employees_response = requests.get(f"{base_url}/hr/employees/", headers=headers)
        print(f"\n📋 Employees API Status: {employees_response.status_code}")
        
        if employees_response.status_code == 200:
            employees_data = employees_response.json()
            print(f"✅ Employees API working - Found {len(employees_data.get('results', []))} employees")
            
            # Print first employee if exists
            if employees_data.get('results'):
                first_employee = employees_data['results'][0]
                print(f"📝 Sample employee: {json.dumps(first_employee, indent=2)}")
        else:
            print(f"❌ Employees API error: {employees_response.text}")
        
        # Test contacts employees endpoint
        contacts_employees_response = requests.get(f"{base_url}/contacts/employees/", headers=headers)
        print(f"\n👥 Contacts Employees API Status: {contacts_employees_response.status_code}")
        
        if contacts_employees_response.status_code == 200:
            contacts_employees_data = contacts_employees_response.json()
            print(f"✅ Contacts Employees API working - Found {len(contacts_employees_data.get('results', []))} contact employees")
        else:
            print(f"❌ Contacts Employees API error: {contacts_employees_response.text}")
            
        # Test departments endpoint
        departments_response = requests.get(f"{base_url}/hr/departments/", headers=headers)
        print(f"\n🏢 Departments API Status: {departments_response.status_code}")
        
        if departments_response.status_code == 200:
            departments_data = departments_response.json()
            print(f"✅ Departments API working - Found {len(departments_data.get('results', []))} departments")
        else:
            print(f"❌ Departments API error: {departments_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure Django server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Employee API...")
    test_employee_api()
    print("\n✨ Test completed!") 