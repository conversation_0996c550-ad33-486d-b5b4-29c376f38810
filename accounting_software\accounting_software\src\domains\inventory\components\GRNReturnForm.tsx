import React, { useState, useEffect } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import { QuantityInput, hasValidQuantity } from '../../../shared/components';
import {
  Undo as UndoIcon,
  Inventory as InventoryIcon,
  Delete as DeleteIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { grnReturnService, GRNReturnFormData, GRNReturnItem, ReturnableGRN, ReturnableGRNDebugResponse } from '../services/grn-return.service';
import { inventoryService, Warehouse } from '../services/inventory.service';
import { useAuth } from '../../../contexts/AuthContext';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import dayjs from 'dayjs';

interface GRNReturnFormProps {
  onClose: () => void;
  onSave: () => void;
  initialValues?: any;
}

const validationSchema = Yup.object({
  original_grn: Yup.number().required('Original GRN is required'),
  warehouse: Yup.number().required('Warehouse is required'),
  return_date: Yup.string().required('Return Date is required'),
  return_reason: Yup.string().required('Return Reason is required'),
  items: Yup.array().of(
    Yup.object({
      quantity_returned: Yup.number()
        .min(0, 'Quantity cannot be negative')
        .required('Return quantity is required'),
      return_reason: Yup.string().required('Return reason is required')
    })
  ).min(1, 'At least one item must be present')
  .test('at-least-one-item', 'At least one item must have a return quantity greater than 0', function(items) {
    if (!items || items.length === 0) return false;
    return items.some(item => {
      const qty = String(item.quantity_returned || '').trim();
      return qty !== '' && parseFloat(qty) > 0;
    });
  }),
});

const GRNReturnForm: React.FC<GRNReturnFormProps> = ({ onClose, onSave, initialValues }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [returnableGRNs, setReturnableGRNs] = useState<ReturnableGRN[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedGRN, setSelectedGRN] = useState<ReturnableGRN | null>(null);
  const [loadingGRNs, setLoadingGRNs] = useState(false);
  const [loadingWarehouses, setLoadingWarehouses] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const { user } = useAuth();
  const { currencyInfo } = useCurrencyInfo();

  const isEditMode = Boolean(initialValues?.grn_return_id);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔍 Loading GRN Return form data...');
        setLoadingGRNs(true);
        setLoadingWarehouses(true);
        
        const [grnsResponse, warehousesData] = await Promise.all([
          grnReturnService.getReturnableGRNs(),
          inventoryService.getAllWarehouses(),
        ]);
        
        // Check if grnsResponse is the new debug format
        let grnsData: ReturnableGRN[] = [];
        let debugData: ReturnableGRNDebugResponse['debug_info'] | null = null;
        
        if (Array.isArray(grnsResponse)) {
          // Old format - just an array of GRNs
          grnsData = grnsResponse;
        } else if (grnsResponse && typeof grnsResponse === 'object' && 'returnable_grns' in grnsResponse) {
          // New debug format
          grnsData = grnsResponse.returnable_grns || [];
          debugData = grnsResponse.debug_info || null;
          console.log('📊 Debug Info:', debugData);
        }
        
        console.log('📦 Returnable GRNs loaded:', grnsData);
        console.log('🏢 Warehouses loaded:', warehousesData);
        
        setReturnableGRNs(Array.isArray(grnsData) ? grnsData : []);
        setWarehouses(warehousesData);
        setDebugInfo(debugData);

        // Debug information
        if (!Array.isArray(grnsData) || grnsData.length === 0) {
          console.warn('⚠️ No returnable GRNs found. Cannot create return without returnable items.');
          if (debugData) {
            console.log('🔧 Debug details:', {
              totalGRNs: debugData.total_grns_in_db,
              statusBreakdown: debugData.status_breakdown,
              grnsWithIssues: debugData.grns_with_issues?.length || 0
            });
          }
        }
        if (warehousesData.length === 0) {
          console.warn('⚠️ No warehouses found. Cannot create return without warehouse.');
        }
        
      } catch (err) {
        console.error('❌ Failed to load GRN Return form data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoadingGRNs(false);
        setLoadingWarehouses(false);
      }
    };

    loadData();
  }, []);

  const formik = useFormik<GRNReturnFormData>({
    initialValues: {
      original_grn: initialValues?.original_grn || '',
      warehouse: initialValues?.warehouse || '',
      return_date: initialValues?.return_date || new Date().toISOString().split('T')[0],
      return_reason: initialValues?.return_reason || '',
      notes: initialValues?.notes || '',
      items: initialValues?.items || [],
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        console.log('📝 Submitting GRN Return form with values:', values);
        setLoading(true);
        setError(null);

        // Additional validation
        const itemsWithQuantity = values.items.filter(item => 
          hasValidQuantity(item.quantity_returned)
        );
        if (itemsWithQuantity.length === 0) {
          throw new Error('Please enter return quantities for at least one item');
        }

                // Clean and validate the data
        const cleanedValues = {
          original_grn: parseInt(String(values.original_grn)),
          warehouse: parseInt(String(values.warehouse)),
          return_date: values.return_date,
          return_reason: values.return_reason,
          notes: values.notes || '',
          items: values.items
            .filter(item => {
              const qty = String(item.quantity_returned || '').trim();
              return qty !== '' && parseFloat(qty) > 0;
            }) // Only include items with quantity > 0 in the submission
            .map(item => ({
            original_grn_item: item.original_grn_item,
            product: item.product,
            quantity_received: parseFloat(String(item.quantity_received)),
            quantity_returned: parseFloat(String(item.quantity_returned)),
            unit_cost: parseFloat(String(item.unit_cost)),
            return_reason: item.return_reason,
            batch_number: item.batch_number || '',
            condition_notes: item.condition_notes || '',
          })),
        };

        console.log('🧹 Cleaned values for submission:', cleanedValues);

        if (isEditMode) {
          await grnReturnService.updateGRNReturn(initialValues.grn_return_id, cleanedValues);
        } else {
          await grnReturnService.createGRNReturn(cleanedValues);
        }

        console.log('✅ GRN Return saved successfully');
        onSave();
        onClose();
      } catch (err) {
        console.error('❌ Failed to save GRN Return:', err);
        setError(err instanceof Error ? err.message : 'Failed to save GRN Return');
      } finally {
        setLoading(false);
      }
    },
  });

  // Removed accordion handler as we're using single form layout

  const handleGRNChange = async (event: any) => {
    const grnId = event.target.value;
    const grn = returnableGRNs.find(g => g.grn_id === parseInt(grnId));
    
    if (grn) {
      setSelectedGRN(grn);
      formik.setFieldValue('original_grn', grnId);
      
      // Auto-populate warehouse from the selected GRN
      if (grn.warehouse_id) {
        formik.setFieldValue('warehouse', grn.warehouse_id);
      }
      
      // Initialize items from returnable items with full available quantities by default
      const items: GRNReturnItem[] = grn.returnable_items.map(item => ({
        original_grn_item: item.grn_item_id,  // Add the missing required field
        product: item.product,
        product_name: item.product_name,
        product_sku: item.product_sku,
        quantity_received: item.quantity,
        quantity_returned: item.available_quantity, // Set to available quantity by default
        unit_cost: parseFloat(item.unit_price),
        return_reason: formik.values.return_reason || 'DEFECTIVE',
        batch_number: item.batch_number || '',
        condition_notes: '',
      }));
      
      formik.setFieldValue('items', items);
      
      // Set default return reason if not already set
      if (!formik.values.return_reason) {
        formik.setFieldValue('return_reason', 'DEFECTIVE');
      }
      
      console.log('📦 GRN selected - all items set to return available quantities by default:', {
        grnNumber: grn.grn_number,
        itemsCount: items.length,
        totalReturnQty: items.reduce((sum, item) => sum + Number(item.quantity_returned), 0)
      });
    }
  };

  const updateItemField = (index: number, field: string, value: any) => {
    const items = [...formik.values.items];
    (items[index] as any)[field] = value;
    formik.setFieldValue('items', items);
  };

  const removeItem = (index: number) => {
    const items = formik.values.items.filter((_, i) => i !== index);
    formik.setFieldValue('items', items);
  };

  const calculateTotalQuantity = () => {
    return formik.values.items.reduce((sum, item) => {
      const qty = String(item.quantity_returned || '').trim();
      return sum + (qty !== '' ? parseFloat(qty) || 0 : 0);
    }, 0);
  };

  const returnReasonOptions = [
    { value: 'DEFECTIVE', label: 'Defective' },
    { value: 'WRONG_ITEM', label: 'Wrong Item' },
    { value: 'EXCESS_QTY', label: 'Excess Quantity' },
    { value: 'QUALITY_ISSUE', label: 'Quality Issue' },
    { value: 'NOT_ORDERED', label: 'Not Ordered' },
    { value: 'EXPIRED', label: 'Expired' },
    { value: 'OTHER', label: 'Other' },
  ];

  const handleGRNSelection = (selectedGRN: ReturnableGRN | null) => {
    if (selectedGRN) {
      // Update form data with GRN details
      formik.setFieldValue('original_grn', selectedGRN.grn_id);
      formik.setFieldValue('warehouse', selectedGRN.warehouse_id || 0);
      formik.setFieldValue('return_date', dayjs().format('YYYY-MM-DD'));
      formik.setFieldValue('return_reason', 'DEFECTIVE');
      formik.setFieldValue('notes', '');
      formik.setFieldValue('items', selectedGRN.returnable_items.map((item, index) => ({
        original_grn_item: item.grn_item_id,  // Use grn_item_id here
        product: item.product,
        product_name: item.product_name,
        quantity_received: item.quantity,
        quantity_returned: 0,
        unit_cost: parseFloat(item.unit_price),
        return_reason: 'DEFECTIVE',
        batch_number: item.batch_number || '',
        line_order: index + 1
      })));
    } else {
      formik.resetForm();
    }
  };

  if (loadingGRNs || loadingWarehouses) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>
          Loading return form data... (GRNs: {loadingGRNs ? 'loading' : 'done'}, Warehouses: {loadingWarehouses ? 'loading' : 'done'})
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Typography variant="body2" color="text.secondary">
          Please refresh the page or contact support if the issue persists.
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <UndoIcon color="primary" />
        {isEditMode ? 'Edit' : 'Create'} Good Return Note
      </DialogTitle>

      <DialogContent sx={{ minWidth: 800, maxHeight: '80vh' }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <form onSubmit={formik.handleSubmit}>
          {/* Debug Information Section - Only show when there are issues */}
          {debugInfo && debugInfo.grns_with_issues && debugInfo.grns_with_issues.length > 0 && (
            <Alert severity="warning" sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                🔧 Debug Information: {debugInfo.grns_with_issues.length} GRNs Found with Issues
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Total GRNs in database: {debugInfo.total_grns_in_db} | 
                Status breakdown: {Object.entries(debugInfo.status_breakdown || {}).map(([status, count]) => `${status}: ${count}`).join(', ')}
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom>
                Issues preventing GRNs from being returnable:
              </Typography>
              {debugInfo.grns_with_issues.slice(0, 5).map((grn: any, index: number) => (
                <Box key={index} sx={{ mb: 1, p: 1, backgroundColor: '#fff3e0', borderRadius: 1 }}>
                  <Typography variant="body2" fontWeight="medium">
                    GRN {grn.grn_number} ({grn.status})
                  </Typography>
                  <Typography variant="caption" color="error">
                    Issues: {grn.issues?.join(', ') || 'Unknown issues'}
                  </Typography>
                  <Typography variant="caption" display="block">
                    PO: {grn.po_number} | Vendor: {grn.vendor_name} | Warehouse: {grn.warehouse_name} | Items: {grn.returnable_items?.length || 0}
                  </Typography>
                </Box>
              ))}
              
              {debugInfo.grns_with_issues.length > 5 && (
                <Typography variant="caption" color="text.secondary">
                  ... and {debugInfo.grns_with_issues.length - 5} more GRNs with issues
                </Typography>
              )}
              
              <Typography variant="body2" sx={{ mt: 2, fontWeight: 'medium' }}>
                💡 To fix: Ensure GRNs have vendors, warehouses, and items assigned. Check the Purchase Orders and GRNs in the system.
              </Typography>
            </Alert>
          )}

          {/* Return Information */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <ReceiptIcon color="primary" />
              Return Information
              {formik.values.original_grn && (
                <Chip 
                  label={`GRN: ${selectedGRN?.grn_number}`} 
                  color="primary" 
                  size="small" 
                />
              )}
            </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Select GRN to Return *</InputLabel>
                    <Select
                      name="original_grn"
                      value={formik.values.original_grn}
                      onChange={handleGRNChange}
                      error={formik.touched.original_grn && Boolean(formik.errors.original_grn)}
                      disabled={isEditMode}
                    >
                      {returnableGRNs.map((grn) => (
                        <MenuItem key={grn.grn_id} value={grn.grn_id}>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {grn.grn_number} - {grn.vendor_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              PO: {grn.po_number} | {grn.total_returnable_qty} items available
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.original_grn && formik.errors.original_grn && (
                      <Typography variant="caption" color="error">
                        {formik.errors.original_grn}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Warehouse *</InputLabel>
                    <Select
                      name="warehouse"
                      value={formik.values.warehouse}
                      onChange={formik.handleChange}
                      error={formik.touched.warehouse && Boolean(formik.errors.warehouse)}
                      disabled={false} // Allow user to change warehouse
                    >
                      {warehouses.map((warehouse) => (
                        <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                          {warehouse.name} - {warehouse.address || 'No address'}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.warehouse && formik.errors.warehouse && (
                      <Typography variant="caption" color="error">
                        {formik.errors.warehouse}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="return_date"
                    label="Return Date *"
                    type="date"
                    value={formik.values.return_date}
                    onChange={formik.handleChange}
                    error={formik.touched.return_date && Boolean(formik.errors.return_date)}
                    helperText={formik.touched.return_date && formik.errors.return_date}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Primary Return Reason *</InputLabel>
                    <Select
                      name="return_reason"
                      value={formik.values.return_reason}
                      onChange={formik.handleChange}
                      error={formik.touched.return_reason && Boolean(formik.errors.return_reason)}
                    >
                      {returnReasonOptions.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {formik.touched.return_reason && formik.errors.return_reason && (
                      <Typography variant="caption" color="error">
                        {formik.errors.return_reason}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    name="notes"
                    label="Return Notes"
                    multiline
                    rows={3}
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    placeholder="Additional notes about this return..."
                  />
                                  </Grid>
                </Grid>
              </Box>

              {/* Return Items */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <InventoryIcon color="primary" />
                  Return Items
                  {formik.values.items.length > 0 && (
                    <Chip 
                      label={`${formik.values.items.length} items`} 
                      color="secondary" 
                      size="small" 
                    />
                  )}
                </Typography>
              {formik.values.items.length === 0 ? (
                                  <Alert severity="info">
                    Please select a GRN above to see returnable items.
                  </Alert>
              ) : (
                <>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      📝 <strong>Instructions:</strong> All available quantities are selected for return by default. 
                      You can adjust the "Return Qty" for each item as needed (set to 0 to skip returning that item), or use the buttons below to quickly modify all quantities.
                    </Typography>
                  </Alert>

                  {/* Quick Action Buttons */}
                  <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button
                      size="small"
                      variant="outlined"
                      color="primary"
                      onClick={() => {
                        const updatedItems = formik.values.items.map(item => {
                          const availableQty = selectedGRN?.returnable_items.find(
                            ri => ri.product === item.product
                          )?.available_quantity || 0;
                          return { ...item, quantity_returned: availableQty };
                        });
                        formik.setFieldValue('items', updatedItems);
                      }}
                    >
                      Select All Available
                    </Button>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        const updatedItems = formik.values.items.map(item => ({
                          ...item,
                          quantity_returned: 0
                        }));
                        formik.setFieldValue('items', updatedItems);
                      }}
                    >
                      Return Nothing
                    </Button>
                  </Box>
                  
                  <TableContainer component={Paper} sx={{ mb: 2 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ width: '30%' }}>Product</TableCell>
                          <TableCell align="right" sx={{ width: '12%' }}>Received</TableCell>
                          <TableCell align="right" sx={{ width: '12%' }}>Available</TableCell>
                          <TableCell align="right" sx={{ width: '12%' }}>Return Qty *</TableCell>
                          <TableCell sx={{ width: '15%' }}>Return Reason</TableCell>
                          <TableCell sx={{ width: '15%' }}>Notes</TableCell>
                          <TableCell align="center" sx={{ width: '4%' }}>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {formik.values.items.map((item, index) => {
                          const availableQty = selectedGRN?.returnable_items.find(
                            ri => ri.product === item.product
                          )?.available_quantity || 0;
                          
                          return (
                            <TableRow key={index}>
                              <TableCell>
                                <Box>
                                  <Typography variant="body2" fontWeight="medium">
                                    {item.product_name}
                                  </Typography>
                                  <Typography variant="caption" color="text.secondary">
                                    SKU: {item.product_sku}
                                  </Typography>
                                  {item.batch_number && (
                                    <Typography variant="caption" color="text.secondary" display="block">
                                      Batch: {item.batch_number}
                                    </Typography>
                                  )}
                                </Box>
                              </TableCell>
                              <TableCell align="right">
                                <Typography variant="body2">
                                  {parseFloat(String(item.quantity_received)).toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                  })}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                <Typography variant="body2" color="primary">
                                  {availableQty.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                  })}
                                </Typography>
                              </TableCell>
                              <TableCell align="right">
                                <QuantityInput
                                  size="small"
                                  value={item.quantity_returned}
                                  onChange={(e) => updateItemField(index, 'quantity_returned', e.target.value)}
                                  min={0}
                                  max={availableQty}
                                  sx={{ width: 80 }}
                                  error={(() => {
                                    const qty = String(item.quantity_returned || '').trim();
                                    const qtyNum = qty !== '' ? parseFloat(qty) : 0;
                                    return qty !== '' && (qtyNum < 0 || qtyNum > availableQty);
                                  })()}
                                  helperText={(() => {
                                    const qty = String(item.quantity_returned || '').trim();
                                    const qtyNum = qty !== '' ? parseFloat(qty) : 0;
                                    if (qty !== '' && qtyNum < 0) {
                                      return 'Cannot be negative';
                                    } else if (qty !== '' && qtyNum > availableQty) {
                                      return `Max: ${availableQty.toLocaleString('en-US', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                      })}`;
                                    } else if (availableQty === 0) {
                                      return 'Not available';
                                    } else {
                                      return `Max: ${availableQty.toLocaleString('en-US', {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                      })} (0 = don't return)`;
                                    }
                                  })()}
                                  placeholder=""
                                />
                              </TableCell>
                              <TableCell>
                                <FormControl size="small" sx={{ minWidth: 100 }}>
                                  <Select
                                    value={item.return_reason}
                                    onChange={(e) => updateItemField(index, 'return_reason', e.target.value)}
                                  >
                                    {returnReasonOptions.map((option) => (
                                      <MenuItem key={option.value} value={option.value}>
                                        {option.label}
                                      </MenuItem>
                                    ))}
                                  </Select>
                                </FormControl>
                              </TableCell>
                              <TableCell>
                                <TextField
                                  size="small"
                                  placeholder="Notes..."
                                  value={item.condition_notes || ''}
                                  onChange={(e) => updateItemField(index, 'condition_notes', e.target.value)}
                                  sx={{ width: 100 }}
                                />
                              </TableCell>
                              <TableCell align="center">
                                <IconButton
                                  size="small"
                                  onClick={() => removeItem(index)}
                                  color="error"
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Summary */}
                  <Card sx={{ bgcolor: 'background.paper' }}>
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">
                            Total Items to Return
                          </Typography>
                          <Typography variant="h6">
                            {formik.values.items.filter(item => {
                              const qty = String(item.quantity_returned || '').trim();
                              return qty !== '' && parseFloat(qty) > 0;
                            }).length}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">
                            Total Quantity
                          </Typography>
                          <Typography variant="h6">
                            {calculateTotalQuantity().toLocaleString('en-US', {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2
                            })}
                          </Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </>
              )}
              </Box>
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        
        {/* Debug Test Button */}
        <Button
          onClick={async () => {
            try {
              console.log('🔧 Testing API connection...');
              const testData = {
                original_grn: 1,
                warehouse: 1,
                return_date: new Date().toISOString().split('T')[0],
                return_reason: 'DEFECTIVE' as const,
                notes: 'Test return',
                items: [{
                  original_grn_item: 1,
                  product: 1,
                  quantity_received: 10,
                  quantity_returned: 1,
                  unit_cost: 100,
                  return_reason: 'DEFECTIVE' as const,
                  batch_number: '',
                  condition_notes: 'Test'
                }]
              };
              console.log('🔧 Test data:', testData);
              await grnReturnService.createGRNReturn(testData);
              alert('✅ Test successful!');
            } catch (err) {
              console.error('❌ Test failed:', err);
              alert(`❌ Test failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
            }
          }}
          color="secondary"
          size="small"
        >
          🔧 Test API
        </Button>
        
        {/* Debug information */}
        {formik.values.items.length > 0 && (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mr: 2 }}>
            <Typography variant="caption" color="text.secondary">
              Items with return qty: {formik.values.items.filter(item => {
                const qty = String(item.quantity_returned || '').trim();
                return qty !== '' && parseFloat(qty) > 0;
              }).length}
            </Typography>
            {formik.errors.items && (
              <Typography variant="caption" color="error">
                {typeof formik.errors.items === 'string' ? formik.errors.items : 'Please check item validation'}
              </Typography>
            )}
            {/* Show validation errors */}
            {Object.keys(formik.errors).length > 0 && (
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="caption" color="error" display="block">
                  ❌ Form has validation errors:
                </Typography>
                {formik.errors.original_grn && (
                  <Typography variant="caption" color="error" display="block">
                    • Select a GRN
                  </Typography>
                )}
                {formik.errors.warehouse && (
                  <Typography variant="caption" color="error" display="block">
                    • Select a warehouse
                  </Typography>
                )}
                {formik.errors.return_date && (
                  <Typography variant="caption" color="error" display="block">
                    • Enter return date
                  </Typography>
                )}
                {formik.errors.return_reason && (
                  <Typography variant="caption" color="error" display="block">
                    • Select primary return reason
                  </Typography>
                )}
                {formik.errors.items && (
                  <Typography variant="caption" color="error" display="block">
                    • Enter return quantities for items
                  </Typography>
                )}
              </Box>
            )}
          </Box>
        )}
        
        <Button 
          onClick={() => {
            console.log('🔍 Form submission attempt:', {
              values: formik.values,
              errors: formik.errors,
              isValid: formik.isValid,
              itemsWithQty: formik.values.items.filter(item => {
                const qty = String(item.quantity_returned || '').trim();
                return qty !== '' && parseFloat(qty) > 0;
              }).length
            });
            
            // Force validation
            formik.validateForm().then((errors) => {
              console.log('🔍 Validation errors:', errors);
              if (Object.keys(errors).length === 0) {
                formik.handleSubmit();
              } else {
                // Show validation errors (form is already in single layout)
                console.log('❌ Form validation failed, errors shown inline');
              }
            });
          }}
          variant="contained" 
          disabled={loading}
        >
          {loading ? <CircularProgress size={20} /> : (isEditMode ? 'Update Return' : 'Create Return')}
        </Button>
      </DialogActions>
    </>
  );
};

export default GRNReturnForm; 