from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from .models import TDS
from .serializers import TDSSerializer
import logging

logger = logging.getLogger(__name__)

# Create your views here.

class TDSViewSet(viewsets.ModelViewSet):
    queryset = TDS.objects.all().order_by('tds_type', 'description')
    serializer_class = TDSSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination for small dataset

    def perform_create(self, serializer):
        logger.info(f"Creating TDS for user: {self.request.user}")
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        logger.info(f"Updating TDS for user: {self.request.user}")
        # Don't change created_by on update
        serializer.save()

    def list(self, request, *args, **kwargs):
        logger.info(f"Listing TDS for user: {request.user}")
        return super().list(request, *args, **kwargs)
