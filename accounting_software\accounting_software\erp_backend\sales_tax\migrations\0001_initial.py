# Generated by Django 4.2.21 on 2025-06-07 12:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SalesTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tax_type', models.CharField(choices=[('input', 'Input Tax'), ('output', 'Output Tax')], max_length=10)),
                ('description', models.CharField(max_length=255)),
                ('rate', models.DecimalField(decimal_places=3, max_digits=6)),
                ('remarks', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Sales Tax',
                'verbose_name_plural': 'Sales Taxes',
                'ordering': ['tax_type', 'description'],
            },
        ),
    ]
