import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
  Grid,
  TextField,
  Autocomplete,
  Box,
  Typography,
  Card,
  CardContent,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  SwapHoriz as SwapIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { InventoryTransfer, InventoryTransferItem, transferService } from '../services/transfer.service';
import { inventoryService, InventoryItem } from '../services/inventory.service';
import QuantityInput from '../../../shared/components/QuantityInput';

interface Warehouse {
  warehouse_id: number;
  name: string;
  warehouse_code: string;
  warehouse_type: string;
}

interface Product {
  id: number;
  name: string;
  sku: string;
}

interface InventoryTransferFormProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  transfer?: InventoryTransfer | null;
}

const InventoryTransferForm: React.FC<InventoryTransferFormProps> = ({
  open,
  onClose,
  onSuccess,
  transfer
}) => {
  const [formData, setFormData] = useState<Partial<InventoryTransfer>>({
    from_warehouse: 0,
    to_warehouse: 0,
    transfer_date: dayjs().format('YYYY-MM-DD'),
    notes: '',
    items: []
  });

  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [availableStock, setAvailableStock] = useState<{[key: string]: number}>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      loadData();
      if (transfer) {
        setFormData(transfer);
      } else {
        resetForm();
      }
    }
  }, [open, transfer]);

  const loadData = async () => {
    try {
      // Load warehouses - using the existing inventory service API
      const warehouseResponse = await inventoryService.getWarehouses();
      setWarehouses(warehouseResponse.results || warehouseResponse || []);

      // Load products from existing inventory service with proper pagination handling
      let allInventoryItems: InventoryItem[] = [];
      let nextUrl = null;
      let page = 1;

      do {
        const inventoryResponse = await inventoryService.getInventory({ 
          page: page,
          page_size: 1000 
        });
        
        const currentItems = inventoryResponse.results || [];
        allInventoryItems = [...allInventoryItems, ...currentItems];
        
        // Check if there's a next page
        nextUrl = inventoryResponse.next;
        page++;
      } while (nextUrl);
      
      // Extract unique products from inventory
      const uniqueProducts = allInventoryItems.reduce((acc: Product[], item: InventoryItem) => {
        if (!acc.find(p => p.id === item.product)) {
          acc.push({
            id: item.product,
            name: item.product_name || '',
            sku: item.product_sku || ''
          });
        }
        return acc;
      }, []);
      
      setProducts(uniqueProducts);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load data');
    }
  };

  const loadAvailableStock = async (warehouseId: number) => {
    if (!warehouseId) return;
    
    try {
      const response = await inventoryService.getInventory({ warehouse: warehouseId });
      const inventory = response.results || response || [];
      
      const stockMap: {[key: string]: number} = {};
      inventory.forEach((item: InventoryItem) => {
        stockMap[`${item.product}-${warehouseId}`] = Number(item.quantity_available) || 0;
      });
      
      setAvailableStock(stockMap);
    } catch (err) {
      console.error('Error loading stock:', err);
    }
  };

  useEffect(() => {
    if (formData.from_warehouse) {
      loadAvailableStock(formData.from_warehouse);
    }
  }, [formData.from_warehouse]);

  const resetForm = () => {
    setFormData({
      from_warehouse: 0,
      to_warehouse: 0,
      transfer_date: dayjs().format('YYYY-MM-DD'),
      notes: '',
      items: []
    });
    setError(null);
  };

  const handleFieldChange = (field: keyof InventoryTransfer, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addTransferItem = () => {
    const newItem: InventoryTransferItem = {
      product: 0,
      quantity: 0,
      batch_number: '',
      notes: '',
      line_order: (formData.items?.length || 0) + 1
    };

    setFormData(prev => ({
      ...prev,
      items: [...(prev.items || []), newItem]
    }));
  };

  const updateTransferItem = (index: number, field: keyof InventoryTransferItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items?.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ) || []
    }));
  };

  const removeTransferItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items?.filter((_, i) => i !== index) || []
    }));
  };

  const swapWarehouses = () => {
    setFormData(prev => ({
      ...prev,
      from_warehouse: prev.to_warehouse,
      to_warehouse: prev.from_warehouse
    }));
  };

  const getAvailableQuantity = (productId: number): number => {
    if (!formData.from_warehouse || !productId) return 0;
    return availableStock[`${productId}-${formData.from_warehouse}`] || 0;
  };

  const validateForm = (): string | null => {
    if (!formData.from_warehouse) return 'Source warehouse is required';
    if (!formData.to_warehouse) return 'Destination warehouse is required';
    if (formData.from_warehouse === formData.to_warehouse) return 'Source and destination warehouses must be different';
    if (!formData.items?.length) return 'At least one item is required';

    for (let i = 0; i < formData.items.length; i++) {
      const item = formData.items[i];
      if (!item.product) return `Product is required for line ${i + 1}`;
      if (!item.quantity || item.quantity <= 0) return `Quantity must be greater than 0 for line ${i + 1}`;
      
      const available = getAvailableQuantity(item.product);
      if (item.quantity > available) {
        const product = products.find(p => p.id === item.product);
        return `Insufficient stock for ${product?.name || 'product'} (Line ${i + 1}). Available: ${available}, Requested: ${item.quantity}`;
      }
    }

    return null;
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      if (transfer?.transfer_id) {
        await transferService.updateTransfer(transfer.transfer_id, formData);
      } else {
        await transferService.createTransfer(formData);
      }

      onSuccess();
      onClose();
    } catch (err: any) {
      setError(err.message || 'Failed to save transfer');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        {transfer ? 'Edit Inventory Transfer' : 'Create Inventory Transfer'}
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Warehouse Selection */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Transfer Details
                </Typography>
                
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>From Warehouse</InputLabel>
                      <Select
                        value={formData.from_warehouse || ''}
                        onChange={(e) => handleFieldChange('from_warehouse', Number(e.target.value))}
                        label="From Warehouse"
                      >
                        {warehouses.map(warehouse => (
                          <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                            {warehouse.name} ({warehouse.warehouse_code})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={1} sx={{ textAlign: 'center' }}>
                    <IconButton onClick={swapWarehouses} color="primary">
                      <SwapIcon />
                    </IconButton>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>To Warehouse</InputLabel>
                      <Select
                        value={formData.to_warehouse || ''}
                        onChange={(e) => handleFieldChange('to_warehouse', Number(e.target.value))}
                        label="To Warehouse"
                      >
                        {warehouses.map(warehouse => (
                          <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                            {warehouse.name} ({warehouse.warehouse_code})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <DatePicker
                      label="Transfer Date"
                      value={dayjs(formData.transfer_date)}
                      onChange={(date: Dayjs | null) =>
                        handleFieldChange('transfer_date', date?.format('YYYY-MM-DD') || '')
                      }
                      slotProps={{ textField: { fullWidth: true } }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Notes"
                      multiline
                      rows={2}
                      value={formData.notes || ''}
                      onChange={(e) => handleFieldChange('notes', e.target.value)}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Transfer Items */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6">
                    Transfer Items
                  </Typography>
                  <Button
                    startIcon={<AddIcon />}
                    onClick={addTransferItem}
                    variant="outlined"
                    disabled={!formData.from_warehouse}
                  >
                    Add Item
                  </Button>
                </Box>

                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Product</TableCell>
                        <TableCell>Available</TableCell>
                        <TableCell>Quantity</TableCell>
                        <TableCell>Batch Number</TableCell>
                        <TableCell>Notes</TableCell>
                        <TableCell width={50}>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {formData.items?.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Autocomplete
                              options={products}
                              getOptionLabel={(option) => `${option.name} (${option.sku})`}
                              value={products.find(p => p.id === item.product) || null}
                              onChange={(_, value) => updateTransferItem(index, 'product', value?.id || 0)}
                              renderInput={(params) => (
                                <TextField {...params} size="small" placeholder="Select product" />
                              )}
                              sx={{ minWidth: 200 }}
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {Number(getAvailableQuantity(item.product) || 0).toFixed(4)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <QuantityInput
                              value={item.quantity}
                              onChange={(e) => updateTransferItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                              placeholder="0.0000"
                              size="small"
                              sx={{ width: 120 }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              size="small"
                              value={item.batch_number || ''}
                              onChange={(e) => updateTransferItem(index, 'batch_number', e.target.value)}
                              placeholder="Batch"
                              sx={{ width: 100 }}
                            />
                          </TableCell>
                          <TableCell>
                            <TextField
                              size="small"
                              value={item.notes || ''}
                              onChange={(e) => updateTransferItem(index, 'notes', e.target.value)}
                              placeholder="Notes"
                              sx={{ width: 150 }}
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => removeTransferItem(index)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                      {!formData.items?.length && (
                        <TableRow>
                          <TableCell colSpan={6} align="center">
                            <Typography color="text.secondary">
                              No items added. Click "Add Item" to start.
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
        >
          {loading ? 'Saving...' : (transfer ? 'Update Transfer' : 'Create Transfer')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InventoryTransferForm; 