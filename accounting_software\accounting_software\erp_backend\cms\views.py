"""
Cash Management System Views

These views handle all Cash Management API endpoints with proper business logic,
permissions, and data validation. Following treasury management best practices
and supporting comprehensive cash flow analysis.

Key Features:
- Cash account management with banking integration
- Cash transaction processing with approval workflows
- Bank reconciliation with automated matching
- Cash flow forecasting and treasury reporting
- Multi-currency support and real-time balance calculations
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, Case, When, DecimalField, F, Avg
from django.utils import timezone
from decimal import Decimal
from datetime import date, datetime, timedelta
from django.db import connection

from .models import (
    CashAccountType, CashAccount, CashTransaction, CashTransfer,
    BankStatement, BankStatementEntry, CashFlowForecast,
    CashAccountBalance, CashTransactionJournal, CashTransferJournal
)
from .serializers import (
    CashAccountTypeSerializer, CashAccountSerializer, CashTransactionSerializer,
    CashTransferSerializer, BankStatementSerializer, BankStatementEntrySerializer,
    CashFlowForecastSerializer
)
from account.models import Company
from gl.models import Account, AccountType, DetailType, JournalEntry, JournalEntryLine


class CashAccountTypeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Cash Account Type management
    
    Cash account types define the behavior and requirements for different
    types of cash accounts (checking, savings, petty cash, etc.).
    
    Only administrators can create/modify account types.
    """
    
    queryset = CashAccountType.objects.all().order_by('sort_order', 'name')
    serializer_class = CashAccountTypeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['requires_bank_details', 'allows_overdraft', 'requires_reconciliation', 'is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['sort_order', 'name', 'created_at']
    ordering = ['sort_order', 'name']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def destroy(self, request, *args, **kwargs):
        """Prevent deletion of account types that have cash accounts."""
        account_type = self.get_object()
        
        if account_type.cash_accounts.exists():
            return Response(
                {
                    "detail": f"Cannot delete account type '{account_type.name}' because it has {account_type.cash_accounts.count()} cash accounts assigned to it."
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)


class CashAccountViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Cash Account management
    
    Now shows only GL Asset accounts with Cash detail type
    Balances calculated from journal entries
    """
    
    def get_queryset(self):
        """Return only GL Asset accounts with Cash detail type"""
        try:
            # Import GL models here to avoid import issues
            from gl.models import AccountType, DetailType, Account
            
            # Get Asset account type (exact match)
            asset_account_type = AccountType.objects.filter(name__iexact='Assets').first()
            if not asset_account_type:
                return Account.objects.none()
            
            # Get Cash detail type (exact match for "Cash and Bank")
            cash_detail_type = DetailType.objects.filter(
                name__iexact='Cash and Bank', 
                account_type=asset_account_type
            ).first()
            if not cash_detail_type:
                return Account.objects.none()
            
            # Return GL accounts with Asset > Cash type
            return Account.objects.filter(
                account_type=asset_account_type,
                detail_type=cash_detail_type,
                is_active=True
            ).order_by('account_number')
            
        except Exception as e:
            # Import GL models here to avoid import issues
            from gl.models import Account
            return Account.objects.none()
    
    def list(self, request):
        """List cash accounts from GL with journal entry balances"""
        try:
            from gl.models import AccountType, DetailType, Account
            from django.db.models import Sum
            
            # Get Asset accounts with Cash detail type
            asset_type = AccountType.objects.get(name='Assets')
            cash_detail = DetailType.objects.get(name='Cash and Bank', account_type=asset_type)
            
            accounts = Account.objects.filter(
                account_type=asset_type,
                detail_type=cash_detail,
                is_active=True
            )
            
            account_data = []
            for account in accounts:
                account_data.append({
                    'id': account.id,
                    'account_name': account.account_name,
                    'account_number': account.account_number,
                    'account_type': {
                        'id': account.account_type.id,
                        'name': account.account_type.name,
                        'code': 'ASS'
                    },
                    'detail_type': {
                        'id': account.detail_type.id,
                        'name': account.detail_type.name
                    },
                    'currency': account.currency or 'USD',
                    'current_balance': 0.00,  # Simple placeholder for now
                    'is_active': account.is_active,
                    'description': account.description or '',
                    'created_at': account.created_at.isoformat() if account.created_at else '',
                    'updated_at': account.updated_at.isoformat() if account.updated_at else ''
                })
            
            return Response({
                'results': account_data,
                'count': len(account_data),
                'next': None,
                'previous': None
            })
            
        except Exception as e:
            return Response({
                'error': str(e),
                'results': [],
                'count': 0
            }, status=500)
    
    def retrieve(self, request, pk=None):
        """Get specific cash account with balance from journal entries"""
        try:
            from django.db import connection
            
            # Use raw SQL query for fast balance calculation
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        a.id,
                        a.account_name,
                        a.account_number,
                        a.currency,
                        a.is_active,
                        a.description,
                        a.created_at,
                        a.updated_at,
                        at.id as account_type_id,
                        at.name as account_type_name,
                        dt.id as detail_type_id,
                        dt.name as detail_type_name,
                        COALESCE(bal.total_debits, 0) - COALESCE(bal.total_credits, 0) as current_balance
                    FROM gl_account a
                    LEFT JOIN gl_accounttype at ON a.account_type_id = at.id
                    LEFT JOIN gl_detailtype dt ON a.detail_type_id = dt.id
                    LEFT JOIN (
                        SELECT 
                            jel.account_id,
                            SUM(jel.debit_amount) as total_debits,
                            SUM(jel.credit_amount) as total_credits
                        FROM gl_journalentryline jel
                        INNER JOIN gl_journalentry je ON jel.journal_entry_id = je.id
                        WHERE je.status = 'POSTED' 
                        AND jel.account_id = %s
                        GROUP BY jel.account_id
                    ) bal ON a.id = bal.account_id
                    WHERE a.id = %s
                    AND at.name ILIKE '%%Asset%%'
                    AND dt.name ILIKE '%%Cash%%'
                    AND a.is_active = true
                """, [pk, pk])
                
                row = cursor.fetchone()
                if not row:
                    return Response(
                        {'error': 'Cash account not found'}, 
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                account_data = {
                    'id': row[0],
                    'account_name': row[1],
                    'account_number': row[2],
                    'account_type': {
                        'id': row[8],
                        'name': row[9],
                        'code': row[9][:3].upper() if row[9] else 'ASS'
                    },
                    'detail_type': {
                        'id': row[10],
                        'name': row[11]
                    },
                    'currency': row[3] or 'USD',
                    'current_balance': float(row[12] or 0),
                    'is_active': row[4],
                    'description': row[5] or '',
                    'created_at': row[6].isoformat() if row[6] else '',
                    'updated_at': row[7].isoformat() if row[7] else ''
                }
                
                return Response(account_data)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to retrieve cash account: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def create(self, request):
        """Creating cash accounts not allowed - use GL Chart of Accounts"""
        return Response(
            {'error': 'Cash accounts must be created in Chart of Accounts (GL Module)'}, 
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    def update(self, request, pk=None):
        """Updating cash accounts not allowed - use GL Chart of Accounts"""
        return Response(
            {'error': 'Cash accounts must be updated in Chart of Accounts (GL Module)'}, 
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )
    
    def destroy(self, request, pk=None):
        """Deleting cash accounts not allowed - use GL Chart of Accounts"""
        return Response(
            {'error': 'Cash accounts must be deleted in Chart of Accounts (GL Module)'}, 
            status=status.HTTP_405_METHOD_NOT_ALLOWED
        )


class CashTransactionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Cash Transaction management
    
    Cash transactions handle all cash receipts, payments, and adjustments
    with comprehensive validation and approval workflows.
    
    Business Rules:
    - Amounts must be positive
    - Currency validation with exchange rates
    - Overdraft protection for non-credit accounts
    - Approval workflow for large transactions
    """
    
    queryset = CashTransaction.objects.select_related(
        'cash_account', 'created_by', 'posted_by', 'approved_by'
    ).order_by('-transaction_date', '-transaction_number')
    
    serializer_class = CashTransactionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'transaction_type', 'cash_account', 'currency', 'status',
        'created_by', 'requires_approval'
    ]
    search_fields = [
        'transaction_number', 'description', 'reference_number', 
        'customer', 'vendor', 'employee'
    ]
    ordering_fields = ['transaction_date', 'transaction_number', 'amount', 'created_at']
    ordering = ['-transaction_date', '-transaction_number']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update']:
            permission_classes = [IsAuthenticated]
        elif self.action in ['destroy', 'post_transaction', 'approve_transaction']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    @action(detail=True, methods=['post'])
    def post_transaction(self, request, pk=None):
        """Post a cash transaction."""
        transaction = self.get_object()
        
        if not transaction.can_be_posted():
            errors = []
            if transaction.status != 'DRAFT':
                errors.append(f"Transaction status is '{transaction.get_status_display()}', must be 'Draft'")
            if transaction.amount <= 0:
                errors.append("Transaction amount must be greater than zero")
            if transaction.requires_approval and not transaction.approved_by:
                errors.append("Transaction requires approval before posting")
            
            return Response(
                {"detail": "Cannot post transaction", "errors": errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Post the cash transaction
            transaction.post_transaction(request.user)
            
            # Also post the linked journal entry if it exists and is still DRAFT
            if transaction.gl_journal_entry and transaction.gl_journal_entry.status == 'DRAFT':
                try:
                    transaction.gl_journal_entry.post_entry(request.user)
                    print(f"✅ Posted linked journal entry #{transaction.gl_journal_entry.entry_number}")
                except Exception as je_error:
                    print(f"⚠️ Warning: Failed to post linked journal entry: {je_error}")
                    # Continue anyway - cash transaction is posted
            
            # Return updated transaction
            serializer = self.get_serializer(transaction)
            return Response({
                "detail": "Transaction and linked journal entry posted successfully",
                "transaction": serializer.data
            })
            
        except Exception as e:
            return Response(
                {"detail": f"Error posting transaction: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def approve_transaction(self, request, pk=None):
        """Approve a cash transaction."""
        transaction = self.get_object()
        
        if transaction.status != 'DRAFT':
            return Response(
                {"detail": "Only draft transactions can be approved"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        transaction.approved_by = request.user
        transaction.approved_at = timezone.now()
        transaction.save()
        
        serializer = self.get_serializer(transaction)
        return Response({
            "detail": "Transaction approved successfully",
            "transaction": serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def pending_approval(self, request):
        """Get transactions pending approval."""
        pending_transactions = self.get_queryset().filter(
            status='DRAFT',
            requires_approval=True,
            approved_by__isnull=True
        )
        
        serializer = self.get_serializer(pending_transactions, many=True)
        return Response({
            'pending_transactions': serializer.data,
            'count': pending_transactions.count()
        })


class CashTransferViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Cash Transfer management
    
    Cash transfers handle movements between cash accounts with
    currency conversion and fee calculations.
    """
    
    queryset = CashTransfer.objects.select_related(
        'from_account', 'to_account', 'fee_account', 'created_by', 'posted_by'
    ).order_by('-transfer_date', '-transfer_number')
    
    serializer_class = CashTransferSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['from_account', 'to_account', 'status', 'created_by']
    search_fields = ['transfer_number', 'description', 'reference_number']
    ordering_fields = ['transfer_date', 'transfer_number', 'amount', 'created_at']
    ordering = ['-transfer_date', '-transfer_number']
    
    @action(detail=True, methods=['post'])
    def post_transfer(self, request, pk=None):
        """Post a cash transfer."""
        transfer = self.get_object()
        
        if not transfer.can_be_posted():
            return Response(
                {"detail": "Transfer cannot be posted"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            transfer.post_transfer(request.user)
            
            serializer = self.get_serializer(transfer)
            return Response({
                "detail": "Transfer posted successfully",
                "transfer": serializer.data
            })
            
        except Exception as e:
            return Response(
                {"detail": f"Error posting transfer: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class BankStatementViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Bank Statement management
    
    Bank statements are imported for reconciliation against cash transactions.
    """
    
    queryset = BankStatement.objects.select_related(
        'cash_account', 'imported_by', 'reconciled_by'
    ).prefetch_related('entries').order_by('-statement_date')
    
    serializer_class = BankStatementSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['cash_account', 'is_reconciled', 'imported_by']
    search_fields = ['statement_number', 'cash_account__account_name']
    ordering_fields = ['statement_date', 'created_at']
    ordering = ['-statement_date']
    
    @action(detail=True, methods=['get'])
    def reconciliation_data(self, request, pk=None):
        """Get data for bank reconciliation."""
        statement = self.get_object()
        
        # Get statement entries
        entries = statement.entries.all().order_by('entry_date')
        entries_serializer = BankStatementEntrySerializer(entries, many=True)
        
        # Get unmatched cash transactions in the period
        unmatched_transactions = CashTransaction.objects.filter(
            cash_account=statement.cash_account,
            transaction_date__range=[statement.period_from, statement.period_to],
            status='POSTED',
            bank_entries__isnull=True  # Not matched to any bank entry
        ).order_by('transaction_date')
        
        transactions_serializer = CashTransactionSerializer(
            unmatched_transactions, 
            many=True, 
            context={'request': request}
        )
        
        return Response({
            'statement': BankStatementSerializer(statement, context={'request': request}).data,
            'bank_entries': entries_serializer.data,
            'unmatched_transactions': transactions_serializer.data,
            'reconciliation_summary': {
                'total_entries': entries.count(),
                'matched_entries': entries.filter(is_matched=True).count(),
                'unmatched_entries': entries.filter(is_matched=False).count(),
                'total_transactions': unmatched_transactions.count(),
                'statement_balance': float(statement.closing_balance),
                'book_balance': float(statement.cash_account.get_current_balance())
            }
        })


class BankStatementEntryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Bank Statement Entry management
    
    Individual entries from bank statements for reconciliation.
    """
    
    queryset = BankStatementEntry.objects.select_related(
        'bank_statement', 'matched_transaction', 'matched_by'
    ).order_by('bank_statement', 'entry_date')
    
    serializer_class = BankStatementEntrySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['bank_statement', 'entry_type', 'is_matched']
    search_fields = ['description', 'reference_number']
    ordering_fields = ['entry_date', 'amount']
    ordering = ['entry_date']
    
    @action(detail=True, methods=['post'])
    def match_transaction(self, request, pk=None):
        """Match a bank entry to a cash transaction."""
        entry = self.get_object()
        transaction_id = request.data.get('transaction_id')
        
        if not transaction_id:
            return Response(
                {"detail": "Transaction ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            transaction = CashTransaction.objects.get(
                id=transaction_id,
                cash_account=entry.bank_statement.cash_account
            )
            
            # Perform matching
            entry.matched_transaction = transaction
            entry.is_matched = True
            entry.matched_by = request.user
            entry.matched_at = timezone.now()
            entry.save()
            
            # Update transaction cleared date
            transaction.cleared_date = entry.entry_date
            transaction.save()
            
            return Response({
                "detail": "Entry matched successfully",
                "entry": BankStatementEntrySerializer(entry, context={'request': request}).data
            })
            
        except CashTransaction.DoesNotExist:
            return Response(
                {"detail": "Transaction not found"},
                status=status.HTTP_404_NOT_FOUND
            )


class CashFlowForecastViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Cash Flow Forecast management
    
    Cash flow forecasts help with treasury planning and analysis.
    """
    
    queryset = CashFlowForecast.objects.select_related(
        'cash_account', 'created_by'
    ).order_by('-period_from')
    
    serializer_class = CashFlowForecastSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['forecast_type', 'cash_account', 'is_active', 'created_by']
    search_fields = ['forecast_name', 'assumptions']
    ordering_fields = ['period_from', 'period_to', 'created_at']
    ordering = ['-period_from']


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def cash_flow_report(request):
    """
    Generate comprehensive cash flow report
    
    Provides detailed cash flow analysis with actual vs forecast comparison.
    """
    try:
        # Get parameters
        account_id = request.GET.get('account_id')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        include_forecast = request.GET.get('include_forecast', 'false').lower() == 'true'
        
        # Validate required parameters
        if not all([date_from, date_to]):
            return Response(
                {"detail": "date_from and date_to are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Parse dates
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get cash accounts
        accounts_query = CashAccount.objects.filter(is_active=True)
        if account_id:
            accounts_query = accounts_query.filter(id=account_id)
        
        accounts = accounts_query.select_related('account_type')
        
        cash_flow_data = []
        
        for account in accounts:
            # Get actual transactions
            transactions = CashTransaction.objects.filter(
                cash_account=account,
                transaction_date__range=[date_from, date_to],
                status='POSTED'
            )
            
            # Calculate actual flows
            receipts = transactions.filter(
                transaction_type='RECEIPT'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            payments = transactions.filter(
                transaction_type='PAYMENT'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
            
            net_flow = receipts - payments
            
            account_data = {
                'account': {
                    'id': account.id,
                    'name': account.account_name,
                    'number': account.account_number,
                    'currency': account.currency,
                    'type': account.account_type.name
                },
                'actual': {
                    'receipts': float(receipts),
                    'payments': float(payments),
                    'net_flow': float(net_flow)
                },
                'opening_balance': float(account.opening_balance),
                'closing_balance': float(account.get_current_balance())
            }
            
            # Add forecast data if requested
            if include_forecast:
                forecasts = CashFlowForecast.objects.filter(
                    cash_account=account,
                    period_from__lte=date_to,
                    period_to__gte=date_from,
                    is_active=True
                )
                
                if forecasts.exists():
                    forecast_receipts = forecasts.aggregate(
                        total=Sum('projected_receipts')
                    )['total'] or Decimal('0.00')
                    
                    forecast_payments = forecasts.aggregate(
                        total=Sum('projected_payments')
                    )['total'] or Decimal('0.00')
                    
                    account_data['forecast'] = {
                        'receipts': float(forecast_receipts),
                        'payments': float(forecast_payments),
                        'net_flow': float(forecast_receipts - forecast_payments)
                    }
                    
                    # Calculate variance
                    account_data['variance'] = {
                        'receipts': float(receipts - forecast_receipts),
                        'payments': float(payments - forecast_payments),
                        'net_flow': float(net_flow - (forecast_receipts - forecast_payments))
                    }
            
            cash_flow_data.append(account_data)
        
        # Summary totals
        total_receipts = sum(account['actual']['receipts'] for account in cash_flow_data)
        total_payments = sum(account['actual']['payments'] for account in cash_flow_data)
        total_net_flow = total_receipts - total_payments
        
        summary = {
            'total_receipts': total_receipts,
            'total_payments': total_payments,
            'total_net_flow': total_net_flow,
            'account_count': len(cash_flow_data)
        }
        
        if include_forecast:
            total_forecast_receipts = sum(
                account.get('forecast', {}).get('receipts', 0) 
                for account in cash_flow_data
            )
            total_forecast_payments = sum(
                account.get('forecast', {}).get('payments', 0) 
                for account in cash_flow_data
            )
            
            summary['forecast'] = {
                'receipts': total_forecast_receipts,
                'payments': total_forecast_payments,
                'net_flow': total_forecast_receipts - total_forecast_payments
            }
            
            summary['variance'] = {
                'receipts': total_receipts - total_forecast_receipts,
                'payments': total_payments - total_forecast_payments,
                'net_flow': total_net_flow - (total_forecast_receipts - total_forecast_payments)
            }
        
        return Response({
            'period': {
                'from': date_from,
                'to': date_to
            },
            'accounts': cash_flow_data,
            'summary': summary,
            'include_forecast': include_forecast,
            'generated_at': timezone.now().isoformat()
        })
        
    except Exception as e:
        return Response(
            {'error': f'Failed to generate cash flow report: {str(e)}'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def treasury_dashboard(request):
    """
    Get treasury dashboard with key cash management metrics
    
    Provides executive summary of cash position and key indicators.
    """
    try:
        # Get company currency info
        company = Company.objects.first()
        functional_currency = company.functional_currency if company else 'USD'
        
        # Get all active cash accounts
        accounts = CashAccount.objects.filter(is_active=True).select_related('account_type')
        
        # Calculate totals by currency and account type
        cash_position = {}
        account_type_summary = {}
        
        for account in accounts:
            balance = account.get_current_balance()
            currency = account.currency
            account_type = account.account_type.name
            
            # Currency totals
            if currency not in cash_position:
                cash_position[currency] = {
                    'total_balance': Decimal('0.00'),
                    'available_balance': Decimal('0.00'),
                    'account_count': 0
                }
            
            cash_position[currency]['total_balance'] += balance
            cash_position[currency]['available_balance'] += account.get_available_balance()
            cash_position[currency]['account_count'] += 1
            
            # Account type totals
            if account_type not in account_type_summary:
                account_type_summary[account_type] = {
                    'total_balance': Decimal('0.00'),
                    'account_count': 0,
                    'currencies': set()
                }
            
            account_type_summary[account_type]['total_balance'] += balance
            account_type_summary[account_type]['account_count'] += 1
            account_type_summary[account_type]['currencies'].add(currency)
        
        # Convert sets to lists for JSON serialization
        for account_type in account_type_summary:
            account_type_summary[account_type]['currencies'] = list(
                account_type_summary[account_type]['currencies']
            )
        
        # Recent transactions (last 30 days)
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_transactions = CashTransaction.objects.filter(
            transaction_date__gte=thirty_days_ago,
            status='POSTED'
        ).count()
        
        # Pending approvals
        pending_approvals = CashTransaction.objects.filter(
            status='DRAFT',
            requires_approval=True,
            approved_by__isnull=True
        ).count()
        
        # Accounts needing reconciliation (over 30 days)
        needs_reconciliation = accounts.filter(
            account_type__requires_reconciliation=True
        ).filter(
            Q(last_reconciled_date__isnull=True) |
            Q(last_reconciled_date__lt=timezone.now().date() - timedelta(days=30))
        ).count()
        
        return Response({
            'cash_position': {
                currency: {
                    'total_balance': float(data['total_balance']),
                    'available_balance': float(data['available_balance']),
                    'account_count': data['account_count']
                }
                for currency, data in cash_position.items()
            },
            'account_type_summary': {
                account_type: {
                    'total_balance': float(data['total_balance']),
                    'account_count': data['account_count'],
                    'currencies': data['currencies']
                }
                for account_type, data in account_type_summary.items()
            },
            'key_metrics': {
                'total_accounts': accounts.count(),
                'active_accounts': accounts.filter(is_active=True).count(),
                'currencies': len(cash_position),
                'recent_transactions': recent_transactions,
                'pending_approvals': pending_approvals,
                'needs_reconciliation': needs_reconciliation
            },
            'functional_currency': functional_currency,
            'generated_at': timezone.now().isoformat()
        })
        
    except Exception as e:
        return Response(
            {'error': f'Failed to generate treasury dashboard: {str(e)}'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_gl_cash_accounts(request):
    """Get available GL cash accounts (Asset > Cash type accounts)"""
    try:
        from gl.models import AccountType, DetailType, Account
        
        # Debug: Print all account types and detail types
        print("=== DEBUG: Available Account Types ===")
        for at in AccountType.objects.all():
            print(f"ID: {at.id}, Name: '{at.name}', Code: '{at.code}'")
        
        print("=== DEBUG: Available Detail Types ===")
        for dt in DetailType.objects.all():
            print(f"ID: {dt.id}, Name: '{dt.name}', Account Type: '{dt.account_type.name if dt.account_type else 'None'}'")
        
        # Try different approaches to find cash accounts
        cash_accounts = []
        
        # Method 1: Look for exact match 'Assets' and 'Cash and Bank'
        try:
            asset_type = AccountType.objects.get(name__iexact='Assets')
            cash_detail = DetailType.objects.get(name__iexact='Cash and Bank', account_type=asset_type)
            method1_accounts = Account.objects.filter(
                account_type=asset_type,
                detail_type=cash_detail,
                is_active=True
            )
            print(f"Method 1 (exact match): Found {method1_accounts.count()} accounts")
            cash_accounts.extend(method1_accounts)
        except (AccountType.DoesNotExist, DetailType.DoesNotExist) as e:
            print(f"Method 1 failed: {e}")
        
        # Method 2: Look for partial matches
        if not cash_accounts:
            try:
                asset_types = AccountType.objects.filter(name__icontains='Asset')
                cash_details = DetailType.objects.filter(name__icontains='Cash')
                print(f"Method 2: Found {asset_types.count()} asset types, {cash_details.count()} cash details")
                
                for asset_type in asset_types:
                    for cash_detail in cash_details.filter(account_type=asset_type):
                        method2_accounts = Account.objects.filter(
                            account_type=asset_type,
                            detail_type=cash_detail,
                            is_active=True
                        )
                        print(f"Asset type '{asset_type.name}' + Detail '{cash_detail.name}': {method2_accounts.count()} accounts")
                        cash_accounts.extend(method2_accounts)
            except Exception as e:
                print(f"Method 2 failed: {e}")
        
        # Method 3: Get all accounts with 'cash' in name if still no results
        if not cash_accounts:
            print("Method 3: Searching for accounts with 'cash' in name")
            cash_accounts = Account.objects.filter(
                Q(account_name__icontains='cash') | Q(account_name__icontains='bank'),
                is_active=True
            )[:10]  # Limit to 10 for safety
        
        # Transform accounts to response format
        account_data = []
        for account in cash_accounts:
            account_data.append({
                'id': account.id,
                'account_number': account.account_number,
                'account_name': account.account_name,
                'balance': float(account.opening_balance or 0),  # Simple balance for now
                'current_balance': float(account.opening_balance or 0),
                'currency': account.currency or 'USD',
                'account_type': account.account_type.name if account.account_type else '',
                'detail_type': account.detail_type.name if account.detail_type else ''
            })
        
        print(f"=== Final Result: Returning {len(account_data)} cash accounts ===")
        for acc in account_data:
            print(f"- {acc['account_number']} - {acc['account_name']} (Balance: {acc['balance']})")
        
        return Response({
            'accounts': account_data,
            'count': len(account_data),
            'account_type': 'Assets',
            'detail_type': 'Cash and Bank',
            'message': f'Found {len(account_data)} cash accounts',
            'debug_info': {
                'total_accounts': Account.objects.count(),
                'active_accounts': Account.objects.filter(is_active=True).count(),
                'account_types_count': AccountType.objects.count(),
                'detail_types_count': DetailType.objects.count()
            }
        })
        
    except Exception as e:
        import traceback
        print(f"Error in get_gl_cash_accounts: {e}")
        print(traceback.format_exc())
        return Response({
            'error': str(e),
            'accounts': [],
            'count': 0,
            'debug_info': {
                'error_details': traceback.format_exc()
            }
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def debug_gl_accounts(request):
    """Debug endpoint to test GL account access"""
    try:
        # Test basic connectivity
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM gl_accounttype")
            account_types_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM gl_detailtype")  
            detail_types_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM gl_account")
            accounts_count = cursor.fetchone()[0]
        
        # Test model imports
        account_types = list(AccountType.objects.values('id', 'name'))
        detail_types = list(DetailType.objects.values('id', 'name', 'account_type_id'))
        accounts = list(Account.objects.values('id', 'account_name', 'account_type_id', 'detail_type_id'))
        
        return Response({
            'db_counts': {
                'account_types': account_types_count,
                'detail_types': detail_types_count,
                'accounts': accounts_count
            },
            'account_types': account_types,
            'detail_types': detail_types,
            'accounts': accounts
        })
        
    except Exception as e:
        import traceback
        return Response({
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_gl_expense_accounts(request):
    """Get GL accounts suitable for cash payment categories (expense accounts)"""
    try:
        from gl.models import AccountType, DetailType, Account
        
        # Get all GL accounts excluding cash accounts
        accounts = Account.objects.filter(
            is_active=True
        ).exclude(
            # Exclude cash accounts (Assets > Cash and Bank)
            account_type__name__iexact='Assets',
            detail_type__name__icontains='Cash'
        ).select_related(
            'account_type', 'detail_type'
        ).order_by('account_number')
        
        account_data = []
        for account in accounts:
            account_data.append({
                'id': account.id,
                'account_number': account.account_number,
                'account_name': account.account_name,
                'account_type': account.account_type.name if account.account_type else '',
                'detail_type': account.detail_type.name if account.detail_type else '',
                'account_type_name': account.account_type.name if account.account_type else '',
                'detail_type_name': account.detail_type.name if account.detail_type else '',
                'currency': account.currency or 'USD',
                'is_active': account.is_active,
                'description': account.description or ''
            })
        
        return Response({
            'results': account_data,
            'count': len(account_data),
            'message': f'Found {len(account_data)} expense accounts'
        })
        
    except Exception as e:
        return Response({
            'error': f'Failed to load expense accounts: {str(e)}',
            'results': [],
            'count': 0
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_gl_income_accounts(request):
    """Get GL accounts suitable for cash receipt categories (all active accounts)"""
    try:
        from gl.models import AccountType, DetailType, Account
        
        # Get all GL accounts excluding cash accounts (same as expense accounts)
        accounts = Account.objects.filter(
            is_active=True
        ).exclude(
            # Exclude cash accounts (Assets > Cash and Bank)
            account_type__name__iexact='Assets',
            detail_type__name__icontains='Cash'
        ).select_related(
            'account_type', 'detail_type'
        ).order_by('account_number')
        
        account_data = []
        for account in accounts:
            account_data.append({
                'id': account.id,
                'account_number': account.account_number,
                'account_name': account.account_name,
                'account_type': account.account_type.name if account.account_type else '',
                'detail_type': account.detail_type.name if account.detail_type else '',
                'account_type_name': account.account_type.name if account.account_type else '',
                'detail_type_name': account.detail_type.name if account.detail_type else '',
                'currency': account.currency or 'USD',
                'is_active': account.is_active,
                'description': account.description or ''
            })
        
        return Response({
            'results': account_data,
            'count': len(account_data),
            'message': f'Found {len(account_data)} accounts'
        })
        
    except Exception as e:
        return Response({
            'error': f'Failed to load accounts: {str(e)}',
            'results': [],
            'count': 0
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_default_tds_rates(request):
    """Create default TDS rates if none exist"""
    try:
        from tds.models import TDS
        
        # Check if TDS rates already exist
        existing_count = TDS.objects.count()
        if existing_count > 0:
            return Response({
                'message': f'TDS rates already exist ({existing_count} rates found)',
                'created': False
            })
        
        # Create default TDS rates
        default_rates = [
            {
                'tds_type': 'payment_deduction',
                'description': 'Professional & Technical Services',
                'rate': 10.0,
                'section': '194J',
                'threshold_limit': 30000.00
            },
            {
                'tds_type': 'payment_deduction',
                'description': 'Contractor & Sub-contractor',
                'rate': 1.0,
                'section': '194C',
                'threshold_limit': 30000.00
            },
            {
                'tds_type': 'payment_deduction',
                'description': 'Rent - Plant & Machinery',
                'rate': 2.0,
                'section': '194I',
                'threshold_limit': 240000.00
            },
            {
                'tds_type': 'payment_deduction',
                'description': 'Rent - Land & Building',
                'rate': 10.0,
                'section': '194I',
                'threshold_limit': 240000.00
            },
            {
                'tds_type': 'payment_deduction',
                'description': 'Commission & Brokerage',
                'rate': 5.0,
                'section': '194H',
                'threshold_limit': 15000.00
            },
            {
                'tds_type': 'payment_deduction',
                'description': 'Interest on Securities',
                'rate': 10.0,
                'section': '194A',
                'threshold_limit': 5000.00
            }
        ]
        
        created_rates = []
        for rate_data in default_rates:
            tds_rate = TDS.objects.create(
                **rate_data,
                created_by=request.user
            )
            created_rates.append({
                'id': tds_rate.id,
                'description': tds_rate.description,
                'rate': float(tds_rate.rate),
                'section': tds_rate.section,
                'tds_type': tds_rate.tds_type
            })
        
        return Response({
            'message': f'Created {len(created_rates)} default TDS rates',
            'created': True,
            'rates': created_rates
        })
        
    except Exception as e:
        import traceback
        return Response({
            'error': f'Failed to create default TDS rates: {str(e)}',
            'traceback': traceback.format_exc()
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_payment(request):
    """
    Create a payment with automatic tax calculations
    
    Workflow:
    1. User provides base amounts and tax rate selections
    2. Backend calculates: Sales Tax + Total + TDS + Net
    3. Backend creates journal entry with proper tax accounting
    4. Backend creates CMS transaction record
    """
    try:
        data = request.data
        
        # Extract basic payment data
        payee = data.get('payee', '')
        payment_account_id = data.get('payment_account_id')
        payment_date = data.get('payment_date')
        reference_number = data.get('reference_number', '')
        memo = data.get('memo', '')
        payment_lines = data.get('payment_lines', [])
        tds_rate_id = data.get('tds_rate_id')
        
        # Validate required fields
        if not payment_account_id:
            return Response({'error': 'Payment account is required'}, status=400)
        if not payment_lines:
            return Response({'error': 'At least one payment line is required'}, status=400)
        
        # Calculate totals
        base_amount = Decimal('0.00')
        total_sales_tax = Decimal('0.00')
        
        for line in payment_lines:
            line_amount = Decimal(str(line.get('amount', 0)))
            base_amount += line_amount
            
            # Calculate sales tax for this line
            if line.get('sales_tax_id') and line.get('sales_tax_rate'):
                sales_tax_rate = Decimal(str(line.get('sales_tax_rate', 0)))
                line_sales_tax = (line_amount * sales_tax_rate) / 100
                total_sales_tax += line_sales_tax
        
        # Calculate total amount (base + sales tax)
        total_amount = base_amount + total_sales_tax
        
        # Calculate TDS (on total amount including sales tax)
        tds_amount = Decimal('0.00')
        if tds_rate_id:
            try:
                from tds.models import TDS
                tds_rate = TDS.objects.get(id=tds_rate_id)
                tds_amount = (total_amount * tds_rate.rate) / 100
            except TDS.DoesNotExist:
                return Response({'error': 'Invalid TDS rate'}, status=400)
        
        # Calculate net payment (total - TDS)
        net_amount = total_amount - tds_amount
        
        # Generate entry number
        from django.utils import timezone
        import random
        date_str = timezone.now().strftime('%Y%m%d')
        random_num = random.randint(1000, 9999)
        entry_number = f"PAY-{date_str}-{random_num}"
        
        # Create journal entry
        from gl.models import JournalEntry, JournalEntryLine, Account
        
        journal_entry = JournalEntry.objects.create(
            entry_number=entry_number,
            transaction_date=payment_date,
            description=f"Cash Payment to {payee}",
            reference_number=reference_number,
            memo=memo,
            status='DRAFT',  # Must be DRAFT to allow posting
            entry_type='GENERAL',
            currency='USD',  # Will be set from company settings
            created_by=request.user
        )
        
        line_number = 1
        
        # 1. Credit payment account (cash out)
        JournalEntryLine.objects.create(
            journal_entry=journal_entry,
            line_number=line_number,
            account_id=payment_account_id,
            debit_amount=Decimal('0.00'),
            credit_amount=net_amount,
            description=f"Payment to {payee}",
            memo=memo
        )
        line_number += 1
        
        # 2. Debit expense accounts (base amounts only)
        for line in payment_lines:
            line_amount = Decimal(str(line.get('amount', 0)))
            category_id = line.get('category_id')
            description = line.get('description', f"Payment to {payee}")
            
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                line_number=line_number,
                account_id=category_id,
                debit_amount=line_amount,
                credit_amount=Decimal('0.00'),
                description=description,
                memo=line.get('memo', '')
            )
            line_number += 1
        
        # 3. Create sales tax entries if applicable
        if total_sales_tax > 0:
            # Get Sales Tax Payable account (ID 67 from earlier investigation)
            try:
                sales_tax_account = Account.objects.get(id=67)  # Sales Tax Payable account
            except Account.DoesNotExist:
                return Response({'error': 'Sales Tax Payable account not found. Please check account ID 67.'}, status=400)
            
            # For payments (INPUT tax) - Debit Sales Tax (asset/prepaid)
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                line_number=line_number,
                account=sales_tax_account,
                debit_amount=total_sales_tax,
                credit_amount=Decimal('0.00'),
                description=f"Input Tax on payment to {payee}",
                memo="Sales tax recoverable"
            )
            line_number += 1
        
        # 4. Create TDS entry if applicable  
        if tds_amount > 0:
            try:
                # Try to find Income Tax Payable account by name or number
                tds_account = Account.objects.filter(
                    Q(account_number='2150') | 
                    Q(account_name__icontains='Income Tax Payable') |
                    Q(account_name__icontains='TDS Payable')
                ).first()
                
                if not tds_account:
                    return Response({'error': 'Income Tax Payable/TDS account not found. Please create an Income Tax Payable account.'}, status=400)
            except Exception:
                return Response({'error': 'Error finding Income Tax Payable account.'}, status=400)
            
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                line_number=line_number,
                account=tds_account,
                debit_amount=Decimal('0.00'),
                credit_amount=tds_amount,
                description=f"TDS on payment to {payee}",
                memo=f"TDS @ {tds_rate.rate}%"
            )
            line_number += 1
        
        # Create CMS transaction record
        transaction_data = {
            'transaction_number': f"TXN-{date_str}-{random_num}",
            'transaction_date': payment_date,
            'transaction_type': 'PAYMENT',
            'amount': net_amount,
            'currency': 'USD',
            'description': f"Cash Payment to {payee}",
            'reference_number': reference_number,
            'memo': memo,
            'vendor': payee,
            'status': 'DRAFT',  # Must be DRAFT to allow posting
            'gl_journal_entry': journal_entry.id
        }
        
        # Find linked cash account
        try:
            from .models import CashAccount
            cash_account = CashAccount.objects.filter(gl_account=payment_account_id).first()
            if cash_account:
                transaction_data['cash_account'] = cash_account.id
        except:
            pass
        
        # Create and immediately post transaction
        from .serializers import CashTransactionSerializer
        from .models import CashTransaction
        transaction_data['status'] = 'DRAFT'  # Start as DRAFT
        
        # Create transaction
        cash_account = None
        try:
            from .models import CashAccount
            cash_account = CashAccount.objects.filter(gl_account=payment_account_id).first()
            if cash_account:
                transaction_data['cash_account'] = cash_account.id
                
                transaction_serializer = CashTransactionSerializer(data=transaction_data, context={'request': request})
                if transaction_serializer.is_valid():
                    transaction = transaction_serializer.save()
                else:
                    # Create manually if serializer fails
                    transaction = CashTransaction.objects.create(
                        transaction_number=transaction_data['transaction_number'],
                        transaction_date=transaction_data['transaction_date'],
                        transaction_type=transaction_data['transaction_type'],
                        amount=transaction_data['amount'],
                        currency=transaction_data['currency'],
                        description=transaction_data['description'],
                        reference_number=transaction_data['reference_number'],
                        memo=transaction_data['memo'],
                        vendor=transaction_data['vendor'],
                        status='DRAFT',
                        cash_account=cash_account,
                        gl_journal_entry=journal_entry,
                        created_by=request.user
                    )
                
                # Note: Entries are created as DRAFT and can be posted manually
                # Users can review the payment before posting via the UI
                print(f"✅ Created journal entry #{journal_entry.entry_number} - Status: {journal_entry.status}")
                print(f"✅ Created cash transaction #{transaction.transaction_number} - Status: {transaction.status}")
                print(f"💡 Payment created as DRAFT - can be posted manually via UI")
                    
        except Exception as e:
            print(f"Error creating/posting cash transaction: {e}")
            # Continue with journal entry only if cash transaction fails
        
        # Return success response with calculations and status info
        journal_entry_status = journal_entry.status
        transaction_status = transaction.status if 'transaction' in locals() else 'Not Created'
        
        return Response({
            'success': True,
            'journal_entry_id': journal_entry.id,
            'entry_number': entry_number,
            'journal_entry_status': journal_entry_status,
            'cash_transaction_status': transaction_status,
            'calculations': {
                'base_amount': float(base_amount),
                'sales_tax_amount': float(total_sales_tax),
                'total_amount': float(total_amount),
                'tds_amount': float(tds_amount),
                'net_payment': float(net_amount)
            },
            'message': f'Payment created successfully as DRAFT. Entry #{entry_number} (Status: {journal_entry_status}) - Ready for review and posting'
        })
        
    except Exception as e:
        import traceback
        return Response({
            'error': f'Error creating payment: {str(e)}',
            'traceback': traceback.format_exc()
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_receipt(request):
    """
    Create a receipt with automatic tax calculations
    
    Workflow:
    1. User provides base amounts and tax rate selections
    2. Backend calculates: Sales Tax + Total + TDS + Net
    3. Backend creates journal entry with proper tax accounting
    4. Backend creates CMS transaction record
    """
    try:
        data = request.data
        
        # Extract basic receipt data
        payer = data.get('payer', '')
        receipt_account_id = data.get('receipt_account_id')
        receipt_date = data.get('receipt_date')
        reference_number = data.get('reference_number', '')
        memo = data.get('memo', '')
        receipt_lines = data.get('receipt_lines', [])
        tds_rate_id = data.get('tds_rate_id')
        
        # Validate required fields
        if not receipt_account_id:
            return Response({'error': 'Receipt account is required'}, status=400)
        if not receipt_lines:
            return Response({'error': 'At least one receipt line is required'}, status=400)
        
        # Calculate totals
        base_amount = Decimal('0.00')
        total_sales_tax = Decimal('0.00')
        
        for line in receipt_lines:
            line_amount = Decimal(str(line.get('amount', 0)))
            base_amount += line_amount
            
            # Calculate sales tax for this line
            if line.get('sales_tax_id') and line.get('sales_tax_rate'):
                sales_tax_rate = Decimal(str(line.get('sales_tax_rate', 0)))
                line_sales_tax = (line_amount * sales_tax_rate) / 100
                total_sales_tax += line_sales_tax
        
        # Calculate total amount (base + sales tax)
        total_amount = base_amount + total_sales_tax
        
        # Calculate TDS (on total amount including sales tax)
        tds_amount = Decimal('0.00')
        tds_rate = None
        if tds_rate_id:
            try:
                from tds.models import TDS
                tds_rate = TDS.objects.get(id=tds_rate_id)
                tds_amount = (total_amount * tds_rate.rate) / 100
            except TDS.DoesNotExist:
                return Response({'error': 'Invalid TDS rate'}, status=400)
        
        # Calculate net receipt (total - TDS)
        net_amount = total_amount - tds_amount
        
        # Generate entry number
        from django.utils import timezone
        import random
        date_str = timezone.now().strftime('%Y%m%d')
        random_num = random.randint(1000, 9999)
        entry_number = f"REC-{date_str}-{random_num}"
        
        # Create journal entry
        from gl.models import JournalEntry, JournalEntryLine, Account
        
        journal_entry = JournalEntry.objects.create(
            entry_number=entry_number,
            transaction_date=receipt_date,
            description=f"Cash Receipt from {payer}",
            reference_number=reference_number,
            memo=memo,
            status='DRAFT',  # Must be DRAFT to allow posting
            entry_type='GENERAL',
            currency='USD',  # Will be set from company settings
            created_by=request.user
        )
        
        line_number = 1
        
        # 1. Debit receipt account (cash in)
        JournalEntryLine.objects.create(
            journal_entry=journal_entry,
            line_number=line_number,
            account_id=receipt_account_id,
            debit_amount=net_amount,
            credit_amount=Decimal('0.00'),
            description=f"Receipt from {payer}",
            memo=memo
        )
        line_number += 1
        
        # 2. Credit income accounts (base amounts only)
        for line in receipt_lines:
            line_amount = Decimal(str(line.get('amount', 0)))
            category_id = line.get('category_id')
            description = line.get('description', f"Receipt from {payer}")
            
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                line_number=line_number,
                account_id=category_id,
                debit_amount=Decimal('0.00'),
                credit_amount=line_amount,
                description=description,
                memo=line.get('memo', '')
            )
            line_number += 1
        
        # 3. Create sales tax entries if applicable
        if total_sales_tax > 0:
            # Get Sales Tax Payable account (ID 67 from earlier investigation)
            try:
                sales_tax_account = Account.objects.get(id=67)  # Sales Tax Payable account
            except Account.DoesNotExist:
                return Response({'error': 'Sales Tax Payable account not found. Please check account ID 67.'}, status=400)
            
            # For receipts (OUTPUT tax) - Credit Sales Tax (liability)
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                line_number=line_number,
                account=sales_tax_account,
                debit_amount=Decimal('0.00'),
                credit_amount=total_sales_tax,
                description=f"Output Tax on receipt from {payer}",
                memo="Sales tax collected"
            )
            line_number += 1
        
        # 4. Create TDS entry if applicable  
        if tds_amount > 0:
            try:
                # Try to find Income Tax Payable account by name or number
                tds_account = Account.objects.filter(
                    Q(account_number='2150') | 
                    Q(account_name__icontains='Income Tax Payable') |
                    Q(account_name__icontains='TDS Payable')
                ).first()
                
                if not tds_account:
                    return Response({'error': 'Income Tax Payable/TDS account not found. Please create an Income Tax Payable account.'}, status=400)
            except Exception:
                return Response({'error': 'Error finding Income Tax Payable account.'}, status=400)
            
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                line_number=line_number,
                account=tds_account,
                debit_amount=tds_amount,
                credit_amount=Decimal('0.00'),
                description=f"TDS on receipt from {payer}",
                memo=f"TDS @ {tds_rate.rate}%"
            )
            line_number += 1
        
        # Create CMS transaction record
        transaction_data = {
            'transaction_number': f"TXN-{date_str}-{random_num}",
            'transaction_date': receipt_date,
            'transaction_type': 'RECEIPT',
            'amount': net_amount,
            'currency': 'USD',
            'description': f"Cash Receipt from {payer}",
            'reference_number': reference_number,
            'memo': memo,
            'customer': payer,
            'status': 'DRAFT',  # Must be DRAFT to allow posting
            'gl_journal_entry': journal_entry.id
        }
        
        # Find linked cash account
        try:
            from .models import CashAccount
            cash_account = CashAccount.objects.filter(gl_account=receipt_account_id).first()
            if cash_account:
                transaction_data['cash_account'] = cash_account.id
        except:
            pass
        
        # Create transaction
        from .serializers import CashTransactionSerializer
        from .models import CashTransaction
        transaction_data['status'] = 'DRAFT'  # Start as DRAFT
        
        # Create transaction
        cash_account = None
        try:
            from .models import CashAccount
            cash_account = CashAccount.objects.filter(gl_account=receipt_account_id).first()
            if cash_account:
                transaction_data['cash_account'] = cash_account.id
                
                transaction_serializer = CashTransactionSerializer(data=transaction_data, context={'request': request})
                if transaction_serializer.is_valid():
                    transaction = transaction_serializer.save()
                else:
                    # Create manually if serializer fails
                    transaction = CashTransaction.objects.create(
                        transaction_number=transaction_data['transaction_number'],
                        transaction_date=transaction_data['transaction_date'],
                        transaction_type=transaction_data['transaction_type'],
                        amount=transaction_data['amount'],
                        currency=transaction_data['currency'],
                        description=transaction_data['description'],
                        reference_number=transaction_data['reference_number'],
                        memo=transaction_data['memo'],
                        customer=transaction_data['customer'],
                        status='DRAFT',
                        cash_account=cash_account,
                        gl_journal_entry=journal_entry,
                        created_by=request.user
                    )
                
                # Note: Entries are created as DRAFT and can be posted manually
                # Users can review the receipt before posting via the UI
                print(f"✅ Created journal entry #{journal_entry.entry_number} - Status: {journal_entry.status}")
                print(f"✅ Created cash transaction #{transaction.transaction_number} - Status: {transaction.status}")
                print(f"💡 Receipt created as DRAFT - can be posted manually via UI")
                    
        except Exception as e:
            print(f"Error creating/posting cash transaction: {e}")
            # Continue with journal entry only if cash transaction fails
        
        # Return success response with calculations and status info
        journal_entry_status = journal_entry.status
        transaction_status = transaction.status if 'transaction' in locals() else 'Not Created'
        
        return Response({
            'success': True,
            'journal_entry_id': journal_entry.id,
            'entry_number': entry_number,
            'journal_entry_status': journal_entry_status,
            'cash_transaction_status': transaction_status,
            'calculations': {
                'base_amount': float(base_amount),
                'sales_tax_amount': float(total_sales_tax),
                'total_amount': float(total_amount),
                'tds_amount': float(tds_amount),
                'net_receipt': float(net_amount)
            },
            'message': f'Receipt created successfully as DRAFT. Entry #{entry_number} (Status: {journal_entry_status}) - Ready for review and posting'
        })
        
    except Exception as e:
        import traceback
        return Response({
            'error': f'Error creating receipt: {str(e)}',
            'traceback': traceback.format_exc()
        }, status=500) 