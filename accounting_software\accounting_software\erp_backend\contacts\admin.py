from django.contrib import admin
from .models import (
    Contact, Customer, Vendor, Employee
)


@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    list_display = ['name', 'contact_type', 'email', 'phone', 'created_at']
    list_filter = ['contact_type', 'created_at']
    search_fields = ['name', 'email', 'phone']
    ordering = ['name']


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = [
        'get_contact_name', 'customer_code', 'get_contact_email', 
        'get_contact_phone', 'credit_limit', 'payment_terms', 
        'customer_category', 'get_contact_active'
    ]
    list_filter = ['customer_category', 'payment_terms', 'tax_exempt']
    search_fields = [
        'contact__name', 'contact__email', 'contact__phone', 
        'customer_code', 'first_name', 'last_name', 'company_name'
    ]
    readonly_fields = ['contact']
    
    fieldsets = (
        ('Contact Information', {
            'fields': ('contact',)
        }),
        ('Customer Details', {
            'fields': ('customer_code', 'customer_category', 'credit_limit', 'payment_terms', 'discount_percentage', 'tax_exempt')
        }),
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'company_name', 'mobile')
        }),
        ('Billing Address', {
            'fields': ('billing_street', 'billing_city', 'billing_state', 'billing_postal_code', 'billing_country')
        }),
        ('Shipping Address', {
            'fields': (
                'shipping_same_as_billing', 'shipping_street', 'shipping_city', 
                'shipping_state', 'shipping_postal_code', 'shipping_country'
            )
        }),
    )
    
    def get_contact_name(self, obj):
        return obj.contact.name
    get_contact_name.short_description = 'Name'
    get_contact_name.admin_order_field = 'contact__name'
    
    def get_contact_email(self, obj):
        return obj.contact.email or '-'
    get_contact_email.short_description = 'Email'
    get_contact_email.admin_order_field = 'contact__email'
    
    def get_contact_phone(self, obj):
        return obj.contact.phone or obj.mobile or '-'
    get_contact_phone.short_description = 'Phone'
    
    def get_contact_active(self, obj):
        return '✅ Active'  # All contacts are considered active by default
    get_contact_active.short_description = 'Status'


@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = ['contact', 'get_contact_name', 'get_contact_email']
    search_fields = ['contact__name', 'contact__email']
    
    def get_contact_name(self, obj):
        return obj.contact.name
    get_contact_name.short_description = 'Name'
    
    def get_contact_email(self, obj):
        return obj.contact.email
    get_contact_email.short_description = 'Email'


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['contact', 'get_contact_name', 'position', 'department']
    list_filter = ['department', 'position']
    search_fields = ['contact__name', 'contact__email', 'position', 'department']
    
    def get_contact_name(self, obj):
        return obj.contact.name
    get_contact_name.short_description = 'Name'


# AuditLog admin temporarily removed 