from django.core.management.base import BaseCommand
from cms.models import CashTransaction
from gl.models import JournalEntry


class Command(BaseCommand):
    help = 'Fix cash transactions where transaction is POSTED but linked journal entry is still DRAFT'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Find mismatched transactions
        mismatched_transactions = CashTransaction.objects.filter(
            status='POSTED',
            gl_journal_entry__status='DRAFT'
        ).select_related('gl_journal_entry')
        
        self.stdout.write(f"Found {mismatched_transactions.count()} transactions with status mismatch")
        
        if mismatched_transactions.count() == 0:
            self.stdout.write(self.style.SUCCESS("No mismatched transactions found!"))
            return
        
        for transaction in mismatched_transactions:
            journal_entry = transaction.gl_journal_entry
            
            self.stdout.write(
                f"Transaction {transaction.transaction_number}: "
                f"Cash={transaction.status}, Journal={journal_entry.status}"
            )
            
            if not dry_run:
                try:
                    # Post the journal entry using the same user who posted the transaction
                    posted_by_user = transaction.posted_by or transaction.created_by
                    journal_entry.post_entry(posted_by_user)
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"✅ Posted journal entry {journal_entry.entry_number}"
                        )
                    )
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(
                            f"❌ Failed to post journal entry {journal_entry.entry_number}: {e}"
                        )
                    )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    "DRY RUN: No changes were made. Run without --dry-run to fix these transactions."
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Fixed {mismatched_transactions.count()} transactions"
                )
            ) 