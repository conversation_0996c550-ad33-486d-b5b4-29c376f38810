"""
Django Management Command: Setup Sample Chart of Accounts with Hierarchical Structure

This command creates a complete sample Chart of Accounts demonstrating:
- Header accounts for organization
- Parent-child relationships
- Proper account numbering
- Real-world account structure

Usage: python manage.py setup_sample_accounts
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from gl.models import AccountType, DetailType, Account

User = get_user_model()

class Command(BaseCommand):
    help = 'Set up sample Chart of Accounts with hierarchical structure'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing accounts before creating new ones',
        )
    
    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing accounts...')
            Account.objects.all().delete()
        
        # Get or create a system user for account creation
        admin_user, created = User.objects.get_or_create(
            username='system',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True
            }
        )
        
        self.stdout.write('Creating hierarchical Chart of Accounts...')
        
        # Get account types and detail types
        try:
            asset_type = AccountType.objects.get(name='Assets')
            liability_type = AccountType.objects.get(name='Liabilities')
            equity_type = AccountType.objects.get(name='Equity')
            revenue_type = AccountType.objects.get(name='Revenue')
            expense_type = AccountType.objects.get(name='Expenses')
            
            # Get detail types
            cash_detail = DetailType.objects.get(name='Cash and Bank')
            ar_detail = DetailType.objects.get(name='Accounts Receivable')
            inventory_detail = DetailType.objects.get(name='Inventory')
            ppe_detail = DetailType.objects.get(name='Fixed Assets')
            ap_detail = DetailType.objects.get(name='Accounts Payable')
            equity_detail = DetailType.objects.get(name='Owner Capital')
            sales_detail = DetailType.objects.get(name='Sales Revenue')
            expense_detail = DetailType.objects.get(name='Office Supplies')
            
        except (AccountType.DoesNotExist, DetailType.DoesNotExist) as e:
            self.stdout.write(
                self.style.ERROR(
                    f'Required account types or detail types not found: {e}\n'
                    'Please run: python manage.py setup_gl_data first'
                )
            )
            return
        
        # Define hierarchical account structure
        accounts_structure = [
            # ASSETS
            {
                'number': '1000',
                'name': 'ASSETS',
                'type': asset_type,
                'detail': cash_detail,  # Placeholder for header
                'is_header': True,
                'parent': None
            },
            {
                'number': '1100',
                'name': 'Current Assets',
                'type': asset_type,
                'detail': cash_detail,
                'is_header': True,
                'parent': '1000'
            },
            {
                'number': '1110',
                'name': 'Cash and Cash Equivalents',
                'type': asset_type,
                'detail': cash_detail,
                'is_header': False,
                'parent': '1100',
                'opening_balance': 50000.00
            },
            {
                'number': '1111',
                'name': 'Petty Cash',
                'type': asset_type,
                'detail': cash_detail,
                'is_header': False,
                'parent': '1110',
                'opening_balance': 500.00
            },
            {
                'number': '1112',
                'name': 'Business Checking Account',
                'type': asset_type,
                'detail': cash_detail,
                'is_header': False,
                'parent': '1110',
                'opening_balance': 25000.00
            },
            {
                'number': '1113',
                'name': 'Business Savings Account',
                'type': asset_type,
                'detail': cash_detail,
                'is_header': False,
                'parent': '1110',
                'opening_balance': 15000.00
            },
            {
                'number': '1120',
                'name': 'Accounts Receivable',
                'type': asset_type,
                'detail': ar_detail,
                'is_header': False,
                'parent': '1100',
                'opening_balance': 12500.00
            },
            {
                'number': '1130',
                'name': 'Inventory',
                'type': asset_type,
                'detail': inventory_detail,
                'is_header': False,
                'parent': '1100',
                'opening_balance': 8000.00
            },
            {
                'number': '1140',
                'name': 'Prepaid Expenses',
                'type': asset_type,
                'detail': expense_detail,
                'is_header': False,
                'parent': '1100',
                'opening_balance': 2500.00
            },
            
            # Fixed Assets
            {
                'number': '1200',
                'name': 'Fixed Assets',
                'type': asset_type,
                'detail': ppe_detail,
                'is_header': True,
                'parent': '1000'
            },
            {
                'number': '1210',
                'name': 'Equipment',
                'type': asset_type,
                'detail': ppe_detail,
                'is_header': False,
                'parent': '1200',
                'opening_balance': 25000.00
            },
            {
                'number': '1220',
                'name': 'Vehicles',
                'type': asset_type,
                'detail': ppe_detail,
                'is_header': False,
                'parent': '1200',
                'opening_balance': 35000.00
            },
            {
                'number': '1230',
                'name': 'Accumulated Depreciation',
                'type': asset_type,
                'detail': ppe_detail,
                'is_header': False,
                'parent': '1200',
                'opening_balance': -5000.00
            },
            
            # LIABILITIES
            {
                'number': '2000',
                'name': 'LIABILITIES',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': True,
                'parent': None
            },
            {
                'number': '2100',
                'name': 'Current Liabilities',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': True,
                'parent': '2000'
            },
            {
                'number': '2110',
                'name': 'Accounts Payable',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': False,
                'parent': '2100',
                'opening_balance': 8500.00
            },
            {
                'number': '2120',
                'name': 'Accrued Expenses',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': False,
                'parent': '2100',
                'opening_balance': 3200.00
            },
            {
                'number': '2130',
                'name': 'Short-term Notes Payable',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': False,
                'parent': '2100',
                'opening_balance': 15000.00
            },
            
            # Long-term Liabilities
            {
                'number': '2200',
                'name': 'Long-term Liabilities',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': True,
                'parent': '2000'
            },
            {
                'number': '2210',
                'name': 'Long-term Notes Payable',
                'type': liability_type,
                'detail': ap_detail,
                'is_header': False,
                'parent': '2200',
                'opening_balance': 50000.00
            },
            
            # EQUITY
            {
                'number': '3000',
                'name': 'EQUITY',
                'type': equity_type,
                'detail': equity_detail,
                'is_header': True,
                'parent': None
            },
            {
                'number': '3100',
                'name': 'Owner\'s Equity',
                'type': equity_type,
                'detail': equity_detail,
                'is_header': False,
                'parent': '3000',
                'opening_balance': 100000.00
            },
            {
                'number': '3200',
                'name': 'Retained Earnings',
                'type': equity_type,
                'detail': equity_detail,
                'is_header': False,
                'parent': '3000',
                'opening_balance': 45000.00
            },
            
            # REVENUE
            {
                'number': '4000',
                'name': 'REVENUE',
                'type': revenue_type,
                'detail': sales_detail,
                'is_header': True,
                'parent': None
            },
            {
                'number': '4100',
                'name': 'Sales Revenue',
                'type': revenue_type,
                'detail': sales_detail,
                'is_header': False,
                'parent': '4000'
            },
            {
                'number': '4200',
                'name': 'Service Revenue',
                'type': revenue_type,
                'detail': sales_detail,
                'is_header': False,
                'parent': '4000'
            },
            
            # EXPENSES
            {
                'number': '5000',
                'name': 'EXPENSES',
                'type': expense_type,
                'detail': expense_detail,
                'is_header': True,
                'parent': None
            },
            {
                'number': '5100',
                'name': 'Operating Expenses',
                'type': expense_type,
                'detail': expense_detail,
                'is_header': True,
                'parent': '5000'
            },
            {
                'number': '5110',
                'name': 'Rent Expense',
                'type': expense_type,
                'detail': expense_detail,
                'is_header': False,
                'parent': '5100'
            },
            {
                'number': '5120',
                'name': 'Utilities Expense',
                'type': expense_type,
                'detail': expense_detail,
                'is_header': False,
                'parent': '5100'
            },
            {
                'number': '5130',
                'name': 'Salaries and Wages',
                'type': expense_type,
                'detail': expense_detail,
                'is_header': False,
                'parent': '5100'
            },
            {
                'number': '5140',
                'name': 'Office Supplies',
                'type': expense_type,
                'detail': expense_detail,
                'is_header': False,
                'parent': '5100'
            },
        ]
        
        # Create accounts in order to establish parent-child relationships
        created_accounts = {}
        
        for account_data in accounts_structure:
            # Find parent account if specified
            parent_account = None
            if account_data['parent']:
                parent_account = created_accounts.get(account_data['parent'])
                if not parent_account:
                    self.stdout.write(
                        self.style.WARNING(
                            f"Parent account {account_data['parent']} not found for {account_data['number']}"
                        )
                    )
            
            # Create the account
            account = Account.objects.create(
                account_number=account_data['number'],
                account_name=account_data['name'],
                description=f"Sample {account_data['name']} account",
                account_type=account_data['type'],
                detail_type=account_data['detail'],
                parent_account=parent_account,
                is_header_account=account_data['is_header'],
                opening_balance=account_data.get('opening_balance', 0.00),
                is_active=True,
                created_by=admin_user
            )
            
            # Store for parent-child reference
            created_accounts[account_data['number']] = account
            
            self.stdout.write(f"Created: {account.account_number} - {account.account_name}")
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\nSuccessfully created {len(accounts_structure)} accounts with hierarchical structure!\n'
                'The Chart of Accounts now demonstrates:\n'
                '• Header accounts for organization\n'
                '• Parent-child relationships\n'
                '• Multi-level account hierarchy\n'
                '• Proper account numbering system'
            )
        ) 