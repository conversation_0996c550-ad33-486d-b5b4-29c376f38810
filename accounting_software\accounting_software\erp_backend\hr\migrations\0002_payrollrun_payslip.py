# Generated by Django 4.2.21 on 2025-06-01 01:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PayrollRun',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('run_date', models.DateTimeField(auto_now_add=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Completed', 'Completed'), ('Failed', 'Failed')], default='Pending', max_length=20)),
                ('total_employees_paid', models.IntegerField(default=0)),
                ('total_gross_pay', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_net_pay', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
            ],
        ),
        migrations.CreateModel(
            name='Payslip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pay_period_start_date', models.DateField()),
                ('pay_period_end_date', models.DateField()),
                ('base_pay', models.DecimalField(decimal_places=2, max_digits=10)),
                ('bonus', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('taxes', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('gross_pay', models.DecimalField(decimal_places=2, max_digits=10)),
                ('net_pay', models.DecimalField(decimal_places=2, max_digits=10)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payslips', to='hr.employee')),
                ('payroll_run', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payslips', to='hr.payrollrun')),
            ],
            options={
                'unique_together': {('payroll_run', 'employee')},
            },
        ),
    ]
