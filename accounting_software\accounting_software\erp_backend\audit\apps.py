"""
Audit Trail App Configuration

Registers signal handlers for automatic audit logging.
"""

from django.apps import AppConfig

class AuditConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'audit'
    verbose_name = 'Audit Trail System'
    
    def ready(self):
        """Import signal handlers when the app is ready."""
        import audit.signals  # This registers the signal handlers
        import audit.middleware  # This registers the authentication signals 