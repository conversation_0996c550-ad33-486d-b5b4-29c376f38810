import React, { useState } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Autocomplete,
  IconButton,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import ContactsService from '../../contacts/services/contacts.service';
import { VendorFormData } from '../../contacts/types/contacts.types';
import { usePaymentTerms } from '../../../contexts/PaymentTermsContext';

interface VendorFormProps {
  onClose: () => void;
  onSave: () => void;
  initialValues?: any;
}

const validationSchema = Yup.object({
  displayName: Yup.string().required('Display name is required'),
  email: Yup.string().email('Invalid email format'),
  phone: Yup.string(),
  billingAddress: Yup.object({
    country: Yup.string().required('Country is required'),
  }),
  paymentTerms: Yup.string().required('Payment terms are required'),
  leadTimeDays: Yup.number().min(0, 'Lead time must be positive').required('Lead time is required'),
  minimumOrderAmount: Yup.number().min(0, 'Minimum order amount must be positive'),
});

// No conversion needed - using the same interface

const VendorForm: React.FC<VendorFormProps> = ({ onClose, onSave, initialValues }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<string>('panel1');
  const { paymentTerms } = usePaymentTerms();

  const isEditMode = Boolean(initialValues?.id);

  const formik = useFormik<VendorFormData>({
    initialValues: {
      firstName: initialValues?.firstName || '',
      lastName: initialValues?.lastName || '',
      displayName: initialValues?.displayName || '',
      companyName: initialValues?.companyName || '',
      email: initialValues?.email || '',
      phone: initialValues?.phone || '',
      mobile: initialValues?.mobile || '',
      billingAddress: {
        street: initialValues?.billingAddress?.street || '',
        city: initialValues?.billingAddress?.city || '',
        state: initialValues?.billingAddress?.state || '',
        postalCode: initialValues?.billingAddress?.postalCode || '',
        country: initialValues?.billingAddress?.country || 'India',
      },
      shippingAddress: {
        sameAsBilling: initialValues?.shippingAddress?.sameAsBilling ?? true,
        street: initialValues?.shippingAddress?.street || '',
        city: initialValues?.shippingAddress?.city || '',
        state: initialValues?.shippingAddress?.state || '',
        postalCode: initialValues?.shippingAddress?.postalCode || '',
        country: initialValues?.shippingAddress?.country || 'India',
      },
      paymentTerms: initialValues?.paymentTerms || '1',
      creditLimit: initialValues?.creditLimit || 0,
      vendorCategory: initialValues?.vendorCategory || '',
      leadTimeDays: initialValues?.leadTimeDays || 7,
      minimumOrderAmount: initialValues?.minimumOrderAmount || 0,
      preferredVendor: initialValues?.preferredVendor || false,
      notes: initialValues?.notes || '',
      is_active: initialValues?.is_active ?? true,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError(null);

        // Add missing required fields
        const vendorData = {
          ...values,
          is_active: true
        };
        
        if (isEditMode) {
          await ContactsService.updateVendor(initialValues.id, vendorData);
        } else {
          await ContactsService.createVendor(vendorData);
        }

        onSave();
        onClose();
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to save vendor');
      } finally {
        setLoading(false);
      }
    },
  });

  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded ? panel : '');
  };

  const handleSameAsBillingChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const sameAsBilling = event.target.checked;
    formik.setFieldValue('shippingAddress.sameAsBilling', sameAsBilling);
    
    if (sameAsBilling) {
      formik.setFieldValue('shippingAddress.street', formik.values.billingAddress.street);
      formik.setFieldValue('shippingAddress.city', formik.values.billingAddress.city);
      formik.setFieldValue('shippingAddress.state', formik.values.billingAddress.state);
      formik.setFieldValue('shippingAddress.postalCode', formik.values.billingAddress.postalCode);
      formik.setFieldValue('shippingAddress.country', formik.values.billingAddress.country);
    }
  };

  return (
    <>
      <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {isEditMode ? 'Edit Vendor' : 'New Vendor'}
        <IconButton onClick={onClose} disabled={loading}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <form onSubmit={formik.handleSubmit}>
        <DialogContent dividers sx={{ p: 0 }}>
          {error && (
            <Alert severity="error" sx={{ m: 3, mb: 0 }}>
              {error}
            </Alert>
          )}

          {/* Basic Information */}
          <Accordion
            expanded={expanded === 'panel1'}
            onChange={handleAccordionChange('panel1')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <PersonIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Basic Information
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="displayName"
                    name="displayName"
                    label="Display Name *"
                    value={formik.values.displayName}
                    onChange={formik.handleChange}
                    error={formik.touched.displayName && Boolean(formik.errors.displayName)}
                    helperText={formik.touched.displayName && formik.errors.displayName}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="firstName"
                    name="firstName"
                    label="First Name"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="lastName"
                    name="lastName"
                    label="Last Name"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="companyName"
                    name="companyName"
                    label="Company Name"
                    value={formik.values.companyName}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="email"
                    name="email"
                    label="Email"
                    type="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="phone"
                    name="phone"
                    label="Phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="mobile"
                    name="mobile"
                    label="Mobile"
                    value={formik.values.mobile}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="vendorCategory"
                    name="vendorCategory"
                    label="Vendor Category"
                    value={formik.values.vendorCategory}
                    onChange={formik.handleChange}
                    placeholder="e.g., Technology, Office Supplies, Raw Materials"
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Billing Address */}
          <Accordion
            expanded={expanded === 'panel2'}
            onChange={handleAccordionChange('panel2')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <LocationIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Billing Address
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="billingAddress.street"
                    name="billingAddress.street"
                    label="Street Address"
                    value={formik.values.billingAddress.street}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="billingAddress.city"
                    name="billingAddress.city"
                    label="City"
                    value={formik.values.billingAddress.city}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="billingAddress.state"
                    name="billingAddress.state"
                    label="State/Province"
                    value={formik.values.billingAddress.state}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="billingAddress.postalCode"
                    name="billingAddress.postalCode"
                    label="Postal Code"
                    value={formik.values.billingAddress.postalCode}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Country *</InputLabel>
                    <Select
                      id="billingAddress.country"
                      name="billingAddress.country"
                      value={formik.values.billingAddress.country}
                      onChange={formik.handleChange}
                      label="Country *"
                    >
                      <MenuItem value="India">India</MenuItem>
                      <MenuItem value="United States">United States</MenuItem>
                      <MenuItem value="United Kingdom">United Kingdom</MenuItem>
                      <MenuItem value="Canada">Canada</MenuItem>
                      <MenuItem value="Australia">Australia</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Shipping Address */}
          <Accordion
            expanded={expanded === 'panel3'}
            onChange={handleAccordionChange('panel3')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <LocationIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Shipping Address
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formik.values.shippingAddress.sameAsBilling}
                        onChange={handleSameAsBillingChange}
                      />
                    }
                    label="Same as billing address"
                  />
                </Grid>
                
                {!formik.values.shippingAddress.sameAsBilling && (
                  <>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="shippingAddress.street"
                        name="shippingAddress.street"
                        label="Street Address"
                        value={formik.values.shippingAddress.street}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="shippingAddress.city"
                        name="shippingAddress.city"
                        label="City"
                        value={formik.values.shippingAddress.city}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="shippingAddress.state"
                        name="shippingAddress.state"
                        label="State/Province"
                        value={formik.values.shippingAddress.state}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="shippingAddress.postalCode"
                        name="shippingAddress.postalCode"
                        label="Postal Code"
                        value={formik.values.shippingAddress.postalCode}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Country</InputLabel>
                        <Select
                          id="shippingAddress.country"
                          name="shippingAddress.country"
                          value={formik.values.shippingAddress.country}
                          onChange={formik.handleChange}
                          label="Country"
                        >
                          <MenuItem value="India">India</MenuItem>
                          <MenuItem value="United States">United States</MenuItem>
                          <MenuItem value="United Kingdom">United Kingdom</MenuItem>
                          <MenuItem value="Canada">Canada</MenuItem>
                          <MenuItem value="Australia">Australia</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Financial Information */}
          <Accordion
            expanded={expanded === 'panel4'}
            onChange={handleAccordionChange('panel4')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <PaymentIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Financial Information
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    options={paymentTerms}
                    getOptionLabel={(option) => option.name}
                    value={paymentTerms.find(term => term.id === parseInt(formik.values.paymentTerms || '1')) || null}
                    onChange={(event, newValue) => {
                      formik.setFieldValue('paymentTerms', newValue ? newValue.id.toString() : '1');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Payment Terms"
                        error={formik.touched.paymentTerms && Boolean(formik.errors.paymentTerms)}
                        helperText={formik.touched.paymentTerms && formik.errors.paymentTerms}
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="creditLimit"
                    name="creditLimit"
                    label="Credit Limit"
                    type="number"
                    value={formik.values.creditLimit}
                    onChange={formik.handleChange}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="minimumOrderAmount"
                    name="minimumOrderAmount"
                    label="Minimum Order Amount"
                    type="number"
                    value={formik.values.minimumOrderAmount}
                    onChange={formik.handleChange}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Vendor Settings */}
          <Accordion
            expanded={expanded === 'panel5'}
            onChange={handleAccordionChange('panel5')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <SettingsIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Vendor Settings
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="leadTimeDays"
                    name="leadTimeDays"
                    label="Lead Time (Days) *"
                    type="number"
                    value={formik.values.leadTimeDays}
                    onChange={formik.handleChange}
                    error={formik.touched.leadTimeDays && Boolean(formik.errors.leadTimeDays)}
                    helperText={formik.touched.leadTimeDays && formik.errors.leadTimeDays}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        id="preferredVendor"
                        name="preferredVendor"
                        checked={formik.values.preferredVendor}
                        onChange={formik.handleChange}
                      />
                    }
                    label="Preferred Vendor"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="notes"
                    name="notes"
                    label="Notes"
                    multiline
                    rows={3}
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    placeholder="Additional notes about this vendor..."
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Saving...' : isEditMode ? 'Update Vendor' : 'Create Vendor'}
          </Button>
        </DialogActions>
      </form>
    </>
  );
};

export default VendorForm; 
