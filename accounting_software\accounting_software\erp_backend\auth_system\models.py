"""
Authentication and Authorization Models for ERP System

This module contains role-based access control models following the ERD:
- Roles
- Role Permissions
- Extended User Profile
"""

from django.db import models
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType


class Role(models.Model):
    """
    Roles for role-based access control
    Following ERD: roles table
    """
    role_id = models.AutoField(primary_key=True)
    role_name = models.CharField(max_length=100, unique=True, help_text="Name of the role")
    description = models.TextField(blank=True, help_text="Description of the role")
    
    # Role settings
    is_active = models.Bo<PERSON>anField(default=True, help_text="Whether this role is active")
    is_system_role = models.BooleanField(default=False, help_text="Whether this is a system-defined role")
    
    # Hierarchy
    parent_role = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='child_roles',
        help_text="Parent role for role hierarchy"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='roles_created',
        null=True,
        blank=True
    )
    
    class Meta:
        db_table = 'roles'
        ordering = ['role_name']
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'
    
    def __str__(self):
        return self.role_name
    
    def get_all_permissions(self):
        """Get all permissions for this role including inherited ones"""
        permissions = set()
        
        # Get direct permissions
        for role_perm in self.role_permissions.filter(is_active=True):
            permissions.add(role_perm.permission)
        
        # Get inherited permissions from parent roles
        if self.parent_role:
            permissions.update(self.parent_role.get_all_permissions())
        
        return list(permissions)


class RolePermission(models.Model):
    """
    Permissions assigned to roles
    Following ERD: role_permissions table
    """
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='role_permissions')
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE, related_name='role_permissions')
    
    # Permission settings
    is_active = models.BooleanField(default=True, help_text="Whether this permission is active")
    granted_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='permissions_granted',
        help_text="User who granted this permission"
    )
    granted_at = models.DateTimeField(auto_now_add=True)
    
    # Additional constraints
    can_delegate = models.BooleanField(
        default=False,
        help_text="Whether users with this role can delegate this permission"
    )
    
    class Meta:
        db_table = 'role_permissions'
        unique_together = ['role', 'permission']
        verbose_name = 'Role Permission'
        verbose_name_plural = 'Role Permissions'
    
    def __str__(self):
        return f"{self.role.role_name} - {self.permission.name}"


class UserProfile(models.Model):
    """
    Extended user profile to match ERD users table structure
    Following ERD: users table (extending Django's User model)
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    
    # Additional user information
    full_name = models.CharField(max_length=200, blank=True, help_text="Full display name")
    employee_id = models.CharField(max_length=50, blank=True, unique=True, help_text="Employee ID")
    department = models.CharField(max_length=100, blank=True, help_text="Department")
    position = models.CharField(max_length=100, blank=True, help_text="Job position")
    
    # Contact information
    phone = models.CharField(max_length=20, blank=True)
    mobile = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    
    # Role assignment
    role = models.ForeignKey(
        Role,
        on_delete=models.PROTECT,
        related_name='users',
        help_text="Primary role for this user"
    )
    
    # Additional roles (many-to-many for multiple role assignment)
    additional_roles = models.ManyToManyField(
        Role,
        blank=True,
        related_name='additional_users',
        help_text="Additional roles assigned to this user"
    )
    
    # User settings
    is_active = models.BooleanField(default=True, help_text="Whether user account is active")
    password_expires_at = models.DateTimeField(null=True, blank=True)
    last_password_change = models.DateTimeField(null=True, blank=True)
    failed_login_attempts = models.PositiveIntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    
    # Preferences
    timezone = models.CharField(max_length=50, default='UTC')
    language = models.CharField(max_length=10, default='en')
    date_format = models.CharField(max_length=20, default='YYYY-MM-DD')
    
    # Audit trail
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='user_profiles_created'
    )
    
    class Meta:
        db_table = 'user_profiles'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
    
    def __str__(self):
        return f"{self.user.username} - {self.full_name or self.user.get_full_name()}"
    
    def get_all_roles(self):
        """Get all roles assigned to this user"""
        roles = [self.role] if self.role else []
        roles.extend(self.additional_roles.all())
        return roles
    
    def get_all_permissions(self):
        """Get all permissions for this user from all assigned roles"""
        permissions = set()
        
        for role in self.get_all_roles():
            permissions.update(role.get_all_permissions())
        
        return list(permissions)
    
    def has_permission(self, permission_codename):
        """Check if user has a specific permission"""
        user_permissions = self.get_all_permissions()
        return any(perm.codename == permission_codename for perm in user_permissions)


# Signal to create UserProfile when User is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create UserProfile when User is created"""
    if created:
        # Try to assign a default role
        default_role = Role.objects.filter(role_name='Employee', is_active=True).first()
        
        # Generate a unique employee_id
        employee_id = f"EMP-{instance.id:04d}"
        
        UserProfile.objects.create(
            user=instance,
            full_name=instance.get_full_name(),
            employee_id=employee_id,
            role=default_role
        )

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save UserProfile when User is saved"""
    if hasattr(instance, 'profile'):
        instance.profile.save()


class ModulePermission(models.Model):
    """
    Custom permissions for ERP modules
    This extends Django's built-in permission system for ERP-specific needs
    """
    
    MODULE_CHOICES = [
        ('GL', 'General Ledger'),
        ('AR', 'Accounts Receivable'),
        ('AP', 'Accounts Payable'),
        ('SALES', 'Sales Management'),
        ('PURCHASE', 'Purchase Management'),
        ('INVENTORY', 'Inventory Management'),
        ('HR', 'Human Resources'),
        ('CMS', 'Cash Management'),
        ('REPORTS', 'Reports & Analytics'),
        ('ADMIN', 'System Administration'),
    ]
    
    PERMISSION_TYPES = [
        ('VIEW', 'View'),
        ('CREATE', 'Create'),
        ('EDIT', 'Edit'),
        ('DELETE', 'Delete'),
        ('APPROVE', 'Approve'),
        ('POST', 'Post Transactions'),
        ('REVERSE', 'Reverse Transactions'),
        ('EXPORT', 'Export Data'),
        ('IMPORT', 'Import Data'),
        ('ADMIN', 'Administrative Access'),
    ]
    
    module = models.CharField(max_length=20, choices=MODULE_CHOICES)
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES)
    description = models.CharField(max_length=200)
    
    # Link to Django permission
    django_permission = models.OneToOneField(
        Permission,
        on_delete=models.CASCADE,
        related_name='module_permission'
    )
    
    # Risk level
    risk_level = models.CharField(
        max_length=10,
        choices=[
            ('LOW', 'Low Risk'),
            ('MEDIUM', 'Medium Risk'),
            ('HIGH', 'High Risk'),
            ('CRITICAL', 'Critical Risk'),
        ],
        default='LOW'
    )
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'module_permissions'
        unique_together = ['module', 'permission_type']
        verbose_name = 'Module Permission'
        verbose_name_plural = 'Module Permissions'
        ordering = ['module', 'permission_type']
    
    def __str__(self):
        return f"{self.get_module_display()} - {self.get_permission_type_display()}"
    
    def save(self, *args, **kwargs):
        # Create corresponding Django permission if it doesn't exist
        if not self.django_permission_id:
            content_type = ContentType.objects.get_for_model(self)
            codename = f"{self.module.lower()}_{self.permission_type.lower()}"
            name = f"Can {self.get_permission_type_display()} {self.get_module_display()}"
            
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                content_type=content_type,
                defaults={'name': name}
            )
            self.django_permission = permission
        
        super().save(*args, **kwargs) 