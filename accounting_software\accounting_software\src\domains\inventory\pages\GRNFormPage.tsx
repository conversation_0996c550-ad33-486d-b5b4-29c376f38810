import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Alert,
  CircularProgress,
  Typography,
} from '@mui/material';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import GRNForm from '../components/GRNForm';
import { grnService } from '../services/grn.service';

const GRNFormPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initialValues, setInitialValues] = useState<any>(null);

  const isEditMode = Boolean(id && id !== 'new');

  useEffect(() => {
    if (isEditMode) {
      loadGRN();
    }
  }, [id, isEditMode]);

  const loadGRN = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      const grn = await grnService.getGRN(parseInt(id));
      setInitialValues(grn);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRN');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = () => {
    navigate('/dashboard/inventory/grn');
  };

  const handleCancel = () => {
    navigate('/dashboard/inventory/grn');
  };

  if (loading) {
    return (
      <PageContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            {isEditMode ? 'Edit Goods Receipt Note' : 'New Goods Receipt Note'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {isEditMode ? 'Update goods receipt details' : 'Create a new goods receipt note'}
          </Typography>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 0, overflow: 'hidden' }}>
        <GRNForm
          onClose={handleCancel}
          onSave={handleSave}
          initialValues={initialValues}
        />
      </Paper>
    </PageContainer>
  );
};

export default GRNFormPage; 