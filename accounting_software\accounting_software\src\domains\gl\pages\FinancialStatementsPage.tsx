import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Alert,
  AlertTitle,
  Chip,
  Divider,
  Grid,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
  Stack,
  Avatar,
  ToggleButton,
  ToggleButtonGroup,
  Tab,
  Tabs,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  AccountBalance as AccountBalanceIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Balance as BalanceIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  CalendarToday as CalendarTodayIcon,
  CurrencyExchange as CurrencyExchangeIcon,
  Assessment as AssessmentIcon,
  Receipt as ReceiptIcon,
  MonetizationOn as MonetizationOnIcon,
  TableChart as TableChartIcon,
  CompareArrows as CompareArrowsIcon,
  DateRange as DateRangeIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrencyInfo } from '../hooks/useCurrencyInfo';
import { 
  loadFinancialStatements, 
  FinancialStatementsParams, 
  FinancialStatementsResponse,
  BalanceSheetData,
  IncomeStatementData,
  CashFlowData,
  BalanceSheetAccount,
  IncomeStatementAccount,
  CashFlowAccount 
} from '../../../services/gl.service';

// Types for period selection
type PeriodType = 'fiscal_year' | 'calendar_year' | 'custom';
type StatementType = 'all' | 'balance_sheet' | 'income_statement' | 'cash_flow';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const CustomTabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`financial-tabpanel-${index}`}
      aria-labelledby={`financial-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const FinancialStatementsPage: React.FC = () => {
  // State management
  const [financialData, setFinancialData] = useState<FinancialStatementsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  
  // Filter controls
  const [selectedPeriodType, setSelectedPeriodType] = useState<PeriodType>('fiscal_year');
  const [selectedYear, setSelectedYear] = useState<number>(dayjs().year());
  const [customDateFrom, setCustomDateFrom] = useState<Dayjs>(dayjs().startOf('year'));
  const [customDateTo, setCustomDateTo] = useState<Dayjs>(dayjs());
  const [selectedCurrency, setSelectedCurrency] = useState<'functional' | 'reporting'>('functional');
  const [includeComparatives, setIncludeComparatives] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  
  // Export dialogs
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  
  // Get currency information
  const { currencyInfo, loading: currencyLoading, error: currencyError } = useCurrencyInfo();

  // Load financial statements
  const loadStatements = async () => {
    setLoading(true);
    setError('');
    
    try {
      const params: FinancialStatementsParams = {
        statement_type: 'all',
        period_type: selectedPeriodType,
        currency: selectedCurrency,
        include_comparatives: includeComparatives,
      };

      if (selectedPeriodType === 'custom') {
        params.from_date = customDateFrom.format('YYYY-MM-DD');
        params.to_date = customDateTo.format('YYYY-MM-DD');
      } else {
        params.year = selectedYear;
      }

      console.log('Loading financial statements with params:', params);
      const data = await loadFinancialStatements(params);
      setFinancialData(data);
      console.log('Financial statements loaded successfully:', data);
    } catch (err) {
      console.error('Error loading financial statements:', err);
      setError(err instanceof Error ? err.message : 'Failed to load financial statements');
    } finally {
      setLoading(false);
    }
  };

  // Load data when filters change
  useEffect(() => {
    loadStatements();
  }, [selectedPeriodType, selectedYear, customDateFrom, customDateTo, selectedCurrency, includeComparatives]);

  // Format currency
  const formatCurrency = (amount: number | null | undefined, showSign = false) => {
    if (amount === null || amount === undefined) return '-';
    if (!currencyInfo) return '0.00';
    
    const currency = selectedCurrency === 'functional' 
      ? currencyInfo.functional_currency 
      : currencyInfo.reporting_currency;
    const locale = currency === 'INR' ? 'en-IN' : 'en-US';
    
    const formatted = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(Math.abs(amount));

    if (showSign && amount < 0) {
      return `(${formatted})`;
    }
    
    return formatted;
  };

  // Get current currency symbol
  const getCurrentCurrencySymbol = () => {
    if (!currencyInfo) return '$';
    return selectedCurrency === 'functional' 
      ? currencyInfo.functional_currency_symbol 
      : currencyInfo.reporting_currency_symbol;
  };

  // Format percentage safely
  const formatPercentage = (percentage: number | null | undefined) => {
    if (percentage === null || percentage === undefined) return '-';
    return `${percentage.toFixed(1)}%`;
  };

  // Export functions
  const handleExportCSV = (statementType: string, data: any[]) => {
    if (!data || data.length === 0) return;

    let csvContent = '';
    let filename = '';

    if (statementType === 'balance_sheet') {
      csvContent = [
        ['Account Number', 'Account Name', 'Account Type', 'Balance', ...(includeComparatives ? ['Previous Balance', 'Variance'] : [])].join(','),
        ...data.map(account => [
          account.account_number,
          `"${account.account_name}"`,
          account.account_type,
          (account.balance || 0).toFixed(2),
          ...(includeComparatives ? [
            (account.comparative_balance || 0).toFixed(2),
            (account.variance || 0).toFixed(2)
          ] : [])
        ].join(','))
      ].join('\n');
      filename = `balance-sheet-${selectedPeriodType}-${selectedYear}.csv`;
    } else if (statementType === 'income_statement') {
      csvContent = [
        ['Account Number', 'Account Name', 'Account Type', 'Period Total', ...(includeComparatives ? ['Previous Period', 'Variance'] : [])].join(','),
        ...data.map(account => [
          account.account_number,
          `"${account.account_name}"`,
          account.account_type,
          (account.period_total || 0).toFixed(2),
          ...(includeComparatives ? [
            (account.comparative_total || 0).toFixed(2),
            (account.variance || 0).toFixed(2)
          ] : [])
        ].join(','))
      ].join('\n');
      filename = `income-statement-${selectedPeriodType}-${selectedYear}.csv`;
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handlePrint = () => {
    window.print();
  };

  // Get period display string
  const getPeriodDisplayString = (): string => {
    if (!financialData) return '';
    
    const startDate = dayjs(financialData.period_start);
    const endDate = dayjs(financialData.period_end);
    
    if (selectedPeriodType === 'fiscal_year') {
      return `Fiscal Year ${selectedYear} (${startDate.format('MMM D, YYYY')} - ${endDate.format('MMM D, YYYY')})`;
    } else if (selectedPeriodType === 'calendar_year') {
      return `Calendar Year ${selectedYear}`;
    } else {
      return `${startDate.format('MMM D, YYYY')} - ${endDate.format('MMM D, YYYY')}`;
    }
  };

  // Handle click on "Profit for the year" to switch to Income Statement
  const handleProfitForYearClick = () => {
    console.log('🔗 Switching to Income Statement tab to show profit/loss details');
    setActiveTab(1); // Switch to Income Statement tab (index 1)
    
    // Scroll to top to ensure user sees the Income Statement
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Render Professional Balance Sheet
  const renderBalanceSheet = () => {
    if (!financialData?.balance_sheet) return null;

    const { balance_sheet } = financialData;

    const renderProfessionalAccountSection = (
      title: string, 
      accounts: BalanceSheetAccount[], 
      total: number, 
      color: string,
      subtitle?: string
    ) => (
      <Card sx={{ mb: 2, border: '1px solid', borderColor: 'divider' }}>
        <CardContent sx={{ pb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 700, color: color }}>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  ({subtitle})
                </Typography>
              )}
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 700, color: color }}>
              {formatCurrency(total)}
            </Typography>
          </Box>
          
          {accounts.length > 0 && (
            <TableContainer>
              <Table size="small">
                <TableBody>
                  {accounts.map((account) => (
                    <TableRow 
                      key={account.account_id} 
                      hover 
                      sx={{ 
                        '&:last-child td': { border: 0 },
                        ...(account.is_virtual && {
                          backgroundColor: 'action.hover',
                          '&:hover': {
                            backgroundColor: 'action.selected',
                          }
                        })
                      }}
                    >
                      <TableCell sx={{ border: 0, py: 0.5, pl: 2 }}>
                        <Typography 
                          variant="body2" 
                          color={account.is_virtual ? "primary.main" : "text.secondary"}
                          sx={{ fontStyle: account.is_virtual ? 'italic' : 'normal' }}
                        >
                          {account.is_virtual ? '(Virtual)' : account.account_number}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ border: 0, py: 0.5 }}>
                        {account.is_virtual && account.virtual_type === 'profit_for_year' ? (
                          <Box 
                            sx={{ 
                              cursor: 'pointer',
                              '&:hover': {
                                backgroundColor: 'action.hover',
                                borderRadius: 1,
                                p: 0.5,
                                m: -0.5
                              }
                            }}
                            onClick={() => handleProfitForYearClick()}
                          >
                            <Typography 
                              variant="body2"
                              sx={{ 
                                fontWeight: 600,
                                color: 'primary.main',
                                textDecoration: 'underline',
                                '&:hover': {
                                  color: 'primary.dark'
                                }
                              }}
                            >
                              {account.account_name}
                            </Typography>
                            <Typography 
                              variant="caption" 
                              color="primary.main"
                              sx={{ fontStyle: 'italic' }}
                            >
                              Click to view Income Statement details
                            </Typography>
                          </Box>
                        ) : (
                          <Box>
                            <Typography 
                              variant="body2"
                              sx={{ 
                                fontWeight: account.is_virtual ? 600 : 400,
                                color: account.is_virtual ? 'primary.main' : 'inherit'
                              }}
                            >
                              {account.account_name}
                            </Typography>
                            <Typography 
                              variant="caption" 
                              color={account.is_virtual ? "primary.main" : "text.secondary"}
                              sx={{ fontStyle: account.is_virtual ? 'italic' : 'normal' }}
                            >
                              {account.is_virtual ? 'Net Income for the period' : account.detail_type}
                            </Typography>
                          </Box>
                        )}
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ 
                          border: 0, 
                          py: 0.5, 
                          fontWeight: account.is_virtual ? 600 : 500,
                          color: account.is_virtual ? 'primary.main' : 'inherit'
                        }}
                      >
                        {formatCurrency(account.balance)}
                      </TableCell>
                      {includeComparatives && (
                        <>
                          <TableCell align="right" sx={{ border: 0, py: 0.5 }}>
                            {account.comparative_balance !== undefined 
                              ? formatCurrency(account.comparative_balance) 
                              : '-'}
                          </TableCell>
                          <TableCell align="right" sx={{ 
                            border: 0, py: 0.5,
                            color: (account.variance || 0) >= 0 ? 'success.main' : 'error.main',
                            fontWeight: 500
                          }}>
                            {account.variance !== undefined 
                              ? formatCurrency(account.variance, true) 
                              : '-'}
                          </TableCell>
                        </>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    );

    return (
      <Box>
        {/* Balance Sheet Header */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            {financialData.company_name}
          </Typography>
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
            Balance Sheet
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            As of {dayjs(balance_sheet.as_of_date).format('MMMM D, YYYY')}
          </Typography>
          {includeComparatives && balance_sheet.comparative_date && (
            <Typography variant="subtitle2" color="text.secondary">
              Compared to {dayjs(balance_sheet.comparative_date).format('MMMM D, YYYY')}
            </Typography>
          )}
        </Box>

        {/* Balance Check Alert */}
        <Alert 
          severity={balance_sheet.is_balanced ? "success" : "error"} 
          sx={{ mb: 3 }}
          icon={balance_sheet.is_balanced ? <CheckCircleIcon /> : <ErrorIcon />}
        >
          <AlertTitle>
            {balance_sheet.is_balanced ? "Balance Sheet is Balanced" : "Balance Sheet is NOT Balanced"}
          </AlertTitle>
          Total Assets: {formatCurrency(balance_sheet.total_assets)} | 
          Total Liabilities & Equity: {formatCurrency(balance_sheet.total_liabilities_and_equity)}
          {!balance_sheet.is_balanced && (
            <div>Difference: {formatCurrency(balance_sheet.total_assets - balance_sheet.total_liabilities_and_equity)}</div>
          )}
        </Alert>

        {/* Profit/Loss for the Year Info */}
        {balance_sheet.current_year_net_income !== undefined && (
          <Alert severity={balance_sheet.current_year_net_income >= 0 ? "success" : "warning"} sx={{ mb: 3 }}>
            <AlertTitle>
              {balance_sheet.current_year_net_income >= 0 ? "Profit for the Year" : "Loss for the Year"}
            </AlertTitle>
            <Typography variant="body2">
              {balance_sheet.current_year_net_income >= 0 ? "Net profit" : "Net loss"} of <strong>{formatCurrency(Math.abs(balance_sheet.current_year_net_income))}</strong> 
              {' '}(from {dayjs(balance_sheet.fiscal_year_start).format('MMM D')} to {dayjs(balance_sheet.as_of_date).format('MMM D, YYYY')}) 
              is displayed as "{balance_sheet.current_year_net_income >= 0 ? "Profit" : "Loss"} for the year" in the Equity section.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
              💡 <strong>Tip:</strong> Click on "{balance_sheet.current_year_net_income >= 0 ? "Profit" : "Loss"} for the year" in the Balance Sheet to view the detailed Income Statement breakdown.
            </Typography>
            <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
              {balance_sheet.current_year_net_income >= 0 
                ? '📈 Profit increases total equity and strengthens financial position' 
                : '📉 Loss decreases total equity but will be reflected accurately'}
              {' '}• This follows QuickBooks-style presentation for professional financial statements.
            </Typography>
          </Alert>
        )}

        {/* Professional Balance Sheet Layout */}
        <Grid container spacing={3}>
          
          {/* LEFT SIDE - ASSETS */}
          <Grid item xs={12} md={6}>
            <Typography variant="h5" sx={{ fontWeight: 700, mb: 3, color: 'success.main' }}>
              ASSETS
            </Typography>
            
            {/* Current Assets */}
            {renderProfessionalAccountSection(
              'Current Assets', 
              balance_sheet.current_assets, 
              balance_sheet.total_current_assets, 
              'success.main',
              'Liquid within 1 year'
            )}
            
            {/* Non-Current Assets */}
            {renderProfessionalAccountSection(
              'Non-Current Assets', 
              balance_sheet.non_current_assets, 
              balance_sheet.total_non_current_assets, 
              'success.dark',
              'Long-term assets'
            )}
            
            {/* Total Assets */}
            <Card sx={{ bgcolor: 'success.50', border: '2px solid', borderColor: 'success.main' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: 'success.main' }}>
                    TOTAL ASSETS
                  </Typography>
                  <Typography variant="h5" sx={{ fontWeight: 700, color: 'success.main' }}>
                    {formatCurrency(balance_sheet.total_assets)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          {/* RIGHT SIDE - LIABILITIES & EQUITY */}
          <Grid item xs={12} md={6}>
            <Typography variant="h5" sx={{ fontWeight: 700, mb: 3, color: 'warning.main' }}>
              LIABILITIES & EQUITY
            </Typography>
            
            {/* Current Liabilities */}
            {renderProfessionalAccountSection(
              'Current Liabilities', 
              balance_sheet.current_liabilities, 
              balance_sheet.total_current_liabilities, 
              'error.main',
              'Due within 1 year'
            )}
            
            {/* Non-Current Liabilities */}
            {renderProfessionalAccountSection(
              'Non-Current Liabilities', 
              balance_sheet.non_current_liabilities, 
              balance_sheet.total_non_current_liabilities, 
              'error.dark',
              'Long-term obligations'
            )}
            
            {/* Equity */}
            {renderProfessionalAccountSection(
              'Equity', 
              balance_sheet.equity, 
              balance_sheet.total_equity, 
              'info.main',
              'Owners equity'
            )}
            
            {/* Total Liabilities & Equity */}
            <Card sx={{ bgcolor: 'warning.50', border: '2px solid', borderColor: 'warning.main' }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 700, color: 'warning.main' }}>
                    TOTAL LIABILITIES & EQUITY
                  </Typography>
                  <Typography variant="h5" sx={{ fontWeight: 700, color: 'warning.main' }}>
                    {formatCurrency(balance_sheet.total_liabilities_and_equity)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Professional Financial Ratios & Analysis */}
        <Box sx={{ mt: 3 }}>
          {/* Liquidity Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'primary.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                <MonetizationOnIcon sx={{ mr: 1 }} />
                Liquidity Ratios
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {formatCurrency(balance_sheet.working_capital)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Working Capital
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Current Assets - Current Liabilities
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {(() => {
                        const currentLiabilities = balance_sheet.total_current_liabilities;
                        
                        // If current liabilities are zero or very small, show N/A instead of infinite ratio
                        if (!currentLiabilities || currentLiabilities < 0.01) {
                          return 'N/A';
                        }
                        
                        const currentRatio = balance_sheet.total_current_assets / currentLiabilities;
                        
                        // Cap the ratio at a reasonable maximum to prevent display issues
                        return currentRatio > 999 ? '999+' : currentRatio.toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Current Ratio
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Current Assets ÷ Current Liabilities
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.dark' }}>
                      {(() => {
                        // Calculate Quick Ratio properly
                        const currentLiabilities = balance_sheet.total_current_liabilities;
                        
                        // If current liabilities are zero or very small, show N/A instead of infinite ratio
                        if (!currentLiabilities || currentLiabilities < 0.01) {
                          return 'N/A';
                        }
                        
                        // Quick Assets = Current Assets - Inventory (estimate using cash, receivables, etc.)
                        // Use a more conservative estimate: assume 60% of current assets are quick assets
                        const quickAssets = balance_sheet.total_current_assets * 0.6;
                        const quickRatio = quickAssets / currentLiabilities;
                        
                        // Cap the ratio at a reasonable maximum (e.g., 999) to prevent display issues
                        return quickRatio > 999 ? '999+' : quickRatio.toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Quick Ratio (Est.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      (Current Assets - Inventory) ÷ Current Liabilities
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Leverage Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'warning.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'warning.main', display: 'flex', alignItems: 'center' }}>
                <BalanceIcon sx={{ mr: 1 }} />
                Leverage Ratios
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                      {((balance_sheet.total_liabilities / Math.max(balance_sheet.total_assets, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Debt to Assets
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Liabilities ÷ Total Assets
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'error.main' }}>
                      {(() => {
                        const totalEquity = balance_sheet.total_equity;
                        
                        // If equity is zero or very small, show N/A instead of infinite ratio
                        if (!totalEquity || Math.abs(totalEquity) < 0.01) {
                          return 'N/A';
                        }
                        
                        const debtToEquity = balance_sheet.total_liabilities / totalEquity;
                        
                        // Cap the ratio at a reasonable maximum to prevent display issues
                        return Math.abs(debtToEquity) > 999 ? '999+' : debtToEquity.toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Debt to Equity
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Liabilities ÷ Total Equity
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                      {((balance_sheet.total_equity / Math.max(balance_sheet.total_assets, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Equity Ratio
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Equity ÷ Total Assets
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {(() => {
                        const totalEquity = balance_sheet.total_equity;
                        
                        // If equity is zero or very small, show N/A instead of infinite ratio
                        if (!totalEquity || Math.abs(totalEquity) < 0.01) {
                          return 'N/A';
                        }
                        
                        const equityMultiplier = balance_sheet.total_assets / totalEquity;
                        
                        // Cap the ratio at a reasonable maximum to prevent display issues
                        return Math.abs(equityMultiplier) > 999 ? '999+' : equityMultiplier.toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Equity Multiplier
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Assets ÷ Total Equity
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Efficiency & Structure Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'success.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'success.main', display: 'flex', alignItems: 'center' }}>
                <AssessmentIcon sx={{ mr: 1 }} />
                Financial Structure Analysis
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {((balance_sheet.total_current_assets / Math.max(balance_sheet.total_assets, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Current Assets %
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Current Assets ÷ Total Assets
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'error.main' }}>
                      {((balance_sheet.total_current_liabilities / Math.max(balance_sheet.total_liabilities, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Current Liabilities %
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Current Liabilities ÷ Total Liabilities
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {((balance_sheet.total_non_current_assets / Math.max(balance_sheet.total_assets, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Fixed Assets %
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Non-Current Assets ÷ Total Assets
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </Box>
    );
  };

  // Render Professional Income Statement
  const renderIncomeStatement = () => {
    if (!financialData?.income_statement) return null;

    const { income_statement } = financialData;

    const renderProfessionalIncomeSection = (
      title: string, 
      accounts: IncomeStatementAccount[], 
      total: number, 
      color: string,
      isRevenue: boolean = false
    ) => (
      <Card sx={{ mb: 2, border: '1px solid', borderColor: 'divider' }}>
        <CardContent sx={{ pb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 700, color: color }}>
                {title}
              </Typography>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 700, color: color }}>
              {formatCurrency(total)}
            </Typography>
          </Box>
          
          {accounts.length > 0 && (
            <TableContainer>
              <Table size="small">
                <TableBody>
                  {accounts.map((account) => (
                    <TableRow key={account.account_id} hover sx={{ '&:last-child td': { border: 0 } }}>
                      <TableCell sx={{ border: 0, py: 0.5, pl: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          {account.account_number}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ border: 0, py: 0.5 }}>
                        <Typography variant="body2">
                          {account.account_name}
                        </Typography>
                      </TableCell>
                      <TableCell align="right" sx={{ border: 0, py: 0.5, fontWeight: 500 }}>
                        {formatCurrency(account.period_total)}
                      </TableCell>
                      {includeComparatives && (
                        <>
                          <TableCell align="right" sx={{ border: 0, py: 0.5 }}>
                            {account.comparative_total !== undefined 
                              ? formatCurrency(account.comparative_total) 
                              : '-'}
                          </TableCell>
                          <TableCell align="right" sx={{ 
                            border: 0, py: 0.5,
                            color: (account.variance || 0) >= 0 ? 'success.main' : 'error.main',
                            fontWeight: 500
                          }}>
                            {account.variance !== undefined 
                              ? formatCurrency(account.variance, true) 
                              : '-'}
                          </TableCell>
                        </>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>
    );

    return (
      <Box>
        {/* Income Statement Header */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            {financialData.company_name}
          </Typography>
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
            Income Statement
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {dayjs(income_statement.period_start).format('MMMM D, YYYY')} to {dayjs(income_statement.period_end).format('MMMM D, YYYY')}
          </Typography>
          {includeComparatives && income_statement.comparative_start && (
            <Typography variant="subtitle2" color="text.secondary">
              Compared to {dayjs(income_statement.comparative_start).format('MMMM D, YYYY')} to {dayjs(income_statement.comparative_end).format('MMMM D, YYYY')}
            </Typography>
          )}
        </Box>

        {/* Performance Summary */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {formatCurrency(income_statement.total_revenue)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Revenue
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.main' }}>
                    <TrendingDownIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {formatCurrency(income_statement.total_expenses)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Expenses
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: income_statement.net_income >= 0 ? 'success.main' : 'error.main' }}>
                    <MonetizationOnIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {formatCurrency(income_statement.net_income)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Net Income
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <AssessmentIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {formatPercentage(income_statement.gross_profit_margin)}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Profit Margin
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Professional Income Statement Layout */}
        <Box>
          {/* Revenue Section */}
          {renderProfessionalIncomeSection(
            'Revenue', 
            income_statement.revenue, 
            income_statement.total_revenue, 
            'success.main',
            true
          )}

          {/* Expenses Section */}
          {renderProfessionalIncomeSection(
            'Expenses', 
            income_statement.expenses, 
            income_statement.total_expenses, 
            'error.main'
          )}

          {/* Net Income Summary */}
          <Card sx={{ 
            bgcolor: income_statement.net_income >= 0 ? 'success.50' : 'error.50',
            border: '2px solid',
            borderColor: income_statement.net_income >= 0 ? 'success.main' : 'error.main',
            mb: 3
          }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h5" sx={{ fontWeight: 700 }}>
                  NET {income_statement.net_income >= 0 ? 'INCOME' : 'LOSS'}
                </Typography>
                <Typography variant="h4" sx={{ 
                  fontWeight: 700,
                  color: income_statement.net_income >= 0 ? 'success.main' : 'error.main'
                }}>
                  {formatCurrency(income_statement.net_income)}
                </Typography>
              </Box>
              <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
                For the period {dayjs(income_statement.period_start).format('MMM D')} - {dayjs(income_statement.period_end).format('MMM D, YYYY')}
              </Typography>
            </CardContent>
          </Card>
        </Box>

        {/* Professional Income Statement Ratios */}
        <Box sx={{ mt: 3 }}>
          {/* Profitability Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'success.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'success.main', display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 1 }} />
                Profitability Ratios
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {((income_statement.net_income / Math.max(income_statement.total_revenue, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Net Profit Margin
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Net Income ÷ Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {(() => {
                        // Estimate gross profit (Revenue - COGS estimate)
                        const estimatedCOGS = income_statement.total_expenses * 0.6;
                        const grossProfit = income_statement.total_revenue - estimatedCOGS;
                        return ((grossProfit / Math.max(income_statement.total_revenue, 1)) * 100 || 0).toFixed(1);
                      })()}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Gross Profit Margin (Est.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      (Revenue - COGS) ÷ Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                      {(() => {
                        // Operating Income = Net Income + Interest & Tax estimate
                        const operatingIncome = income_statement.net_income * 1.2;
                        return ((operatingIncome / Math.max(income_statement.total_revenue, 1)) * 100 || 0).toFixed(1);
                      })()}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Operating Margin (Est.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Operating Income ÷ Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {(() => {
                        // EBITDA estimate
                        const ebitda = income_statement.net_income * 1.4;
                        return ((ebitda / Math.max(income_statement.total_revenue, 1)) * 100 || 0).toFixed(1);
                      })()}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      EBITDA Margin (Est.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      EBITDA ÷ Revenue
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Efficiency Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'warning.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'warning.main', display: 'flex', alignItems: 'center' }}>
                <CompareArrowsIcon sx={{ mr: 1 }} />
                Efficiency Ratios
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'error.main' }}>
                      {((income_statement.total_expenses / Math.max(income_statement.total_revenue, 1)) * 100 || 0).toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Expense Ratio
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total Expenses ÷ Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {includeComparatives && income_statement.revenue.length > 0 && income_statement.revenue[0].comparative_total ? 
                        (((income_statement.total_revenue - income_statement.revenue[0].comparative_total) / Math.max(income_statement.revenue[0].comparative_total, 1)) * 100 || 0).toFixed(1) + '%'
                        : 'N/A'
                      }
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Revenue Growth
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Period vs Previous Period
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                      {formatCurrency(income_statement.total_revenue / Math.max(new Date(income_statement.period_end).getDate() - new Date(income_statement.period_start).getDate() + 1, 1))}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Daily Revenue (Avg.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Revenue ÷ Days in Period
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card sx={{ mb: 3, bgcolor: 'info.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'info.main', display: 'flex', alignItems: 'center' }}>
                <AssessmentIcon sx={{ mr: 1 }} />
                Performance Metrics
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {formatCurrency(income_statement.total_revenue)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Period Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'error.main' }}>
                      {formatCurrency(income_statement.total_expenses)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Expenses
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Period Expenses
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: income_statement.net_income >= 0 ? 'success.main' : 'error.main' }}>
                      {(() => {
                        // Break-even point estimate
                        const breakEvenRevenue = income_statement.total_expenses;
                        const currentRevenue = income_statement.total_revenue;
                        const percentToBreakEven = ((breakEvenRevenue / Math.max(currentRevenue, 1)) * 100).toFixed(0);
                        return currentRevenue >= breakEvenRevenue ? 'Profitable' : `${percentToBreakEven}%`;
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Break-even Status
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Revenue vs Expense Coverage
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      </Box>
    );
  };

  // Render Professional Cash Flow Statement
  const renderCashFlowStatement = () => {
    if (!financialData?.cash_flow) return null;

    const { cash_flow } = financialData;

    return (
      <Box>
        {/* Cash Flow Header */}
        <Box sx={{ mb: 3, textAlign: 'center' }}>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            {financialData.company_name}
          </Typography>
          <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
            Cash Flow Statement
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {dayjs(cash_flow.period_start).format('MMMM D, YYYY')} to {dayjs(cash_flow.period_end).format('MMMM D, YYYY')}
          </Typography>
        </Box>

        {/* Professional Cash Flow Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%', bgcolor: 'success.50' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {formatCurrency(cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_inflows, 0))}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Cash Inflows
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%', bgcolor: 'error.50' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'error.main' }}>
                    <TrendingDownIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {formatCurrency(Math.abs(cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_outflows, 0)))}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Cash Outflows
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%', bgcolor: cash_flow.total_cash_flow >= 0 ? 'info.50' : 'warning.50' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: cash_flow.total_cash_flow >= 0 ? 'info.main' : 'warning.main' }}>
                    <AccountBalanceIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {formatCurrency(cash_flow.total_cash_flow)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Net Cash Flow
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%', bgcolor: 'primary.50' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <MonetizationOnIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" sx={{ fontWeight: 700 }}>
                      {(() => {
                        // Free Cash Flow estimate (Net Cash Flow - Capital Expenditures estimate)
                        const freeCashFlow = cash_flow.total_cash_flow * 0.85;
                        return formatCurrency(freeCashFlow);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Free Cash Flow (Est.)
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Net Cash Flow Summary */}
        <Card sx={{ 
          mb: 3, 
          bgcolor: cash_flow.total_cash_flow >= 0 ? 'success.50' : 'error.50',
          border: '2px solid',
          borderColor: cash_flow.total_cash_flow >= 0 ? 'success.main' : 'error.main'
        }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h5" sx={{ fontWeight: 700 }}>
                NET CASH {cash_flow.total_cash_flow >= 0 ? 'INFLOW' : 'OUTFLOW'}
              </Typography>
              <Typography variant="h4" sx={{ 
                fontWeight: 700,
                color: cash_flow.total_cash_flow >= 0 ? 'success.main' : 'error.main'
              }}>
                {formatCurrency(cash_flow.total_cash_flow)}
              </Typography>
            </Box>
            <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 1 }}>
              For the period {dayjs(cash_flow.period_start).format('MMM D')} - {dayjs(cash_flow.period_end).format('MMM D, YYYY')}
            </Typography>
          </CardContent>
        </Card>

        {/* Cash Accounts Detail */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <AccountBalanceIcon />
              Cash & Bank Accounts Detail
            </Typography>
            
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Account #</TableCell>
                    <TableCell>Account Name</TableCell>
                    <TableCell align="right">Cash Inflows</TableCell>
                    <TableCell align="right">Cash Outflows</TableCell>
                    <TableCell align="right">Net Cash Flow</TableCell>
                    <TableCell align="right">Flow Ratio</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {cash_flow.cash_accounts.map((account) => (
                    <TableRow key={account.account_id} hover>
                      <TableCell>{account.account_number}</TableCell>
                      <TableCell>{account.account_name}</TableCell>
                      <TableCell align="right" sx={{ color: 'success.main', fontWeight: 600 }}>
                        {formatCurrency(account.cash_inflows)}
                      </TableCell>
                      <TableCell align="right" sx={{ color: 'error.main', fontWeight: 600 }}>
                        {formatCurrency(Math.abs(account.cash_outflows))}
                      </TableCell>
                      <TableCell align="right" sx={{ 
                        fontWeight: 600,
                        color: account.net_cash_flow >= 0 ? 'success.main' : 'error.main' 
                      }}>
                        {formatCurrency(account.net_cash_flow)}
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: 500 }}>
                        {((account.cash_inflows / Math.max(Math.abs(account.cash_outflows), 1)) || 0).toFixed(2)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Professional Cash Flow Ratios */}
        <Box sx={{ mt: 3 }}>
          {/* Liquidity & Cash Management Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'primary.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
                <MonetizationOnIcon sx={{ mr: 1 }} />
                Cash Flow Liquidity Ratios
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
                      {(() => {
                        const totalInflows = cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_inflows, 0);
                        const totalOutflows = Math.abs(cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_outflows, 0));
                        return ((totalInflows / Math.max(totalOutflows, 1)) || 0).toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cash Flow Ratio
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Cash Inflows ÷ Cash Outflows
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {(() => {
                        // Operating Cash Flow Ratio estimate
                        const operatingCashFlow = cash_flow.total_cash_flow * 0.7;
                        return ((operatingCashFlow / Math.max(Math.abs(cash_flow.total_cash_flow), 1)) || 0).toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Operating CF Ratio (Est.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Operating Cash Flow ÷ Total Cash Flow
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                      {formatCurrency(cash_flow.total_cash_flow / Math.max(new Date(cash_flow.period_end).getDate() - new Date(cash_flow.period_start).getDate() + 1, 1))}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Daily Cash Flow (Avg.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Net Cash Flow ÷ Days in Period
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Cash Flow Efficiency Ratios */}
          <Card sx={{ mb: 3, bgcolor: 'success.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'success.main', display: 'flex', alignItems: 'center' }}>
                <CompareArrowsIcon sx={{ mr: 1 }} />
                Cash Flow Efficiency
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {(() => {
                        // Cash Flow to Sales ratio (using revenue from income statement)
                        const revenue = financialData?.income_statement?.total_revenue || 1;
                        return ((cash_flow.total_cash_flow / Math.max(revenue, 1)) * 100 || 0).toFixed(1);
                      })()}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cash Flow to Sales
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Cash Flow ÷ Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
                      {(() => {
                        // Cash Coverage Ratio estimate
                        const totalInflows = cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_inflows, 0);
                        const totalExpenses = financialData?.income_statement?.total_expenses || 1;
                        return ((totalInflows / Math.max(totalExpenses, 1)) || 0).toFixed(2);
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cash Coverage Ratio
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Cash Inflows ÷ Total Expenses
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'secondary.main' }}>
                      {(() => {
                        // Free Cash Flow Yield
                        const freeCashFlow = cash_flow.total_cash_flow * 0.85;
                        const totalAssets = financialData?.balance_sheet?.total_assets || 1;
                        return ((freeCashFlow / Math.max(totalAssets, 1)) * 100 || 0).toFixed(1);
                      })()}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Free CF Yield (Est.)
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Free Cash Flow ÷ Total Assets
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Cash Flow Quality Metrics */}
          <Card sx={{ mb: 3, bgcolor: 'info.50' }}>
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 700, mb: 3, color: 'info.main', display: 'flex', alignItems: 'center' }}>
                <AssessmentIcon sx={{ mr: 1 }} />
                Cash Flow Quality Metrics
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: cash_flow.total_cash_flow >= 0 ? 'success.main' : 'error.main' }}>
                      {cash_flow.total_cash_flow >= 0 ? 'POSITIVE' : 'NEGATIVE'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cash Flow Status
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Overall Cash Generation
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
                      {(() => {
                        const totalInflows = cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_inflows, 0);
                        const totalOutflows = Math.abs(cash_flow.cash_accounts.reduce((sum, acc) => sum + acc.cash_outflows, 0));
                        const efficiency = ((totalInflows - totalOutflows) / Math.max(totalInflows, 1)) * 100;
                        return efficiency.toFixed(1) + '%';
                      })()}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Cash Efficiency
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      (Inflows - Outflows) ÷ Inflows
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                      {cash_flow.cash_accounts.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Cash Accounts
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Bank & Cash Accounts
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>

        {/* Note about cash flow statement */}
        <Alert severity="info" sx={{ mt: 2 }}>
          <AlertTitle>Cash Flow Statement Information</AlertTitle>
          {cash_flow.note || 'This cash flow statement shows the movement of cash and cash equivalents during the period. Professional cash flow statements would typically include Operating, Investing, and Financing activities breakdown.'}
        </Alert>
      </Box>
    );
  };

  if (currencyLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (currencyError) {
    return (
      <Alert severity="error">
        <AlertTitle>Error Loading Currency Information</AlertTitle>
        {currencyError}
      </Alert>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {/* Page Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
              Financial Statements
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Comprehensive financial reporting with fiscal/calendar year support
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={2}>
            <Tooltip title="Export Reports">
              <IconButton 
                onClick={() => setExportDialogOpen(true)} 
                disabled={!financialData}
                color="primary"
              >
                <DownloadIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Print Reports">
              <IconButton 
                onClick={handlePrint} 
                disabled={!financialData}
                color="primary"
              >
                <PrintIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Refresh Data">
              <IconButton 
                onClick={loadStatements} 
                disabled={loading}
                color="primary"
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Box>

        {/* Filter Controls */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              {/* Period Type Selection */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Period Type</InputLabel>
                  <Select
                    value={selectedPeriodType}
                    onChange={(e) => setSelectedPeriodType(e.target.value as PeriodType)}
                    label="Period Type"
                  >
                    <MenuItem value="fiscal_year">Fiscal Year</MenuItem>
                    <MenuItem value="calendar_year">Calendar Year</MenuItem>
                    <MenuItem value="custom">Custom Period</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Year Selection or Custom Dates */}
              {selectedPeriodType !== 'custom' ? (
                <Grid item xs={12} md={2}>
                  <TextField
                    fullWidth
                    label="Year"
                    type="number"
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <CalendarTodayIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              ) : (
                <>
                  <Grid item xs={12} md={2}>
                    <DatePicker
                      label="From Date"
                      value={customDateFrom}
                      onChange={(date) => date && setCustomDateFrom(date)}
                      sx={{ width: '100%' }}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <DatePicker
                      label="To Date"
                      value={customDateTo}
                      onChange={(date) => date && setCustomDateTo(date)}
                      sx={{ width: '100%' }}
                    />
                  </Grid>
                </>
              )}

              {/* Currency Selection */}
              <Grid item xs={12} md={2}>
                <ToggleButtonGroup
                  value={selectedCurrency}
                  exclusive
                  onChange={(_, value) => value && setSelectedCurrency(value)}
                  aria-label="currency selection"
                  fullWidth
                >
                  <ToggleButton value="functional" aria-label="functional currency">
                    Functional
                  </ToggleButton>
                  <ToggleButton value="reporting" aria-label="reporting currency">
                    Reporting
                  </ToggleButton>
                </ToggleButtonGroup>
              </Grid>

              {/* Comparative Toggle */}
              <Grid item xs={12} md={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={includeComparatives}
                      onChange={(e) => setIncludeComparatives(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Include Comparatives"
                />
              </Grid>
            </Grid>

            {/* Period Info Display */}
            {financialData && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2" color="textSecondary">
                  <strong>Reporting Period:</strong> {getPeriodDisplayString()} • 
                  <strong> Currency:</strong> {selectedCurrency === 'functional' ? 'Functional' : 'Reporting'} 
                  ({currencyInfo?.functional_currency || 'USD'}) • 
                  <strong> Generated:</strong> {dayjs(financialData.generated_at).format('MMM D, YYYY h:mm A')}
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
            <CircularProgress size={60} />
          </Box>
        )}

        {/* Error State */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <AlertTitle>Error Loading Financial Statements</AlertTitle>
            {error}
            <br /><br />
            <Typography variant="body2">
              <strong>Troubleshooting:</strong>
              <ul>
                <li>Make sure the Django backend server is running on port 8000</li>
                <li>Check that the financial statements API endpoint is available</li>
                <li>Verify that you have proper authentication tokens</li>
                <li>Ensure the company has fiscal/calendar year settings configured</li>
              </ul>
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Button 
                variant="outlined" 
                onClick={loadStatements}
                startIcon={<RefreshIcon />}
              >
                Retry Loading
              </Button>
            </Box>
          </Alert>
        )}

        {/* Financial Statements Tabs */}
        {financialData && !loading && !error && (
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
                <Tab 
                  label="Balance Sheet" 
                  icon={<BalanceIcon />} 
                  iconPosition="start"
                />
                <Tab 
                  label="Income Statement" 
                  icon={<AssessmentIcon />} 
                  iconPosition="start"
                />
                <Tab 
                  label="Cash Flow" 
                  icon={<MonetizationOnIcon />} 
                  iconPosition="start"
                />
              </Tabs>
            </Box>

            <CustomTabPanel value={activeTab} index={0}>
              {renderBalanceSheet()}
            </CustomTabPanel>

            <CustomTabPanel value={activeTab} index={1}>
              {renderIncomeStatement()}
            </CustomTabPanel>

            <CustomTabPanel value={activeTab} index={2}>
              {renderCashFlowStatement()}
            </CustomTabPanel>
          </Card>
        )}

        {/* Export Dialog */}
        <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)}>
          <DialogTitle>Export Financial Statements</DialogTitle>
          <DialogContent>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Choose which statements to export as CSV files:
            </Typography>
            <Stack spacing={2}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={() => {
                  if (financialData?.balance_sheet) {
                    const allAccounts = [
                      ...financialData.balance_sheet.assets,
                      ...financialData.balance_sheet.liabilities,
                      ...financialData.balance_sheet.equity
                    ];
                    handleExportCSV('balance_sheet', allAccounts);
                  }
                }}
                disabled={!financialData?.balance_sheet}
              >
                Export Balance Sheet
              </Button>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={() => {
                  if (financialData?.income_statement) {
                    const allAccounts = [
                      ...financialData.income_statement.revenue,
                      ...financialData.income_statement.expenses
                    ];
                    handleExportCSV('income_statement', allAccounts);
                  }
                }}
                disabled={!financialData?.income_statement}
              >
                Export Income Statement
              </Button>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setExportDialogOpen(false)}>Close</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default FinancialStatementsPage; 