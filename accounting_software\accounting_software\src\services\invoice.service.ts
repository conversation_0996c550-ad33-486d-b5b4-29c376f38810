import api from './api';

// Invoice Types
export interface InvoiceCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface InvoiceItem {
  id?: number;
  product?: number | null;  // Allow null for non-product items
  product_name?: string;
  product_sku?: string;
  description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
  available_quantity?: number; // Available inventory quantity
}

export interface Warehouse {
  warehouse_id: number;
  name: string;
  warehouse_code: string;
  warehouse_type: string;
  location: string;
  is_active: boolean;
}

export interface InventoryAvailability {
  product_id: number;
  product_name: string;
  product_sku: string;
  required_quantity: number;
  available_quantity: number;
  on_hand_quantity: number;
  reserved_quantity: number;
  sufficient_stock: boolean;
  error?: string;
}

export interface Invoice {
  id?: number;
  invoice_number?: string;
  customer: number;
  customer_details?: InvoiceCustomer;
  warehouse?: number | null;  // Allow null for non-inventory invoices
  warehouse_name?: string;
  invoice_date: string;
  due_date: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  line_items?: InvoiceItem[];  // Make line_items optional
  items?: InvoiceItem[];  // For backward compatibility
  subtotal: number;
  tax_total: number;
  total_amount: number;
  notes?: string;
  terms?: string;
  inventory_posted?: boolean;
  inventory_posted_date?: string;
  inventory_posted_by?: string;
  created_at?: string;
  updated_at?: string;
}

export interface InvoiceFilters {
  status?: string;
  customer?: number;
  warehouse?: number;
  inventory_posted?: boolean;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface InvoiceStats {
  total_invoices: number;
  total_revenue: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
  inventory_posted_count: number;
}

class InvoiceService {
  private baseUrl = '/sales';

  // Get all invoices with optional filters
  async getInvoices(filters?: InvoiceFilters): Promise<{ results: Invoice[]; count: number }> {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customer) params.append('customer', filters.customer.toString());
    if (filters?.warehouse) params.append('warehouse', filters.warehouse.toString());
    if (filters?.inventory_posted !== undefined) params.append('inventory_posted', filters.inventory_posted.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/invoices/?${params}`);
    return response.data;
  }

  // Get single invoice by ID
  async getInvoice(id: number): Promise<Invoice> {
    const response = await api.get(`${this.baseUrl}/invoices/${id}/`);
    return response.data;
  }

  // Create new invoice
  async createInvoice(invoiceData: Omit<Invoice, 'id' | 'created_at' | 'updated_at'>): Promise<Invoice> {
    const { items, ...rest } = invoiceData;
    const response = await api.post(`${this.baseUrl}/invoices/`, {
      ...rest,
      warehouse: rest.warehouse || null,  // Ensure null for no warehouse
      invoice_number: rest.invoice_number || undefined,  // Let backend generate if not provided
      line_items: (rest.line_items || []).map(item => ({
        ...item,
        product: item.product === undefined ? null : item.product  // Convert undefined to null
      }))
    });
    return response.data;
  }

  // Update existing invoice
  async updateInvoice(id: number, invoiceData: Partial<Invoice>): Promise<Invoice> {
    const { items, ...rest } = invoiceData;
    const response = await api.put(`${this.baseUrl}/invoices/${id}/`, {
      ...rest,
      warehouse: rest.warehouse || null,  // Ensure null for no warehouse
      invoice_number: rest.invoice_number || undefined,  // Let backend generate if not provided
      line_items: (rest.line_items || []).map(item => ({
        ...item,
        product: item.product === undefined ? null : item.product  // Convert undefined to null
      }))
    });
    return response.data;
  }

  // Delete invoice
  async deleteInvoice(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/invoices/${id}/`);
  }

  // Get invoice statistics
  async getInvoiceStats(): Promise<InvoiceStats> {
    const response = await api.get(`${this.baseUrl}/invoices/stats/`);
    return response.data;
  }

  // Send invoice to customer
  async sendInvoice(id: number, emailData?: { to?: string; subject?: string; message?: string }): Promise<void> {
    await api.post(`${this.baseUrl}/invoices/${id}/send/`, emailData || {});
  }

  // Mark invoice as paid
  async markAsPaid(id: number, paymentData?: { amount?: number; payment_date?: string; notes?: string }): Promise<Invoice> {
    const response = await api.post(`${this.baseUrl}/invoices/${id}/mark_paid/`, paymentData || {});
    return response.data;
  }

  // Post invoice to inventory
  async postToInventory(id: number): Promise<{ message: string; invoice_number: string; warehouse: string; posted_at: string; posted_by: string }> {
    const response = await api.post(`${this.baseUrl}/invoices/${id}/post_to_inventory/`);
    return response.data;
  }

  // Check inventory availability for invoice
  async checkInventoryAvailability(id: number): Promise<{
    invoice_number: string;
    warehouse: string;
    can_post_to_inventory: boolean;
    inventory_posted: boolean;
    products: InventoryAvailability[];
  }> {
    const response = await api.get(`${this.baseUrl}/invoices/${id}/inventory_availability/`);
    return response.data;
  }

  // Get warehouses for invoice creation
  async getWarehouses(): Promise<Warehouse[]> {
    const response = await api.get('/inventory/warehouses/');
    return response.data.results || response.data;
  }

  // Get customers for invoice creation
  async getCustomers(): Promise<InvoiceCustomer[]> {
    const response = await api.get('/contacts/customers/');
    return response.data.results || response.data;
  }

  // Get products for invoice line items with inventory info
  async getProducts(warehouseId?: number): Promise<any[]> {
    const params = new URLSearchParams();
    if (warehouseId) params.append('warehouse', warehouseId.toString());
    
    const response = await api.get(`${this.baseUrl}/products/?${params}`);
    return response.data.results || response.data;
  }

  // Get product inventory for specific warehouse
  async getProductInventory(productId: number, warehouseId: number): Promise<{
    available_quantity: number;
    on_hand_quantity: number;
    reserved_quantity: number;
  }> {
    const response = await api.get(`/inventory/inventory/?product=${productId}&warehouse=${warehouseId}`);
    const inventoryData = response.data.results?.[0];
    
    if (!inventoryData) {
      return {
        available_quantity: 0,
        on_hand_quantity: 0,
        reserved_quantity: 0
      };
    }
    
    return {
      available_quantity: inventoryData.quantity_available || 0,
      on_hand_quantity: inventoryData.quantity_on_hand || 0,
      reserved_quantity: inventoryData.quantity_reserved || 0
    };
  }

  // Calculate invoice totals
  calculateTotals(items: InvoiceItem[]): { subtotal: number; tax_total: number; total_amount: number } {
    const subtotal = items.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const tax_total = items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const total_amount = subtotal + tax_total;

    return {
      subtotal,
      tax_total,
      total_amount
    };
  }

  // Helper method to generate due date based on payment terms
  calculateDueDate(invoiceDate: string, paymentTerms: string = 'net_30'): string {
    const date = new Date(invoiceDate);
    const daysToAdd = this.getPaymentTermsDays(paymentTerms);
    date.setDate(date.getDate() + daysToAdd);
    return date.toISOString().split('T')[0];
  }

  private getPaymentTermsDays(paymentTerms: string): number {
    const termMap: { [key: string]: number } = {
      'net_15': 15,
      'net_30': 30,
      'net_45': 45,
      'net_60': 60,
      'due_on_receipt': 0,
      'due_end_of_month': 30,
    };
    return termMap[paymentTerms] || 30;
  }
}

export const invoiceService = new InvoiceService();
export default invoiceService; 