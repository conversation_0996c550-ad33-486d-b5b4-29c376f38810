import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Vendor, VendorFormData } from '../shared/types/purchase.types';

interface VendorContextType {
  vendors: Vendor[];
  addVendor: (vendorData: VendorFormData) => Promise<Vendor>;
  updateVendor: (vendor: Vendor) => Promise<void>;
  deleteVendor: (vendorId: number) => Promise<void>;
  getVendorById: (vendorId: number) => Vendor | undefined;
  isLoading: boolean;
  error: string | null;
  refreshVendors: () => Promise<void>;
}

const VendorContext = createContext<VendorContextType | undefined>(undefined);

export const VendorProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch vendors from API
  const fetchVendors = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/purchase/vendors/', {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform backend vendor data to frontend format
      const transformedVendors = (data.results || data).map((vendor: any) => ({
        id: vendor.id, // Keep as number from backend
        name: vendor.display_name,
        contactName: vendor.first_name && vendor.last_name ? `${vendor.first_name} ${vendor.last_name}` : vendor.display_name,
        email: vendor.email || '',
        phone: vendor.phone || '',
        address: {
          street: vendor.billing_street || '',
          city: vendor.billing_city || '',
          state: vendor.billing_state || '',
          postalCode: vendor.billing_postal_code || '',
          country: vendor.billing_country || 'USA',
        },
        taxId: vendor.gstin || vendor.tax_registration_number || '',
        paymentTerms: vendor.payment_terms_name || vendor.payment_terms || 'net30',
        currency: vendor.currency || 'USD',
        status: vendor.status || 'active',
        notes: vendor.notes || '',
        balance: parseFloat(vendor.current_balance || '0'),
        createdAt: vendor.created_at,
        updatedAt: vendor.updated_at,
      }));
      
      setVendors(transformedVendors);
    } catch (err) {
      console.error('Error fetching vendors:', err);
      setError(err instanceof Error ? err.message : 'Failed to load vendors');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchVendors();
  }, []);

  const addVendor = async (vendorData: VendorFormData): Promise<Vendor> => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/purchase/vendors/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        },
        body: JSON.stringify({
          display_name: vendorData.name,
          first_name: vendorData.contactName?.split(' ')[0] || '',
          last_name: vendorData.contactName?.split(' ').slice(1).join(' ') || '',
          email: vendorData.email,
          phone: vendorData.phone,
          billing_street: vendorData.address.street,
          billing_city: vendorData.address.city,
          billing_state: vendorData.address.state,
          billing_postal_code: vendorData.address.postalCode,
          billing_country: vendorData.address.country,
          gstin: vendorData.taxId,
          payment_terms: vendorData.paymentTerms,
          currency: vendorData.currency,
          status: vendorData.status,
          notes: vendorData.notes,
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const createdVendor = await response.json();
      
      // Transform the created vendor to frontend format
      const newVendor: Vendor = {
        id: createdVendor.id,
        name: createdVendor.display_name,
        contactName: createdVendor.first_name && createdVendor.last_name ? `${createdVendor.first_name} ${createdVendor.last_name}` : createdVendor.display_name,
        email: createdVendor.email || '',
        phone: createdVendor.phone || '',
        address: {
          street: createdVendor.billing_street || '',
          city: createdVendor.billing_city || '',
          state: createdVendor.billing_state || '',
          postalCode: createdVendor.billing_postal_code || '',
          country: createdVendor.billing_country || 'USA',
        },
        taxId: createdVendor.gstin || '',
        paymentTerms: createdVendor.payment_terms_name || createdVendor.payment_terms || 'net30',
        currency: createdVendor.currency || 'USD',
        status: createdVendor.status || 'active',
        notes: createdVendor.notes || '',
        balance: parseFloat(createdVendor.current_balance || '0'),
        createdAt: createdVendor.created_at,
        updatedAt: createdVendor.updated_at,
      };

      setVendors(prevVendors => [...prevVendors, newVendor]);
      return newVendor;
    } catch (err) {
      console.error('Error adding vendor:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to add vendor');
    }
  };

  const updateVendor = async (updatedVendor: Vendor): Promise<void> => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/purchase/vendors/${updatedVendor.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        },
        body: JSON.stringify({
          display_name: updatedVendor.name,
          first_name: updatedVendor.contactName?.split(' ')[0] || '',
          last_name: updatedVendor.contactName?.split(' ').slice(1).join(' ') || '',
          email: updatedVendor.email,
          phone: updatedVendor.phone,
          billing_street: updatedVendor.address.street,
          billing_city: updatedVendor.address.city,
          billing_state: updatedVendor.address.state,
          billing_postal_code: updatedVendor.address.postalCode,
          billing_country: updatedVendor.address.country,
          gstin: updatedVendor.taxId,
          payment_terms: updatedVendor.paymentTerms,
          currency: updatedVendor.currency,
          status: updatedVendor.status,
          notes: updatedVendor.notes,
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      setVendors(prevVendors =>
        prevVendors.map(vendor =>
          vendor.id === updatedVendor.id
            ? { ...updatedVendor, updatedAt: new Date().toISOString() }
            : vendor
        )
      );
    } catch (err) {
      console.error('Error updating vendor:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to update vendor');
    }
  };

  const deleteVendor = async (vendorId: number): Promise<void> => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/purchase/vendors/${vendorId}/`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Token ${token}` })
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      setVendors(prevVendors => prevVendors.filter(vendor => vendor.id !== vendorId));
    } catch (err) {
      console.error('Error deleting vendor:', err);
      throw new Error(err instanceof Error ? err.message : 'Failed to delete vendor');
    }
  };

  const getVendorById = (vendorId: number): Vendor | undefined => {
    return vendors.find(vendor => vendor.id === vendorId);
  };

  const refreshVendors = async (): Promise<void> => {
    await fetchVendors();
  };

  const value = {
    vendors,
    addVendor,
    updateVendor,
    deleteVendor,
    getVendorById,
    isLoading,
    error,
    refreshVendors,
  };

  return <VendorContext.Provider value={value}>{children}</VendorContext.Provider>;
};

export const useVendors = (): VendorContextType => {
  const context = useContext(VendorContext);
  if (context === undefined) {
    throw new Error('useVendors must be used within a VendorProvider');
  }
  return context;
}; 