import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '../shared/types/invoice.types';
import dayjs from 'dayjs';

class PDFService {
  /**
   * Generate a PDF from an HTML element
   * @param element The HTML element to convert to PDF
   * @param filename The name of the PDF file
   */
  async generatePDFFromElement(element: HTMLElement, filename: string): Promise<Blob> {
    try {
      const canvas = await html2canvas(element, {
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true
      });
      
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      const imgProps = pdf.getImageProperties(imgData);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
      
      return pdf.output('blob');
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF');
    }
  }

  /**
   * Generate an invoice PDF directly from invoice data
   * @param invoice The invoice data
   * @param companyInfo The company information
   */
  generateInvoicePDF(invoice: Invoice, companyInfo: any): Blob {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    // Set up document properties
    const pageWidth = pdf.internal.pageSize.getWidth();
    const margin = 20;
    const col1 = margin;
    const col2 = pageWidth / 2;
    let y = margin;
    
    // Add company logo and info
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    pdf.text('INVOICE', col1, y);
    
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    y += 10;
    pdf.text(companyInfo.companyName, col1, y);
    y += 5;
    pdf.text(companyInfo.address, col1, y);
    y += 5;
    pdf.text(`Phone: ${companyInfo.phone}`, col1, y);
    y += 5;
    pdf.text(`Email: ${companyInfo.email}`, col1, y);
    
    // Add invoice details
    y = margin;
    pdf.setFontSize(10);
    pdf.text(`Invoice #: ${invoice.invoiceNumber}`, col2, y);
    y += 5;
    pdf.text(`Date: ${dayjs(invoice.invoiceDate).format('MMM D, YYYY')}`, col2, y);
    y += 5;
    pdf.text(`Due Date: ${dayjs(invoice.dueDate).format('MMM D, YYYY')}`, col2, y);
    y += 5;
    pdf.text(`Status: ${invoice.status.toUpperCase()}`, col2, y);
    
    // Add customer info
    y = margin + 30;
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Bill To:', col1, y);
    
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    y += 7;
    pdf.text(invoice.customer.displayName, col1, y);
    y += 5;
    pdf.text(invoice.customer.billingAddress.street, col1, y);
    y += 5;
    pdf.text(`${invoice.customer.billingAddress.city}, ${invoice.customer.billingAddress.state} ${invoice.customer.billingAddress.postalCode}`, col1, y);
    y += 5;
    pdf.text(invoice.customer.billingAddress.country, col1, y);
    
    // Add items table
    y += 15;
    const tableTop = y;
    const tableHeaders = ['Item', 'Description', 'Qty', 'Unit Price', 'Tax', 'Amount'];
    const tableWidths = [60, 60, 15, 20, 15, 20];
    const tableX = [col1, col1 + tableWidths[0], col1 + tableWidths[0] + tableWidths[1], 
                   col1 + tableWidths[0] + tableWidths[1] + tableWidths[2],
                   col1 + tableWidths[0] + tableWidths[1] + tableWidths[2] + tableWidths[3],
                   col1 + tableWidths[0] + tableWidths[1] + tableWidths[2] + tableWidths[3] + tableWidths[4]];
    
    // Draw table header
    pdf.setFillColor(240, 240, 240);
    pdf.rect(col1, y, pageWidth - (2 * margin), 8, 'F');
    pdf.setFont('helvetica', 'bold');
    
    for (let i = 0; i < tableHeaders.length; i++) {
      pdf.text(tableHeaders[i], tableX[i], y + 5);
    }
    
    // Draw table rows
    y += 8;
    pdf.setFont('helvetica', 'normal');
    
    invoice.items.forEach((item) => {
      const itemY = y;
      
      // Check if we need a new page
      if (y > 250) {
        pdf.addPage();
        y = margin;
      }
      
      pdf.text(item.name, tableX[0], y + 5);
      pdf.text(item.description || '', tableX[1], y + 5);
      pdf.text(item.quantity.toString(), tableX[2], y + 5);
      pdf.text(`${invoice.currency} ${item.unitPrice.toFixed(2)}`, tableX[3], y + 5);
      pdf.text(`${item.taxRate || 0}%`, tableX[4], y + 5);
      pdf.text(`${invoice.currency} ${item.subtotal.toFixed(2)}`, tableX[5], y + 5);
      
      y += 10;
    });
    
    // Draw table bottom line
    pdf.line(col1, y, pageWidth - margin, y);
    
    // Add totals
    y += 10;
    pdf.text('Subtotal:', tableX[4] - 20, y);
    pdf.text(`${invoice.currency} ${invoice.subtotal.toFixed(2)}`, tableX[5], y);
    
    y += 7;
    pdf.text('Tax:', tableX[4] - 20, y);
    pdf.text(`${invoice.currency} ${invoice.taxAmount.toFixed(2)}`, tableX[5], y);
    
    y += 7;
    pdf.setFont('helvetica', 'bold');
    pdf.text('Total:', tableX[4] - 20, y);
    pdf.text(`${invoice.currency} ${invoice.total.toFixed(2)}`, tableX[5], y);
    
    // Add payment info
    y += 15;
    pdf.setFont('helvetica', 'normal');
    pdf.text('Payment Information:', col1, y);
    y += 7;
    pdf.text(`Amount Due: ${invoice.currency} ${invoice.balanceDue.toFixed(2)}`, col1, y);
    y += 7;
    pdf.text(`Amount Paid: ${invoice.currency} ${invoice.paidAmount.toFixed(2)}`, col1, y);
    
    // Add notes and terms
    y += 15;
    if (invoice.notes) {
      pdf.text('Notes:', col1, y);
      y += 7;
      pdf.text(invoice.notes, col1, y);
      y += 10;
    }
    
    if (invoice.terms) {
      pdf.text('Terms & Conditions:', col1, y);
      y += 7;
      pdf.text(invoice.terms, col1, y);
    }
    
    // Add footer
    const footerY = pdf.internal.pageSize.getHeight() - 10;
    pdf.setFontSize(8);
    pdf.text(`Generated on ${dayjs().format('MMM D, YYYY [at] h:mm A')}`, col1, footerY);
    pdf.text('Thank you for your business!', pageWidth / 2, footerY, { align: 'center' });
    pdf.text(`Page 1 of 1`, pageWidth - margin, footerY, { align: 'right' });
    
    return pdf.output('blob');
  }

  /**
   * Save a blob as a file
   * @param blob The blob to save
   * @param filename The name of the file
   */
  saveBlob(blob: Blob, filename: string): void {
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
    URL.revokeObjectURL(link.href);
  }
}

export const pdfService = new PDFService(); 