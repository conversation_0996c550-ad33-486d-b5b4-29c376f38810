import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  MenuItem,
  Grid,
  Paper,
  CircularProgress,
} from '@mui/material';
import { useSnackbar } from 'notistack';

const departmentSchema = z.object({
  name: z.string().min(1, 'Department name is required'),
  code: z.string().min(1, 'Department code is required').max(20, 'Code must be 20 characters or less'),
  description: z.string().optional(),
  manager: z.number().nullable(),
  parent_department: z.number().nullable(),
  is_active: z.boolean().default(true),
});

type DepartmentFormValues = z.infer<typeof departmentSchema>;

interface DepartmentFormProps {
  onCancel: () => void;
  onSubmit?: (data: DepartmentFormValues) => void;
  initialValues?: Partial<DepartmentFormValues & { id?: number }>;
  loading?: boolean;
}

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
}

interface Department {
  id: number;
  name: string;
}

const DepartmentForm: React.FC<DepartmentFormProps> = ({
  onCancel,
  onSubmit,
  initialValues,
  loading = false,
}) => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loadingData, setLoadingData] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  const form = useForm<DepartmentFormValues>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: initialValues?.name || '',
      code: initialValues?.code || '',
      description: initialValues?.description || '',
      manager: initialValues?.manager || null,
      parent_department: initialValues?.parent_department || null,
      is_active: initialValues?.is_active ?? true,
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoadingData(true);
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // Fetch employees for manager selection
        const employeesResponse = await fetch('http://localhost:8000/api/hr/employees/', {
          headers: {
            'Authorization': `Token ${token}`,
          },
        });

        if (!employeesResponse.ok) {
          throw new Error('Failed to fetch employees');
        }

        const employeesData = await employeesResponse.json();
        setEmployees(employeesData.results || employeesData);

        // Fetch departments for parent department selection
        const departmentsResponse = await fetch('http://localhost:8000/api/hr/departments/', {
          headers: {
            'Authorization': `Token ${token}`,
          },
        });

        if (!departmentsResponse.ok) {
          throw new Error('Failed to fetch departments');
        }

        const departmentsData = await departmentsResponse.json();
        // Filter out the current department if editing
        const filteredDepartments = (departmentsData.results || departmentsData).filter(
          (dept: Department) => dept.id !== initialValues?.id
        );
        setDepartments(filteredDepartments);
      } catch (error) {
        enqueueSnackbar(error instanceof Error ? error.message : 'Error loading form data', {
          variant: 'error',
        });
      } finally {
        setLoadingData(false);
      }
    };

    fetchData();
  }, [initialValues?.id, enqueueSnackbar]);

  const handleSubmit = async (data: DepartmentFormValues) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch('http://localhost:8000/api/hr/departments/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`,
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to create department');
      }

      enqueueSnackbar('Department created successfully', { variant: 'success' });
      if (onSubmit) {
        onSubmit(data);
      }
      onCancel();
    } catch (error) {
      enqueueSnackbar(error instanceof Error ? error.message : 'Error creating department', {
        variant: 'error',
      });
    }
  };

  if (loadingData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={form.handleSubmit(handleSubmit)} sx={{ mt: 2 }}>
      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Controller
              name="name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  label="Department Name"
                  fullWidth
                  required
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name="code"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  label="Department Code"
                  fullWidth
                  required
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name="description"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  label="Description"
                  fullWidth
                  multiline
                  rows={3}
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name="manager"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  select
                  label="Department Manager"
                  fullWidth
                  value={field.value || ''}
                  onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                  error={!!error}
                  helperText={error?.message}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {employees.map((employee) => (
                    <MenuItem key={employee.id} value={employee.id}>
                      {employee.firstName} {employee.lastName}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name="parent_department"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  select
                  label="Parent Department"
                  fullWidth
                  value={field.value || ''}
                  onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                  error={!!error}
                  helperText={error?.message}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {departments.map((department) => (
                    <MenuItem key={department.id} value={department.id}>
                      {department.name}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name="is_active"
              control={form.control}
              render={({ field: { value, onChange } }) => (
                <FormControlLabel
                  control={
                    <Switch
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Active"
                />
              )}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button variant="outlined" onClick={onCancel} disabled={loading}>
            Cancel
          </Button>
          <Button type="submit" variant="contained" color="primary" disabled={loading}>
            {loading ? 'Saving...' : initialValues ? 'Update Department' : 'Create Department'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default DepartmentForm; 