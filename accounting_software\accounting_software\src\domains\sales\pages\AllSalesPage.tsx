import React from 'react';
import { 
  Container, Typography, Paper, Box, Grid, 
  Card, CardContent, IconButton, Tooltip 
} from '@mui/material';
import { 
  TrendingUp, AttachMoney, Receipt, 
  ShowChart, Download as DownloadIcon 
} from '@mui/icons-material';
import StatCard from '../../../shared/components/StatCard';
import RecentSales from '../components/RecentSales';
import SalesChart from '../components/SalesChart';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';

const AllSalesPage: React.FC = () => {
  // Mock data for analytics
  const salesStats = {
    totalSales: 245000,
    monthlyGrowth: 15.8,
    averageOrderValue: 1580,
    conversionRate: 68.5,
  };

  const monthlyData = [
    { month: 'Jan', sales: 18500 },
    { month: 'Feb', sales: 20400 },
    { month: 'Mar', sales: 19800 },
    { month: 'Apr', sales: 22100 },
    { month: 'May', sales: 23400 },
    { month: 'Jun', sales: 25800 },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Typography variant="h4">
          Sales Overview
        </Typography>
        <Tooltip title="Download Sales Report">
          <IconButton>
            <DownloadIcon />
          </IconButton>
        </Tooltip>
      </PageHeader>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Sales"
            value={`$${salesStats.totalSales.toLocaleString()}`}
            trend={+12.5}
            icon={<AttachMoney />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Monthly Growth"
            value={`${salesStats.monthlyGrowth}%`}
            trend={+2.4}
            icon={<TrendingUp />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Avg. Order Value"
            value={`$${salesStats.averageOrderValue}`}
            trend={+5.2}
            icon={<Receipt />}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Conversion Rate"
            value={`${salesStats.conversionRate}%`}
            trend={-0.8}
            icon={<ShowChart />}
          />
        </Grid>
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <SalesChart />
          </Paper>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Monthly Performance
            </Typography>
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              gap: 2 
            }}>
              {monthlyData.map((month, index) => (
                <Card 
                  key={month.month} 
                  sx={{ 
                    bgcolor: 'background.default',
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                >
                  <CardContent sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    py: 1,
                    '&:last-child': { pb: 1 }
                  }}>
                    <Typography variant="body1">
                      {month.month}
                    </Typography>
                    <Typography variant="h6" color="primary">
                      ${month.sales.toLocaleString()}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <RecentSales />
          </Paper>
        </Grid>
      </Grid>
    </PageContainer>
  );
};

export default AllSalesPage;