"""
Audit Trail Context Processor

Automatically sets user context for audit logging in Django views.
This ensures all database changes are properly attributed to the current user.
"""

from .signals import set_current_user, set_request_context

class AuditContextMiddleware:
    """
    Middleware to automatically set audit context for all requests.
    
    This ensures that database changes made during request processing
    are properly attributed to the authenticated user and include
    request context information.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Set audit context before processing request
        if hasattr(request, 'user') and request.user.is_authenticated:
            set_current_user(request.user)
        else:
            set_current_user(None)
        
        # Set request context
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        session_key = getattr(request.session, 'session_key', None)
        
        set_request_context(ip_address, user_agent, session_key)
        
        response = self.get_response(request)
        
        return response
    
    def get_client_ip(self, request):
        """Get the client's IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip 