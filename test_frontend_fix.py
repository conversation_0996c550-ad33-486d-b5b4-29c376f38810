#!/usr/bin/env python3
"""
Test script to verify the frontend fix for payment_terms field
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"
TOKEN = "e0d1048e1f14689c6eba6616845a074bad2167bc"

def test_correct_payment_terms():
    """Test that payment_terms as string works correctly"""
    
    headers = {
        "Authorization": f"Token {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test with correct field mapping (like the fixed frontend should send)
    invoice_data = {
        "customer": 51,
        "invoice_date": "2025-06-29",
        "due_date": "2025-07-29",
        "status": "draft",
        "line_items": [
            {
                "description": "Test Service - Frontend Fix",
                "quantity": 1.0,
                "unit_price": 150.0,
                "discount_percent": 0.0,
                "line_total": 150.0,
                "taxable": True,
                "tax_rate": 10.0,
                "tax_amount": 15.0,
                "line_order": 0
            }
        ],
        "subtotal": 150.0,
        "tax_amount": 15.0,  # Correct field name
        "total_amount": 165.0,
        "memo": "Test invoice with correct field mapping",  # Correct field name
        "payment_terms": "Net 30"  # Correct field name as string
    }
    
    print("Testing corrected frontend field mapping:")
    print(json.dumps(invoice_data, indent=2))
    
    response = requests.post(
        f"{BASE_URL}/sales/invoices/",
        headers=headers,
        json=invoice_data
    )
    
    print(f"\nStatus Code: {response.status_code}")
    
    if response.status_code == 201:
        print("✅ SUCCESS - Invoice created with correct field mapping!")
        result = response.json()
        print(f"Invoice ID: {result.get('id')}")
        print(f"Invoice Number: {result.get('invoice_number')}")
        print(f"Payment Terms: {result.get('payment_terms')}")
        print(f"Memo: {result.get('memo')}")
        print(f"Tax Amount: {result.get('tax_amount')}")
        return True
    else:
        print("❌ FAILED")
        try:
            error_data = response.json()
            print(f"Error: {json.dumps(error_data, indent=2)}")
        except:
            print(f"Raw response: {response.text[:500]}")
        return False

if __name__ == "__main__":
    success = test_correct_payment_terms()
    if success:
        print("\n🎉 Frontend fix verification successful!")
        print("The corrected field mapping should resolve the 400 Bad Request error.")
    else:
        print("\n💥 Frontend fix verification failed!")
        print("There may be additional issues to resolve.")
