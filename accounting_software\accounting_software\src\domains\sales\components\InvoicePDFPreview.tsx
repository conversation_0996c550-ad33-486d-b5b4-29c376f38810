import React, { useRef, useEffect, useState } from 'react';
import { Box, Typography, Paper, Divider, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Grid, CircularProgress } from '@mui/material';
import { Invoice } from '../../../shared/types/invoice.types';
import { useCompany } from '../../../contexts/CompanyContext';
import dayjs from 'dayjs';

interface InvoicePDFPreviewProps {
  invoice: Invoice;
}

const InvoicePDFPreview: React.FC<InvoicePDFPreviewProps> = ({ invoice }) => {
  const { companyInfo } = useCompany();
  const previewRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time for PDF generation
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [invoice]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="500px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box ref={previewRef} sx={{ p: 4, bgcolor: 'white', width: '100%', maxWidth: '800px', mx: 'auto' }}>
      {/* Header */}
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={6}>
          <Typography variant="h4" gutterBottom>INVOICE</Typography>
          <Typography variant="body2">{companyInfo.companyName}</Typography>
          <Typography variant="body2">{companyInfo.address}</Typography>
          <Typography variant="body2">{companyInfo.city}, {companyInfo.state} {companyInfo.postalCode}</Typography>
          <Typography variant="body2">{companyInfo.country}</Typography>
          <Typography variant="body2">Phone: {companyInfo.phone}</Typography>
          <Typography variant="body2">Email: {companyInfo.email}</Typography>
        </Grid>
        <Grid item xs={6} sx={{ textAlign: 'right' }}>
          <Typography variant="body2" gutterBottom>Invoice #: {invoice.invoiceNumber}</Typography>
          <Typography variant="body2" gutterBottom>Date: {dayjs(invoice.invoiceDate).format('MMM D, YYYY')}</Typography>
          <Typography variant="body2" gutterBottom>Due Date: {dayjs(invoice.dueDate).format('MMM D, YYYY')}</Typography>
          <Typography variant="body2" gutterBottom>Status: {invoice.status.toUpperCase()}</Typography>
        </Grid>
      </Grid>

      {/* Customer Info */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>Bill To:</Typography>
        <Typography variant="body2">{invoice.customer.displayName}</Typography>
        <Typography variant="body2">{invoice.customer.billingAddress.street}</Typography>
        <Typography variant="body2">{invoice.customer.billingAddress.city}, {invoice.customer.billingAddress.state} {invoice.customer.billingAddress.postalCode}</Typography>
        <Typography variant="body2">{invoice.customer.billingAddress.country}</Typography>
      </Box>

      {/* Items Table */}
      <TableContainer component={Paper} variant="outlined" sx={{ mb: 4 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: 'grey.100' }}>
              <TableCell>Item</TableCell>
              <TableCell>Description</TableCell>
              <TableCell align="right">Qty</TableCell>
              <TableCell align="right">Unit Price</TableCell>
              <TableCell align="right">Tax</TableCell>
              <TableCell align="right">Amount</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {invoice.items.map((item, index) => (
              <TableRow key={index}>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.description}</TableCell>
                <TableCell align="right">{item.quantity}</TableCell>
                <TableCell align="right">{invoice.currency} {item.unitPrice.toFixed(2)}</TableCell>
                <TableCell align="right">{item.taxRate || 0}%</TableCell>
                <TableCell align="right">{invoice.currency} {item.subtotal.toFixed(2)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Totals */}
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mb: 4 }}>
        <Box sx={{ display: 'flex', width: '250px', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2">Subtotal:</Typography>
          <Typography variant="body2">{invoice.currency} {invoice.subtotal.toFixed(2)}</Typography>
        </Box>
        <Box sx={{ display: 'flex', width: '250px', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2">Tax:</Typography>
          <Typography variant="body2">{invoice.currency} {invoice.taxAmount.toFixed(2)}</Typography>
        </Box>
        <Divider sx={{ width: '250px', my: 1 }} />
        <Box sx={{ display: 'flex', width: '250px', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body1" fontWeight="bold">Total:</Typography>
          <Typography variant="body1" fontWeight="bold">{invoice.currency} {invoice.total.toFixed(2)}</Typography>
        </Box>
      </Box>

      {/* Payment Info */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>Payment Information:</Typography>
        <Typography variant="body2">Amount Due: {invoice.currency} {invoice.balanceDue.toFixed(2)}</Typography>
        <Typography variant="body2">Amount Paid: {invoice.currency} {invoice.paidAmount.toFixed(2)}</Typography>
      </Box>

      {/* Notes and Terms */}
      {invoice.notes && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>Notes:</Typography>
          <Typography variant="body2">{invoice.notes}</Typography>
        </Box>
      )}

      {invoice.terms && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>Terms & Conditions:</Typography>
          <Typography variant="body2">{invoice.terms}</Typography>
        </Box>
      )}

      {/* Footer */}
      <Box sx={{ mt: 6, pt: 2, borderTop: '1px solid #eee', display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="caption">Generated on {dayjs().format('MMM D, YYYY [at] h:mm A')}</Typography>
        <Typography variant="caption">Thank you for your business!</Typography>
        <Typography variant="caption">Page 1 of 1</Typography>
      </Box>
    </Box>
  );
};

export default InvoicePDFPreview; 