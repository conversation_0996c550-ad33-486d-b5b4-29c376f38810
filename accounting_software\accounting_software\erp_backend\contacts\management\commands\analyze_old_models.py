from django.core.management.base import BaseCommand
from django.db import models
from django.apps import apps
from django.db.models import Q
import sys


class Command(BaseCommand):
    help = 'Analyze dependencies of old Customer and Vendor models before cleanup'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== ANALYZING OLD MODEL DEPENDENCIES ==='))
        
        # Get model references
        try:
            SalesCustomer = apps.get_model('sales', 'Customer')
            PurchaseVendor = apps.get_model('purchase', 'Vendor')
            ContactCustomer = apps.get_model('contacts', 'Customer')
            ContactVendor = apps.get_model('contacts', 'Vendor')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting model references: {e}'))
            return

        # Analyze Sales Customer dependencies
        self.stdout.write(self.style.WARNING('\n1. SALES CUSTOMER MODEL ANALYSIS'))
        self.stdout.write(f'   - Sales Customer records: {SalesCustomer.objects.count()}')
        self.stdout.write(f'   - Contacts Customer records: {ContactCustomer.objects.count()}')
        
        # Check related models that reference Sales Customer
        customer_refs = self._get_model_references(SalesCustomer)
        self.stdout.write(f'   - Models referencing Sales Customer: {len(customer_refs)}')
        for ref in customer_refs:
            count = ref['model'].objects.filter(**{ref['field']: SalesCustomer.objects.first()}).count() if SalesCustomer.objects.exists() else 0
            self.stdout.write(f'     * {ref["app"]}.{ref["model_name"]}.{ref["field"]} - {count} records')

        # Analyze Purchase Vendor dependencies
        self.stdout.write(self.style.WARNING('\n2. PURCHASE VENDOR MODEL ANALYSIS'))
        self.stdout.write(f'   - Purchase Vendor records: {PurchaseVendor.objects.count()}')
        self.stdout.write(f'   - Contacts Vendor records: {ContactVendor.objects.count()}')
        
        # Check related models that reference Purchase Vendor
        vendor_refs = self._get_model_references(PurchaseVendor)
        self.stdout.write(f'   - Models referencing Purchase Vendor: {len(vendor_refs)}')
        for ref in vendor_refs:
            count = ref['model'].objects.filter(**{ref['field']: PurchaseVendor.objects.first()}).count() if PurchaseVendor.objects.exists() else 0
            self.stdout.write(f'     * {ref["app"]}.{ref["model_name"]}.{ref["field"]} - {count} records')

        # Check if data migration is needed
        self.stdout.write(self.style.WARNING('\n3. DATA MIGRATION STATUS'))
        
        # Check if old models have data that's not in contacts
        sales_customer_ids = set(SalesCustomer.objects.values_list('id', flat=True))
        contacts_customer_count = ContactCustomer.objects.count()
        
        purchase_vendor_ids = set(PurchaseVendor.objects.values_list('id', flat=True))
        contacts_vendor_count = ContactVendor.objects.count()
        
        self.stdout.write(f'   - Sales customers needing migration: {len(sales_customer_ids)}')
        self.stdout.write(f'   - Contacts customers available: {contacts_customer_count}')
        self.stdout.write(f'   - Purchase vendors needing migration: {len(purchase_vendor_ids)}')
        self.stdout.write(f'   - Contacts vendors available: {contacts_vendor_count}')

        # Recommendations
        self.stdout.write(self.style.SUCCESS('\n4. CLEANUP RECOMMENDATIONS'))
        
        if len(customer_refs) == 0 and len(sales_customer_ids) == 0:
            self.stdout.write('   ✅ Sales Customer model can be safely removed')
        else:
            self.stdout.write('   ⚠️  Sales Customer model has dependencies - migration needed')
            
        if len(vendor_refs) == 0 and len(purchase_vendor_ids) == 0:
            self.stdout.write('   ✅ Purchase Vendor model can be safely removed')
        else:
            self.stdout.write('   ⚠️  Purchase Vendor model has dependencies - migration needed')

        self.stdout.write(self.style.SUCCESS('\n=== ANALYSIS COMPLETE ==='))

    def _get_model_references(self, target_model):
        """Find all models that have foreign key references to target_model"""
        references = []
        
        for app_config in apps.get_app_configs():
            for model in app_config.get_models():
                for field in model._meta.get_fields():
                    if (hasattr(field, 'related_model') and 
                        field.related_model == target_model and
                        isinstance(field, (models.ForeignKey, models.OneToOneField))):
                        references.append({
                            'app': app_config.label,
                            'model': model,
                            'model_name': model.__name__,
                            'field': field.name
                        })
        
        return references 