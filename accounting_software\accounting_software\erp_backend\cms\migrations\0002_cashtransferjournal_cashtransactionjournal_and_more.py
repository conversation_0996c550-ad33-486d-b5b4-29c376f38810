# Generated by Django 4.2.21 on 2025-06-10 08:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('gl', '0005_add_multi_currency_support'),
        ('cms', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CashTransferJournal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cash_transfer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='journal_link', to='cms.cashtransfer')),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cms_transfers', to='gl.journalentry')),
            ],
            options={
                'db_table': 'cms_transfer_journal_link',
            },
        ),
        migrations.CreateModel(
            name='CashTransactionJournal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cash_transaction', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='journal_link', to='cms.cashtransaction')),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cms_transactions', to='gl.journalentry')),
            ],
            options={
                'db_table': 'cms_transaction_journal_link',
            },
        ),
        migrations.CreateModel(
            name='CashAccountBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0.0, help_text='Current balance calculated from journal entries', max_digits=15)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('cash_account', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='balance_record', to='cms.cashaccount')),
                ('gl_account', models.ForeignKey(help_text='Linked GL Asset account', on_delete=django.db.models.deletion.CASCADE, to='gl.account')),
                ('last_journal_entry', models.ForeignKey(blank=True, help_text='Last journal entry that affected this balance', null=True, on_delete=django.db.models.deletion.SET_NULL, to='gl.journalentry')),
            ],
            options={
                'db_table': 'cms_cash_account_balance',
            },
        ),
    ]
