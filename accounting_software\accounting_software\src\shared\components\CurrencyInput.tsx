import React, { useState, useEffect, useRef } from 'react';
import {
  TextField,
  InputAdornment,
  TextFieldProps,
} from '@mui/material';

interface CurrencyInputProps extends Omit<TextFieldProps, 'onChange' | 'value'> {
  value: number;
  onChange: (value: number) => void;
  currencySymbol?: string;
  decimalPlaces?: number;
  allowNegative?: boolean;
  placeholder?: string;
}

/**
 * A reusable currency input component that formats numbers with thousands separators
 * and decimal places while allowing users to type or paste values directly.
 * 
 * Features:
 * - Formats numbers with thousands separators (e.g., 10,000,000.00)
 * - Displays appropriate currency symbol
 * - Automatically formats on blur, Enter key, or Tab key
 * - Shows unformatted value when focused for easier editing
 * - Preserves decimal places consistently
 * - Uses placeholder "0.00" that disappears on focus
 */
const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChange,
  currencySymbol = '$',
  decimalPlaces = 2,
  allowNegative = false,
  placeholder = "0.00",
  ...textFieldProps
}) => {
  // State to track the displayed value (formatted string)
  const [displayValue, setDisplayValue] = useState<string>('');
  // State to track if we're in edit mode (unformatted)
  const [isEditing, setIsEditing] = useState<boolean>(false);
  // Ref to store the raw input value
  const rawInputRef = useRef<string>('');
  
  // Format a number as currency string
  const formatAsCurrency = (num: number): string => {
    // Handle negative numbers
    const sign = num < 0 ? '-' : '';
    const absNum = Math.abs(num);
    
    // Format with thousands separators and fixed decimal places
    const parts = absNum.toFixed(decimalPlaces).split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    return sign + parts.join('.');
  };
  
  // Parse a string to get the numeric value
  const parseValue = (str: string): number => {
    // Remove currency symbol, commas, and other non-numeric characters except decimal point and minus sign
    const cleanedStr = str.replace(new RegExp(`[^0-9${allowNegative ? '\\-' : ''}\\.]`, 'g'), '');
    
    // Handle case where only a minus sign is present
    if (cleanedStr === '-') return 0;
    
    // Parse the cleaned string to a number
    const parsedValue = parseFloat(cleanedStr);
    
    // Return 0 if parsing results in NaN
    return isNaN(parsedValue) ? 0 : parsedValue;
  };
  
  // Update the displayed value when the actual value changes
  useEffect(() => {
    if (!isEditing) {
      // Only show formatted value if it's not zero or we're not editing
      if (value === 0) {
        setDisplayValue('');
      } else {
        setDisplayValue(formatAsCurrency(value));
      }
    }
  }, [value, decimalPlaces, isEditing]);
  
  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    rawInputRef.current = inputValue; // Store the raw input
    
    // If the input is empty, set value to 0 but display empty string
    if (!inputValue.trim()) {
      setDisplayValue('');
      onChange(0);
      return;
    }
    
    // Parse the input to get the numeric value
    const numericValue = parseValue(inputValue);
    
    // Only update if the value is valid
    if (!allowNegative && numericValue < 0) {
      return;
    }
    
    // Update the parent component with the numeric value
    onChange(numericValue);
    
    // If the user is actively typing, don't format yet to avoid cursor jumping
    if (inputValue.endsWith('.') || inputValue.endsWith(',')) {
      setDisplayValue(inputValue);
    } else if (isEditing) {
      // When in edit mode, show the raw input
      setDisplayValue(inputValue);
    }
  };
  
  // Format the current value
  const formatCurrentValue = () => {
    // If the raw input ends with a decimal point, we should preserve it
    if (rawInputRef.current.endsWith('.') || rawInputRef.current.endsWith(',')) {
      const numericValue = parseValue(rawInputRef.current);
      onChange(numericValue);
    }
    
    setIsEditing(false);
    
    // Only show formatted value if it's not zero
    if (value === 0) {
      setDisplayValue('');
    } else {
      setDisplayValue(formatAsCurrency(value));
    }
  };
  
  // Handle key down events
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Format on Enter
    if (e.key === 'Enter') {
      formatCurrentValue();
    }
    
    // For Tab key, we'll format the value but not prevent default behavior
    // This allows normal tabbing between form fields
    if (e.key === 'Tab') {
      formatCurrentValue();
      // Do not call e.preventDefault() here to allow normal tab behavior
    }
    
    if (textFieldProps.onKeyDown) {
      textFieldProps.onKeyDown(e);
    }
  };
  
  // Handle focus to show unformatted value for easier editing
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // When focused, show the raw number without formatting
    // This makes it easier to edit
    setIsEditing(true);
    
    // Clear the field if the value is 0, so user doesn't need to delete it
    if (value === 0) {
      setDisplayValue('');
      rawInputRef.current = '';
    } else {
      rawInputRef.current = value.toString();
      // Only select the text if there's a non-zero value
      e.target.value = value.toString();
      e.target.select();
    }
    
    if (textFieldProps.onFocus) {
      textFieldProps.onFocus(e);
    }
  };
  
  // Handle blur to format the value
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // When blurred, format the value
    formatCurrentValue();
    
    if (textFieldProps.onBlur) {
      textFieldProps.onBlur(e);
    }
  };
  
  return (
    <TextField
      {...textFieldProps}
      value={displayValue}
      onChange={handleChange}
      onKeyDown={handleKeyDown}
      onFocus={handleFocus}
      onBlur={handleBlur}
      placeholder={placeholder}
      InputProps={{
        ...textFieldProps.InputProps,
        startAdornment: (
          <InputAdornment position="start">
            {currencySymbol}
          </InputAdornment>
        ),
      }}
    />
  );
};

export default CurrencyInput; 