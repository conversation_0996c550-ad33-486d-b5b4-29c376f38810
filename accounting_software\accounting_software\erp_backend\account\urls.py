from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(r'users', views.UserViewSet) # Register UserViewSet
router.register(r'company', views.CompanyViewSet)

urlpatterns = [
    path('', include(router.urls)), # Include router generated URLs
    path('user/', views.current_user, name='current-user'),
    path('company/current/', views.current_company, name='current-company'),
    path('csrf/', views.get_csrf_token, name='csrf-token'),
] 